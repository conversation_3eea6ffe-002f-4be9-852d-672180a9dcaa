<?php

use Common\Models\TrainingAction;

return [
    TrainingAction::CREATE_CUSTOMER => 'Create Customer',
    TrainingAction::CREATE_DRAWING => 'Create Drawing',
    TrainingAction::CREATE_BID => 'Create Bid',
    TrainingAction::UPDATE_COMPANY_LOGO => 'Update Company Logo',
    TrainingAction::UPDATE_COMPANY_WEBSITE => 'Update Company Website',
    TrainingAction::UPDATE_COMPANY_COLORS => 'Update Company Colors',
    TrainingAction::UPDATE_COMPANY_REPLY_TO_ADDRESS => 'Update Company Reply-To Address',
    TrainingAction::UPDATE_COMPANY_FROM_ADDRESS => 'Update Company From Address',
    TrainingAction::CREATE_PRODUCT => 'Create Product',
    TrainingAction::ADD_MARKETPLACE_FORM => 'Add Marketplace Form',
    TrainingAction::CREATE_TERMS_CONDITIONS_ITEM => 'Create Terms and Conditions Item',
    TrainingAction::CONNECT_QUICKBOOKS_ONLINE => 'Connect Quickbooks Online',
    TrainingAction::CONNECT_COMPANY_GOOGLE_CALENDAR => 'Connect Company Google Calendar',
    TrainingAction::CREATE_MEDIA_ITEM => 'Create Media Item',
    TrainingAction::UPDATE_EMAIL_TEMPLATE => 'Update Email Template',
    TrainingAction::CREATE_WARRANTY => 'Create Warranty',
    TrainingAction::UPDATE_BID_INTRO => 'Update Bid Intro',
    TrainingAction::UPLOAD_USER_PROFILE_PHOTO => 'Upload User Profile Photo',
    TrainingAction::UPDATE_USER_BIO => 'Update User Bio',
    TrainingAction::CONNECT_USER_GOOGLE_CALENDAR => 'Connect User Google Calendar',
    TrainingAction::SETUP_USER_CALENDAR_FEED => 'Setup User Calendar Feed'
];
