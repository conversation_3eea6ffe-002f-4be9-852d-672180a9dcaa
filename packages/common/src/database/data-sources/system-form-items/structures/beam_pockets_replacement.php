<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Dependencies\ProductItemDependency;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleConditionSideValues\NumericSideValue;
use App\Services\Form\Components\RuleEvents\LineItemAdd\ProductLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextField::make($group, 'unit-quantity')->setLabel('Unit Quantity')->setIsRequired()->setDisplayMaskDecimal(2);
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Beam Pockets Replacement Service Description')
                    ->setDescription('Add an explanation of your beam pockets replacement service. The description will be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                FieldLayoutItem::make($layout, 'unit-quantity')->setSize(4);
                FieldLayoutItem::make($layout, 'photos')->setSize(8)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                FieldLayoutItem::make($layout, 'unit-quantity')->setSize(4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                FieldLayoutItem::make($layout, 'unit-quantity')->setSize(4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('unit-quantity'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('unit-quantity'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-line-item')->setProduct('{{dependency:beam-pockets}}')->setQuantityField('unit-quantity');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-line-item');
                });
        });
})
    ->withDependencies(function (BidStructure $structure) {
        ProductItemDependency::make($structure, 'beam-pockets')->setLabel('Beam Pockets Replacement Product')
            ->setDescription('Choose or create the beam pockets replacement product. This will be used to create the product line item on the bid.')
            ->setDefaultProductItemName('Beam Pockets Replacement')
            ->setProductItemCreateInstruction('Create the Beam Pockets Replacement product needed for the form.');
    });
