<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Dependencies\ProductItemDependency;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleEvents\CalculateRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemAdd\ProductLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextField::make($group, 'height')->setLabel('Height (inches)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'width')->setLabel('Distance from Foundation (lf)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'length')->setLabel('Length (lf)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'total')->setLabel('Total Yards')->setDisplayDisabled();
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Grading Service Description')
                    ->setDescription('Add an explanation of your grading service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                FieldLayoutItem::make($layout, 'height')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
                FieldLayoutItem::make($layout, 'photos')->setSize(4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                FieldLayoutItem::make($layout, 'height')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                FieldLayoutItem::make($layout, 'height')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
            });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('height'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('width'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-grading')->setExpression('((((({{field:height|FLT}}/12)*{{field:width|FLT}})*0.5)*{{field:length|FLT}})/27)*2.25')->setResultField('total');
                    ProductLineItemAddRuleEvent::make($rule, 'add-grading')->setProduct('{{dependency:exterior-grading}}')->setQuantityField('total');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-grading');
                });
        });
})
    ->withDependencies(function (BidStructure $structure) {
        ProductItemDependency::make($structure, 'exterior-grading')->setLabel('Exterior Grading Product')
            ->setDescription('Choose or create the exterior grading product. The product will be used to calculate labor costs for your bid.')
            ->setDefaultProductItemName('Exterior Grading')
            ->setProductItemCreateInstruction('Create the Exterior Grading product needed for the form.');
    });
