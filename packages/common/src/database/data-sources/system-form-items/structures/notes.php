<?php

declare(strict_types=1);

use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Structures\BidStructure;

return BidStructure::make()
    ->withGroups(function (BidStructure $structure) {
        DefaultGroup::make($structure, 'main')
            ->withFields(function (DefaultGroup $group) {
                TextareaField::make($group, 'notes')->setLabel('Notes');
                FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
            })
            ->withLayouts(function (DefaultGroup $group) {
                InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                    FieldLayoutItem::make($layout, 'notes')->setSize(8);
                    FieldLayoutItem::make($layout, 'photos')->setSize(4);
                });
                OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
                OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
            });
    });