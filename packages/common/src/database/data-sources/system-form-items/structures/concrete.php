<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Concrete Service Description',
        'description' => 'Add an explanation of your concrete installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Concrete Product Category',
        'description' => 'Choose or create the concrete product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Concrete',
        'create_instruction' => 'Create the Concrete product category needed for the form.'
    ]
]);
