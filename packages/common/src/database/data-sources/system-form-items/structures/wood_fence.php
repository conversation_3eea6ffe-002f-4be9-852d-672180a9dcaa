<?php

declare(strict_types=1);

use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\GroupLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;
use Common\Classes\FormHelpers;

return BidStructure::make()
    ->withGroups(function (BidStructure $structure) {
        DefaultGroup::make($structure, 'main')
            ->withFields(function (DefaultGroup $group) {
                TextareaField::make($group, 'notes')->setLabel('Notes');
                FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
            })
            ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
                FormHelpers::productQuantityGroup($structure, $group, [
                    'table-alias' => 'fencing-table',
                    'name' => 'Fencing',
                    'product_category' => [
                        'label' => 'Wood Fence Product Category',
                        'description' => 'Choose or create the wood fence product category. The category will be used to attach and organize your products within the form dropdown.',
                        'default_name' => 'Wood Fence',
                        'create_instruction' => 'Create the Wood Fence product category needed for the form.'
                    ]
                ]);
                FormHelpers::productQuantityGroup($structure, $group, [
                    'table-alias' => 'gates-table',
                    'name' => 'Gates',
                    'product_category' => [
                        'label' => 'Wood Gates Product Category',
                        'description' => 'Choose or create the wood gates product category. The category will be used to attach and organize your products within the form dropdown.',
                        'default_name' => 'Wood Gates',
                        'create_instruction' => 'Create the Wood Gates product category needed for the form.'
                    ]
                ]);
            })
            ->withTemplates(function (DefaultGroup $group) {
                ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                    $override->setLabel('Wood Fence Description')
                        ->setDescription('Add an explanation of your wood fence installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
                });
            })
            ->withLayouts(function (DefaultGroup $group) {
                InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                    GroupLayoutItem::make($layout, 'fencing-table')->setSize(12);
                    GroupLayoutItem::make($layout, 'gates-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(8);
                    FieldLayoutItem::make($layout, 'photos')->setSize(4);
                });
                OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                    TemplateLayoutItem::make($layout, 'description')->setSize(12);
                    GroupLayoutItem::make($layout, 'fencing-table')->setSize(12);
                    GroupLayoutItem::make($layout, 'gates-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
                OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                    GroupLayoutItem::make($layout, 'fencing-table')->setSize(12);
                    GroupLayoutItem::make($layout, 'gates-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
            });
    });