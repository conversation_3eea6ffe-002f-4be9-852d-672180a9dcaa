<?php

declare(strict_types=1);

use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\GroupLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;
use Common\Classes\FormHelpers;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextField::make($group, 'floor-to-joist-measurement')->setLabel('What is the floor to joist measurement?')
                ->setDisplayLabel('Floor to joist measurement')->setDisplayMaskDecimal(2)->setDisplayIsInternal();
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
            FormHelpers::productQuantityGroup($structure, $group, [
                'table-alias' => 'product-table',
                'product_category' => [
                    'label' => 'Wall Braces Product Category',
                    'description' => 'Choose or create the wall braces product category. The category will be used to attach and organize your products within the form dropdown.',
                    'default_name' => 'Wall Braces',
                    'create_instruction' => 'Create the Wall Braces product category needed for the form.'
                ]
            ]);
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Wall Brace Service Description')
                    ->setDescription('Add an explanation of your wall brace installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'floor-to-joist-measurement')->setSize(4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
                FieldLayoutItem::make($layout, 'photos')->setSize(4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'floor-to-joist-measurement')->setSize(4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        });
});
