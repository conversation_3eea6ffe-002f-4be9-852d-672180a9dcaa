<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Radon Mitigation Service Description',
        'description' => 'Add an explanation of your radon mitigation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Radon Mitigation Product Category',
        'description' => 'Choose or create the radon mitigation product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Radon Mitigation',
        'create_instruction' => 'Create the Radon Mitigation product category needed for the form.'
    ]
]);
