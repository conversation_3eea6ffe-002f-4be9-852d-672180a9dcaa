<?php

declare(strict_types=1);

use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Structures\BidStructure;

return BidStructure::make()
    ->withGroups(function (BidStructure $structure) {
        DefaultGroup::make($structure, 'main')
            ->withFields(function (DefaultGroup $group) {
                TextareaField::make($group, 'field-notes')->setLabel('Notes')->setIsRequired()->setDisplayIsInternal();
            })
            ->withLayouts(function (DefaultGroup $group) {
                InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                    FieldLayoutItem::make($layout, 'field-notes')->setSize( 9);
                });
                OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                    FieldLayoutItem::make($layout, 'field-notes')->setSize( 12);
                });
            });
    });