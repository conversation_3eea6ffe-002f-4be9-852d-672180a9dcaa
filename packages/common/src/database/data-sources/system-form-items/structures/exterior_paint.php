<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Dependencies\ProductItemDependency;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\Option\SelectField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\Groups\RepeatableTableGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\GroupLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Layouts\TableRow\InputTableRowLayout;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleConditionSideValues\NumericSideValue;
use App\Services\Form\Components\RuleEvents\LineItemAdd\ProductLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use Common\Classes\FormHelpers;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextField::make($group, 'labor')->setLabel('Labor (days)')->setDisplayMaskInteger(2);
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
            FormHelpers::productQuantityGroup($structure, $group, [
                'table-alias' => 'product-table',
                'product_category' => [
                    'label' => 'Exterior Paint Product Category',
                    'description' => 'Choose or create the exterior paint product category. The category will be used to attach and organize your products within the form dropdown.',
                    'default_name' => 'Exterior Paint',
                    'create_instruction' => 'Create the Exterior Paint product category needed for the form.'
                ]
            ]);
            RepeatableTableGroup::make($structure, 'areas-table', $group)
                ->withFields(function (RepeatableTableGroup $group) {
                    SelectField::make($group, 'area')->withOptions([
                        'Back Patio', 'Eaves', 'Fascia Board', 'Front Door', 'Garage Door', 'Garage Door (frame only)',
                        'Gate', 'Other', 'Pressure Wash Before Painting', 'Railing', 'Rain Gutters', 'Screen Door',
                        'Shatter', 'Shutters', 'Small Storage', 'Stucco', 'Trim'
                    ])->setLabel('Area')->setIsRequired();
                    TextareaField::make($group, 'description')->setLabel('Description/Color');
                })
                ->withLayouts(function (RepeatableTableGroup $group) {
                    InputTableRowLayout::make($group)->withItems(function (InputTableRowLayout $layout) {
                        FieldLayoutItem::make($layout, 'area')->setSize(3);
                        FieldLayoutItem::make($layout, 'description')->setSize(9);
                    });
                });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('labor'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('labor'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-labor')->setProduct('{{dependency:labor}}')->setQuantityField('labor');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-labor');
                });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                GroupLayoutItem::make($layout, 'areas-table')->setSize(12);
                FieldLayoutItem::make($layout, 'labor')->setSize(3)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
                FieldLayoutItem::make($layout, 'photos')->setSize(4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                GroupLayoutItem::make($layout, 'areas-table')->setSize(12);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                GroupLayoutItem::make($layout, 'areas-table')->setSize(12);
                FieldLayoutItem::make($layout, 'labor')->setSize(3)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        });
})
    ->withDependencies(function (BidStructure $structure) {
        ProductItemDependency::make($structure, 'labor')->setLabel('Labor Product')
            ->setDescription('Choose or create the labor product. The product will be used to calculate labor costs for your bid.')
            ->setDefaultProductItemName('Labor')
            ->setProductItemCreateInstruction('Create the Labor product needed for the form.');
    });
