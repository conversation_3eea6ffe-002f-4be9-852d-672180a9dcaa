<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\Option\SelectField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\Groups\RepeatableTableGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\GroupLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Layouts\TableRow\InputTableRowLayout;
use App\Services\Form\Components\Layouts\TableRow\OutputTableRowLayout;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleConditionSideValues\NumericSideValue;
use App\Services\Form\Components\RuleEvents\LineItemAdd\GeneralLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;

return BidStructure::make()
    ->withGroups(function (BidStructure $structure) {
        DefaultGroup::make($structure, 'main')
            ->withFields(function (DefaultGroup $group) {
                TextareaField::make($group, 'notes')->setLabel('Notes');
                FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
            })
            ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
                RepeatableTableGroup::make($structure, 'obstructions-table', $group)
                    ->withFields(function (RepeatableTableGroup $group) {
                        TextField::make($group, 'obstruction')->setLabel('Obstruction')->setMaxLength(100);
                        SelectField::make($group, 'responsibility')->withOptions([
                            'Owner', 'Contractor'
                        ])->setLabel('Responsibility');
                        TextField::make($group, 'cost')->setLabel('Cost')->setDisplayMaskCurrency();
                    })
                    ->withLayouts(function (RepeatableTableGroup $group) {
                        InputTableRowLayout::make($group)->withItems(function (InputTableRowLayout $layout) {
                            FieldLayoutItem::make($layout, 'obstruction')->setSize(8);
                            FieldLayoutItem::make($layout, 'responsibility')->setSize(2);
                            FieldLayoutItem::make($layout, 'cost')->setSize(2);
                        });
                        OutputTableRowLayout::make($group)->withItems(function (OutputTableRowLayout $layout) {
                            FieldLayoutItem::make($layout, 'obstruction')->setSize(8);
                            FieldLayoutItem::make($layout, 'responsibility')->setSize(4);
                        });
                    })
                    ->withRules(function (RepeatableTableGroup $group) {
                        PresentationRule::make($group)
                            ->withConditions(function (PresentationRule $rule) {
                                AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                                    Condition::make($group)->setLeft(FieldSideValue::make('obstruction'))->notEmpty();
                                    Condition::make($group)->setLeft(FieldSideValue::make('cost'))->notEmpty();
                                    Condition::make($group)->setLeft(FieldSideValue::make('cost'))->greaterThan(NumericSideValue::make(0));
                                });
                            })
                            ->withPassEvents(function (PresentationRule $rule) {
                                GeneralLineItemAddRuleEvent::make($rule, 'add-obstruction')
                                    ->setName('{{field:obstruction}}')->setQuantity('1')->setPriceField('cost');
                            })
                            ->withFailEvents(function (PresentationRule $rule) {
                                LineItemRemoveRuleEvent::make($rule)->setEvent('add-obstruction');
                            });
                    });
            })
            ->withLayouts(function (DefaultGroup $group) {
                InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                    GroupLayoutItem::make($layout, 'obstructions-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(8);
                    FieldLayoutItem::make($layout, 'photos')->setSize(4);
                });
                OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                    GroupLayoutItem::make($layout, 'obstructions-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
                OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                    GroupLayoutItem::make($layout, 'obstructions-table')->setSize(12);
                    FieldLayoutItem::make($layout, 'notes')->setSize(12);
                });
            });
    });