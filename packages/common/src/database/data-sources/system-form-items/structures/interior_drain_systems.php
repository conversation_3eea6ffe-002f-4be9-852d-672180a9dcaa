<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Interior Drain Systems Service Description',
        'description' => 'Add an explanation of your interior drain systems installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Interior Drain Systems Product Category',
        'description' => 'Choose or create the interior drain systems product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Interior Drain Systems',
        'create_instruction' => 'Create the Interior Drain Systems product category needed for the form.'
    ]
]);
