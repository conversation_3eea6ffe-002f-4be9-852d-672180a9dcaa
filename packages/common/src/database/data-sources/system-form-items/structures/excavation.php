<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Excavation Service Description',
        'description' => 'Add an explanation of your excavation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Excavation Product Category',
        'description' => 'Choose or create the excavation product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Excavation',
        'create_instruction' => 'Create the Excavation product category needed for the form.'
    ]
]);
