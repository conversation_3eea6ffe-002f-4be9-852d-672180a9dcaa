<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;

class GoogleCalendarNotificationChannelHit extends Model
{
    use UuidTrait;

    public const STATUS_PENDING = 1;
    public const STATUS_COMPLETED = 2;

    protected $table = 'googleCalendarNotificationChannelHits';
    protected $primaryKey = 'googleCalendarNotificationChannelHitID';
    protected $fillable = [
        'googleCalendarNotificationChannelHitID', 'googleCalendarNotificationChannelID', 'status', 'count',
        'completedAt', 'createdAt'
    ];
    protected $casts = [
        'status' => 'int',
        'count' => 'int'
    ];
    protected $dates = ['completedAt', 'createdAt'];
    protected $dateFormat = 'Y-m-d H:i:s.u';

    public $timestamps = false;
}
