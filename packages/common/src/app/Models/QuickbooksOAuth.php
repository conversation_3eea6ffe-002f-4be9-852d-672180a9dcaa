<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuickbooksOAuth extends Model
{
    use SoftDeletes;

    public const STATUS_CONNECTED = 1;
    public const STATUS_DISCONNECTED = 2;

    protected $table = 'quickbooksOAuth';
    protected $primaryKey = 'quickbooksOAuthID';
    protected $fillable = [
        'companyID', 'status', 'realmID', 'accessToken', 'accessTokenExpiresAt', 'refreshToken',
        'refreshTokenExpiresAt', 'allowOnlinePayments', 'isCurrent', 'disconnectedAt', 'disconnectedByUserID',
        'isExternalDisconnect'
    ];
    protected $casts = [
        'companyID' => 'int',
        'status' => 'int',
        'allowOnlinePayments' => 'bool',
        'isCurrent' => 'bool',
        'disconnectedByUserID' => 'int',
        'isExternalDisconnect' => 'bool'
    ];
    protected $dates = ['accessTokenExpiresAt', 'refreshTokenExpiresAt', 'disconnectedAt'];
}
