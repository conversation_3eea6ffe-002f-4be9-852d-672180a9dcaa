<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\QuiteSaveTrait;
use Common\Traits\DB\UuidTrait;

class MailgunWebhookLog extends Model
{
    use QuiteSaveTrait;
    use UuidTrait;

    protected $connection = 'utility';

    protected $table = 'mailgunWebhookLog';
    protected $primaryKey = 'mailgunWebhookLogID';
    protected $fillable = [
        'mailgunWebhookLogID', 'body', 'createdAt'
    ];
    protected $casts = [
        'body' => 'array'
    ];
    protected $dates = ['createdAt'];
    protected $dateFormat = 'Y-m-d H:i:s.u';

    public $timestamps = false;
}
