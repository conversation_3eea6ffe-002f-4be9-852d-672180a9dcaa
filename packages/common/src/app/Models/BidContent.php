<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidContent extends HistoryEntityModel implements Interfaces\BidContentInterface
{
    use Common\BidContentCommon;

    protected $table = 'bidContent';
    protected $primaryKey = 'bidContentID';
    protected $fillable = [
        'bidContentID', 'companyID', 'type', 'status', 'name', 'content', 'contentHash', 'order', 'isDefault',
        'isLocked', 'isRequired', 'isAnswerRequired', 'archivedAt', 'archivedByUserID', 'createdAt', 'createdByUserID',
        'updatedAt', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
