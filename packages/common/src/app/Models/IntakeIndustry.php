<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class IntakeIndustry extends Model
{
    protected $table = 'intakeIndustries';
    protected $primaryKey = 'intakeIndustryID';
    protected $fillable = ['name', 'order'];

    public function companyIntakes()
    {
        return $this->hasMany(CompanyIntake::class, 'intakeIndustryID');
    }

    public function productItems()
    {
        return $this->hasMany(IntakeIndustryProductItem::class, 'intakeIndustryID');
    }
}
