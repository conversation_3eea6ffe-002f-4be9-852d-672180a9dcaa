<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class SubscriptionOption extends Model
{
    const INTERVAL_UNIT_DAY = 1;
    const INTERVAL_UNIT_MONTH = 2;
    const INTERVAL_UNIT_YEAR = 3;

    protected $table = 'subscriptionOptions';
    protected $primaryKey = 'subscriptionOptionID';
    protected $fillable = [
        'subscriptionID', 'name', 'description', 'pricingDescription', 'price', 'cost', 'users', 'intervalLength', 'intervalUnit', 'isPreferred'
    ];
    protected $casts = [
        'subscriptionID' => 'int',
        'users' => 'int',
        'intervalLength' => 'int',
        'intervalUnit' => 'int',
        'isPreferred' => 'bool'
    ];

    public function priceAdjustments()
    {
        return $this->hasMany(SubscriptionOptionPriceAdjustment::class, 'subscriptionOptionID');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscriptionID', 'subscriptionID');
    }
}
