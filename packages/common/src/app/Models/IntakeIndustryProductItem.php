<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class IntakeIndustryProductItem extends Model
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'intakeIndustryProductItems';
    protected $primaryKey = 'intakeIndustryProductItemID';
    protected $fillable = ['intakeIndustryID', 'name', 'description', 'price', 'unit', 'systemFormItemID', 'formName', 'formContent', 'createdAt', 'updatedAt'];

    public function industry()
    {
        return $this->belongsTo(intakeIndustry::class, 'intakeIndustryID', 'intakeIndustryID');
    }
}
