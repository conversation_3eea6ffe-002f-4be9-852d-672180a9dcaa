<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class CompanyFeature extends Model
{
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 2;

    protected $table = 'companiesFeatures';
    protected $primaryKey = 'companyFeatureID';
    protected $fillable = ['companyID', 'featureID', 'status'];
    protected $casts = [
        'companyID' => 'int',
        'featureID' => 'int',
        'status' => 'int'
    ];
}
