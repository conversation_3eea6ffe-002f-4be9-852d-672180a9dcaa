<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\HistoryEntityPivot;
use Common\Models\Common\ProductCategoryItemCommon;
use Common\Models\Interfaces\ProductCategoryItemInterface;

class ProductCategoryItem extends HistoryEntityPivot implements ProductCategoryItemInterface
{
    use ProductCategoryItemCommon;

    protected $table = 'productCategoriesItems';
    protected $primaryKey = 'productCategoryItemID';
}
