<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\Pivot;
use Common\Models\TrainingSectionModuleUserAction;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingSectionModuleUser extends Pivot
{
    use SoftDeletes;

    protected $table = 'trainingSectionModulesUsers';
    protected $primaryKey = 'trainingSectionModuleUserID';
    protected $fillable = [
        'trainingSectionModuleID', 'userID', 'isCompleted', 'completedAt'
    ];
    protected $casts = [
        'trainingSectionModuleID' => 'int',
        'userID' => 'int',
        'isCompleted' => 'bool'
    ];
    protected $dates = ['completedAt'];

    public function actions()
    {
        return $this->hasMany(TrainingSectionModuleUserAction::class, 'trainingSectionModuleUserID');
    }
}
