<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\HistoryEntityPivot;
use Common\Models\Common\BidContentProductItemCommon;
use Common\Models\Interfaces\BidContentProductItemInterface;

class BidContentProductItem extends HistoryEntityPivot implements BidContentProductItemInterface
{
    use BidContentProductItemCommon;

    protected $table = 'bidContentProductItems';
    protected $primaryKey = 'bidContentProductItemID';
}
