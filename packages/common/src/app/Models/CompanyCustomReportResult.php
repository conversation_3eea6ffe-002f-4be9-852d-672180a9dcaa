<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyCustomReportResult extends Model
{
    use UuidTrait;
    use SoftDeletes;

    public const STATUS_GENERATING = 1;
    public const STATUS_GENERATED = 2;
    public const STATUS_EXPIRED = 3;
    public const STATUS_FAILED = 4;

    protected $table = 'companyCustomReportResults';
    protected $primaryKey = 'companyCustomReportResultID';
    protected $fillable = [
        'companyCustomReportResultID', 'companyCustomReportID', 'userID', 'inputs', 'status', 'fileID', 'rowCount',
        'time', 'generatedAt', 'expiredAt', 'failedAt'
    ];
    protected $casts = [
        'userID' => 'int',
        'inputs' => 'array',
        'status' => 'int',
        'rowCount' => 'int',
        'time' => 'int'
    ];
    protected $dates = ['generatedAt', 'expiredAt', 'failedAt'];

    public function customReport()
    {
        return $this->belongsTo(CompanyCustomReport::class, 'companyCustomReportID', 'companyCustomReportID');
    }

    public function file()
    {
        return $this->belongsTo(File::class, 'fileID', 'fileID');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        return $query->where("{$this->table}.userID", $user);
    }
}
