<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class CompanyCreditCardPaymentMethod extends HistoryEntityModel implements Interfaces\CompanyCreditCardPaymentMethodInterface
{
    use Common\CompanyCreditCardPaymentMethodCommon;

    protected $table = 'companyCreditCardPaymentMethods';
    protected $primaryKey = 'companyCreditCardPaymentMethodID';
    protected $fillable = [
        'number', 'expirationDate', 'address', 'address2', 'city', 'state', 'zip', 'createdAt', 'createdByUserID',
        'updatedAt', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
