<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemFieldOption extends Model implements Interfaces\FormItemGroupFieldOptionInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'companyFormItemFieldOptions';
    protected $primaryKey = 'companyFormItemFieldOptionID';
    protected $fillable = [
        'companyFormItemFieldOptionID', 'companyFormItemFieldID', 'alias', 'label', 'order', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItemFields', 'companyFormItemFields.companyFormItemFieldID', '=', "{$this->table}.companyFormItemFieldID")
            ->join('companyFormItems', 'companyFormItems.companyFormItemID', 'companyFormItemFields.companyFormItemID');
        return $query->where('companyFormItems.companyID', $company);
    }
}
