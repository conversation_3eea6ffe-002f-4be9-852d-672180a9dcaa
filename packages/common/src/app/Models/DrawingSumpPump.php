<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrawingSumpPump extends Model
{
    use SoftDeletes;

    protected $table = 'drawingSumpPumps';
    protected $primaryKey = 'ID';
    protected $fillable = [
        'drawingID',
        'nodeID',
        'xPos',
        'yPos'
    ];
    protected $hidden = ['drawingID'];

    public function appDrawing()
    {
        return $this->belongsTo('AppDrawing', 'drawingID', 'drawingID');
    }
}
