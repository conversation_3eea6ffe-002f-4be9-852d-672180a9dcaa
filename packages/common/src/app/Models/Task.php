<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Task extends HistoryEntityModel implements Interfaces\TaskInterface, ScopeSearchInterface
{
    use Common\TaskCommon;

    protected $table = 'tasks';
    protected $primaryKey = 'taskID';
    protected $fillable = [
        'taskID', 'companyID', 'status', 'title', 'notes', 'completionNotes', 'type', 'priority', 'associationType',
        'associationID', 'associationUUID', 'assignedToUserID', 'dueDate', 'reminderType', 'completedAt',
        'completedByUserID', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
