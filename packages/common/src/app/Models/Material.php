<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Material extends HistoryEntityModel implements Interfaces\MaterialInterface, ScopeSearchInterface
{
    use Common\MaterialCommon;

    protected $table = 'materials';
    protected $primaryKey = 'materialID';
    protected $fillable = [
        'materialID', 'ownerType', 'ownerID', 'name', 'unitID', 'cost', 'markup', 'unitPrice', 'status', 'archivedAt',
        'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
