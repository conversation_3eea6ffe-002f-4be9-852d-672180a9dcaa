<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleService extends Model
{
    use SoftDeletes;
    use UuidTrait;

    public const SERVICE_CALENDAR = 1;

    public const STATUS_REQUESTED = 1;
    public const STATUS_CONNECTED = 2;
    public const STATUS_DISCONNECTING = 3;
    public const STATUS_DISCONNECTED = 4;
    public const STATUS_DISCONNECT_FAILED = 5;

    protected $table = 'googleServices';
    protected $primaryKey = 'googleServiceID';
    protected $fillable = [
        'googleServiceID', 'googleAuthRequestID', 'googleOAuthID', 'service', 'status', 'disconnectedAt'
    ];
    protected $casts = [
        'service' => 'int',
        'status' => 'int'
    ];
    protected $dates = ['disconnectedAt'];

    public function authRequest()
    {
        return $this->belongsTo(GoogleAuthRequest::class, 'googleAuthRequestID', 'googleAuthRequestID');
    }

    public function calendars()
    {
        return $this->hasMany(GoogleCalendar::class, 'googleServiceID');
    }

    public function oAuth()
    {
        return $this->belongsTo(GoogleOAuth::class, 'googleOAuthID', 'googleOAuthID');
    }
}
