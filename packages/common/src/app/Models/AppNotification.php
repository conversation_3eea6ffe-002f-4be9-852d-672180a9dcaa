<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppNotification extends Model implements ScopeSearchInterface
{
    use ScopeSearchTrait;
    use SoftDeletes;
    use UuidTrait;

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 2;

    const PLACEMENT_GLOBAL = 1;
    const PLACEMENT_NOTIFICATION_CENTER = 2;
    const PLACEMENT_BANNER = 3;

    const TYPE_INFO = 1;
    const TYPE_MAINTENANCE = 2;
    const TYPE_ERROR = 3;

    const ASSOCIATION_TYPE_CUSTOMER = 29;
    const ASSOCIATION_TYPE_PROPERTY = 47;
    const ASSOCIATION_TYPE_PROJECT = 48;
    const ASSOCIATION_TYPE_LEAD = 49;
    const ASSOCIATION_TYPE_TASK = 50;
    const ASSOCIATION_TYPE_BID = 51;


    protected $table = 'appNotifications';
    protected $primaryKey = 'appNotificationID';
    protected $casts = [
        'metadata' => 'array'
    ];

    protected $dates = [
        'createdAt',
        'updatedAt'
    ];
    protected $fillable = [
        'appNotificationID',
        'title',
        'summary',
        'content',
        'link',
        'metadata',
        'associationType',
        'associationID',
        'associationUUID',
        'placement',
        'type',
        'status',
        'intentKey',
        'automationType',
        'isCompletionRequired',
        'primaryButtonText',
        'secondaryButtonText',
        'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID',  'deletedAt', 'deletedByUserID'
    ];
    protected $searchColumns = ['title', 'summary'];


    public function toArray()
    {
        $array = parent::toArray();
        $array['appNotificationID'] = strtoupper(bin2hex($this->appNotificationID));
        $array['associationUUID'] = strtoupper(bin2hex($this->associationUUID));
        return $array;
    }

    public function distributions()
    {
        return $this->hasMany(AppNotificationDistribution::class, 'appNotificationID');
    }

    public function association()
    {
        return $this->morphTo('association', 'associationType', 'associationID');
    }

}
