<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormItemGroupField extends Model implements Interfaces\FormItemGroupFieldInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'formItemGroupFields';
    protected $primaryKey = 'formItemGroupFieldID';
    protected $fillable = [
        'formItemGroupFieldID', 'formItemGroupID', 'alias', 'type', 'label', 'displayLabel', 'alias', 'isRequired',
        'tooltip', 'config', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'isRequired' => 'bool',
        'config' => 'array',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function group()
    {
        return $this->belongsTo(FormItemGroup::class, 'formItemGroupID', 'formItemGroupID');
    }

    public function options()
    {
        return $this->hasMany(FormItemGroupFieldOption::class, 'formItemGroupFieldID');
    }

    public function products()
    {
        return $this->hasMany(FormItemGroupFieldProduct::class, 'formItemGroupFieldID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('formItemGroups', 'formItemGroups.formItemGroupID', "{$this->table}.formItemGroupID")
            ->join('formItems', 'formItems.formItemID', '=', 'formItemGroups.formItemID')
            ->leftJoin('companyFormItems', function ($join) {
                $join->where('formItems.ownerType', FormItem::OWNER_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItems.ownerID');
            })
            ->where(function ($query) use ($company) {
                $query->where('formItems.ownerType', FormItem::OWNER_TYPE_SYSTEM)
                    ->orWhere('companyFormItems.companyID', $company);
            });
    }
}
