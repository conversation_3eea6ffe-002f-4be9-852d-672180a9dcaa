<?php

namespace Common\Models;

use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Traits\DB\ScopeSearchTrait;
use Illuminate\Database\Eloquent\Model;

class EvaluationInvoice extends Model implements ScopeSearchInterface
{
    use ScopeSearchTrait;

    protected $table = 'evaluationInvoice';
    protected $primaryKey = null;
    protected $casts = [
        'evaluationID' => 'int'
    ];
    protected $searchColumns = ['invoiceNumber'];

    public $timestamps = false;

    public function evaluation()
    {
        return $this->belongsTo(Evaluation::class, 'evaluationID', 'evaluationID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('evaluation', 'evaluation.evaluationID', '=', "{$this->table}.evaluationID");
        $query->join('project', 'project.projectID', '=', "evaluation.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        $query->join('customer', 'customer.customerID', '=', 'property.customerID');
        return $query->where('customer.companyID', $company);
    }
}
