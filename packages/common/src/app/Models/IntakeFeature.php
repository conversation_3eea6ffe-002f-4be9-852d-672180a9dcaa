<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Models\Pivots\CompanyIntakeFeature;

class IntakeFeature extends Model
{
    protected $table = 'intakeFeatures';
    protected $primaryKey = 'intakeFeatureID';
    protected $fillable = ['name', 'order'];

    public function companyIntakes()
    {
        return $this->belongsToMany(CompanyIntake::class, 'companyIntakeFeatures', 'intakeFeatureID', 'companyIntakeID')
            ->using(CompanyIntakeFeature::class)
            ->withTimestamps();
    }
}
