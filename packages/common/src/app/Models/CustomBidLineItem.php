<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class CustomBidLineItem extends Model
{
    // @todo add hourly service type
    const TYPE_GENERAL = 1;
    const TYPE_PRODUCT = 2;
    const TYPE_SERVICE = 3;

    protected $table = 'customBidLineItems';
    protected $primaryKey = 'customBidLineItemID';

    public function item()
    {
        return $this->morphTo('item', 'type', 'itemID');
    }
}
