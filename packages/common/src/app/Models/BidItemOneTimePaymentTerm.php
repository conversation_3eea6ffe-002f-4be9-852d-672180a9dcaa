<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemOneTimePaymentTerm extends HistoryEntityModel implements Interfaces\BidItemOneTimePaymentTermInterface
{
    use Common\BidItemOneTimePaymentTermCommon;

    protected $table = 'bidItemOneTimePaymentTerms';
    protected $primaryKey = 'bidItemOneTimePaymentTermID';
    protected $fillable = [
        'bidItemOneTimePaymentTermID', 'dueTimeFrame', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
