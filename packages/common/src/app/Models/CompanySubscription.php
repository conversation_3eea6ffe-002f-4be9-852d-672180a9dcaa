<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class CompanySubscription extends HistoryEntityModel implements Interfaces\CompanySubscriptionInterface
{
    use Common\CompanySubscriptionCommon;

    protected $table = 'companySubscriptions';
    protected $primaryKey = 'companySubscriptionID';
    protected $fillable = [
        'companySubscriptionUUID', 'companyID', 'subscriptionOptionID', 'name', 'price', 'cost', 'users',
        'additionalUsers', 'intervalLength', 'intervalUnit', 'nextBillAt', 'firstBilledAt', 'lastBilledAt',
        'lastBillingCycle', 'status', 'isCurrent', 'replacedAt', 'replacedByUserID', 'replacedByCompanySubscriptionID',
        'cancelledAt', 'cancelledByUserID', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID', 'deletedAt',
        'deletedByUserID'
    ];
    protected $dates = ['firstBilledAt', 'lastBilledAt'];
}
