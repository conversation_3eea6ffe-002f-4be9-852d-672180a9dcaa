<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailMessageRecipient extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const STATUS_ACCEPTED = 1;
    const STATUS_FAILED = 2;
    const STATUS_DELIVERED = 3;
    const STATUS_OPENED = 4;

    const OPENED_BY_IP_ADDRESS_TYPE_V4 = 'IPv4';
    const OPENED_BY_IP_ADDRESS_TYPE_V6 = 'IPv6';

    protected $table = 'emailMessageRecipients';
    protected $primaryKey = 'emailMessageRecipientID';
    protected $fillable = [
        'emailMessageRecipientID', 'emailMessageID', 'emailMessageAddressID', 'status', 'deliveredAt', 'openedAt',
        'openedByIpAddressType', 'openedByIpAddress', 'openedByClientInfo', 'failedAt'
    ];
    protected $casts = [
        'status' => 'int',
        'openedByClientInfo' => 'array'
    ];
    protected $dateFormat = 'Y-m-d H:i:s.u';
    protected $dates = ['deliveredAt', 'openedAt', 'failedAt'];

    public function message()
    {
        return $this->belongsTo(EmailMessage::class, 'emailMessageID', 'emailMessageID');
    }

    public function address()
    {
        return $this->belongsTo(EmailMessageAddress::class, 'emailMessageAddressID', 'emailMessageAddressID');
    }
}
