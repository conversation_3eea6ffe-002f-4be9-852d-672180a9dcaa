<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TextNumberSuppression extends Model
{
    use SoftDeletes;

    const SOURCE_UNSUBSCRIBE = 1;
    const SOURCE_NUMBER_INVALID = 2; // twilio error - 21614

    protected $table = 'textNumberSuppressions';
    protected $primaryKey = 'textNumberSuppressionID';
    protected $fillable = ['source', 'number'];
    protected $casts = [
        'source' => 'int'
    ];
}
