<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Customer extends HistoryEntityModel implements Interfaces\CustomerInterface, ScopeSearchInterface
{
    use Common\CustomerCommon;

    protected $table = 'customer';
    protected $primaryKey = 'customerID';
    protected $fillable = [
        'customerUUID', 'companyID', 'leadID', 'quickbooksID', 'businessName', 'firstName', 'lastName', 'email', 'ownerAddress',
        'ownerAddress2', 'ownerCity', 'ownerState', 'ownerZip', 'isDuplicateMatchShown', 'isUnsubscribed',
        'unsubscribedAt', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUser<PERSON>', 'deletedAt', 'deletedByUserID'
    ];
}
