<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\CompanySubscription;
use Common\Models\SubscriptionOptionPriceAdjustment;

trait CompanySubscriptionPriceAdjustmentCommon
{
    protected $castMap = [
        'companySubscriptionID' => 'int',
        'subscriptionOptionPriceAdjustmentID' => 'int',
        'type' => 'int',
        'amountType' => 'int',
        'delayCount' => 'int',
        'occurrenceCount' => 'int',
        'cycleCount' => 'int',
        'billedCount' => 'int',
        'isRefundable' => 'bool',
        'isTransferable' => 'bool',
        'addonID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function subscription()
    {
        return $this->belongsTo(CompanySubscription::class, 'companySubscriptionID', 'companySubscriptionID');
    }

    public function subscriptionOptionPriceAdjustment()
    {
        return $this->belongsTo(SubscriptionOptionPriceAdjustment::class, 'subscriptionOptionPriceAdjustmentID', 'subscriptionOptionPriceAdjustmentID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companySubscriptions', 'companySubscriptions.companySubscriptionID', '=', "{$this->table}.companySubscriptionID");
        return $query->where('companySubscriptions.companyID', $company);
    }
}
