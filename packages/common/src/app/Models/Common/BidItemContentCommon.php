<?php

namespace Common\Models\Common;

use Common\Models\BidContent;
use Common\Models\BidItem;
use Common\Models\Company;
use Common\Traits\DB\UuidTrait;

trait BidItemContentCommon
{
    use UuidTrait;

    protected $castMap = [
        'type' => 'int',
        'source' => 'int',
        'status' => 'int',
        'isDefault' => 'bool',
        'isLocked' => 'bool',
        'isRequired' => 'bool',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function bidItem()
    {
        return $this->belongsTo(BidItem::class, 'bidItemID', 'bidItemID');
    }

    public function bidContent()
    {
        return $this->belongsTo(BidContent::class, 'bidContentID', 'bidContentID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('bidItems', 'bidItems.bidItemID', '=', "{$this->table}.bidItemID")
            ->join('project', 'project.projectID', '=', 'bidItems.projectID')
            ->join('customer', 'customer.customerID', '=', 'project.customerID');
        return $query->where('customer.companyID', $company);
    }
}
