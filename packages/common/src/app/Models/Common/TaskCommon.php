<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait TaskCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'status' => 'int',
        'type' => 'int',
        'priority' => 'int',
        'associationType' => 'int',
        'associationID' => 'int',
        'assignedToUserID' => 'int',
        'reminderType' => 'int',
        'completedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['dueDate', 'completedAt'];
    protected $searchColumns = ['title', 'notes', 'completionNotes'];

    public function association()
    {
        return $this->morphTo('association', 'associationType', 'associationID');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assignedToUserID', 'userID');
    }

    public function completedByUser()
    {
        return $this->belongsTo(User::class, 'completedByUserID', 'userID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        $query->where("{$this->table}.assignedToUserID", $user)
            ->orWhere("{$this->table}.createdByUserID", $user);
    }
}
