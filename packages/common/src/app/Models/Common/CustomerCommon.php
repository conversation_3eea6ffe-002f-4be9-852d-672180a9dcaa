<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\CustomerPhone;
use Common\Models\Lead;
use Common\Models\Property;
use Common\Models\Task;
use Common\Traits\DB\ScopeSearchTrait;

trait CustomerCommon
{
    use ScopeSearchTrait;

    protected $castMap = [
        'customerID' => 'int',
        'companyID' => 'int',
        'leadID' => 'int',
        'quickbooksID' => 'int',
        'isDuplicateMatchShown' => 'bool',
        'isUnsubscribed' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['unsubscribedAt'];
    protected $searchColumns = [
        'firstName', 'lastName', 'email', 'businessName', 'ownerAddress', 'ownerAddress2', 'ownerCity',
        'ownerState', 'ownerZip'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function lead()
    {
        return $this->belongsTo(Lead::class, 'leadID', 'leadID');
    }

    public function phones()
    {
        return $this->hasMany(CustomerPhone::class, 'customerID');
    }

    public function primaryPhone()
    {
        return $this->hasOne(CustomerPhone::class, 'customerID')->where('isPrimary', 1);
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'customerID');
    }

    public function textablePhones()
    {
        return $this->hasMany(CustomerPhone::class, 'customerID')
            ->where('type', CustomerPhone::TYPE_CELL);
    }

    public function canEmail()
    {
        return (!$this->isUnsubscribed && $this->email !== null);
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

    public function scopeWithPrimaryPhone($query)
    {
        return $query->join('customerPhone', function ($join) {
            $join->on('customerPhone.customerID', '=', 'customer.customerID')
                ->where('customerPhone.isPrimary', 1)
                ->whereNull('customerPhone.deletedAt');
        });
    }

    public function scopeWithProperties($query)
    {
        return $query->join('property', function ($join) {
            $join->on('property.customerID', '=', 'customer.customerID')
                ->whereNull('property.deletedAt');
        });
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'tasks', 'associationType', 'associationID');
    }
}
