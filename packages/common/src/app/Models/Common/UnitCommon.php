<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\Interfaces\UnitInterface;
use Common\Models\ProductItem;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait UnitCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'status' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name', 'abbreviation'];

    public function owner()
    {
        return $this->morphTo('owner', 'ownerType', 'ownerID');
    }

    public function productItems()
    {
        return $this->hasMany(ProductItem::class, 'unitID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where(function ($query) use ($company) {
            $query->where('ownerType', UnitInterface::OWNER_COMPANY)
                ->where('ownerID', $company);
        });
    }
}
