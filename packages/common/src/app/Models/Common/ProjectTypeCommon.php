<?php

namespace Common\Models\Common;

use App\Resources\ProjectTypeResource;
use Common\Models\Company;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait ProjectTypeCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'status' => 'int',
        'inactiveByUserID' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['inactiveAt', 'archivedAt'];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function inactiveByUser()
    {
        return $this->belongsTo(User::class, 'inactiveByUserID', 'userID');
    }

    public function archivedByUser()
    {
        return $this->belongsTo(User::class, 'archivedByUserID', 'userID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("name", 'asc');
    }

}
