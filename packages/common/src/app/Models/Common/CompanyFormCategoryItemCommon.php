<?php

namespace Common\Models\Common;

use Common\Models\CompanyFormCategory;
use Common\Models\CompanyFormItem;
use Common\Models\Company;
use Common\Traits\DB\UuidTrait;

trait CompanyFormCategoryItemCommon
{
    use UuidTrait;

    protected $castMap = [
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function category()
    {
        return $this->belongsTo(CompanyFormCategory::class, 'companyFormCategoryID', 'companyFormCategoryID');
    }

    public function item()
    {
        return $this->belongsTo(CompanyFormItem::class, 'companyFormItemID', 'companyFormItemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('companyFormCategories', 'companyFormCategories.companyFormCategoryID', '=', "{$this->table}.companyFormCategoryID")
            ->where('companyFormCategories.companyID', $company);
    }
}
