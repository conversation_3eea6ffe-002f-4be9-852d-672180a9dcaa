<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\FileVariant;
use Common\Traits\DB\UuidTrait;

trait FileCommon
{
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'type' => 'int',
        'status' => 'int',
        'size' => 'int',
        'data' => 'array',
        'time' => 'int',
        'isDataDeleted' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function variants()
    {
        return $this->hasMany(FileVariant::class, 'fileID');
    }

    public function scopeOfCompany($query, $company)
    {
        if ($company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }
}
