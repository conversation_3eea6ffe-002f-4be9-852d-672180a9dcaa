<?php

namespace Common\Models\Common;

use Common\Models\BidItemContent;
use Common\Models\BidItemCustomDrawing;
use Common\Models\BidItemDrawing;
use Common\Models\BidItemLineItem;
use Common\Models\BidItemMedia;
use Common\Models\BidItemPaymentTerm;
use Common\Models\BidItemSection;
use Common\Models\Company;
use Common\Models\File;
use Common\Models\Project;
use Common\Models\User;
use Common\Models\WisetackTransaction;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait BidItemCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'version' => 'int',
        'projectID' => 'int',
        'type' => 'int',
        'followUpNotificationStatus' => 'int',
        'status' => 'int',
        'isFileValid' => 'bool',
        'isFileGenerating' => 'bool',
        'isScopeOfWorkFileValid' => 'bool',
        'isScopeOfWorkFileGenerating' => 'bool',
        'isLocked' => 'bool',
        'submittedByUserID' => 'int',
        'finalizedByUserID' => 'int',
        'acceptedByUserID' => 'int',
        'cancelledByUserID' => 'int',
        'followUpNotificationsDisabledByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['submittedAt', 'finalizedAt', 'acceptedAt', 'cancelledAt', 'followUpNotificationsDisabledAt'];

    public function content()
    {
        return $this->hasMany(BidItemContent::class, 'bidItemID');
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'createdByUserID', 'userID');
    }

    public function customDrawings()
    {
        return $this->hasMany(BidItemCustomDrawing::class, 'bidItemID');
    }

    public function drawings()
    {
        return $this->hasMany(BidItemDrawing::class, 'bidItemID');
    }

    public function file()
    {
        return $this->belongsTo(File::class, 'fileID', 'fileID');
    }

    public function lineItems()
    {
        return $this->hasMany(BidItemLineItem::class, 'bidItemID');
    }

    public function media()
    {
        return $this->hasMany(BidItemMedia::class, 'bidItemID');
    }

    public function paymentTerms()
    {
        return $this->hasMany(BidItemPaymentTerm::class, 'bidItemID');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'projectID', 'projectID');
    }

    public function scopeOfWorkFile()
    {
        return $this->belongsTo(File::class, 'scopeOfWorkFileID', 'fileID');
    }

    public function sections()
    {
        return $this->hasMany(BidItemSection::class, 'bidItemID');
    }

    public function submittedByUser()
    {
        return $this->belongsTo(User::class, 'submittedByUserID', 'userID');
    }

    public function wisetackTransactions()
    {
        return $this->hasMany(WisetackTransaction::class, 'bidItemID');
    }

    public function scopeWithProperty($query)
    {
        $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        return $query;
    }

    public function scopeWithCustomer($query)
    {
        return $this->scopeWithProperty($query)
            ->join('customer', 'customer.customerID', '=', 'property.customerID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $this->scopeWithCustomer($query)->where('customer.companyID', $company);
    }

    public function scopeWithLegacyEvaluation($query)
    {
        return $query->join('evaluation', 'evaluation.bidItemID', '=', "{$this->table}.bidItemID")
            ->join('customBid', 'customBid.evaluationID', '=', 'evaluation.evaluationID');
    }

    public function scopeOfUser($query, $user)
    {
        $company = $user->company;
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }

        return $this->scopeOfCompany($query, $company)
            ->where('project.projectSalesperson', $user)
            ->orWhere('project.createdByUserID', $user);
    }
}
