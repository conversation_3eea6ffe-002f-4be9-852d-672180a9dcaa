<?php

namespace Common\Models\Common;

use Common\Models\BidItemInstallmentPaymentTerm;
use Common\Models\Company;
use Common\Traits\DB\UuidTrait;

trait BidItemInstallmentPaymentTermInstallmentCommon
{
    use UuidTrait;

    protected $castMap = [
        'dueTimeFrame' => 'int',
        'amountType' => 'int',
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function installmentPaymentTerm()
    {
        return $this->belongsTo(BidItemInstallmentPaymentTerm::class, 'bidItemInstallmentPaymentTermID', 'bidItemInstallmentPaymentTermID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('bidItemInstallmentPaymentTerms', 'bidItemInstallmentPaymentTerms.bidItemInstallmentPaymentTermID', '=', "{$this->table}.bidItemInstallmentPaymentTermID")
            ->join('user', 'user.userID', '=', 'bidItemInstallmentPaymentTerms.createdByUserID');
        return $query->where('user.companyID', $company);
    }
}
