<?php

namespace Common\Models\Common;

use Common\Models\FormType;
use Common\Models\Interfaces\SystemFormCategoryInterface;
use Common\Models\Pivots\SystemFormCategoryItem;
use Common\Models\SystemFormItem;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait SystemFormCategoryCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'type' => 'int',
        'status' => 'int',
        'order' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name'];

    public function children()
    {
        return $this->hasMany(static::class, 'parentSystemFormCategoryID');
    }

    public function formType()
    {
        return $this->belongsTo(FormType::class, 'type', 'formTypeID');
    }

    public function items()
    {
        return $this->belongsToMany(
            SystemFormItem::class, 'systemFormCategoriesItems', 'systemFormCategoryID',
            'systemFormItemID'
        )
            ->using(SystemFormCategoryItem::class)
            ->withPivot('systemFormCategoryItemID', 'createdByUserID', 'updatedByUserID', 'deletedByUserID')
            ->withTimestamps()
            ->whereNull('systemFormCategoriesItems.deletedAt');
    }

    public function parent()
    {
        return $this->belongsTo(static::class, 'parentSystemFormCategoryID', 'systemFormCategoryID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", SystemFormCategoryInterface::STATUS_ACTIVE);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.name", 'asc');
    }
}
