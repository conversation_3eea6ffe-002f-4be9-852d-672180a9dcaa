<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\Interfaces\MaterialInterface;
use Common\Models\Unit;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait MaterialCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'status' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name'];

    public function owner()
    {
        return $this->morphTo('owner', 'ownerType', 'ownerID');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unitID', 'unitID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", MaterialInterface::STATUS_ACTIVE);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.name", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where(function ($query) use ($company) {
            $query->where("{$this->table}.ownerType", MaterialInterface::OWNER_COMPANY)
                ->where("{$this->table}.ownerID", $company);
        });
    }
}
