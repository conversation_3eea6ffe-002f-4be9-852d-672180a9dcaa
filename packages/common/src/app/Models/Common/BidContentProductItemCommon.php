<?php

namespace Common\Models\Common;

use Common\Models\BidContent;
use Common\Models\Company;
use Common\Models\ProductItem;
use Common\Traits\DB\UuidTrait;

trait BidContentProductItemCommon
{
    use UuidTrait;

    protected $castMap = [
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function bidContent()
    {
        return $this->belongsTo(BidContent::class, 'bidContentID', 'bidContentID');
    }

    public function productItem()
    {
        return $this->belongsTo(ProductItem::class, 'productItemID', 'productItemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('bidContent', 'bidContent.bidContentID', '=', "{$this->table}.bidContentID")
            ->where('bidContent.companyID', $company);
    }
}
