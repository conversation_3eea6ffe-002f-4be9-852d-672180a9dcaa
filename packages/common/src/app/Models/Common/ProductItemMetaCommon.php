<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\ProductItem;
use Common\Traits\DB\UuidTrait;

trait ProductItemMetaCommon
{
    use UuidTrait;

    protected $castMap = [
        'valueType' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function product()
    {
        return $this->belongsTo(ProductItem::class, 'productItemID', 'productItemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join(
            'productItems',
            function ($join) use ($company) {
                $join->on('productItems.productItemID', '=', "{$this->table}.productItemID")
                    ->where('productItems.ownerType', ProductItem::OWNER_COMPANY)
                    ->where('productItems.ownerID', $company);
            }
        );
    }
}
