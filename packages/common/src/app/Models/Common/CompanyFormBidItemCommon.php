<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\CompanyFormItem;
use Common\Models\Interfaces\CompanyFormItemInterface;
use Common\Traits\DB\UuidTrait;

trait CompanyFormBidItemCommon
{
    use UuidTrait;

    protected $castMap = [
        'isBidDefault' => 'bool',
        'isSectionDefault' => 'bool',
        'isHiddenFromList' => 'bool',
        'isHiddenFromBid' => 'bool',
        'isHiddenFromScopeOfWork' => 'bool',
        'isFormNameHidden' => 'bool',
        'defaultOrder' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function item()
    {
        return $this->morphOne(CompanyFormItem::class, 'item', 'type', 'itemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItems', function ($join) {
            $join->on('companyFormItems.itemID', '=', 'companyFormBidItems.companyFormBidItemID')
                ->where('companyFormItems.type', CompanyFormItemInterface::TYPE_BID);
        });
        return $query->where('companyFormItems.companyID', $company);
    }
}
