<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class CompanyFormCategory extends HistoryEntityModel implements Interfaces\CompanyFormCategoryInterface, ScopeSearchInterface
{
    use Common\CompanyFormCategoryCommon;

    protected $table = 'companyFormCategories';
    protected $primaryKey = 'companyFormCategoryID';
    protected $fillable = [
        'companyFormCategoryID', 'companyID', 'parentCompanyFormCategoryID', 'type', 'status', 'name', 'order', 'archivedAt',
        'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
