<?php

declare(strict_types=1);

namespace Common\Models;

use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppNotificationDistribution extends Model
{
    use MutableTrait;
    use TimestampFieldsTrait;
    use UuidTrait;

    const STATUS_UNREAD = 1;    // User has not seen the notification. Initial status.
    const STATUS_SEEN = 2;      // User has opened/seen the notification.
    const STATUS_READ = 3;      // User has read/acknowledged/dismissed the notification.

    const STATUS_COMPLETED = 4; // User has made the primary action of the notification.

    protected $table = 'appNotificationDistribution';
    protected $primaryKey = 'appNotificationDistributionID';
    protected $dates = [
        'createdAt',
        'updatedAt'
    ];
    protected $fillable = [
        'appNotificationDistributionID',
        'appNotificationID',
        'userID',
        'companyID',
        'status',
        'createdAt', 'createdByID', 'updatedAt', 'updatedByID',
        'seenAt', 'readAt', 'completedAt'
    ];


    public function toArray()
    {
        $array = parent::toArray();
        $array['appNotificationDistributionID'] = strtoupper(bin2hex($this->appNotificationDistributionID));
        $array['appNotificationID'] = strtoupper(bin2hex($this->appNotificationID));
        return $array;
    }

    public function notification()
    {
        return $this->belongsTo(AppNotification::class, 'appNotificationID');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        return $query->where("{$this->table}.userID", $user);
    }

    /**
     * Scope a query to retrieve notifications for the Notification Center.
     *
     * This scope applies the following logic:
     * - For notifications that require completion:
     *     - Status is UNREAD, SEEN, or READ, and the related notification has isCompletionRequired = true.
     * - OR for notifications that do NOT require completion:
     *     - Status is UNREAD and the related notification has isCompletionRequired = false.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForNotificationCenter($query)
    {
        return $query->where(function ($outerQuery) {
            // For notifications requiring completion
            $outerQuery->where(function ($q) {
                $q->whereIn('status', [
                    self::STATUS_UNREAD,
                    self::STATUS_SEEN,
                    self::STATUS_READ,
                ])->whereHas('notification', function ($q2) {
                    $q2->where('isCompletionRequired', true);
                });
            })
                // OR for notifications that do NOT require completion
                ->orWhere(function ($q) {
                    $q->whereIn('status', [
                        self::STATUS_UNREAD,
                    ])->whereHas('notification', function ($q2) {
                        $q2->where('isCompletionRequired', false);
                    });
                });
        });
    }

}