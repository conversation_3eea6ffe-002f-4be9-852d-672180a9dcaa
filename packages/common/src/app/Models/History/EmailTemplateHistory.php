<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\EmailTemplateCommon;
use Common\Models\Interfaces\EmailTemplateInterface;

class EmailTemplateHistory extends HistoryModel implements EmailTemplateInterface, ScopeSearchInterface
{
    use EmailTemplateCommon;

    protected $table = 'emailTemplatesHistory';
    protected $primaryKey = 'emailTemplatesHistoryID';
    protected $entityPrimaryKey = 'emailTemplateID';
}
