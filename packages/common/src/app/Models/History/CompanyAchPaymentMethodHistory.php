<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\CompanyAchPaymentMethodCommon;
use Common\Models\Interfaces\CompanyAchPaymentMethodInterface;

class CompanyAchPaymentMethodHistory extends HistoryModel implements CompanyAchPaymentMethodInterface
{
    use CompanyAchPaymentMethodCommon;

    protected $table = 'companyAchPaymentMethodsHistory';
    protected $primaryKey = 'companyAchPaymentMethodsHistoryID';
    protected $entityPrimaryKey = 'companyAchPaymentMethodID';
}
