<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\CustomerPhoneCommon;
use Common\Models\Interfaces\CustomerPhoneInterface;

class CustomerPhoneHistory extends HistoryModel implements CustomerPhoneInterface, ScopeSearchInterface
{
    use CustomerPhoneCommon;

    protected $table = 'customerPhoneHistory';
    protected $primaryKey = 'customerPhoneHistoryID';
    protected $entityPrimaryKey = 'customerPhoneID';
}
