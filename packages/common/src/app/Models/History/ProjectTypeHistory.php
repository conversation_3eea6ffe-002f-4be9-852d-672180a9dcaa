<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\ProjectTypeCommon;
use Common\Models\Interfaces\ProjectTypeInterface;

class ProjectTypeHistory extends HistoryModel implements ProjectTypeInterface, ScopeSearchInterface
{
    use ProjectTypeCommon;

    protected $table = 'projectTypesHistory';
    protected $primaryKey = 'projectTypesHistoryID';
    protected $entityPrimaryKey = 'projectTypeID';
}
