<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\CompanyFormCategoryItemCommon;
use Common\Models\Interfaces\CompanyFormCategoryItemInterface;

class CompanyFormCategoryItemHistory extends HistoryModel implements CompanyFormCategoryItemInterface
{
    use CompanyFormCategoryItemCommon;

    protected $table = 'companyFormCategoriesItemsHistory';
    protected $primaryKey = 'companyFormCategoriesItemsHistoryID';
    protected $entityPrimaryKey = 'companyFormCategoryItemID';
}
