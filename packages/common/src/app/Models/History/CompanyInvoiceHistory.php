<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\CompanyInvoiceCommon;
use Common\Models\Interfaces\CompanyInvoiceInterface;

class CompanyInvoiceHistory extends HistoryModel implements CompanyInvoiceInterface
{
    use CompanyInvoiceCommon;

    protected $table = 'companyInvoicesHistory';
    protected $primaryKey = 'companyInvoicesHistoryID';
    protected $entityPrimaryKey = 'companyInvoiceID';
}
