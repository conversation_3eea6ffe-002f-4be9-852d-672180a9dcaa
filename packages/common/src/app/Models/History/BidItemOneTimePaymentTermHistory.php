<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\BidItemOneTimePaymentTermCommon;
use Common\Models\Interfaces\BidItemOneTimePaymentTermInterface;

class BidItemOneTimePaymentTermHistory extends HistoryModel implements BidItemOneTimePaymentTermInterface
{
    use BidItemOneTimePaymentTermCommon;

    protected $table = 'bidItemOneTimePaymentTermsHistory';
    protected $primaryKey = 'bidItemOneTimePaymentTermsHistoryID';
    protected $entityPrimaryKey = 'bidItemOneTimePaymentTermID';
}
