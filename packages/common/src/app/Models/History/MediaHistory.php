<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\MediaCommon;
use Common\Models\Interfaces\MediaInterface;

class MediaHistory extends HistoryModel implements MediaInterface, ScopeSearchInterface
{
    use MediaCommon;

    protected $table = 'mediaHistory';
    protected $primaryKey = 'mediaHistoryID';
    protected $entityPrimaryKey = 'mediaID';
}
