<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\EmailValidationCommon;
use Common\Models\Interfaces\EmailValidationInterface;

class EmailValidationHistory extends HistoryModel implements EmailValidationInterface
{
    use EmailValidationCommon;

    protected $table = 'emailValidationsHistory';
    protected $primaryKey = 'emailValidationsHistoryID';
    protected $entityPrimaryKey = 'emailValidationID';
}
