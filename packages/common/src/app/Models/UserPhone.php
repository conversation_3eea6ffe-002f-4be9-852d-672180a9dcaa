<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserPhone extends Model
{
    use SoftDeletes;

    protected $table = 'userPhone';
    protected $primaryKey = 'userPhoneID';
    protected $casts = [
        'userID' => 'int',
        'isPrimary' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $fillable = [
        'userID', 'phoneNumber', 'phoneDescription', 'isPrimary', 'createdByUserID', 'updatedByUserID', 'deletedAt',
        'deletedByUserID'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('user', 'user.userID', '=', "{$this->table}.userID");
        return $query->where('user.companyID', $company);
    }
}
