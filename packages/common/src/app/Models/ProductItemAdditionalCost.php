<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class ProductItemAdditionalCost extends HistoryEntityModel implements Interfaces\ProductItemAdditionalCostInterface
{
    use Common\ProductItemAdditionalCostCommon;

    protected $table = 'productItemAdditionalCosts';
    protected $primaryKey = 'productItemAdditionalCostID';
    protected $fillable = [
        'productItemAdditionalCostID', 'productItemID', 'additionalCostID', 'unitPrice', 'quantity', 'total', 'order',
        'status', 'archivedAt', 'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
