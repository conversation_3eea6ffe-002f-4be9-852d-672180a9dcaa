<?php

namespace Common\Models;

use Illuminate\Database\Eloquent\Model;

class EvaluationDrawing extends Model
{
    protected $table = 'evaluationDrawing';
    protected $primaryKey = 'evaluationDrawingID';
    protected $casts = [
        'evaluationID' => 'int'
    ];

    public $timestamps = false;

    public function evaluation()
    {
        return $this->belongsTo(Evaluation::class, 'evaluationID', 'evaluationID');
    }
}
