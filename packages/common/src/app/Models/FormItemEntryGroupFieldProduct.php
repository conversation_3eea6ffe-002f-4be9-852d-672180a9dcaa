<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class FormItemEntryGroupFieldProduct extends HistoryEntityModel implements Interfaces\FormItemEntryGroupFieldProductInterface
{
    use Common\FormItemEntryGroupFieldProductCommon;

    protected $table = 'formItemEntryGroupFieldProducts';
    protected $primaryKey = 'formItemEntryGroupFieldProductID';
    protected $fillable = [
        'formItemEntryGroupFieldProductID', 'formItemEntryGroupID', 'fieldSource', 'fieldID', 'productItemID',
        'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
