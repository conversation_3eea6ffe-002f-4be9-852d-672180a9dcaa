<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemPaymentTerm extends HistoryEntityModel implements Interfaces\BidItemPaymentTermInterface
{
    use Common\BidItemPaymentTermCommon;

    protected $table = 'bidItemPaymentTerms';
    protected $primaryKey = 'bidItemPaymentTermID';
    protected $fillable = [
        'bidItemPaymentTermID', 'bidItemID', 'type', 'itemID', 'order', 'createdByUserID', 'updatedByUserID',
        'deletedAt', 'deletedByUserID'
    ];
}
