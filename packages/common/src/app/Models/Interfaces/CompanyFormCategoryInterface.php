<?php

namespace Common\Models\Interfaces;

/**
 * Interface CompanyFormCategoryInterface
 *
 * @package Common\Models\Interfaces
 */
interface CompanyFormCategoryInterface
{
    const TYPE_BID = 14;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    public function children();

    public function company();

    public function formType();

    public function items();

    public function parent();

    public function scopeActive($query);

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
