<?php

namespace Common\Models\Interfaces;

/**
 * Interface CompanyInterface
 *
 * @package Common\Models\Interfaces
 */
interface CompanyInterface
{
    const STATUS_SIGNUP = 4;
    const STATUS_TRIAL = 5;
    const STATUS_ACTIVE = 1;
    const STATUS_SUSPENDED = 2;
    const STATUS_DORMANT = 3;

    const SIGNUP_STATUS_ADDRESS = 1;
    const SIGNUP_STATUS_INTAKE = 2;
    const SIGNUP_STATUS_SUBSCRIPTION = 3;
    const SIGNUP_STATUS_COMPLETE = 4;

    const SETUP_WIZARD_STEP_INSTRUCTION = 0;
    const SETUP_WIZARD_STEP_GENERAL = 1;
    const SETUP_WIZARD_STEP_USERS = 2;
    const SETUP_WIZARD_STEP_BID_CUSTOMIZATION = 3;
    const SETUP_WIZARD_STEP_EMAILS = 4;
    const SETUP_WIZARD_STEP_TERMS_CONDITIONS = 5;
    const SETUP_WIZARD_STEP_PRODUCTS = 6;
    const SETUP_WIZARD_STEP_MEDIA = 7;
    const SETUP_WIZARD_STEP_WARRANTY_PACKET = 8;
    const SETUP_WIZARD_STEP_QUICKBOOKS_ONLINE = 9;
    const SETUP_WIZARD_STEP_GOOGLE_CALENDAR = 10;
    const SETUP_WIZARD_STEP_ADDITIONAL_SERVICES = 11;
    const SETUP_WIZARD_STEP_REVIEW = 12;
    const SETUP_WIZARD_STEP_COMPLETE = 13;

    public function customers();

    public function currentSubscription();

    public function defaultPaymentMethod();

    public function emailTemplates();

    public function features();

    public function invoiceCredits();

    public function leads();

    public function logo();

    public function paymentMethods();

    public function phones();

    public function primaryPhone();

    public function products();

    public function productCategories();

    public function projectTypes();

    public function projectCostCategories();

    public function projectCostTypes();

    public function registration();

    public function reseller();

    public function setup();

    public function smtpCredential();

    public function subscription();

    public function subscriptions();

    public function successManager();

    public function users();

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     * @todo rename to timezone when timezone column is removed from company table
     */
    public function _timezone();

    public function scopeWithPrimaryDomain($query);

    public function scopeWithTimezone($query);

    public function scopeOfCompany($query, $company);
}
