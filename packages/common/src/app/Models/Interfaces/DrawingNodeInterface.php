<?php

namespace Common\Models\Interfaces;

/**
 * Interface DrawingInterface
 *
 * @package Common\Models\Interfaces
 */
interface DrawingNodeInterface
{
    public const TYPE_WALL = 1; // now called FOUNDATION
    public const TYPE_WALL_BRACE = 3;
    public const TYPE_PIER = 4;
    public const TYPE_SUMP_PUMP = 5;
    public const TYPE_MUDJACKING = 6;
    public const TYPE_POLYFOAM = 7;
    public const TYPE_INTERIOR_DRAIN_TILE = 8;
    public const TYPE_NOTE = 9;
    public const TYPE_CUSTOM_SERVICE = 10;
    public const TYPE_COMPASS = 11;
    public const TYPE_INTERIOR_PIER = 12;
    public const TYPE_SUPPORT_POST = 13;
    public const TYPE_WALL_CRACK = 14;
    public const TYPE_FLOOR_CRACK = 15;
    public const TYPE_SOIL_ANCHOR = 16;
    public const TYPE_DEADMAN = 17;
    public const TYPE_CARBON_FIBER = 18;
    public const TYPE_ARROW = 19;
    public const TYPE_LINE_DIMENSION_MAIN = 20;
    public const TYPE_LINE_DIMENSION_ATTACHMENT = 21;
    public const TYPE_INTERIOR_WALL = 22;
    public const TYPE_EXTERIOR_DRAIN = 23;
    public const TYPE_FURNACE = 24;
    public const TYPE_AC = 25;
    public const TYPE_WATER_HEATER = 26;
    public const TYPE_VEGETATION = 27;
    public const TYPE_BEAM = 28;
    public const TYPE_LINE = 29;
    public const TYPE_RECTANGLE = 30;
    public const TYPE_SQUARE = 31;
    public const TYPE_ELLIPSE = 32;
    public const TYPE_CIRCLE = 33;
    // 34 is used by connector node which isn't persisted
    public const TYPE_LINTEL_REPAIR = 35;
    public const TYPE_SCALE = 36;
    public const TYPE_STAIRS = 37;
    public const TYPE_DEHUMIDIFIER = 38;
    public const TYPE_DOOR = 39;
    public const TYPE_WINDOW = 40;
    public const TYPE_EXTERIOR_WALL = 41;
    public const TYPE_FENCE = 42;
    public const TYPE_EXISTING_FENCE = 43;
    public const TYPE_GATE = 44;
    public const TYPE_UTILITY = 45;
    public const TYPE_CONCRETE = 46;
    public const TYPE_ENCAPSULATION = 47;
    public const TYPE_FREEFORM_LINE = 48;
    public const TYPE_GARAGE_DOOR = 49;
    public const TYPE_DOUBLE_DOOR = 50;
    public const TYPE_SLIDING_DOOR = 51;
    public const TYPE_DOUBLE_GATE = 52;
    public const TYPE_POLYGON = 53;
    public const TYPE_REFERENCE_POINT = 54;
    public const TYPE_ELEVATION_READING = 55;
    public const TYPE_DOWNSPOUT = 56;
    public const TYPE_DRY_WELL = 57;
    public const TYPE_CATCH_BASIN = 58;
    public const TYPE_CMU = 59;
    public const TYPE_BASEMENT_WINDOW_WELL = 60;
    public const TYPE_EGRESS_WINDOW_WELL = 61;
    public const TYPE_FRENCH_DRAIN = 62;
    public const TYPE_VAPOR_BARRIER = 63;
    public const TYPE_ROOT_BARRIER = 64;
    public const TYPE_INTERIOR_DRAIN = 65;
    public const TYPE_FLOOR_JOIST = 66;
    public const TYPE_JOINT_SEALANT = 67;
    public const TYPE_FENCE_POST = 68;
    public const TYPE_SINGLE_CANTILEVER = 69;
    public const TYPE_DOUBLE_CANTILEVER = 70;
    public const TYPE_LINE_DIMENSION = 71;

    public const TYPE_BEAM_REPLACEMENT = 72;
    public const TYPE_TRENCH_DRAIN = 73;
    public const TYPE_TOILET = 74;
    public const TYPE_SHOWER = 75;
    public const TYPE_BATH_TUB = 76;
    public const TYPE_CABINET = 77;
    public const TYPE_KITCHEN_SINK = 78;
    public const TYPE_BATTERY_BACKUP = 79;
    public const TYPE_PATIO = 80;
    public const TYPE_DECK = 81;
    public const TYPE_SIDEWALK = 82;
    public const TYPE_FLOOR_VENT = 83;
    public const TYPE_AIR_DUCT = 84;
    public const TYPE_FOOTING = 85;
    public const TYPE_POOL_ROUND = 86;
    public const TYPE_POOL_RECTANGLE = 87;
    public const TYPE_SPRINKLER_HEAD = 88;
    public const TYPE_SPRINKLER_LINE = 89;
    public const TYPE_RANGE = 90;
    public const TYPE_WASHER_DRYER = 91;
    public const TYPE_DISHWASHER = 92;
    public const TYPE_SINK = 93;
    public const TYPE_DOUBLE_SINK = 94;
    public const TYPE_FLOOR_DRAIN = 95;
    public const TYPE_OPENING = 96;
    public const TYPE_FIREPLACE = 97;
    public const TYPE_UTIILTY_LINE = 98;
    public const TYPE_GAS = 99;
    public const TYPE_ELECTRICAL = 100;
    public const TYPE_WATER = 101;
    public const TYPE_RETAINING_WALL = 102;

    public static function getTypes();

    public function drawing();

    public function scopeOfCompany($query, $company);
}
