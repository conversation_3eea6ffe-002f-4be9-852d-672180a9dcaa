<?php

namespace Common\Models\Interfaces;

/**
 * Interface FormItemEntryGroupFieldInterface
 *
 * @package Common\Models\Interfaces
 */
interface FormItemEntryGroupFieldInterface
{
    public const FIELD_SOURCE_GROUP = 11;
    public const FIELD_SOURCE_COMPANY = 39;

    public function group();

    public function field();

    public function scopeWithFormItem($query);

    public function scopeOfCompany($query, $company);
}
