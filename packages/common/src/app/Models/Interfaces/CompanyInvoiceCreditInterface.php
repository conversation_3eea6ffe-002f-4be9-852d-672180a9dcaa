<?php

namespace Common\Models\Interfaces;

/**
 * Interface CompanyInvoiceCreditInterface
 *
 * @package Common\Models\Interfaces
 */
interface CompanyInvoiceCreditInterface
{
    const TYPE_SUBSCRIPTION = 1;
    const TYPE_GENERAL = 2;

    public function company();

    public function companySubscription();

    public function scopeNotExpended($query);

    public function scopeOfCompany($query, $company);

    public function scopeOrdered($query);
}
