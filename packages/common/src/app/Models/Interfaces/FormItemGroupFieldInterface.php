<?php

namespace Common\Models\Interfaces;

/**
 * Interface FormItemGroupFieldInterface
 *
 * @package Common\Models\Interfaces
 */
interface FormItemGroupFieldInterface
{
    public const TYPE_TEXT = 1;
    public const TYPE_TEXTAREA = 2;
    public const TYPE_SELECT = 3;
    public const TYPE_RADIO = 4;
    public const TYPE_CHECKBOX = 5;
    public const TYPE_FILE = 6;
    public const TYPE_PRODUCT_LIST = 7;

    public function options();

    public function products();

    public function scopeOfCompany($query, $company);
}
