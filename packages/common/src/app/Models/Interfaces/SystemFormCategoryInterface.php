<?php

namespace Common\Models\Interfaces;

/**
 * Interface SystemFormCategoryInterface
 *
 * @package Common\Models\Interfaces
 */
interface SystemFormCategoryInterface
{
    public const TYPE_BID = 14;

    public const STATUS_ACTIVE = 1;
    public const STATUS_ARCHIVED = 2;

    public function children();

    public function formType();

    public function items();

    public function parent();

    public function scopeActive($query);

    public function scopeOrdered($query);
}
