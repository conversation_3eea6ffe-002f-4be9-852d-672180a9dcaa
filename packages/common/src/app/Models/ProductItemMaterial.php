<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class ProductItemMaterial extends HistoryEntityModel implements Interfaces\ProductItemMaterialInterface
{
    use Common\ProductItemMaterialCommon;

    protected $table = 'productItemMaterials';
    protected $primaryKey = 'productItemMaterialID';
    protected $fillable = [
        'productItemMaterialID', 'productItemID', 'materialID', 'unitPrice', 'quantity', 'total', 'order', 'status',
        'archivedAt', 'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
