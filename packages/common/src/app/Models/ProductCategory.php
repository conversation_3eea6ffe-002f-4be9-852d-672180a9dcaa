<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class ProductCategory extends HistoryEntityModel implements Interfaces\ProductCategoryInterface, ScopeSearchInterface
{
    use Common\ProductCategoryCommon;

    protected $table = 'productCategories';
    protected $primaryKey = 'productCategoryID';
    protected $fillable = [
        'productCategoryID', 'ownerType', 'ownerID', 'parentProductCategoryID', 'alias', 'name', 'status', 'archivedAt',
        'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
