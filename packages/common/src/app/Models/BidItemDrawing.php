<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemDrawing extends HistoryEntityModel implements Interfaces\BidItemDrawingInterface
{
    use Common\BidItemDrawingCommon;

    protected $table = 'bidItemDrawings';
    protected $primaryKey = 'bidItemDrawingID';
    protected $fillable = [
        'bidItemDrawingID', 'bidItemID', 'drawingID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
