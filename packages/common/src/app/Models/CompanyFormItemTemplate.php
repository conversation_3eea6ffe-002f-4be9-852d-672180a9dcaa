<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemTemplate extends Model implements Interfaces\FormItemGroupTemplateInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'companyFormItemTemplates';
    protected $primaryKey = 'companyFormItemTemplateID';
    protected $fillable = [
        'companyFormItemTemplateID', 'companyFormItemID', 'formItemGroupTemplateID', 'type', 'styles', 'content',
        'scripts', 'config', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'config' => 'array',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->belongsTo(CompanyFormItem::class, 'companyFormItemID', 'companyFormItemID');
    }

    public function formItemGroupTemplate()
    {
        return $this->belongsTo(FormItemGroupTemplate::class, 'formItemGroupTemplateID', 'formItemGroupTemplateID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItems', 'companyFormItems.companyFormItemID', "{$this->table}.companyFormItemID");
        return $query->where('companyFormItems.companyID', $company);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where("{$this->table}.type", $type);
    }
}
