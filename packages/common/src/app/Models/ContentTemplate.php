<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class ContentTemplate extends HistoryEntityModel implements Interfaces\ContentTemplateInterface
{
    use Common\ContentTemplateCommon;

    protected $table = 'contentTemplates';
    protected $primaryKey = 'contentTemplateID';
    protected $fillable = [
        'contentTemplateID', 'companyID', 'type', 'name', 'alias', 'styles', 'content', 'scripts', 'isDefault',
        'createdByUserID', 'updatedByUser<PERSON>', 'deletedAt', 'deletedByUserID'
    ];
}
