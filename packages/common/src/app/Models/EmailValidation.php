<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class EmailValidation extends HistoryEntityModel implements Interfaces\EmailValidationInterface
{
    use Common\EmailValidationCommon;

    protected $table = 'emailValidations';
    protected $primaryKey = 'emailValidationID';
    protected $fillable = [
        'emailValidationID', 'email', 'status', 'apiResponse', 'hits', 'time', 'expiresAt'
    ];
}
