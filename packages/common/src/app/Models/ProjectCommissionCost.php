<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectCommissionCost extends Model
{
    use SoftDeletes;

    protected $table = 'projectCommissionCosts';
    protected $primaryKey = 'projectCommissionCostID';

    protected $fillable = [
        'userID', 'amount', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID'
    ];

    public function parent()
    {
        return $this->morphOne(ProjectCost::class, 'cost', 'type', 'costID');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }
}
