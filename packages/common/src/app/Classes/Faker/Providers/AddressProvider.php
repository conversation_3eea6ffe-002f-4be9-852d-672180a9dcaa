<?php

declare(strict_types=1);

namespace Common\Classes\Faker\Providers;

/**
 * Class AddressProvider
 *
 * @package Common\Classes\Faker\Providers
 */
class AddressProvider extends \Faker\Provider\Base
{
    /**
     * Address for FX inputs
     *
     * @return string
     */
    public function fxAddress(): string
    {
        return "{$this->generator->buildingNumber} {$this->generator->streetName}";
    }

    /**
     * Secondary address for FX address_2 related inputs
     *
     * @return string|null
     */
    public function fxAddress2(): ?string
    {
        return $this->generator->optional()->secondaryAddress;
    }

    /**
     * Abbreviated state used for FX state dropdowns
     *
     * @return string
     */
    public function fxState(): string
    {
        return $this->generator->stateAbbr;
    }

    /**
     * Zip code for FX inputs
     *
     * @return string
     */
    public function fxZip(): string
    {
        return $this->generator->postcode;
    }

    /**
     * Latitude within the continental US
     *
     * @return float
     */
    public function fxLatitude(): float
    {
        return $this->generator->latitude(24.7433195, 49.3457868);
    }

    /**
     * Longitude within the continental US
     *
     * @return float
     */
    public function fxLongitude(): float
    {
        return $this->generator->longitude(-124.7844079, -66.9513812);
    }
}
