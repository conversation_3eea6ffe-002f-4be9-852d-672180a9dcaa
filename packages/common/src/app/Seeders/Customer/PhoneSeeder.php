<?php

declare(strict_types=1);

namespace Common\Seeders\Customer;

use App\Classes\Acl;
use App\Resources\Customer\PhoneResource;
use Common\Classes\Seeder;
use Common\Traits\Seeder\CustomerTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;

/**
 * Class PhoneSeeder
 *
 * @package Common\Seeders\Customer
 */
class PhoneSeeder extends Seeder
{
    use CustomerTrait;

    /**
     * @var bool
     */
    protected bool $primary = false;

    /**
     * Set if phone is primary
     *
     * @param bool $primary
     * @return PhoneSeeder
     */
    public function primary(bool $primary): self
    {
        $this->primary = $primary;
        return $this;
    }

    /**
     * Determine if phone is primary
     *
     * @return bool
     */
    public function isPrimary(): bool
    {
        return $this->primary;
    }

    /**
     * Get nested entity without any identifiers
     *
     * @return array
     */
    public function nestedEntity(): array
    {
        $faker = $this->getFaker();

        $types = [
            PhoneResource::TYPE_CELL => 'Cell',
            PhoneResource::TYPE_CELL_TEXT_OPT_OUT => 'Cell',
            PhoneResource::TYPE_HOME => 'Home',
            PhoneResource::TYPE_WORK => 'Work',
            PhoneResource::TYPE_OTHER => 'Other'
        ];

        $type = $faker->randomKey($types);

        return [
            'type' => $type,
            'number' => $faker->fxPhoneNumber,
            'description' => $types[$type],
            'is_primary' => $this->isPrimary()
        ];
    }

    /**
     * Run seeder
     *
     * @throws AppException
     */
    public function run(): void
    {
        $entity = $this->nestedEntity();
        $entity['customer_id'] = $this->getCustomerID();

        $id = PhoneResource::make(Acl::make())->create(Entity::make($entity))->run();

        $this->primaryKey($id);
    }
}
