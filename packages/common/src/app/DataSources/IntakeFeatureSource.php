<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\IntakeFeature;

/**
 * Class IntakeFeatureSource
 *
 * @package Common\DataSources
 */
class IntakeFeatureSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'intake_features';

    /**
     * Setup intake features by importing data into database
     */
    public function setup(): void
    {
        $features = $this->loadData();
        $counter = 0;
        foreach($features as $feature) {
            IntakeFeature::create([
                'intakeFeatureID' => $feature['id'],
                'name' => $feature['name'],
                'order' => ++$counter
            ]);
        }
    }
}
