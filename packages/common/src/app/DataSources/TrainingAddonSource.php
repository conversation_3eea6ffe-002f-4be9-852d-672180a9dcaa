<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\TrainingAddon;

/**
 * Class TrainingAddonSource
 *
 * @package Common\DataSources
 */
class TrainingAddonSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'training_addons';

    /**
     * Setup source by importing data into database
     *
     * @param string $env
     * @throws \Core\Exceptions\AppException
     */
    public function setup(string $env): void
    {
        $addons = $this->loadData($env);
        $order = 1;
        foreach ($addons as $addon) {
            TrainingAddon::create([
                'title' => $addon['title'],
                'price' => $addon['price'],
                'content' => $addon['content'],
                'order' => $order++
            ]);
        }
    }
}
