<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\IntakeMarketingSource;

/**
 * Class IntakeMarketingSourceSource
 *
 * @package Common\DataSources
 */
class IntakeMarketingSourceSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'intake_marketing_sources';

    /**
     * Setup intake marketing sources by importing data into database
     */
    public function setup(): void
    {
        $marketing_sources = $this->loadData();
        $counter = 0;
        foreach($marketing_sources as $marketing_source) {
            IntakeMarketingSource::create([
                'intakeMarketingSourceID' => $marketing_source['id'],
                'name' => $marketing_source['name'],
                'order' => ++$counter
            ]);
        }
    }
}
