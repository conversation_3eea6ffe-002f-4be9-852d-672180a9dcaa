<?php

namespace Common\Traits\DB;

use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

trait UuidTrait
{
    /**
     * Handle dynamic method calls into the model.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        if (in_array($method, ['findByUuid'])) {
            return $this->internalFindByUuid(...$parameters);
        }
        return parent::__call($method, $parameters);
    }

    public function getIncrementing()
    {
        return false;
    }

    public function getKeyType()
    {
        return 'string';
    }

    protected function internalFindByUuid($id, $columns = ['*'])
    {
        if (!is_object($id) || !($id instanceof UuidInterface)) {
            $id = Uuid::fromString($id);
        }
        return $this->find($id->getBytes(), $columns);
    }

    public function getUuid($key)
    {
        $value = $this->getAttribute($key);
        if ($value !== null) {
            $value = Uuid::fromBytes($value);
        }
        return $value;
    }

    public function getUuidKey()
    {
        return $this->getUuid($this->getKeyName());
    }

    public function setUuid($key, $value)
    {
        if (is_object($value) && $value instanceof UuidInterface) {
            $value = $value->getBytes();
        } elseif (Uuid::isValid($value)) {
            $value = Uuid::fromString($value)->getBytes();
        }
        $this->setAttribute($key, $value);
        return $this;
    }
}
