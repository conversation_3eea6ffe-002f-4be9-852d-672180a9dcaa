<?php

declare(strict_types=1);

namespace Core\Classes;

/**
 * Class File
 *
 * @package Core\Classes
 */
abstract class File
{
    /**
     * Get extension of file
     *
     * @param string $file
     * @return string
     */
    public static function getExtension(string $file): string
    {
        return \strtolower(\pathinfo($file, \PATHINFO_EXTENSION));
    }

    /**
     * Format byte count into human-readable form
     *
     * @param int $bytes
     * @param int $p
     * @return string
     */
    public static function formatFilesize(int $bytes, int $p = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = \max($bytes, 0);
        $pow = \min(\floor(($bytes ? \log($bytes) : 0) / \log(1024)), (\count($units) - 1));
        $bytes /= \pow(1024, $pow);
        return \round($bytes, $p) . ' ' . $units[$pow];
    }

    /**
     * Translate human-readable filesize into bytes
     *
     * @param string $str
     * @return bool|int
     */
    public static function toBytes(string $str): bool|int
    {
        $sizes = ['KB', 'MB', 'GB', 'TB'];
        $multi = 1024;
        foreach ($sizes as $size) {
            if (\stripos($str, $size) === false) {
                $multi *= 1024;
                continue;
            }
            return ((int)\str_replace($size, '', $str) * $multi);
        }
        return false;
    }

    /**
     * Create clean filename
     *
     * @param string $name
     * @param string $extn
     * @return string
     */
    public static function sanitizeName(string $name, string $extn = ''): string
    {
        $length = \strlen($name);
        if ($extn === '') {
            $extn = self::getExtension($name);
            $length -= (\strlen($extn) + 1);
        }
        $name = \preg_replace(
            '#[^a-zA-Z0-9_]+#',
            '_',
            \substr($name, 0, $length)
        );
        return \trim(\preg_replace('#[_]{2,}#', '_', $name), '_') . ".{$extn}";
    }

    /**
     * Shorten file name to specified length
     *
     * @param string $filename
     * @param int $chars
     * @return string
     */
    public static function shortenName(string $filename, int $chars): string
    {
        $length = \strlen($filename);
        if ($length <= $chars) {
            return $filename;
        }
        $extn = false;
        if (($pos = \strrpos($filename, '.')) !== false) {
            $name = \substr($filename, 0, $pos);
            $extn = \substr($filename, ($pos + 1));
        }
        $remove = ($length - $chars) + 3;
        return \substr($name, 0, (\strlen($name) - $remove)) . '...' . ($extn !== false ? ".{$extn}" : '');
    }

    /**
     * Get file data, optionally inject vars via config
     *
     * @param string $path
     * @param array $config
     * @return bool|string
     */
    public static function getData(string $path, array $config = []): bool|string
    {
        if (!\file_exists($path)) {
            return false;
        }
        if (isset($config['raw']) && $config['raw']) {
            return \file_get_contents($path);
        }
        if (isset($config['vars'])) {
            \extract($config['vars']);
        }
        \ob_start();
        include $path;
        $data = \ob_get_contents();
        \ob_end_clean();
        return $data;
    }
}
