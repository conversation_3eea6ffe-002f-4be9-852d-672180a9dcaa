<?php

namespace Core\Traits;

use Core\Exceptions\AppException;

/**
 * Class ExtendableSingletonTrait
 *
 * Provides default functions to create classes which can be extended and still use singleton pattern
 *
 * @package Core\Traits
 */
trait ExtendableSingletonTrait
{
    /**
     * Collection of instances
     *
     * @var array
     */
    protected static $_instances = [];

    /**
     * Get instance of class
     *
     * @return static
     */
    public static function instance()
    {
        $class = get_called_class();
        if (isset(static::$_instances[$class])) {
            return static::$_instances[$class];
        }
        return new static;
    }

    /**
     * Magic method to handle statically called methods which don't exist
     *
     * @param string $method
     * @param array $args
     * @return mixed
     *
     * @throws AppException
     */
    public static function __callStatic($method, array $args)
    {
        $class = static::instance();
        $method = "_{$method}";
        if (!method_exists($class, $method)) {
            throw new AppException("Method '%s' not found", $method);
        }
        return call_user_func_array([$class, $method], $args);
    }

    /**
     * Magic method to handle called method which don't exist
     *
     * @param $method
     * @param array $args
     * @return mixed
     *
     * @throws AppException
     */
    public function __call($method, array $args)
    {
        $method = "_{$method}";
        if (!method_exists($this, $method)) {
            throw new AppException("Method '%s' not found", $method);
        }
        return call_user_func_array([$this, $method], $args);
    }
}
