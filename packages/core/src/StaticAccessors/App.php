<?php

declare(strict_types=1);

namespace Core\StaticAccessors;

use Core\Classes\StaticAccessor;
use Core\Interfaces\KernelInterface;

/**
 * Class App
 *
 * @package Core\StaticAccessors
 *
 * @method static bool debugEnabled()
 * @method static object get(string $name)
 * @method static object create(string $class, array $args = [])
 * @method static mixed call(callable $callable, array $args = [])
 */
class App extends StaticAccessor
{
    /**
     * Get instance of App
     *
     * @return KernelInterface
     */
    protected static function getInstance(): KernelInterface
    {
        return static::$kernel;
    }
}
