<?php

namespace Core\Components\DB\Classes;

use Core\Classes\Config;
use Core\Interfaces\DBInterface;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager;
use Illuminate\Events\Dispatcher;

/**
 * Class DB
 *
 * @package Core\Components\DB\Classes
 */
class DB implements DBInterface
{
    /**
     * Config instance
     *
     * @var Config
     */
    protected $config;

    /**
     * Illuminate DB Capsule Manager
     *
     * @var Manager
     */
    protected $manager;

    /**
     * DB constructor
     *
     * @param Config $config
     * @param Manager $manager
     */
    public function __construct(Config $config, Manager $manager)
    {
        $this->config = $config;
        $this->manager = $manager;

        $manager->getDatabaseManager()->setDefaultConnection($config->get('database.default', 'default'));

        foreach ($config->get('database.connections', []) as $name => $data) {
            $manager->addConnection($data, $name);
        }

        $manager->setEventDispatcher(new Dispatcher(new Container));

        $manager->bootEloquent();
        $manager->setAsGlobal();

        if ($config->get('database.debug', false)) {
            $manager->getConnection()->enableQueryLog();
        }
    }

    /**
     * Magic method to pass all method calls to the manager instance
     *
     * @param string $method
     * @param array $args
     * @return mixed
     */
    public function __call($method, $args)
    {
        return $this->manager->{$method}(...$args);
    }
}
