<?php

declare(strict_types=1);

namespace Core\Components\RequestValidation\Classes;

use Core\Classes\Arr;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Validation\Classes\{FieldConfig, Rules};

/**
 * Class Validator
 *
 * @package Core\Components\Validation\Classes\Request
 */
class Validator extends \Core\Components\Validation\Classes\Validator
{
    /**
     * Validator constructor
     *
     * @param RequestInterface $request
     * @param FieldConfig $config
     * @param Rules $rules
     */
    public function __construct(protected RequestInterface $request, FieldConfig $config, Rules $rules)
    {
        parent::__construct($config, $rules, $this->request->input()->all());
    }

    /**
     * Push validation errors and data to session
     *
     * @param array $data
     */
    protected function handleFlash(array $data): void
    {
        $session = $this->request->session();
        $session->setFlash('validation', [
            'data' => $data,
            'errors' => $this->errors->get()
        ]);
    }

    /**
     * Flash validation data into session
     *
     * @return $this
     */
    public function flash(): self
    {
        $this->handleFlash($this->data);
        return $this;
    }

    /**
     * Flash only specific input data
     *
     * @param string|array $keys
     * @return $this
     */
    public function flashOnly(string|array $keys): self
    {
        $this->handleFlash(Arr::only($this->data, $keys));
        return $this;
    }

    /**
     * Flash all data expect specific keys
     *
     * @param string|array $keys
     * @return $this
     */
    public function flashExcept(string|array $keys): self
    {
        $this->handleFlash(Arr::except($this->data, $keys));
        return $this;
    }
}
