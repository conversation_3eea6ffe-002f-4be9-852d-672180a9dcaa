<?php

declare(strict_types=1);

namespace Core\Components\RequestValidation;

use Core\Components\Http\Interfaces\{RequestInterface, ViewFactoryInterface};
use Core\Components\RequestValidation\Classes\{Validation, ViewHelper};
use Core\Components\Validation\Classes\Rules;

/**
 * Class Component
 *
 * @package Core\Components\RequestValidation
 */
class Component extends \Core\Classes\Component
{
    /**
     * Boot component
     */
    public function boot(): void
    {
        $this->kernel->bind(
            Validation::class,
            fn(): Validation => new Validation($this->kernel->get(RequestInterface::class), $this->kernel->get(Rules::class))
        );
        $this->kernel->singleton(
            ViewHelper::class,
            fn(): ViewHelper => new ViewHelper($this->kernel->get(RequestInterface::class))
        );
    }

    /**
     * Register component
     */
    public function register(): void
    {
        $view_factory = $this->kernel->get(ViewFactoryInterface::class);
        $view_factory->helper('validator', ViewHelper::class);
    }
}
