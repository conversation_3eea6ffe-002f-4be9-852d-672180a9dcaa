<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Core\Components\Queue\Interfaces\DaemonOptionsInterface;
use JsonSerializable;

/**
 * Class DaemonOptions
 *
 * @package Core\Components\Queue\Classes
 */
class DaemonOptions implements DaemonOptionsInterface, JsonSerializable
{
    /**
     * DaemonOptions constructor
     *
     * @param float $sleep - Number of seconds to wait between cycles
     * @param int $max_memory - Maximum amount of memory (in MB) the daemon can use before being killed
     * @param int $max_time - Maximum amount of time (in seconds) the daemon can run before being killed
     */
    public function __construct(
        protected float $sleep = 1.0,
        protected int $max_memory = 128,
        protected int $max_time = 14400
    ) {}

    /**
     * Set sleep time (in seconds)
     *
     * @param float $sleep
     */
    public function setSleep(float $sleep): void
    {
        $this->sleep = $sleep;
    }

    /**
     * Get sleep time
     *
     * @return float
     */
    public function getSleep(): float
    {
        return $this->sleep;
    }

    /**
     * Set max memory (in mb)
     *
     * @param int $memory
     */
    public function setMaxMemory(int $memory): void
    {
        $this->max_memory = $memory;
    }

    /**
     * Get max memory
     *
     * @return int
     */
    public function getMaxMemory(): int
    {
        return $this->max_memory;
    }

    /**
     * Set max time (in seconds)
     *
     * @param int $time
     */
    public function setMaxTime(int $time): void
    {
        $this->max_time = $time;
    }

    /**
     * Get max time
     *
     * @return int
     */
    public function getMaxTime(): int
    {
        return $this->max_time;
    }

    /**
     * Convert options into array format
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'sleep' => $this->getSleep(),
            'max_memory' => $this->getMaxMemory(),
            'max_time' => $this->getMaxTime()
        ];
    }

    /**
     * Output array format to JSON
     *
     * @return array
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
