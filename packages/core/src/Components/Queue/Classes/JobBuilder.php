<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Carbon\Carbon;
use Core\Components\Queue\Interfaces\{JobBuilderInterface, JobInterface};

/**
 * Class JobBuilder
 *
 * @package Core\Components\Queue\Classes
 */
class JobBuilder implements JobBuilderInterface
{
    /**
     * @var null|int Number of times to try job before failing
     */
    protected ?int $max_tries = null;

    /**
     * @var string|null Channel which job is run
     */
    protected ?string $channel = null;

    /**
     * @var Carbon|null Datetime which job is handled
     */
    protected ?Carbon $handle_at = null;

    /**
     * @var string|null Name of connection to send job with
     */
    protected ?string $connection = null;

    /**
     * JobBuilder constructor
     *
     * @param JobInterface $job
     */
    public function __construct(protected JobInterface $job)
    {}

    /**
     * Get job instance
     *
     * @return JobInterface
     */
    public function getJob(): JobInterface
    {
        return $this->job;
    }

    /**
     * Set connection name to send job on
     *
     * @param string|null $name
     * @return $this
     */
    public function connection(?string $name): static
    {
        $this->connection = $name;
        return $this;
    }

    /**
     * Get connection name
     *
     * @return string|null
     */
    public function getConnection(): ?string
    {
        return $this->connection;
    }

    /**
     * Set channel for this job to run on
     *
     * @param string|null $name
     * @return $this
     */
    public function channel(?string $name): static
    {
        $this->channel = $name;
        return $this;
    }

    /**
     * Get channel or pull from associated job
     *
     * @return string|null
     */
    public function getChannel(): ?string
    {
        return $this->channel;
    }

    /**
     * Set handle at datetime to control when job will be run
     *
     * @param Carbon|null $handle_at
     * @return $this
     */
    public function handleAt(?Carbon $handle_at): static
    {
        $this->handle_at = $handle_at;
        return $this;
    }

    /**
     * Get handle at datetime
     *
     * If one isn't defined, the current time is used.
     *
     * @return Carbon|null
     */
    public function getHandleAt(): ?Carbon
    {
        return $this->handle_at;
    }

    /**
     * Set max tries
     *
     * @param int|null $max_tries
     * @return $this
     */
    public function maxTries(?int $max_tries): static
    {
        $this->max_tries = $max_tries;
        return $this;
    }

    /**
     * Get max try count
     *
     * @return int|null
     */
    public function getMaxTries(): ?int
    {
        return $this->max_tries;
    }
}
