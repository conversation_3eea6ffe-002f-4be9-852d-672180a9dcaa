<?php

namespace Core\Components\Queue\Interfaces;

/**
 * Interface WorkerOptionsInterface
 *
 * @package Core\Components\Queue\Interfaces
 */
interface WorkerOptionsInterface extends DaemonOptionsInterface
{
    /**
     * Set max tries
     *
     * @param int $tries
     */
    public function setMaxTries(int $tries): void;

    /**
     * Get max tries
     *
     * @return int
     */
    public function getMaxTries(): int;

    /**
     * Set max jobs
     *
     * @param int $jobs
     */
    public function setMaxJobs(int $jobs): void;

    /**
     * Get max jobs
     *
     * @return int
     */
    public function getMaxJobs(): int;

    /**
     * Set rest time between jobs (in seconds)
     *
     * @param float $rest
     */
    public function setRest(float $rest): void;

    /**
     * Get rest time
     *
     * @return float
     */
    public function getRest(): float;

    /**
     * Set timeout for job execution (in seconds)
     *
     * @param int $timeout
     */
    public function setTimeout(int $timeout): void;

    /**
     * Get timeout
     *
     * @return int
     */
    public function getTimeout(): int;

    /**
     * Set if jobs should be handled sequentially
     *
     * @param bool $sequential
     */
    public function setSequential(bool $sequential): void;

    /**
     * Get sequential setting
     *
     * @return bool
     */
    public function getSequential(): bool;

    /**
     * Get backoff times (array of seconds)
     *
     * Each item in the array corresponds to a try attempt starting from 0 index.
     *
     * @param array|null $backoff
     */
    public function setBackoff(?array $backoff): void;

    /**
     * Get backoff config
     *
     * @return array|null
     */
    public function getBackoff(): ?array;

    /**
     * Set if worker stops when all jobs are done
     *
     * @param bool $stop
     */
    public function setStopWhenEmpty(bool $stop): void;

    /**
     * Get stop when empty setting
     *
     * @return bool
     */
    public function getStopWhenEmpty(): bool;
}
