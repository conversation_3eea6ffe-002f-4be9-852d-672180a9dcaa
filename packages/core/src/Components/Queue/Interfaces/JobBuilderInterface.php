<?php

declare(strict_types=1);

namespace Core\Components\Queue\Interfaces;

use Carbon\Carbon;

/**
 * Interface JobBuilderInterface
 *
 * @package Core\Components\Queue\Interfaces
 */
interface JobBuilderInterface
{
    /**
     * Get job instance
     *
     * @return JobInterface
     */
    public function getJob(): JobInterface;

    /**
     * Set connection to send job with
     *
     * @param string|null $name
     * @return $this
     */
    public function connection(?string $name): static;

    /**
     * Get connection name
     *
     * @return string|null
     */
    public function getConnection(): ?string;

    /**
     * Set channel to send job on
     *
     * @param string|null $name
     * @return $this
     */
    public function channel(?string $name): static;

    /**
     * Get channel
     *
     * @return string|null
     */
    public function getChannel(): ?string;

    /**
     * Set timestamp of when to run job
     *
     * @param Carbon|null $handle_at
     * @return $this
     */
    public function handleAt(?Carbon $handle_at): static;

    /**
     * Get handle at timestamp
     *
     * @return Carbon|null
     */
    public function getHandleAt(): ?Carbon;

    /**
     * Set max tries job has to complete successfully
     *
     * @param int|null $max_tries
     * @return $this
     */
    public function maxTries(?int $max_tries): static;

    /**
     * Get max tries
     *
     * @return int|null
     */
    public function getMaxTries(): ?int;
}
