<?php

namespace Core\Components\Queue\Interfaces;

use Carbon\Carbon;
use Ramsey\Uuid\UuidInterface;
use Throwable;

/**
 * Interface JobProcessorInterface
 *
 * @package Core\Components\Queue\Interfaces
 */
interface JobProcessorInterface
{
    /**
     * Get job envelope instance
     *
     * @return JobEnvelopeInterface
     */
    public function getJobEnvelope(): JobEnvelopeInterface;

    /**
     * Get id from job envelope
     *
     * @return UuidInterface
     */
    public function getID(): UuidInterface;

    /**
     * Get name from job envelope
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Get tries from job envelope
     *
     * @return int
     */
    public function getTries(): int;

    /**
     * Get max tries from job envelope
     *
     * @return int|null
     */
    public function getMaxTries(): ?int;

    /**
     * Get timeout from job envelope
     *
     * @return int|null
     */
    public function getTimeout(): ?int;

    /**
     * Get backoff config from job envelope
     *
     * @return array|null
     */
    public function getBackoff(): ?array;

    /**
     * Process job
     *
     * @param WorkerInterface|null $worker
     */
    public function run(?WorkerInterface $worker = null): void;

    /**
     * Get start time of processing
     *
     * @return float|null
     */
    public function getStartTime(): ?float;

    /**
     * Get the total execution time of a job in milliseconds
     *
     * @return int|null
     */
    public function getElapsedTime(): ?int;

    /**
     * Determines if job was ran successfully
     *
     * @return bool
     */
    public function isHandled(): bool;

    /**
     * Trigger retry of job
     *
     * If backoff seconds are passed and no retry time is provided by job, then a future retry timestamp is generated.
     *
     * @param Throwable $e
     * @param int|null $backoff_seconds
     */
    public function retry(Throwable $e, ?int $backoff_seconds = null): void;

    /**
     * Get retry at timestamp
     *
     * Only available after job has been marked as retry.
     *
     * @return Carbon|null
     */
    public function getRetryAt(): ?Carbon;

    /**
     * Determines if job will be retried
     *
     * @return bool
     */
    public function isRetrying(): bool;

    /**
     * Get exception which caused retry
     *
     * @return Throwable|null
     */
    public function getRetryException(): ?Throwable;

    /**
     * Mark job as failed
     *
     * @param Throwable $e
     */
    public function failed(Throwable $e): void;

    /**
     * Determines if job is failed
     *
     * @return bool
     */
    public function isFailed(): bool;

    /**
     * Get exception which caused failure
     *
     * @return Throwable|null
     */
    public function getFailException(): ?Throwable;
}
