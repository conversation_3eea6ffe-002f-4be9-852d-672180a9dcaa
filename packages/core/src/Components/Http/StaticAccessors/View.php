<?php

namespace Core\Components\Http\StaticAccessors;

use Core\Components\Http\Interfaces\ViewFactoryInterface;
use Core\Classes\StaticAccessor;

/**
 * Class View
 *
 * @package Core\Components\Http\StaticAccessors
 */
class View extends StaticAccessor
{
    /**
     * Get instance of ViewFactoryInterface
     *
     * @return \Core\Components\Http\Interfaces\ViewFactoryInterface
     */
    protected static function getInstance()
    {
        return static::$kernel->get(ViewFactoryInterface::class);
    }
}
