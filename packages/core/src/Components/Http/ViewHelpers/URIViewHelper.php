<?php

namespace Core\Components\Http\ViewHelpers;

use Closure;
use Core\Components\Http\Classes\Html;
use Core\Components\Http\Interfaces\RequestInterface;

/**
 * Class URIViewHelper
 *
 * @package Core\Components\Http\ViewHelpers
 */
class URIViewHelper
{
    /**
     * @var \Core\Components\Http\Classes\URI
     */
    protected $uri;

    /**
     * URIViewHelper constructor
     *
     * @param RequestInterface $request
     */
    public function __construct(RequestInterface $request)
    {
        $this->uri = $request->uri();
    }

    /**
     * @param null|bool $secure
     * @return string
     */
    public function baseRaw($secure = null)
    {
        return $this->uri->base($secure);
    }

    /**
     * @param null|bool $secure
     * @return string
     */
    public function base($secure = null)
    {
        return Html::entityEncode($this->baseRaw($secure));
    }

    /**
     * @param Closure|null $closure
     * @return \Core\Components\Http\Classes\URLBuilder|string
     */
    public function createRaw($path, Closure $closure = null)
    {
        return $this->uri->create(function ($uri) use ($path, $closure) {
            $uri->path($path);
            if ($closure !== null) {
                $closure($uri);
            }
            return $uri;
        });
    }

    /**
     * @param Closure|null $closure
     * @return string
     */
    public function create($path, Closure $closure = null)
    {
        return Html::entityEncode($this->createRaw($path, $closure));
    }

    /**
     * @param Closure|null $closure
     * @return \Core\Components\Http\Classes\URLBuilder|string
     */
    public function currentRaw(Closure $closure = null)
    {
        return $this->uri->current($closure);
    }

    /**
     * @param Closure|null $closure
     * @return string
     */
    public function current(Closure $closure = null)
    {
        return Html::entityEncode($this->currentRaw($closure));
    }

    /**
     * @param string $name
     * @param array $data
     * @param Closure|null $closure
     * @return \Core\Components\Http\Classes\URLBuilder|string
     */
    public function routeRaw($name, array $data = [], Closure $closure = null)
    {
        return $this->uri->route($name, $data, $closure);
    }

    /**
     * @param string $name
     * @param array $data
     * @param Closure|null $closure
     * @return string
     */
    public function route($name, array $data = [], Closure $closure = null)
    {
        return Html::entityEncode($this->routeRaw($name, $data, $closure));
    }
}
