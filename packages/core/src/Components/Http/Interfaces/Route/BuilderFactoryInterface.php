<?php

namespace Core\Components\Http\Interfaces\Route;

use Closure;
use Core\Components\Http\Classes\Route\BuilderGroup;

/**
 * Interface BuilderFactoryInterface
 *
 * @package Core\Components\Http\Interfaces\Route
 */
interface BuilderFactoryInterface
{
    /**
     * @param Closure $closure
     * @return BuilderGroup
     */
    public function group(Closure $closure);

    /**
     * @param string $type
     * @param Closure $closure
     */
    public function register($type, Closure $closure);

    /**
     * @return array
     */
    public function getRoutes();
}
