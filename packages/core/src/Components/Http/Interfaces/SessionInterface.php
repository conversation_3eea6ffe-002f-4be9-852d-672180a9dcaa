<?php

namespace Core\Components\Http\Interfaces;

/**
 * Interface SessionInterface
 *
 * @package Core\Components\Http\Interfaces
 */
interface SessionInterface
{
    const FLASH_KEY = 'flash';

    /**
     * @param string $key
     * @return bool
     */
    public function has($key);

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function get($key = null, $default = null);

    /**
     * @param string $key
     * @param mixed $value
     */
    public function set($key, $value);

    /**
     * @param string $key
     */
    public function remove($key);

    /**
     * @return void
     */
    public function write();

    /**
     * @return void
     */
    public function destroy();

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function flash($key = null, $default = null);

    /**
     * @param string $key
     * @param mixed $value
     * @param bool $new
     * @return $this
     */
    public function setFlash($key, $value, $new = true);

    /**
     * @return $this
     */
    public function preserveFlash();

    /**
     * @return $this
     */
    public function clear();
}
