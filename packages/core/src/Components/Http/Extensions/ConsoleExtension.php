<?php

namespace Core\Components\Http\Extensions;

use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\RouterInterface;

/**
 * Class ConsoleExtension
 *
 * @package Core\Components\Http\Extensions
 */
class ConsoleExtension
{
    /**
     * Set router on URI instance so routes work within console
     *
     * @param RouterInterface $router
     * @param RequestInterface $request
     */
    public function load(RouterInterface $router, RequestInterface $request)
    {
        $request->uri()->setRouter($router);
    }
}
