<?php

namespace Core\Components\Resource\Interfaces\MediaHandler;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Interfaces\MediaHandlerInterface;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;

/**
 * Interface OriginalMediaHandlerInterface
 *
 * @package Core\Components\Resource\Interfaces\MediaHandler
 */
interface OriginalMediaHandlerInterface extends MediaHandlerInterface
{
    /**
     * Handle create action
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     */
    public function create(Entity $entity, PolyCreateRequest $request): void;

    /**
     * Handle update action
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     */
    public function update(Entity $entity, UpdateRequest $request): void;
}
