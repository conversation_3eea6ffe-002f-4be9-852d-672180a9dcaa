<?php

namespace Core\Components\Resource\Exceptions;

use Core\Components\Validation\Classes\Validator;

class ValidationException extends ResourceException
{
    protected $validator = null;

    protected $errors = [];

    public function setValidator(Validator $validator)
    {
        $this->validator = $validator;
        return $this;
    }

    /**
     * @return Validator|null
     */
    public function getValidator()
    {
        return $this->validator;
    }

    public function setErrors(array $errors)
    {
        $this->errors = $errors;
        return $this;
    }

    public function getErrors()
    {
        if ($this->validator !== null) {
            return $this->validator->errors()->get();
        }
        return $this->errors;
    }

    public function getContext()
    {
        return [
            'errors' => $this->getErrors()
        ];
    }
}
