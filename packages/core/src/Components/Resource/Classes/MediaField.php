<?php

namespace Core\Components\Resource\Classes;

use Core\Components\Validation\Classes\Field as ValidationField;

class MediaField
{
    /**
     * @var null|FieldList
     */
    protected $list = null;

    protected $name;

    protected $media_type = null;

    protected $validation = null;

    protected $validation_field = null;

    public function __construct($name)
    {
        $this->name($name);
    }

    public function setList(FieldList $list)
    {
        $this->list = $list;
        return $this;
    }

    public function name($name)
    {
        $this->name = $name;
        return $this;
    }

    public function getName()
    {
        return $this->name;
    }

    public function mediaType($type)
    {
        $this->media_type = $type;
        return $this;
    }

    public function getMediaType()
    {
        return isset($this->media_type) ? $this->media_type : $this->name;
    }

    public function validation($label, array $rules)
    {
        $this->validation = compact('label', 'rules');
        return $this;
    }

    /**
     * @return ValidationField|null
     */
    public function getValidationField()
    {
        if ($this->validation === null) {
            return null;
        }
        if ($this->validation_field === null) {
            $config = $this->validation;
            $config['name'] = $this->getName();
            // wrap rules in 'upload' rule since that is the proper name for upload validation
            $config['rules'] = [
                'upload' => $config['rules']
            ];
            $this->validation_field = ValidationField::fromArray($config);
        }
        return $this->validation_field;
    }
}
