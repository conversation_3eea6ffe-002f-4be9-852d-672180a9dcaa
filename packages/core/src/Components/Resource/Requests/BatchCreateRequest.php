<?php

namespace Core\Components\Resource\Requests;

use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Interfaces\BatchableRequestInterface;
use Core\Components\Resource\Traits\Request\BatchableTrait;
use Core\Components\Resource\Traits\Request\BatchTrait;
use Core\Components\Resource\Traits\Request\NestedTrait;
use Core\Components\Resource\Traits\Request\ScopeTrait;

class BatchCreateRequest extends Request implements BatchableRequestInterface
{
    use BatchTrait;
    use BatchableTrait;
    use ScopeTrait;
    use NestedTrait;

    public function __construct(Resource $resource, Collection $collection)
    {
        parent::__construct($resource);

        $this->setCollection($collection);
    }

    protected function newRequest(Entity $entity)
    {
        $request = $this->resource->create($entity)->actionCheck(false);
        if ($this->isNested()) {
            $request->nested();
        }
        if ($this->hasScope()) {
            $request->scope($this->getScope());
        }
        return $request;
    }

    public function prepare()
    {
        parent::prepare();

        $action = Resource::ACTION_BATCH_CREATE;
        if ($this->isNested()) {
            $action = Resource::ACTION_BATCH_NESTED_CREATE;
        }

        if (!$this->actionAllowed($action)) {
            throw new ActionNotAllowedException('Batch create action not allowed');
        }

        $this->setAction($action);

        $this->batchPrepare();

        $this->prepared = true;
    }

    public function handle()
    {
        parent::handle();

        $this->batchHandle();

        $batch_request = $this->getBatchRequest();

        $primary_field = $this->resource->getPrimaryField();
        if ($this->hasScope()) {
            $collection = array_map(function (CreateRequest $request) use ($primary_field) {
                return $this->resource->entity($primary_field->outputValueFromModel($request->getModel()))
                    ->scope(clone $this->getScope())
                    ->run();
            }, $batch_request->getRequests());
            $response = new Collection($collection);
        } else {
            $response = array_map(function (CreateRequest $request) use ($primary_field) {
                return $primary_field->outputValueFromModel($request->getModel());
            }, $batch_request->getRequests());
        }
        $this->setResponse($response);
    }
}
