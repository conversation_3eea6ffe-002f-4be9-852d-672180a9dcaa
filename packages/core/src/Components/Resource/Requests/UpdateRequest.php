<?php

namespace Core\Components\Resource\Requests;

use Core\Classes\Arr;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Interfaces\BatchableRequestInterface;
use Core\Components\Resource\Interfaces\ValidatedRequestInterface;
use Core\Components\Resource\Traits\Request\BatchableTrait;
use Core\Components\Resource\Traits\Request\EntityTrait;
use Core\Components\Resource\Traits\Request\ModelTrait;
use Core\Components\Resource\Traits\Request\NestedTrait;
use Core\Components\Resource\Traits\Request\PolyParentTrait;
use Core\Components\Resource\Traits\Request\SaveTrait;
use Core\Components\Resource\Traits\Request\ScopeTrait;
use Core\Components\Resource\Traits\Request\UpdateTrait;
use Core\Components\Validation\Classes\FieldConfig;
use Exception;

class UpdateRequest extends Request implements BatchableRequestInterface, ValidatedRequestInterface
{
    use BatchableTrait;
    use EntityTrait;
    use ModelTrait;
    use NestedTrait;
    use PolyParentTrait;
    use SaveTrait;
    use ScopeTrait;
    use UpdateTrait;

    public function __construct(Resource $resource, Entity $entity)
    {
        parent::__construct($resource);

        $this->setEntity($entity);
    }

    public function prepare()
    {
        parent::prepare();

        $action = Resource::ACTION_UPDATE;
        if ($this->isNested()) {
            $action = Resource::ACTION_NESTED_UPDATE;
        } else if ($this->isPartial()) {
            $action = Resource::ACTION_PARTIAL_UPDATE;
        }

        if (!$this->actionAllowed($action)) {
            throw new ActionNotAllowedException('Update action not allowed');
        }

        $this->setAction($action);
        if ($this->fields === null) {
            $this->setFields($this->resource->getFields()->ofAction($action, $this->resource->getAccessLevel()));
        }

        $entity = clone $this->getEntity();

        $model = $this->getModelFromEntity($entity);
        if (!$this->isForced()) {
            $this->resource->isModelUpdatable($model);
        }
        $this->setModel($model);

        if ($action === Resource::ACTION_PARTIAL_UPDATE) {
            // @todo potentially make payload available to all update actions
            $existing_data = $this->toPayload(Resource::ACTION_UPDATE, $model);
            $entity->setData(Arr::mergeRecursiveDistinct($existing_data->toArray(), $entity->toArray()));
        }
        $this->setInputEntity($entity);

        $this->attach('validation_field_config', function (FieldConfig $config) use ($model) {
            $config->store('_model', $model);
            return $config;
        });

        $this->validate();

        $this->prepared = true;
    }

    public function handle()
    {
        parent::handle();

        try {
            $this->save();
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
        $primary_field = $this->getFields()->primaryField()->outputValueFromModel($this->getModel());
        if ($this->hasScope()) {
            $response = $this->resource->entity($primary_field)->scope($this->getScope())->run();
        } else {
            $response = $primary_field;
        }
        $this->setResponse($response);
    }
}
