<?php

namespace Core\Components\Resource\Traits\Request;

trait ValidationProxyTrait
{
    use ProxyTrait;

    public function getInputEntity()
    {
        return $this->getRequest()->getInputEntity();
    }

    public function getValidatedEntity()
    {
        return $this->getRequest()->getValidatedEntity();
    }

    public function isValidated()
    {
        return $this->getRequest()->isValidated();
    }
}
