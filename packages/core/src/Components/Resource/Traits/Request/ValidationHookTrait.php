<?php

namespace Core\Components\Resource\Traits\Request;

use Core\Components\Resource\Classes\FieldList;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;

trait ValidationHookTrait
{
    /**
     * @return \Core\Components\Resource\Classes\Resource
     */
    abstract public function resource();

    /**
     * @param $base_name
     * @param array $hooks
     * @param bool $include_base
     * @return array
     */
    abstract protected function buildHooks($base_name, array $hooks, $include_base = true);

    /**
     * @return array
     */
    abstract protected function getHookNames();

    /**
     * @return \Core\Components\Resource\Classes\Callbacks
     */
    abstract public function callbacks();

    /**
     * @param FieldList $fields
     * @return FieldList
     */
    protected function handleValidationFields(FieldList $fields)
    {
        $fields = $this->resource()->runFilterHook(
            $this->buildHooks('request.validation_fields', $this->getHookNames()), $fields, $this
        );
        return $this->callbacks()->apply('validation_fields', $fields);
    }

    /**
     * @param FieldConfig $config
     * @return FieldConfig
     */
    protected function handleValidationFieldConfig(FieldConfig $config)
    {
        $config = $this->resource()->runFilterHook(
            $this->buildHooks('request.validation_field_config', $this->getHookNames()), $config, $this
        );
        return $this->callbacks()->apply('validation_field_config', $config);
    }

    /**
     * @param Rules $rules
     * @return Rules
     */
    protected function handleValidationRules(Rules $rules)
    {
        $rules = $this->resource()->runFilterHook(
            $this->buildHooks('request.validation_rules', $this->getHookNames()), $rules, $this
        );
        return $this->callbacks()->apply('validation_rules', $rules);
    }
}
