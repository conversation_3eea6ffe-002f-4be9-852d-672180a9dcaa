<?php

namespace Core\Components\Session\Middleware;

use Closure;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Components\Http\Interfaces\SessionInterface;

/**
 * Class SessionMiddleware
 *
 * @package Core\Components\Session\Middleware
 */
class SessionMiddleware
{
    /**
     * @var SessionInterface
     */
    protected $session;

    /**
     * SessionMiddleware constructor
     *
     * @param SessionInterface $session
     */
    public function __construct(SessionInterface $session)
    {
        $this->session = $session;
    }

    /**
     * @param RequestInterface $request
     * @param Closure $next
     * @return ResponseInterface
     */
    public function handle(RequestInterface $request, Closure $next)
    {
        // transfer new flash items to current status
        $flash = $this->session->get(SessionInterface::FLASH_KEY, []);
        if (isset($flash['new'])) {
            $flash['current'] = $flash['new'];
            unset($flash['new']);
        }
        $this->session->set(SessionInterface::FLASH_KEY, $flash);

        return $next($request);
    }

    /**
     * @param RequestInterface $request
     */
    public function terminate(RequestInterface $request)
    {
        // remove any current data before saving session
        $flash = $this->session->get(SessionInterface::FLASH_KEY, []);
        if (isset($flash['current'])) {
            unset($flash['current']);
        }
        $this->session->set(SessionInterface::FLASH_KEY, $flash);

        if (!$request->isAjax()) {
            $this->session->set('last_page', $request->uri()->current()->build());
        }
        $this->session->set('last_activity', time());

        $this->session->write();
    }
}
