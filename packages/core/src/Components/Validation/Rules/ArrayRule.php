<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use InvalidArgumentException;
use Core\Components\Validation\Classes\{Rule, Validator};

/**
 * Class ArrayRule
 *
 * @package Core\Components\Validation\Rules
 */
class ArrayRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'exact_count' => '{label} must contain {count} item(s)',
            'min_count' => '{label} must contain at least {count} item(s)',
            'max_count' => '{label} must contain less than {count} item(s)',
            'in_array_multi' => '{label} contains a invalid value',
            'in_array' => '{label} is not a valid value'
        ];
    }

    /**
     * Verify size of array matches the defined count
     *
     * @param mixed $field
     * @param mixed $count
     * @return array|bool
     */
    #[RuleAttribute('exact_count')]
    public function exactCount(mixed $field, mixed $count): array|bool
    {
        if (!is_array($field) || count($field) !== (int) $count) {
            return ['exact_count', ['count' => $count]];
        }
        return true;
    }

    /**
     * Verify size of array meets defined minimum
     *
     * @param mixed $field
     * @param mixed $count
     * @return array|bool
     */
    #[RuleAttribute('min_count')]
    public function minCount(mixed $field, mixed $count): array|bool
    {
        if (!is_array($field) || count($field) < (int) $count) {
            return ['min_count', ['count' => $count]];
        }
        return true;
    }

    /**
     * Verify size of array meets defined maximum
     *
     * @param mixed $field
     * @param mixed $count
     * @return array|bool
     */
    #[RuleAttribute('max_count')]
    public function maxCount(mixed $field, mixed $count): array|bool
    {
        if (!is_array($field) || count($field) > (int) $count) {
            return ['max_count', ['count' => $count]];
        }
        return true;
    }

    /**
     * Verify field value exists in defined array
     *
     * @param mixed $field
     * @param string|array $array
     * @param Validator $validator
     * @param bool $negate
     * @return string|bool
     */
    #[RuleAttribute('in_array')]
    public function inArray(mixed $field, string|array $array, Validator $validator, bool $negate = false): string|bool
    {
        if (is_string($array)) {
            $array = $validator->getConfig()->storage($array);
        }
        if (!is_array($array)) {
            throw new InvalidArgumentException('in_array rule requires an array');
        }
        if (isset($array['callable'])) {
            $array = call_user_func($array['callable'], $field, $validator);
        }
        if (is_array($field)) {
            foreach ($field as $value) {
                $in_array = !in_array($value, $array);
                if ($negate) {
                    $in_array = !$in_array;
                }
                if ($in_array) {
                    return 'in_array_multi';
                }
            }
            return true;
        }
        $in_array = in_array($field, $array);
        if ($negate) {
            $in_array = !$in_array;
        }
        if ($in_array) {
            return true;
        }
        return 'in_array';
    }

    /**
     * Verify field value does not exist in defined array
     *
     * @param mixed $field
     * @param string|array $array
     * @param Validator $validator
     * @return string|bool
     */
    #[RuleAttribute('not_in_array')]
    public function notInArray(mixed $field, string|array $array, Validator $validator): string|bool
    {
        return $this->inArray($field, $array, $validator, true);
    }
}
