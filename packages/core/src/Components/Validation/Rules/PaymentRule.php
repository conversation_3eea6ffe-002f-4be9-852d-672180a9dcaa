<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Carbon\Carbon;
use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;

/**
 * Class PaymentRule
 *
 * @package Core\Components\Validation\Rules
 */
class PaymentRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'credit_card_number_invalid' => '{label} is not valid',
            'credit_card_expiration_invalid' => '{label} must be in the format [YYYY-MM]',
            'credit_card_expiration_past' => '{label} cannot be in the past',
            'aba_routing_transit_number_invalid' => '{label} is not valid'
        ];
    }

    /**
     * Validate number against <PERSON>hn algorithm
     *
     * @param string $number
     * @return bool
     */
    public static function validateLuhn(string $number): bool
    {
        $checksum = '';
        foreach (str_split(strrev($number)) as $i => $d) {
            $checksum .= $i % 2 !== 0 ? $d * 2 : $d;
        }
        return array_sum(str_split($checksum)) % 10 === 0;
    }

    /**
     * Verify credit card number is valid
     *
     * @param mixed $number
     * @param mixed $config
     * @return bool|string
     */
    #[RuleAttribute('credit_card_number')]
    public function creditCardNumber(mixed &$number, mixed $config): string|bool
    {
        $number = preg_replace('#[^0-9]#', '', (string) $number);

        // references jQuery credit card validator which was in use on the site which needed this validator addition
        $types = [
            'amex' => [
                'name' => 'Amex',
                'pattern' => '#^3[47]#',
                'valid_length' => [15]
            ],
            'diners_club_carte_blanche' => [
                'name' => 'Diners Club Carte Blanche',
                'pattern' => '#^30[0-5]#',
                'valid_length' => [14]
            ],
            'diners_club_international' => [
                'name' => 'Diners Club International',
                'pattern' => '#^36#',
                'valid_length' => [14]
            ],
            'jcb' => [
                'name' => 'JCB',
                'pattern' => '#^35(2[89]|[3-8][0-9])#',
                'valid_length' => [16]
            ],
            'laser' => [
                'name' => 'Laser',
                'pattern' => '#^(6304|670[69]|6771)#',
                'valid_length' => [16, 17, 18, 19]
            ],
            'visa_electron' => [
                'name' => 'Visa Electron',
                'pattern' => '#^(4026|417500|4508|4844|491(3|7))#',
                'valid_length' => [16]
            ],
            'visa' => [
                'name' => 'Visa',
                'pattern' => '#^4#',
                'valid_length' => [16]
            ],
            'mastercard' => [
                'name' => 'Mastercard',
                'pattern' => '#^5[1-5]#',
                'valid_length' => [16]
            ],
            'maestro' => [
                'name' => 'Maestro',
                'pattern' => '#^(5018|5020|5038|6304|6759|676[1-3])#',
                'valid_length' => [12, 13, 14, 15, 16, 17, 18, 19]
            ],
            'discover' => [
                'name' => 'Discover',
                'pattern' => '#^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)#',
                'valid_length' => [16]
            ]
        ];

        if (!is_array($config)) {
            $config = [];
        }
        $accept = isset($config['accept']) && is_array($config['accept']) ? $config['accept'] : array_keys($types);

        $number_type = null;
        foreach ($types as $type => $type_config) {
            if (!in_array($type, $accept)) {
                continue;
            }
            if (preg_match($type_config['pattern'], $number) !== 1) {
                continue;
            }
            $number_type = $type;
            break;
        }

        if (
            $number_type === null ||
            !self::validateLuhn($number) ||
            !in_array(strlen($number), $types[$number_type]['valid_length'])
        ) {
            return 'credit_card_number_invalid';
        }
        return true;
    }

    /**
     * Verify credit card expiration is valid
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('credit_card_expiration')]
    public function creditCardExpiration(mixed &$field): string|bool
    {
        $field = (string) $field;
        if (preg_match('#^(?<year>[0-9]{4})[^0-9]*(?<month>(0[1-9]|1[0-2]))$#', $field, $match) !== 1) {
            return 'credit_card_expiration_invalid';
        }

        $now = Carbon::now('UTC');

        $year = (int) $match['year'];
        $month = (int) $match['month'];
        if ($year < $now->year || ($year === $now->year && $month < $now->month)) {
            return 'credit_card_expiration_past';
        }
        $field = $year . '-' . str_pad((string) $month, 2, '0', STR_PAD_LEFT);
        return true;
    }

    /**
     * Verify ABA routing transit number is valid
     *
     * @param mixed $field
     * @return string|bool
     *
     * @see https://en.wikipedia.org/wiki/ABA_routing_transit_number
     */
    #[RuleAttribute('aba_routing_transit_number')]
    public function abaRoutingTransitNumber(mixed $field): string|bool
    {
        $field = (string) $field;
        if (preg_match('#^[0-9]{9}$#', $field) !== 1) {
            return 'aba_routing_transit_number_invalid';
        }
        $number = str_split($field);
        $sum = 3 * ($number[0] + $number[3] + $number[6]);
        $sum += 7 * ($number[1] + $number[4] + $number[7]);
        $sum += ($number[2] + $number[5] + $number[8]);
        if ($sum % 10 !== 0) {
            return 'aba_routing_transit_number_invalid';
        }
        return true;
    }
}
