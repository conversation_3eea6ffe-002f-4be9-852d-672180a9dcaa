<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;
use InvalidArgumentException;
use Ramsey\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;
use Throwable;

/**
 * Class TypeRule
 *
 * @package Core\Components\Validation\Rules
 */
class TypeRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'type_string' => '{label} is not a string type',
            'type_integer' => '{label} is not an integer type',
            'type_float' => '{label} is not a float type',
            'type_boolean' => '{label} is not a boolean type',
            'type_array' => '{label} is not an array type',
            'uuid' => '{label} is not a valid UUID',
            'json' => '{label} is not a valid JSON string',
        ];
    }

    /**
     * Set field to null if it doesn't have a value
     *
     * @param mixed $field
     * @return bool
     */
    #[RuleAttribute('nullable')]
    public function nullable(mixed &$field): bool
    {
        if ($field === '') {
            $field = null;
        }
        return true;
    }

    /**
     * Validate field data type
     *
     * @param mixed $field
     * @param string $param
     * @return bool|string
     */
    #[RuleAttribute('type')]
    public function type(mixed $field, string $param): bool|string
    {
        switch ($param) {
            case 'string':
                if (is_string($field)) {
                    return true;
                }
                return 'type_string';
            case 'int':
            case 'integer':
                if (is_integer($field)) {
                    return true;
                }
                return 'type_integer';
            case 'float':
                if (is_float($field)) {
                    return true;
                }
                return 'type_float';
            case 'bool':
            case 'boolean':
                if (is_bool($field)) {
                    return true;
                }
                return 'type_boolean';
            case 'array':
                if (is_array($field)) {
                    return true;
                }
                return 'type_array';
            default:
                throw new InvalidArgumentException('Invalid type');
        }
    }

    /**
     * Cast field to a specified type
     *
     * @param mixed $field
     * @param string $type
     * @return bool
     */
    #[RuleAttribute('cast')]
    public function cast(mixed &$field, string $type): bool
    {
        $field = match ($type) {
            'bool', 'boolean' => in_array($field, [true, 'true', 1, '1', 'y', 'yes'], true),
            'int', 'integer' => (int) $field,
            default => throw new InvalidArgumentException('Invalid type')
        };
        return true;
    }

    /**
     * Validate UUID and set field to UUID instance
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('uuid')]
    public function uuid(mixed &$field): bool|string
    {
        try {
            if (is_string($field)) {
                $field = Uuid::fromString($field);
            } elseif (!($field instanceof UuidInterface)) {
                return 'uuid';
            }
            return true;
        } catch (Throwable) {
            return 'uuid';
        }
    }

    /**
     * Validate that the field is a valid JSON string.
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('json')]
    public function json(mixed $field): bool|string
    {
        // Accept empty or null values (optional field)
        if ($field === null || $field === '') {
            return true;
        }

        // If it's already an array (possibly already decoded JSON), accept it
        if (is_array($field)) {
            return true;
        }

        // At this point, we expect a string that can be parsed as JSON
        if (!is_string($field)) {
            return 'type_string';
        }

        json_decode($field);

        return json_last_error() === JSON_ERROR_NONE ? true : 'json';
    }
}
