<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Closure;
use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\{Rule, Rules, Validator};
use Core\Components\Validation\Exceptions\ValidationException;

/**
 * Class ExistenceRule
 *
 * @package Core\Components\Validation\Rules
 */
class ExistenceRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'required' => '{label} is required'
        ];
    }

    /**
     * Set field requirement based on rule set or closure
     *
     * @param mixed $field
     * @param array $config
     * @param Validator $validator
     * @return bool
     * @throws ValidationException
     */
    #[RuleAttribute('required_if')]
    public function requiredIf(mixed $field, mixed $config, Validator $validator): bool|string|int
    {
        if (is_string($config)) {
            $config = $validator->getConfig()->storage($config);
        }
        if ($config instanceof Closure) {
            $required = $config($field, $validator, $this);
            if (!$required) {
                return $this->optional($field);
            }
            return $this->required($field);
        }
        // @todo add rules engine
        throw new ValidationException('Required if rules engine not yet implemented');
    }

    /**
     * Set field to be ignored based on rule set or closure
     *
     * @param mixed $field
     * @param array $config
     * @param Validator $validator
     * @return bool|int
     * @throws ValidationException
     */
    #[RuleAttribute('ignored_if')]
    public function ignoredIf(mixed &$field, mixed $config, Validator $validator): bool|int
    {
        if (is_string($config)) {
            $config = $validator->getConfig()->storage($config);
        }
        if (!is_object($config) || !($config instanceof Closure)) {
            throw new ValidationException('Ignored if requires a closure');
        }
        $ignored = $config($field, $validator, $this);
        if (!$ignored) {
            return true;
        }
        $field = null;
        return Rules::STOP;
    }

    /**
     * Require field if it doesn't have a value
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('required')]
    public function required(mixed $field): bool|string
    {
        if ($this->hasValue($field)) {
            return true;
        }
        return 'required';
    }

    /**
     * Stop validation if field doesn't have a value
     *
     * @param mixed $field
     * @return bool|int
     */
    #[RuleAttribute('optional')]
    public function optional(mixed $field): bool|int
    {
        if ($this->hasValue($field)) {
            return true;
        }
        return Rules::STOP;
    }

    /**
     * Set default value for field if it doesn't have a value
     *
     * @param mixed $field
     * @param mixed $default
     * @return bool|int
     */
    #[RuleAttribute('empty_default')]
    public function emptyDefault(mixed &$field, mixed $default): bool|int
    {
        if ($this->hasValue($field)) {
            return true;
        }
        $field = $default;
        return Rules::STOP;
    }
}
