<?php

declare(strict_types=1);

namespace Core\Components\Validation\Classes;

use Core\Classes\Arr;

/**
 * Class FieldConfig
 *
 * @package Core\Components\Validation\Classes
 */
class FieldConfig
{
    /**
     * Field configuration
     *
     * @var array
     */
    protected array $fields = [];

    /**
     * Data storage
     *
     * @var array
     */
    protected array $storage = [];

    /**
     * Add field
     *
     * @param Field $field
     * @return $this
     */
    public function add(Field $field): self
    {
        $this->fields[$field->getName()] = $field;
        return $this;
    }

    /**
     * Get all fields
     *
     * @return Field[]
     */
    public function getFields(): array
    {
        return $this->fields;
    }

    /**
     * @param string $name
     * @return Field|null
     */
    public function getField(string $name): ?Field
    {
        return $this->fields[$name] ?? null;
    }

    /**
     * Clear all fields
     *
     * @return $this
     */
    public function clearFields(): self
    {
        $this->fields = [];
        return $this;
    }

    /**
     * Store arbitrary data in storage
     *
     * @param string $key
     * @param mixed $data
     * @return $this
     */
    public function store(string $key, mixed $data): self
    {
        Arr::set($this->storage, $key, $data);
        return $this;
    }

    /**
     * Get data from storage
     *
     * @param string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function storage(string $key, mixed $default = null): mixed
    {
        return Arr::get($this->storage, $key, $default);
    }

    /**
     * Build FieldConfig instance from array format
     *
     * @param array $fields
     * @return static
     */
    public static function fromArray(array $fields): static
    {
        $self = new static();
        foreach ($fields as $name => $config) {
            if ($config instanceof Field) {
                $self->add($config);
                continue;
            }
            $field = Field::fromArray($config);
            $field->name($name);
            $self->add($field);
        }
        return $self;
    }
}
