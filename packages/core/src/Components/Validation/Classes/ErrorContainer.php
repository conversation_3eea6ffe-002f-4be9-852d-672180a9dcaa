<?php

declare(strict_types=1);

namespace Core\Components\Validation\Classes;

use Core\Classes\Arr;
use Countable;

/**
 * Class ErrorContainer
 *
 * Basic error container class, will be expanded upon as needed
 *
 * @package Core\Components\Validation\Classes
 */
class ErrorContainer implements Countable
{
    /**
     * Holds all errors
     *
     * @var array
     */
    protected array $errors = [];

    /**
     * ErrorContainer constructor
     *
     * @param array $errors
     */
    public function __construct(array $errors = [])
    {
        $this->errors = $errors;
    }

    /**
     * Add error
     *
     * @param string $key
     * @param string $message
     * @return $this
     */
    public function add(string $key, string $message): self
    {
        Arr::set($this->errors, $key, $message);
        return $this;
    }

    /**
     * Get error count
     *
     * @return int
     */
    public function count(): int
    {
        return count($this->errors);
    }

    /**
     * Check if error has been added for a specific key
     *
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        return Arr::get($this->errors, $key) !== null;
    }

    /**
     * Get error for specific key or all errors
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    public function get(?string $key = null, mixed $default = null): mixed
    {
        return Arr::get($this->errors, $key, $default);
    }
}
