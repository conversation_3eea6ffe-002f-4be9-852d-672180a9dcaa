<?php

declare(strict_types=1);

namespace Core\Components\Validation\Attributes;

use Attribute;

/**
 * Class RuleAttribute
 *
 * @package Core\Components\Validation\Attributes
 */
#[Attribute(Attribute::TARGET_METHOD)]
class RuleAttribute
{
    /**
     * RuleAttribute Constructor
     *
     * @param string $name
     */
    public function __construct(protected string $name)
    {}

    /**
     * Get name of rule
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }
}
