<?php

declare(strict_types=1);

namespace Core\Components\Asset\StaticAccessors;

use Core\Components\Asset\Classes\Manager as AssetManager;
use Core\Classes\StaticAccessor;

/**
 * Class Asset
 *
 * @package Core\Components\Asset\StaticAccessors
 */
class Asset extends StaticAccessor
{
    /**
     * Get instance of Asset
     *
     * @return AssetManager
     */
    protected static function getInstance(): AssetManager
    {
        return static::$kernel->get(AssetManager::class);
    }
}
