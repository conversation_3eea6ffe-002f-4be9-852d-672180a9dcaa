<?php

declare(strict_types=1);

namespace Core\Components\Asset\Classes;

use Closure;
use Core\Components\Asset\Assets\Adhoc\{RawScriptAsset, RawStyleAsset};
use Core\Components\Asset\Assets\File\{IconAsset, PreloadAsset, ScriptAsset, StyleAsset, SvgAsset, WebManifestAsset};
use Core\Components\Asset\Exceptions\AssetException;
use Core\Components\Http\Classes\{Html, URLBuilder};
use Core\Components\Http\Interfaces\ViewInterface;

use function base64_encode;

/**
 * Class ViewHelper
 *
 * @package Core\Components\Asset\Classes
 */
class ViewHelper
{
    /**
     * @var ViewInterface View associated with helper
     */
    protected ViewInterface $view;

    /**
     * @var RawStyleAsset|null Style asset which captured content will be injected into
     */
    protected ?RawStyleAsset $style_capture = null;

    /**
     * @var RawScriptAsset|null Script asset which captured content will be injected into
     */
    protected ?RawScriptAsset $script_capture = null;

    /**
     * @var array<string, string> Output placeholders which will get replaced with content for each location
     */
    protected array $outputs = [];

    /**
     * ViewHelper constructor
     *
     * @param Manager $manager
     * @param Stack $stack
     */
    public function __construct(protected Manager $manager, protected Stack $stack)
    {}

    /**
     * Boot helper
     *
     * @param ViewInterface $view
     */
    public function boot(ViewInterface $view): void
    {
        $this->view = $view;
        $this->view->on('rendered', function ($data) {
            return [$this->handleOutputs($data)];
        });
    }

    /**
     * Get absolute path to asset non encoded
     *
     * @param string $name
     * @param string $path
     * @return string
     * @throws AssetException
     */
    public function pathRaw(string $name, string $path = ''): string
    {
        return $this->manager->path($name, $path);
    }

    /**
     * Get encoded absolute path to asset
     *
     * @param string $name
     * @param string $path
     * @return string
     * @throws AssetException
     */
    public function path(string $name, string $path = ''): string
    {
        return Html::entityEncode($this->pathRaw($name, $path));
    }

    /**
     * Get non encoded version of URI
     *
     * @param string $name
     * @param string|null $path
     * @param Closure|null $uri_config
     * @param bool $version
     * @return URLBuilder
     * @throws AssetException
     */
    public function uriRaw(string $name, ?string $path = null, ?Closure $uri_config = null, bool $version = true): URLBuilder
    {
        return $this->manager->uri($name, $path, $uri_config, $version);
    }

    /**
     * Get URI for path
     *
     * @param string $name
     * @param string|null $path
     * @param Closure|null $uri_config
     * @param bool $version
     * @return string
     * @throws AssetException
     */
    public function uri(string $name, ?string $path = null, ?Closure $uri_config = null, bool $version = true): string
    {
        return Html::entityEncode($this->uriRaw($name, $path, $uri_config, $version)->build());
    }

    /**
     * Get manifest view helper of manifest at specified path
     *
     * @param string $path
     * @param string $file
     * @return ManifestViewHelper
     * @throws AssetException
     */
    public function manifest(string $path, string $file = 'manifest.json'): ManifestViewHelper
    {
        return new ManifestViewHelper($this, new Manifest($this->manager, $path, $file));
    }

    /**
     * Add asset to stack
     *
     * @param Asset $asset
     * @return void
     */
    protected function add(Asset $asset): void
    {
        $this->stack->add($asset);
    }

    /**
     * Add asset group to helper stack
     *
     * @param string $name
     * @return $this
     * @throws AssetException
     */
    public function group(string $name): self
    {
        if (($stack = $this->manager->getGroup($name)) === null) {
            throw new AssetException('Unable to find asset group: %s', $name);
        }
        $this->stack->merge($stack);
        return $this;
    }

    /**
     * Add style asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return StyleAsset
     */
    public function style(Closure|string $path, array $config = []): StyleAsset
    {
        $asset = new StyleAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Add raw style asset
     *
     * @param Closure|string $content
     * @param array $config
     * @return RawStyleAsset
     */
    public function rawStyle(Closure|string $content, array $config = []): RawStyleAsset
    {
        $asset = new RawStyleAsset($this->manager);
        $asset->content($content);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Capture content into raw style asset
     *
     * @param array $config
     * @return Asset
     * @throws AssetException
     */
    public function captureStyle(array $config = []): Asset
    {
        if ($this->style_capture !== null) {
            throw new AssetException('Nested captures are not allowed');
        }
        ob_start();
        $this->style_capture = $this->rawStyle('', $config);
        return $this->style_capture;
    }

    /**
     * End output buffering for style capture and inject content into asset
     *
     * @throws AssetException
     */
    public function endCaptureStyle(): void
    {
        if ($this->style_capture === null) {
            throw new AssetException('You cannot end that which has not begun');
        }
        $this->style_capture->content(ob_get_clean());
        $this->style_capture = null;
    }

    /**
     * Add icon asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return IconAsset
     */
    public function icon(Closure|string $path, array $config = []): IconAsset
    {
        $asset = new IconAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Add script asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return ScriptAsset
     */
    public function script(Closure|string $path, array $config = []): ScriptAsset
    {
        $asset = new ScriptAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Add raw script asset
     *
     * @param mixed $content
     * @param array $config
     * @return RawScriptAsset
     */
    public function rawScript(Closure|string $content, array $config = []): RawScriptAsset
    {
        $asset = new RawScriptAsset($this->manager);
        $asset->content($content);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Capture content into raw script asset
     *
     * @param array $config
     * @return Asset
     *
     * @throws AssetException
     */
    public function captureScript(array $config = []): Asset
    {
        if ($this->script_capture !== null) {
            throw new AssetException('Nested captures are not allowed');
        }
        ob_start();
        $this->script_capture = $this->rawScript('', $config);
        return $this->script_capture;
    }

    /**
     * End output buffering for script capture and inject content into asset
     *
     * @throws AssetException
     */
    public function endCaptureScript(): void
    {
        if ($this->script_capture === null) {
            throw new AssetException('You cannot end that which has not begun');
        }
        $this->script_capture->content(ob_get_clean());
        $this->script_capture = null;
    }

    /**
     * Add data to window variable
     *
     * @param string $var_name
     * @param array $data
     * @param array $config
     * @return RawScriptAsset
     */
    public function scriptData(string $var_name, array $data, array $config = []): RawScriptAsset
    {
        $data = "window.{$var_name} = " . json_encode($data) . ';';
        return $this->rawScript($data, $config);
    }

    /**
     * Add SVG asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return SvgAsset
     */
    public function svg(Closure|string $path, array $config = []): SvgAsset
    {
        $asset = new SvgAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Add web manifest asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return WebManifestAsset
     */
    public function webManifest(Closure|string $path, array $config = []): WebManifestAsset
    {
        $asset = new WebManifestAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Preload asset
     *
     * @param Closure|string $path
     * @param array $config
     * @return PreloadAsset
     */
    public function preload(Closure|string $path, array $config = []): PreloadAsset
    {
        $asset = new PreloadAsset($this->manager);
        $asset->path($path);
        $asset->config($config);
        $this->add($asset);
        return $asset;
    }

    /**
     * Embed content from asset
     *
     * @param string $name
     * @param string $path
     * @param bool $base64_encode
     * @return string
     *
     * @throws AssetException
     */
    public function embed(string $name, string $path, bool $base64_encode = true): string
    {
        $file = $this->manager->path($name, $path);
        if (!file_exists($file)) {
            throw new AssetException('File does not exist: %s', $file);
        }
        if (($contents = file_get_contents($file)) === false) {
            throw new AssetException('Unable to read file: %s', $file);
        }
        if ($base64_encode) {
            $contents = base64_encode($contents);
        }
        return $contents;
    }

    /**
     * Get stack from helper
     *
     * @return Stack
     */
    public function getAssetStack(): Stack
    {
        return $this->stack;
    }

    /**
     * Replace output placeholders with their respective asset data
     *
     * Collect all assets for all nested views, render assets per location, and inject back into view at output
     * placeholders.
     *
     * @param string $view
     * @return string
     */
    protected function handleOutputs(string $view): string
    {
        if (count($this->outputs) === 0) {
            return $view;
        }
        $assets = [];
        $stack = clone $this->stack;
        foreach ($this->view->getChildren() as $child) {
            if (!$child->hasHelper('asset')) {
                continue;
            }
            $stack->merge($child->helper('asset')->getAssetStack());
        }
        if ($stack->count() > 0) {
            $assets = $this->manager->renderAssets($stack);
        }
        foreach ($this->outputs as $key => $location) {
            $view = str_replace($key, $assets[$location] ?? '', $view);
        }
        return $view;
    }

    /**
     * Add output placeholder for location
     *
     * @param string $location
     * @return string
     */
    public function output(string $location): string
    {
        $key = "{{|ASSET_OUTPUT:{$location}|}}";
        $this->outputs[$key] = $location;
        return $key;
    }
}
