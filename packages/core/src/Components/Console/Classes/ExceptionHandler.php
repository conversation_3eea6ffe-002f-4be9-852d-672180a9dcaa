<?php

declare(strict_types=1);

namespace Core\Components\Console\Classes;

use Core\Components\Console\Interfaces\ConsoleInterface;
use Core\Classes\Exception\ConsoleHandler;
use Throwable;

/**
 * Class ExceptionHandler
 *
 * @package Core\Components\Console\Classes
 */
class ExceptionHandler extends ConsoleHandler
{
    /**
     * Handle uncaught exception
     *
     * @param Throwable $exception
     */
    public function handleException(Throwable $exception): void
    {
        $this->log($exception);
        $this->render($exception);
    }

    /**
     * Render exception to user
     *
     * @param Throwable $e
     */
    protected function render(Throwable $e): void
    {
        $console = $this->kernel->get(ConsoleInterface::class);
        $console->error($e->getMessage());
        $console->line();
        $console->error('Stack trace:');
        $console->line($this->getTraceAsString($e));
    }
}
