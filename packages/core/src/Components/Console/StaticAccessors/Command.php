<?php

declare(strict_types=1);

namespace Core\Components\Console\StaticAccessors;

use Core\Components\Console\Classes\Command as ConsoleCommand;
use Core\Classes\StaticAccessor;

/**
 * Class Command
 *
 * @package Core\Components\Console\StaticAccessors
 *
 * @method static ConsoleCommand add(string $name, string $class, string|null $method = null)
 */
class Command extends StaticAccessor
{
    /**
     * Get instance of Command
     *
     * @return ConsoleCommand
     */
    protected static function getInstance(): ConsoleCommand
    {
        return static::$kernel->get(ConsoleCommand::class);
    }
}
