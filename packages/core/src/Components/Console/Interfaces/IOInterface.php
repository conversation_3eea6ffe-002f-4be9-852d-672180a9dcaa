<?php

namespace Core\Components\Console\Interfaces;

use Core\Components\Console\Classes\Arguments;

/**
 * Interface IOInterface
 *
 * @package Core\Components\Console\Interfaces
 */
interface IOInterface
{
    /**
     * Get arguments instance
     *
     * @return Arguments
     */
    public function getArguments(): Arguments;

    /**
     * Get input stream resource
     *
     * @return resource
     */
    public function getInputStream();

    /**
     * Get output stream resource
     *
     * @return resource
     */
    public function getOutputStream();

    /**
     * Get error stream resource
     *
     * @return resource
     */
    public function getErrorStream();
}
