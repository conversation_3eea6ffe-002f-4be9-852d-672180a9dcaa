<?php

namespace Core\Components\Console\Interfaces;

/**
 * Interface RouterInterface
 *
 * @package Core\Components\Console\Interfaces
 */
interface RouterInterface
{
    /**
     * Add file to pull route definitions from
     *
     * @param string $file
     */
    public function addRouteFile(string $file): void;

    /**
     * Boot router
     *
     * @param CommandInterface $command
     * @param ConsoleInterface $console
     */
    public function boot(CommandInterface $command, ConsoleInterface $console): void;
}
