FROM ubuntu:22.04

# Set DEBIAN_FRONTEND to noninteractive for apt
ENV DEBIAN_FRONTEND=noninteractive

# 1. Install prerequisites
RUN apt-get update && apt-get install -y \
    software-properties-common \
    curl \
    git \
    wget \
    lsb-release \
    ca-certificates \
    gnupg2 \
    tzdata \
    locales \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 2. Configure timezone
RUN ln -fs /usr/share/zoneinfo/America/New_York /etc/localtime && \
    dpkg-reconfigure --frontend noninteractive tzdata

# 3. Configure locale
RUN locale-gen en_US.UTF-8 && update-locale LANG=en_US.UTF-8
ENV LANG en_US.UTF-8
ENV LC_ALL en_US.UTF-8

# 4. Add the PHP repository (Ondrej) and install PHP-FPM + extensions
RUN add-apt-repository ppa:ondrej/php && apt-get update && \
    apt-get install -y \
    php8.0 \
    php8.0-fpm \
    php8.0-cli \
    php8.0-bcmath \
    php8.0-curl \
    php8.0-gd \
    php8.0-intl \
    php8.0-mbstring \
    php8.0-mysql \
    php8.0-soap \
    php8.0-xml \
    php8.0-zip \
    php8.0-imagick \
    php8.0-redis \
    php8.0-xdebug \
    nginx \
    mariadb-client \
    redis-tools \
    p7zip-full \
    vim-tiny \
    iputils-ping && \
    rm -rf /var/lib/apt/lists/*

# 5.1 Add Ubuntu 20.04 repository temporarily for libssl1.1
RUN echo "deb [arch=arm64] http://ports.ubuntu.com/ubuntu-ports focal main universe" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y libssl1.1 && \
    sed -i '/focal/d' /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists/*


# 6. Install TeX Live minimal installation and create symbolic link for pdflatex
RUN apt-get update && apt-get install -y \
    texlive-base \
    texlive-latex-base \
    texlive-latex-recommended \
    texlive-latex-extra \
    texlive-fonts-recommended \
    texlive-fonts-extra \
    texlive-binaries \
    texlive-bibtex-extra \
    texlive-science \
    lmodern \
    && ln -s /usr/bin/pdflatex /usr/local/bin/pdflatex \
    && rm -rf /var/lib/apt/lists/*



# 10. Install PDF dependencies
RUN apt-get update && apt-get install -y \
    pdftk \
    fontconfig \
    xfonts-75dpi \
    xfonts-base \
    xfonts-utils \
    libxrender1 \
    libfontconfig1 \
    libx11-dev \
    && rm -rf /var/lib/apt/lists/*

# 11. Install wkhtmltopdf from a precompiled binary
RUN curl -sL https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.focal_arm64.deb -o wkhtmltox.deb && \
    dpkg -i wkhtmltox.deb || apt-get install -fy && \
    ln -s /usr/local/bin/wkhtmltopdf /usr/bin/wkhtmltopdf && \
    rm wkhtmltox.deb


# 12. Create the wrapper script for wkhtmltopdf
RUN mv /usr/local/bin/wkhtmltopdf /usr/local/bin/wkhtmltopdf-original

# 12.1 Create the wrapper script
RUN echo '#!/bin/bash\n\
ORIGINAL_WKHTMLTOPDF="/usr/local/bin/wkhtmltopdf-original"\n\
if [ ! -f "$ORIGINAL_WKHTMLTOPDF" ]; then\n\
    echo "Error: Original wkhtmltopdf binary not found at $ORIGINAL_WKHTMLTOPDF"\n\
    exit 1\n\
fi\n\
"$ORIGINAL_WKHTMLTOPDF" --enable-local-file-access --allow /tmp/ "$@"' \
> /usr/local/bin/wkhtmltopdf && chmod +x /usr/local/bin/wkhtmltopdf



# 13. Install Composer
RUN curl -sS https://getcomposer.org/installer -o composer-setup.php && \
    php composer-setup.php --version=2.1.6 --install-dir=/usr/local/bin --filename=composer && \
    rm composer-setup.php

# 14. Copy and run PHP/Nginx setup scripts if you have them
COPY scripts/php-setup.sh /usr/local/bin/php-setup.sh
RUN chmod +x /usr/local/bin/php-setup.sh && /usr/local/bin/php-setup.sh

COPY scripts/nginx-setup.sh /usr/local/bin/nginx-setup.sh
RUN chmod +x /usr/local/bin/nginx-setup.sh && /usr/local/bin/nginx-setup.sh

# 15 Generate self-signed SSL cert for app.ca.test
RUN mkdir -p /etc/nginx/ssl && \
    openssl req -x509 -nodes -days 365 \
    -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/local.key \
    -out /etc/nginx/ssl/local.crt \
    -subj "/C=US/ST=New York/L=New York/O=LocalDev/CN=app.ca.test"

# 16. Create a new user and group for Nginx
RUN groupadd -r nginx && useradd -r -g nginx nginx
RUN usermod -aG www-data nginx

# 16.1 Install NVM, Node.js & Yarn
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash && \
    export NVM_DIR="/root/.nvm" && \
    [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh" && \
    nvm install 14.17.6 && \
    nvm alias default 14.17.6 && \
    npm install -g yarn

# 16.2 Persist Bash History Across Container Runs
RUN echo 'export HISTFILE=/root/.bash_history' >> /root/.bashrc && \
    echo 'PROMPT_COMMAND="history -a; history -c; history -r; $PROMPT_COMMAND"' >> /root/.bashrc

# 17. Set working directory
WORKDIR /var/www/applications/company

# 18. Create required directories
RUN mkdir -p storage/cache/brands && mkdir -p /var/ca-app/files && mkdir -p storage/assets
# RUN php /var/www/applications/company/console environment:reset-storage

RUN chown -R www-data:www-data /var/ca-app/files /var/www/applications/company /var/www/applications/company/storage/assets && \
    chmod -R 775 /var/ca-app/files /var/www/applications/company/storage /var/www/applications/company/storage/assets

RUN chmod 1777 /tmp


# 19. Expose port 80
EXPOSE 80
CMD service php8.0-fpm start && nginx -g 'daemon off;'