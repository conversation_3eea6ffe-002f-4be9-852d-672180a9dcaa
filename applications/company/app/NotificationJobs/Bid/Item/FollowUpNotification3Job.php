<?php

declare(strict_types=1);

namespace App\NotificationJobs\Bid\Item;

use App\Attributes\JobAttribute;
use App\Services\Email\Types\Customer\BidFollowUp3Type;
use App\Traits\Job\NotificationTrait;
use Common\Models\BidItem;
use Common\Models\Customer;
use Common\Models\Notification;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\UuidInterface;

/**
 * Class FollowUpNotification3Job
 *
 * @package App\NotificationJobs\Bid\Item
 */
#[JobAttribute(type: 47)]
class FollowUpNotification3Job extends Job
{
    use NotificationTrait;

    /**
     * FollowUpNotification3Job constructor
     *
     * @param UuidInterface $bid_item_id
     */
    public function __construct(protected UuidInterface $bid_item_id)
    {}

    /**
     * Handle notification
     *
     * Send bid to customer
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $bid_item = BidItem::query()->withProperty()->whereKey($this->bid_item_id->getBytes())
            ->first(['bidItems.*', 'property.customerID']);
        if ($bid_item === null) {
            throw new JobFailedException('Unable to find bid: %s', $this->bid_item_id->toString());
        }

        $customer = Customer::find($bid_item->customerID);
        if ($customer->canEmail()) {
            $notification = $this->createNotification(Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_3, $bid_item->getUuidKey());
            BidFollowUp3Type::send([
                'notification_id' => $notification->getUuidKey()->toString()
            ]);
        }

        // send other types of notifications (sms, push, etc.) here
    }
}
