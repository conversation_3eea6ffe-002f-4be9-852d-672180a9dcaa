<?php

declare(strict_types=1);

namespace App\NotificationJobs\User;

use App\Attributes\JobAttribute;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\User\LeadAssignedType;
use App\Services\AppNotification\Types\User\LeadAssignedType as AppNotificationLeadAssignedType;
use App\Services\UserSettingService;
use App\Traits\Job\NotificationTrait;
use Common\Models\Lead;
use Common\Models\Notification;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\Uuid;

/**
 * Class LeadAssignmentNotificationJob
 *
 * @package App\NotificationJobs\User
 */
#[JobAttribute(type: 51)]
class LeadAssignmentNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * LeadAssignmentNotificationJob constructor
     *
     * @param int $lead_id
     */
    public function __construct(protected int $lead_id)
    {}

    /**
     * Handle lead assignment notification
     *
     * Send notification to user when a lead has been created and assigned to them
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $errors = [];
        $lead = Lead::whereKey($this->lead_id)
            ->first(['leadID', 'leadUUID', 'assignedToUserID', 'companyID']);
        if ($lead === null) {
            throw new JobFailedException('Unable to find lead: %d', $this->lead_id);
        }

        $lead_id = $lead->lead_id;
        $setting_service = new UserSettingService($lead->assignedToUserID);


        if ($setting_service->get('email_notification_lead_assigned', false)) {
            try {
                $notification = $this->createNotification(Notification::TYPE_LEAD_ASSIGNMENT_NOTIFICATION, Uuid::fromBytes($lead->leadUUID));

                LeadAssignedType::send([
                    'notification_id' => $notification->getUuidKey()->toString()
                ]);
            } catch(TypeException $e) {
                $errors[] = "Email notification failed [Lead: ${$lead_id}]: ".$e->getMessage();
            }
        }


        if ($setting_service->get('app_notification_lead_assigned', false)) {
            try {
                AppNotificationLeadAssignedType::create($lead);
            } catch (\Throwable $e) {
                $errors[] = "App notification failed [Lead: {$lead_id}]: ".$e->getMessage();
            }
        }

        if (!empty($errors)) {
            throw new JobFailedException(
                'LeadAssignmentNotificationJob encountered issues: ' . implode(' | ', $errors)
            );
        }
    }

}
