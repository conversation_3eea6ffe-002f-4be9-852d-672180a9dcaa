<?php

declare(strict_types=1);

namespace App\NotificationJobs\User;

use App\Attributes\JobAttribute;
use App\Services\AppNotification\Types\User\ProjectEventCreatedType as AppNotificationProjectEventCreated;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\User\ProjectEventCreatedType;
use App\Services\UserSettingService;
use App\Traits\Job\NotificationTrait;
use App\Traits\Notification\CustomerTrait;
use Common\Models\Notification;
use Common\Models\ProjectSchedule;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\Uuid;

/**
 * Class CreatedNotification
 *
 * @package App\NotificationJobs\Project\Event
 */
#[JobAttribute(type: 60)]
class ProjectEventCreatedNotificationJob extends Job
{
    use CustomerTrait;
    use NotificationTrait;

    /**
     * CreatedNotificationJob constructor
     *
     * @param int $project_event_id
     */
    public function __construct(protected int $project_event_id)
    {}

    /**
     * Handle project event created notification
     *
     * Send email to customer
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $event = ProjectSchedule::withProperty()
            ->whereKey($this->project_event_id)
            ->first([
                'projectSchedule.projectScheduleID',
                'projectSchedule.projectID',
                'projectSchedule.projectScheduleUUID',
                'projectSchedule.scheduledUserID',
                'project.projectUUID as projectUUID',
            ]);

        if ($event === null) {
            throw new JobFailedException('Unable to find project schedule event: %d', $this->project_event_id);
        }

        if ($event->scheduledUserID === null) {
            throw new JobFailedException('Unable to find scheduled user for project schedule event: %d', $this->project_event_id);
        }

        $event_id = $event->projectScheduleID;
        $setting_service = new UserSettingService($event->scheduledUserID);

        if ($setting_service->get('email_notification_appointment_scheduled', false)) {
            try {
                $notification = $this->createNotification(
                    Notification::TYPE_USER_APPOINTMENT_SCHEDULED_NOTIFICATION,
                    Uuid::fromBytes($event->projectScheduleUUID));

                ProjectEventCreatedType::send([
                    'notification_id' => $notification->getUuidKey()->toString()
                ]);
            } catch(TypeException $e) {
                $errors[] = "Email notification failed [Event: {$event_id}]: ".$e->getMessage();
            }
        }

        if ($setting_service->get('app_notification_appointment_scheduled')) {
            try {
                AppNotificationProjectEventCreated::create($event);
            } catch (\Throwable $e) {
                $errors[] = "App notification failed [Event: {$event_id}]: ".$e->getMessage();
            }
        }

        if (!empty($errors)) {
            throw new JobFailedException(
                'EventCreatedNotificationJob encountered issues: ' . implode(' | ', $errors)
            );
        }
    }

}
