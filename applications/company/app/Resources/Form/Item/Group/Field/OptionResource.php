<?php

namespace App\Resources\Form\Item\Group\Field;

use App\Interfaces\Resource\FormFieldOptionResourceInterface;
use App\ResourceDelegates\Form\Item\Group\Field\OptionDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemGroupFieldOption;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;

class OptionResource extends Resource implements FormFieldOptionResourceInterface
{
    use UserActionTrackingTrait;

    protected $available_actions = (self::ACTION_GROUP_READ_ONLY | self::ACTION_GROUP_CREATE | self::ACTION_NESTED_UPDATE | self::ACTION_DELETE) & ~self::ACTION_GROUP_BATCH;

    protected $table = 'formItemGroupFieldOptions';
    protected $model = FormItemGroupFieldOption::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(OptionDelegate::class);
    }

    /**
     * Delete any options of specified field id whose id is not in passed array
     *
     * @param string $field_id
     * @param array $option_ids
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteMissingOptionsByFieldID(string $field_id, array $option_ids): void
    {
        $primary_field = $this->getPrimaryField();
        $field_id = $this->getFields()->get('field_id')
            ->saveValue($field_id);
        $option_ids = array_map(function ($id) use ($primary_field) {
            return $primary_field->saveValue($id);
        }, $option_ids);
        $this->newScopedQuery()
            ->where("{$this->table}.formItemGroupFieldID", $field_id)
            ->whereNotIn("{$this->table}.formItemGroupFieldOptionID", $option_ids)
            ->each(function ($option) use ($primary_field) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($option)
                ]))
                    ->force()
                    ->run();
            });
    }
}
