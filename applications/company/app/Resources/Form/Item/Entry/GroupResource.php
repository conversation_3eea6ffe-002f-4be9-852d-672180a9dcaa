<?php

namespace App\Resources\Form\Item\Entry;

use App\ResourceDelegates\Form\Item\Entry\GroupDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemEntryGroup;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;

class GroupResource extends Resource
{
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_SEARCH;

    protected $table = 'formItemEntryGroups';
    protected $model = FormItemEntryGroup::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(GroupDelegate::class);
    }

    public function getList(Collection $collection)
    {
        $groups = [];
        $roots = [];
        foreach ($collection as $group) {
            if (!isset($groups[$group['id']])) {
                $groups[$group['id']] = [
                    'children' => []
                ];
            }
            $groups[$group['id']]['entity'] = $group;
            if ($group['parent_id'] === null) {
                $roots[] = $group['id'];
            } else {
                if (!isset($groups[$group['parent_id']])) {
                    $groups[$group['parent_id']] = [
                        'children' => []
                    ];
                }
                $groups[$group['parent_id']]['children'][] = $group['id'];
            }
        }

        return new Collection([
            'all' => $groups,
            'root' => $roots
        ]);
    }

    public function deleteByEntryID($entry_id, $force = false)
    {
        $entry_id = $this->getFields()->get('entry_id')->saveValue($entry_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.formItemEntryID', $entry_id)
            ->get()
            ->each(function ($group) use ($primary_field, $force) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($group)
                ]))->force($force)->findConfig(['check_mutability' => !$force])->run();
            });
    }
}
