<?php

namespace App\Resources\Form\Item\Entry\Group;

use App\Interfaces\Resource\FormEntryFieldResourceInterface;
use App\ResourceDelegates\Form\Item\Entry\Group\FieldProductDelegate;
use App\Traits\Resource\FormEntryFieldResourceTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemEntryGroupFieldProduct;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Interfaces\SyncInterface;
use Core\Components\Resource\Requests\SyncRequest;
use Core\Components\Resource\Traits\SyncTrait;
use Ramsey\Uuid\Uuid;

class FieldProductResource extends Resource implements SyncInterface, FormEntryFieldResourceInterface
{
    use FormEntryFieldResourceTrait;
    use SyncTrait;
    use UserActionTrackingTrait;

    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SYNC) & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    protected $table = 'formItemEntryGroupFieldProducts';
    protected $model = FormItemEntryGroupFieldProduct::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(FieldProductDelegate::class);
    }

    public function sync(Entity $entity)
    {
        return (new SyncRequest($this, $entity))
            ->scopeFields(['group_id', 'field_source', 'field_id'])
            ->syncedField('product_item_id');
    }

    /**
     * @param $entry_id
     * @param Scope|null $scope
     * @return \Core\Components\Resource\Classes\Collection
     */
    public function getAllProductsByEntryID($entry_id, Scope $scope = null)
    {
        $primary_field = $this->getPrimaryField();
        $products = [];
        $query = $this->newQuery()->distinct();
        // since this method can be used without a user, we cannot rely on a scoped query to provide necessary joins
        // we have to deal with it ourselves
        if ($this->acl()->user() !== null) {
            $this->applyQueryScopeGlobal($query);
        } else {
            $query->withFormItem();
        }
        $query->where('formItemEntries.formItemEntryID', Uuid::fromString($entry_id)->getBytes())
            ->get([$this->getTableAlias() . '.formItemEntryGroupFieldProductID'])
            ->each(function ($product) use (&$products, $primary_field) {
                $products[] = $primary_field->outputValueFromModel($product);
            });

        if (count($products) === 0) {
            return new Collection;
        }

        if ($scope === null) {
            $scope = Scope::make();
        }
        $scope->filter('id', 'in', $products);

        return $this->collection()->scope($scope)->run();
    }

    public function deleteByGroupID($group_id, $force = false)
    {
        $group_id = $this->getFields()->get('group_id')->saveValue($group_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.formItemEntryGroupID', $group_id)
            ->get()
            ->each(function ($model) use ($primary_field, $force) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($model)
                ]))->force($force)->findConfig(['check_mutability' => !$force])->run();
            });
    }
}
