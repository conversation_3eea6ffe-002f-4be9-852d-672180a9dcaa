<?php

namespace App\Resources\Bid;

use App\Resources\Bid\Item\ContentResource as ItemContentResource;
use App\ResourceDelegates\Bid\ContentDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\BidContent;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;

class ContentResource extends Resource
{
    const TYPE_DISCLAIMER = 1;
    const TYPE_WAIVER = 2;
    const TYPE_WARRANTY = 3;
    const TYPE_CONTRACT = 4;
    const TYPE_ACKNOWLEDGEMENT = 5;
    const TYPE_INTRO = 6;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'bidContent';
    protected $model = BidContent::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getTypes()
    {
        return [
            static::TYPE_DISCLAIMER, static::TYPE_WAIVER, static::TYPE_WARRANTY, static::TYPE_CONTRACT, static::TYPE_ACKNOWLEDGEMENT, static::TYPE_INTRO
        ];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(ContentDelegate::class);
    }

    /**
     * Get all defaults for company and copy them to passed bid
     *
     * @todo this might need to live in a service since it works with multiple resources
     *
     * @param string $item_id Bid item uuid
     */
    public function createDefaultsForBidItem($item_id)
    {
        $scope = Scope::make()
            ->fields(['id', 'type', 'name', 'content', 'is_default', 'is_locked', 'is_required', 'is_answer_required'])
            ->filter('type', 'in', [
                static::TYPE_DISCLAIMER, static::TYPE_WAIVER, static::TYPE_WARRANTY,
                static::TYPE_CONTRACT, static::TYPE_ACKNOWLEDGEMENT
            ])
            ->filter('status', 'eq', static::STATUS_ACTIVE)
            ->filter('is_default', 'eq', true)
            ->query(function ($query) {
                return $query->ordered();
            });
        $content = $this->collection()->scope($scope)->run();
        if (count($content) > 0) {
            $content_types = $content->groupBy('type');
            $item_content_resource = ItemContentResource::make($this->acl());
            foreach ($content_types as $content) {
                $order = 1;
                foreach ($content as $item) {
                    $item->item_id = $item_id;
                    $item->source = ItemContentResource::SOURCE_BID_DEFAULT;
                    $item->bid_content_id = $item->id;
                    $item->order = $order++;
                    $item->id = $item_content_resource->generateId();
                    $item_content_resource->create($item)->run();
                }
            }
        }
    }
}
