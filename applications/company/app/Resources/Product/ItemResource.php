<?php

namespace App\Resources\Product;

use App\ResourceDelegates\Product\ItemDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ProductItem;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;

class ItemResource extends Resource
{
    const OWNER_TYPE_MANUFACTURER = 1;
    const OWNER_TYPE_COMPANY = 2;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    const PRICING_TYPE_BASIC = 1;
    const PRICING_TYPE_COMPONENT = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'productItems';
    protected $model = ProductItem::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getOwnerTypes()
    {
        return [
            static::OWNER_TYPE_MANUFACTURER, static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getOwnerTypeMap()
    {
        return [
            ProductItem::OWNER_MANUFACTURER => static::OWNER_TYPE_MANUFACTURER,
            ProductItem::OWNER_COMPANY => static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getPricingTypes()
    {
        return [
            static::PRICING_TYPE_BASIC, static::PRICING_TYPE_COMPONENT
        ];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(ItemDelegate::class);
    }

    public function isAliasInUse($owner_type, $owner_id, $alias)
    {
        $owner_type = array_search($owner_type, static::getOwnerTypeMap());

        $count = $this->newQuery()
            ->where('ownerType', $owner_type)
            ->where('ownerID', $owner_id)
            ->where('alias', $alias)
            ->count();
        return $count !== 0;
    }

    public function getIdsFromAliases($owner_type, $owner_id, array $aliases)
    {
        $owner_type = array_search($owner_type, static::getOwnerTypeMap());

        $primary_field = $this->getPrimaryField();
        $ids = [];
        $this->newQuery()
            ->where('ownerType', $owner_type)
            ->where('ownerID', $owner_id)
            ->whereIn('alias', $aliases)
            ->get([
                $primary_field->getColumn($this->getTableAlias(), true, false),
                'alias'
            ])
            ->each(function ($item) use ($primary_field, &$ids) {
                $ids[$item->alias] = $primary_field->outputValueFromModel($item);
            });
        return $ids;
    }

    public function recalculateProductPrice($id)
    {
        $product_item = $this->entity($id)->scope(Scope::make()->format('item-update-v1'))->run();

        $material_resource = $this->relationResource('materials');
        $additional_cost_resource = $this->relationResource('additional_costs');
        $product_new_total = 0;

        // loop through product item materials (materials attached directly to the product)
        foreach ($product_item['materials'] as $product_item_material) {
            // set the material total to the total of the current product material total
            $product_item_material_total = (float) $product_item_material['total'];

            // raw material details (this is the item that was just updated and triggered the price update)
            $raw_material = $product_item_material['material'];

            // if raw material unit price is not equal to the product item material stored unit price then update it
            if ($raw_material['unit_price'] !== $product_item_material['unit_price']) {
                $markup = $raw_material['markup'];
                $cost = (float) $raw_material['cost'];
                if ($markup === '0.0000') {
                    $markup = null;
                }
                $material_unit_price = $cost;
                if ($markup !== null) {
                    $markup = (float) $markup;
                    $material_unit_price = ($cost * ($markup / 100)) + $cost;
                }
                $product_item_material_total = ((float) $product_item_material['quantity']) * $material_unit_price;

                // update the product item material with the new unit price and new total
                $material_resource->partialUpdate(Entity::make([
                    'id' => $product_item_material['id'],
                    'unit_price' => (string) $material_unit_price,
                    'total' => (string) $product_item_material_total
                ]))->run();
            }
            // add new product item material total to product's new total
            $product_new_total = $product_new_total + $product_item_material_total;
        }


        // loop through product item additional costs (additional costs attached directly to the product)
        foreach ($product_item['additional_costs'] as $product_item_additional_cost) {
            // set the additional cost total to the total of the current product additional cost total
            $product_item_additional_cost_total = (float) $product_item_additional_cost['total'];

            // raw additional cost details (this is the item that was just updated and triggered the price update)
            $raw_additional_cost = $product_item_additional_cost['additional_cost'];

            // if raw additional cost unit price is not equal to the product item additional cost stored unit price then update it
            if ($raw_additional_cost['unit_price'] !== $product_item_additional_cost['unit_price']) {
                $markup = $raw_additional_cost['markup'];
                $cost = (float) $raw_additional_cost['cost'];
                if ($markup === '0.0000') {
                    $markup = null;
                }
                $additional_cost_unit_price = $cost;
                if ($markup !== null) {
                    $markup = (float) $markup;
                    $additional_cost_unit_price = ($cost * ($markup / 100)) + $cost;
                }
                $product_item_additional_cost_total = ((float) $product_item_additional_cost['quantity']) * $additional_cost_unit_price;

                // update the product item additional cost with the new unit price and new total
                $additional_cost_resource->partialUpdate(Entity::make([
                   'id' => $product_item_additional_cost['id'],
                   'unit_price' => (string) $additional_cost_unit_price,
                   'total' => (string) $product_item_additional_cost_total
               ]))->run();
            }
            // add new product item additional cost total to product's new total
            $product_new_total = $product_new_total + $product_item_additional_cost_total;
        }

        // loop through prices to apply new calculated component total
        $price_resource = $this->relationResource('prices');
        foreach ($product_item['prices'] as $price) {
            if ($price['min_count'] === '0.0000' && $price['price'] == (string) $product_new_total) {
                break;
            }
            // if price item has an adjustment, then calculate and update product_new_total
            if ($price['adjustment'] !== null && $price['adjustment'] !== '0.0000') {
                $product_new_total = $product_new_total - ($product_new_total * ($price['adjustment'] / 100));
            }
            $price_resource->partialUpdate(Entity::make([
                'id' => $price['id'],
                'price' => (string) $product_new_total,
                'status' => static::STATUS_ACTIVE
            ]))->run();
        }
    }
}
