<?php

declare(strict_types=1);

namespace App\Resources\Financing;

use App\ResourceDelegates\Financing\WisetackTransactionDelegate;
use Common\Models\WisetackTransaction;
use App\Traits\Resource\{UserActionTrackingTrait};
use Core\Components\Resource\Classes\Resource;

/**
 * Class TaskResource
 *
 * @package App\Resources
 */
class WisetackTransactionResource extends Resource
{
    const STATUS_PENDING = 1;
    const STATUS_INITIATED = 2;
    const STATUS_ACTIONS_REQUIRED = 3;
    const STATUS_AUTHORIZED = 4;
    const STATUS_LOAN_TERMS_ACCEPTED = 5;
    const STATUS_LOAN_CONFIRMED = 6;
    const STATUS_SETTLED = 7;
    const STATUS_DECLINED = 8;
    const STATUS_EXPIRED = 9;
    const STATUS_CANCELED = 10;
    const STATUS_REFUNDED = 11;

    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_READ_ONLY_FULL | self::ACTION_GROUP_DELETE;

    /**
     * @var string Table name
     */
    protected $table = 'wisetackTransactions';

    /**
     * @var string Model class name
     */
    protected $model = WisetackTransaction::class;

    protected $generate_id = true;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Get available statuses
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            static::STATUS_PENDING,
            static::STATUS_INITIATED,
            static::STATUS_ACTIONS_REQUIRED,
            static::STATUS_AUTHORIZED,
            static::STATUS_LOAN_TERMS_ACCEPTED,
            static::STATUS_LOAN_CONFIRMED,
            static::STATUS_SETTLED,
            static::STATUS_DECLINED,
            static::STATUS_EXPIRED,
            static::STATUS_CANCELED,
            static::STATUS_REFUNDED

        ];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(WisetackTransactionDelegate::class);
    }

}
