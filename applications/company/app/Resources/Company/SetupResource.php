<?php

declare(strict_types=1);

namespace App\Resources\Company;

use App\ResourceDelegates\Company\SetupDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\CompanySetup;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, ImmutableRelationException, RequestFailedException};
use Core\Components\Validation\Classes\FieldConfig;
use Exception;

/**
 * Class SetupResource
 *
 * @package App\Resources\Company
 */
class SetupResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_DELETE);

    /**
     * @var string Table name
     */
    protected $table = 'companySetup';

    /**
     * @var string Model class name
     */
    protected $model = CompanySetup::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(SetupDelegate::class);
    }
}
