<?php

namespace App\Resources\Company\Form;

use App\ResourceDelegates\Company\Form\CategoryItemDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyFormCategoryItem;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Interfaces\SyncInterface;
use Core\Components\Resource\Traits\SyncTrait;

class CategoryItemResource extends Resource implements SyncInterface
{
    use SyncTrait {
        sync as baseSync;
    }
    use UserActionTrackingTrait;

    // enable all but searching since there are no text fields to search
    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SYNC) & ~(self::ACTION_SEARCH);

    protected $table = 'companyFormCategoriesItems';
    protected $model = CompanyFormCategoryItem::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(CategoryItemDelegate::class);
    }

    public function syncCategories(Entity $entity)
    {
        return $this->baseSync($entity)->scopeFields('item_id')->syncedField('category_id');
    }

    public function syncItems(Entity $entity)
    {
        return $this->baseSync($entity)->scopeFields('category_id')->syncedField('item_id');
    }

    public function deleteByCategoryID($category_id)
    {
        $category_id = $this->getFields()->get('category_id')->saveValue($category_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where('companyFormCategoriesItems.companyFormCategoryID', $category_id)
            ->each(function ($category_item) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category_item)
                ]))->run();
            });
    }

    public function deleteByItemID($item_id)
    {
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where('companyFormCategoriesItems.companyFormItemID', $item_id)
            ->each(function ($category_item) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category_item)
                ]))->run();
            });
    }
}
