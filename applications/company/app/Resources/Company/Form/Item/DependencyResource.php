<?php

declare(strict_types=1);

namespace App\Resources\Company\Form\Item;

use App\Interfaces\Resource\TypeDependencyResourceInterface;
use App\ResourceDelegates\Company\Form\Item\DependencyDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyFormItemDependency;
use Core\Components\Resource\Classes\Resource;

/**
 * Class DependencyResource
 *
 * @package App\Resources\Company\Form\Item
 */
class DependencyResource extends Resource implements TypeDependencyResourceInterface
{
    use UserActionTrackingTrait;

    public const TYPE_PRODUCT_ITEM = 1;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = (self::ACTION_GROUP_READ_ONLY | self::ACTION_GROUP_CREATE | self::ACTION_NESTED_UPDATE) & ~self::ACTION_GROUP_BATCH;

    /**
     * @var string Table name
     */
    protected $table = 'companyFormItemDependencies';

    /**
     * @var string Model associated with resource
     */
    protected $model = CompanyFormItemDependency::class;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Get available types
     *
     * @return int[]
     */
    public static function getTypes()
    {
        return [static::TYPE_PRODUCT_ITEM];
    }

    /**
     * Get mapping of model type to resource type
     *
     * @return array
     */
    public static function getTypeMap(): array
    {
        return [
            CompanyFormItemDependency::TYPE_PRODUCT_ITEM => static::TYPE_PRODUCT_ITEM
        ];
    }

    /**
     * Boot resource
     */
    protected static function boot()
    {
        static::delegate(DependencyDelegate::class);
    }
}
