<?php

declare(strict_types=1);

namespace App\Resources\Company\Form\Item;

use App\Interfaces\Resource\FormFieldResourceInterface;
use App\ResourceDelegates\Company\Form\Item\FieldDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyFormItemField;
use Core\Components\Resource\Classes\Resource;

/**
 * Class FieldResource
 *
 * @package App\Resources\Company\Form\Item
 */
class FieldResource extends Resource implements FormFieldResourceInterface
{
    use UserActionTrackingTrait;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = (self::ACTION_GROUP_READ_ONLY | self::ACTION_GROUP_CREATE | self::ACTION_NESTED_UPDATE) & ~self::ACTION_GROUP_BATCH;

    /**
     * @var string Table name
     */
    protected $table = 'companyFormItemFields';

    /**
     * @var string Model associated with resource
     */
    protected $model = CompanyFormItemField::class;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Get available types
     *
     * @return array
     */
    public static function getTypes()
    {
        return [
            static::TYPE_TEXT, static::TYPE_TEXTAREA, static::TYPE_SELECT, static::TYPE_RADIO, static::TYPE_CHECKBOX,
            static::TYPE_FILE, static::TYPE_PRODUCT_LIST
        ];
    }

    /**
     * Boot resource
     */
    protected static function boot()
    {
        static::delegate(FieldDelegate::class);
    }
}
