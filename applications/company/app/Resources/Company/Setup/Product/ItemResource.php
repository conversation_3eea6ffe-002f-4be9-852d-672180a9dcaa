<?php

declare(strict_types=1);

namespace App\Resources\Company\Setup\Product;

use App\ResourceDelegates\Company\Setup\Product\ItemDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\CompanySetupProductItem;
use Core\Components\Resource\Classes\Resource;

/**
 * Class ItemResource
 *
 * @package App\Resources\Company\Setup\Product
 */
class ItemResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_DELETE);

    /**
     * @var string Table name
     */
    protected $table = 'companySetupProductItems';

    /**
     * @var string Model class name
     */
    protected $model = CompanySetupProductItem::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(ItemDelegate::class);
    }
}
