<?php

declare(strict_types=1);

namespace App\Resources\System\Form;

use App\ResourceDelegates\System\Form\CategoryItemDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\SystemFormCategoryItem;
use Core\Components\Resource\Requests\SyncRequest;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Interfaces\SyncInterface;
use Core\Components\Resource\Traits\SyncTrait;

/**
 * Class CategoryItemResource
 *
 * @package App\Resources\System\Form
 */
class CategoryItemResource extends Resource implements SyncInterface
{
    use SyncTrait {
        sync as baseSync;
    }
    use UserActionTrackingTrait;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SYNC) & ~(self::ACTION_SEARCH);

    /**
     * @var string Table name
     */
    protected $table = 'systemFormCategoriesItems';

    /**
     * @var string Model associated with resource
     */
    protected $model = SystemFormCategoryItem::class;

    /**
     * @var bool Determines if id is automatically generated
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Boot resource
     */
    protected static function boot()
    {
        static::delegate(CategoryItemDelegate::class);
    }

    /**
     * Sync categories list in entity with current persisted list
     *
     * @param Entity $entity
     * @return SyncRequest
     */
    public function syncCategories(Entity $entity): SyncRequest
    {
        return $this->baseSync($entity)->scopeFields('item_id')->syncedField('category_id');
    }

    /**
     * Sync items list in entity with current persisted list
     *
     * @param Entity $entity
     * @return SyncRequest
     */
    public function syncItems(Entity $entity): SyncRequest
    {
        return $this->baseSync($entity)->scopeFields('category_id')->syncedField('item_id');
    }

    /**
     * Delete all by category id
     *
     * @param string $category_id UUID
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteByCategoryID(string $category_id): void
    {
        $category_id = $this->getFields()->get('category_id')->saveValue($category_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where('systemFormCategoriesItems.systemFormCategoryID', $category_id)
            ->each(function ($category_item) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category_item)
                ]))->run();
            });
    }

    /**
     * Delete all by item id
     *
     * @param string $item_id UUID
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteByItemID(string $item_id): void
    {
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where('systemFormCategoriesItems.systemFormItemID', $item_id)
            ->each(function ($category_item) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category_item)
                ]))->run();
            });
    }
}
