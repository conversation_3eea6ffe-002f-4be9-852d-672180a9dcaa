<?php

namespace App\Interfaces\Resource;

use Core\Components\Resource\Classes\MediaTypeVersion;

/**
 * Interface FileHandlerResourceInterface
 *
 * @package App\Interfaces\Resource
 */
interface FileHandlerResourceInterface
{
    /**
     * Get MediaTypeVersion instance from app file type
     *
     * @param int $type
     * @return MediaTypeVersion
     */
    public function getMediaTypeVersionByType(int $type): MediaTypeVersion;
}
