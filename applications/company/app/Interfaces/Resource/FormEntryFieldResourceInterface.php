<?php

namespace App\Interfaces\Resource;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\UpdateOrCreateRequest;

/**
 * Interface FormEntryFieldResourceInterface
 *
 * @package App\Interfaces\Resource
 */
interface FormEntryFieldResourceInterface
{
    public const FIELD_SOURCE_GROUP = 1;
    public const FIELD_SOURCE_COMPANY = 2;

    /**
     * Get list of field sources
     *
     * @return array
     */
    public static function getFieldSources(): array;

    /**
     * Get mapping of model source id to resource source id
     *
     * @return array
     */
    public static function getFieldSourceMap(): array;

    /**
     * Get new update or create request for specified entity
     *
     * @param Entity $entity
     * @return UpdateOrCreateRequest
     */
    public function updateOrCreate(Entity $entity): UpdateOrCreateRequest;
}
