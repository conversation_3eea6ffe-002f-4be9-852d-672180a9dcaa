<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Drawing\Image;

use App\Classes\Func;
use App\ResourceMediaHandlers\BaseCompanyFileVariantHandler;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Classes\Entity;
use App\Resources\File\VariantResource;
use App\Traits\ResourceMedia\ImageTrait;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\StaticAccessors\Response;

/**
 * Class EvaluationThumbnailHandler
 *
 * @package App\ResourceMediaHandlers\Drawing\Image
 */
class EvaluationThumbnailHandler extends BaseCompanyFileVariantHandler
{
    use ImageTrait;

    /**
     * Generate thumbnail image from variant entity
     *
     * @param Entity $entity
     * @return array
     * @throws \Core\Exceptions\AppException
     */
    public function generate(Entity $entity): array
    {
        try {
            $manager = $this->newImageManager();
            $temp_file = Func::createTempFile(null, false);
            $image = $manager->make($this->getOriginalPathFromEntity($entity))
                ->fit(600, 448)
                ->save($temp_file);
            $extension = Http::getExtensionByMimeType($image->mime());
            $data = $this->saveFileVariant($entity, "thumbnail.{$extension}", $temp_file);

            $image->destroy();
            return $data;
        } finally {
            if (isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }
        }
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $drawing = $this->resource->findOrFail($id);

        $data = $this->getVariantFromField(VariantResource::TYPE_DRAWING_EVALUATION_THUMBNAIL, 'image_file_id', $drawing);

        $response = Response::file($data['path']);
        $response->contentType($data['content_type']);
        $response->filename($data['filename']);
        return $response;
    }
}
