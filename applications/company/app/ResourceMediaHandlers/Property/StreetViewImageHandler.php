<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Property;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use Core\Components\Http\Responses\FileResponse;
use App\Classes\{Func, Log};
use App\Services\GoogleStaticImage\Exceptions\{LookupFailedException, NotFoundException};
use App\Services\GoogleStaticImageService;
use Core\Components\Http\StaticAccessors\Response;
use Core\StaticAccessors\Path;
use Throwable;

/**
 * Class StreetViewImageHandler
 *
 * @package App\ResourceMediaHandlers\Property
 */
class StreetViewImageHandler extends BaseCompanyFileHandler
{
    /**
     * Get response for street view image based on property found by passed id
     *
     * If street view image cannot be found, we fall back to static map image. If all images are not available,
     * we just return a generic 'no image available' one.
     *
     * @param int $id
     * @param array $config
     * @return FileResponse
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $property = $this->resource->findOrFail($id);

        try {
            $image_service = new GoogleStaticImageService();
            $location = $property->address;
            if (($address_2 = Func::emptyToNull($property->address2)) !== null) {
                $location .= " {$address_2}";
            }
            $location .= " {$property->city}, {$property->state} {$property->zip}";
            try {
                $path = $image_service->getStreetViewImage([
                    'location' => $location,
                    'width' => 640,
                    'height' => 320
                ]);
            } catch (LookupFailedException | NotFoundException) {
                // if street view image cannot be found, then we look for map image
                $path = $image_service->getMapImage([
                    'markers' => [
                        'styles' => [
                            'color' => 'red'
                        ],
                        'locations' => [$location]
                    ],
                    'width' => 640,
                    'height' => 320,
                    'scale' => 2
                ]);
            }
        } catch (Throwable $e) {
            // if any error occurs during lookup, we just log it and return a default image to keep PDF generation from
            // failing
            $path = Path::resource('assets/images/property-street-view/no_image_available.png');
            Log::create('street_view_image_handler')->error('Unable to get street view image', [
                'exception' => $e
            ]);
        }
        return Response::file($path);
    }
}
