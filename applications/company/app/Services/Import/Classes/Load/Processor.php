<?php

declare(strict_types=1);

namespace App\Services\Import\Classes\Load;

use App\Services\Import\Classes\{BaseResource, Load};
use App\Services\Import\Exceptions\{ImportException, RelationNotFoundException, RelationNotImportedException};
use Carbon\Carbon;
use Generator;
use Illuminate\Database\{Connection, Query\Builder};
use Ramsey\Uuid\{Uuid, UuidInterface};
use Throwable;

/**
 * Class Processor
 *
 * @package App\Services\Import\Classes\Load
 */
class Processor
{
    /**
     * @var UuidInterface Result id
     */
    protected UuidInterface $id;

    /**
     * @var Connection App database connection
     */
    protected Connection $app_db;

    /**
     * @var Connection Import database connection
     */
    protected Connection $import_db;

    /**
     * @var BaseResource Resource instance
     */
    protected BaseResource $resource;

    /**
     * @var string Import table cache for quick access
     */
    protected string $import_table;

    /**
     * @var string Binary import id for quick access
     */
    protected string $import_id;

    /**
     * @var array Company data cache for quick access
     */
    protected array $company;

    /**
     * @var int[] Result counts
     */
    protected array $counts = [
        'inserted' => 0,
        'insert_skipped' => 0,
        'updated' => 0,
        'update_skipped' => 0,
        'errored' => 0
    ];

    /**
     * Processor constructor
     *
     * @param Load $load
     * @param int $resource_type
     * @throws ImportException
     */
    public function __construct(protected Load $load, protected int $resource_type)
    {
        $this->id = Uuid::uuid4();

        $service = $load->getService();
        $this->app_db = $service->getAppDb();
        $this->import_db = $service->getImportDb();

        // cache import id and company data for easy use by resources
        $this->import_id = $service->getID()->getBytes();
        $this->company = $service->getCompany();

        $this->resource = BaseResource::getInstanceByType($resource_type);

        $this->import_table = $this->resource->getImportTable();
    }

    /**
     * Get load instance
     *
     * @return Load
     */
    public function getLoad(): Load
    {
        return $this->load;
    }

    /**
     * Get binary import id (cached from service for speed)
     *
     * @return string
     */
    public function getImportID(): string
    {
        return $this->import_id;
    }

    /**
     * Get company data (cached from service for speed)
     *
     * @return array
     */
    public function getCompany(): array
    {
        return $this->company;
    }

    /**
     * Determine if row should be inserted or updated by on type configuration
     *
     * @param object $row
     * @throws ImportException
     */
    protected function insertOrUpdate(object $row): void
    {
        if (($existing_row = $this->resource->find($row, $this)) === null) {
            if ($this->load->allowInsert()) {
                $import = $this->resource->create($row, $this);
                $this->counts['inserted']++;
            } else {
                $this->counts['insert_skipped']++;
            }
        } elseif ($this->load->allowUpdate()) {
            $import = $this->resource->update($row, $existing_row, $this);
            $this->counts['updated']++;
        } else {
            $this->counts['update_skipped']++;
        }
        if (isset($import) && is_array($import) && count($import) > 0) {
            $changed = false;
            foreach ($import as $key => $value) {
                if ($row->{$key} === $value) {
                    continue;
                }
                $changed = true;
                break;
            }
            if ($changed) {
                $this->import_db->table($this->import_table)->where('id', $row->id)->update($import);
            }
        }
    }

    /**
     * Fetch data from database using pagination scheme
     *
     * Result is broken up into multiple queries to prevent buffering from filling memory. This method will return a
     * continuous result set as if we were using one big query.
     *
     * @param int $total
     * @param int $per_page
     * @param Builder $query
     * @return Generator
     */
    protected function fetchData(int $total, int $per_page, Builder $query): Generator
    {
        $pages = (int) ceil($total / $per_page);
        for ($page = 1; $page <= $pages; $page++) {
            yield from (clone $query)->forPage($page, $per_page)->cursor();
        }
    }

    /**
     * Divide query results into chunks for use with eager loading
     *
     * @param Generator $data
     * @param int $chunk_size
     * @return Generator
     */
    protected function getChunks(Generator $data, int $chunk_size): Generator
    {
        $i = 0;
        $rows = $ids = [];
        while ($data->valid()) {
            $i++;
            $row = $data->current();
            if (isset($row->id_fx)) {
                $ids[] = $row->id_fx;
            }
            $rows[] = $row;
            $data->next();
            if ($i === $chunk_size || !$data->valid()) {
                yield ['ids' => $ids, 'rows' => $rows];
                $rows = $ids = [];
                $i = 0;
            }
        }
    }

    /**
     * Process all rows for resource
     *
     * @throws Throwable
     */
    public function run(): void
    {
        $logger = $this->load->getLogger();
        $logger->info('Processing resource: ' . $this->resource->getName());

        $query = $this->import_db->table($this->import_table)
            ->select("{$this->import_table}.*");
        $this->resource->chunkQuery($query, $this);
        $query->where("{$this->import_table}.import_id", $this->import_id);

        $total = $query->getCountForPagination();
        if ($total === 0) {
            $logger->info('No rows to process');
            return;
        }
        $chunk_size = $this->resource->getChunkSize();
        $chunks = (int) ceil($total / $chunk_size);

        $logger->info("Processing {$total} row(s) in {$chunks} chunk(s)", ['chunk_size' => $chunk_size]);

        $start_time = microtime(true);
        $c = 0;
        $data = $this->fetchData($total, 1000, $query);
        foreach ($this->getChunks($data, $chunk_size) as ['ids' => $ids, 'rows' => $rows]) {
            $c++;
            $this->resource->load($ids, $this);
            foreach ($rows as $row) {
                try {
                    $this->import_db->beginTransaction();
                    $this->app_db->beginTransaction();
                    $this->insertOrUpdate($row);
                    $this->import_db->commit();
                    $this->app_db->commit();
                } catch (Throwable $e) {
                    $this->import_db->rollBack();
                    $this->app_db->rollBack();
                    if ($e instanceof RelationNotFoundException || $e instanceof RelationNotImportedException) {
                        $logger->error($e->getMessage());
                    } else {
                        $logger->error('Unable to process row', [
                            'row' => (array) $row,
                            'exception' => $e
                        ]);
                    }
                    $this->counts['errored']++;
                }
            }
            $logger->info("Processed chunk {$c}", ['memory' => round(memory_get_usage() / 1000000, 2) . 'MB']);
            gc_collect_cycles();
        }

        $now = Carbon::now('UTC');
        $this->import_db->table('import_load_results')
            ->insert([
                'id' => $this->id->getBytes(),
                'import_load_id' => $this->load->getID()->getBytes(),
                'resource_type' => $this->resource_type,
                'insert_count' => $this->counts['inserted'],
                'insert_skip_count' => $this->counts['insert_skipped'],
                'update_count' => $this->counts['updated'],
                'update_skip_count' => $this->counts['update_skipped'],
                'error_count' => $this->counts['errored'],
                'time' => (int) round(((microtime(true) - $start_time) * 1000000) / 1000),
                'created_at' => $now,
                'updated_at' => $now
            ]);
    }

    /**
     * Get result counts
     *
     * @return int[]
     */
    public function getCounts(): array
    {
        return $this->counts;
    }
}
