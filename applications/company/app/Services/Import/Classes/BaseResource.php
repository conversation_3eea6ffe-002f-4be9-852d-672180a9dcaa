<?php

declare(strict_types=1);

namespace App\Services\Import\Classes;

use App\Services\Import\Classes\Load\Processor;
use App\Services\Import\Resources;
use App\Services\Import\Exceptions\ImportException;
use Core\Components\Validation\Classes\{FieldConfig, Rules};

/**
 * Class Resource
 *
 * @package App\Services\Import\Classes
 */
class BaseResource
{
    public const TYPE_USER = 1;
    public const TYPE_PRODUCT_ITEM = 2;
    public const TYPE_CUSTOMER = 3;
    public const TYPE_PROPERTY = 4;
    public const TYPE_PROJECT = 5;
    public const TYPE_PROJECT_EVENT = 6;
    public const TYPE_PROJECT_NOTE = 7;
    public const TYPE_PROJECT_CONTACT = 8;
    public const TYPE_MATERIAL = 9;
    public const TYPE_ADDITIONAL_COST = 10;

    /**
     * @var array<int, string> List of available resources
     */
    protected static array $resources = [
        self::TYPE_USER => Resources\UserResource::class,
        self::TYPE_PRODUCT_ITEM => Resources\ProductResource::class,
        self::TYPE_CUSTOMER => Resources\CustomerResource::class,
        self::TYPE_PROPERTY => Resources\PropertyResource::class,
        self::TYPE_PROJECT => Resources\ProjectResource::class,
        self::TYPE_PROJECT_EVENT => Resources\Project\AppointmentResource::class,
        self::TYPE_PROJECT_NOTE => Resources\Project\NoteResource::class,
        self::TYPE_PROJECT_CONTACT => Resources\Project\ContactResource::class,
        self::TYPE_MATERIAL => Resources\MaterialResource::class,
        self::TYPE_ADDITIONAL_COST => Resources\AdditionalCostResource::class
    ];

    /**
     * @var array<int, BaseResource> Resource instance cache
     */
    protected static array $instance_cache = [];

    /**
     * @var string Display name of resource
     */
    protected string $name;

    /**
     * @var string App table name
     */
    protected string $table;

    /**
     * @var string Import table name
     */
    protected string $import_table;

    /**
     * @var string Name of file in import zip archive
     */
    protected string $csv_file_name;

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 100;

    /**
     * @var array Eager loaded data cache
     */
    protected array $loaded = [];

    /**
     * Get available resource types
     *
     * @return array<int, string>
     */
    public static function getAvailableTypes(): array
    {
        return self::$resources;
    }

    /**
     * Get resource instance from resource class name
     *
     * @param string $class
     * @return BaseResource
     */
    public static function getInstance(string $class): BaseResource
    {
        if (!isset(self::$instance_cache[$class])) {
            self::$instance_cache[$class] = new $class();
        }
        return self::$instance_cache[$class];
    }

    /**
     * Get resource instance by type
     *
     * @param int $type
     * @return BaseResource
     * @throws ImportException
     */
    public static function getInstanceByType(int $type): BaseResource
    {
        if (!isset(self::$resources[$type])) {
            throw new ImportException('Unable to find resource type: %d', $type);
        }
        return self::getInstance(self::$resources[$type]);
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Get app table name
     *
     * @return string
     */
    public function getTable(): string
    {
        return $this->table;
    }

    /**
     * Get import table name
     *
     * @return string
     */
    public function getImportTable(): string
    {
        return $this->import_table;
    }

    /**
     * Get chunk size
     *
     * @return int
     */
    public function getChunkSize(): int
    {
        return $this->chunk_size;
    }

    /**
     * Get column config
     *
     * @return array
     */
    public function getColumns(): array
    {
        return [];
    }

    /**
     * Get CSV file name
     *
     * @return string
     */
    public function getCsvFileName(): string
    {
        return $this->csv_file_name;
    }

    /**
     * Get Rules instance for CSV line validation
     *
     * Allows for adding custom rules for this resource
     *
     * @param Rules $rules
     * @return Rules
     */
    public function getCsvValidationRules(Rules $rules): Rules
    {
        return $rules;
    }

    /**
     * Get FieldConfig instance for CSV line validation
     *
     * Allows for storing addition validation data for this resource
     *
     * @return FieldConfig
     */
    public function getCsvValidationFieldConfig(): FieldConfig
    {
        return FieldConfig::fromArray($this->getColumns());
    }

    /**
     * Validate row after validation library has run
     *
     * If any errors are found, an array of error strings needs to be returned. Otherwise null signifies it passes
     * validation. Row is passed by reference so data can be manipulated if necessary.
     *
     * @param array $row
     * @return string[]|null
     */
    public function csvRowPostValidation(array &$row): ?array
    {
        return null;
    }

    /**
     * Modify chunk query used when loading in data from import tables
     *
     * @param object $query
     * @param Processor $processor
     * @return object
     */
    public function chunkQuery(object $query, Processor $processor): object
    {
        return $query;
    }

    /**
     * Cache eager loaded data based on chunk so it can be used for lookups in the find() method
     *
     * Data should be keyed by an identifier of some sort, usually id_fx
     *
     * @param array $data
     */
    protected function setEagerLoadedData(array $data): void
    {
        $this->loaded = $data;
    }

    /**
     * Eager load data from FA software if it has already been imported once and an id has been assigned in the import
     * record
     *
     * @param array $ids
     * @param Processor $processor
     */
    public function load(array $ids, Processor $processor): void
    {
        //
    }

    /**
     * Search for existing record using row data
     *
     * @param object $row
     * @param Processor $processor
     * @return object|null
     */
    public function search(object $row, Processor $processor): ?object
    {
        return null;
    }

    /**
     * Find existing record by either searching or looking up in eager loaded data
     *
     * @param object $row
     * @param Processor $processor
     * @return object|null
     * @throws ImportException
     */
    public function find(object $row, Processor $processor): ?object
    {
        if ($row->id_fx === null) {
            return $processor->getLoad()->allowSearch() ? $this->search($row, $processor) : null;
        }
        if (!isset($this->loaded[$row->id_fx])) {
            throw new ImportException('Already imported row not in eager loaded data');
        }
        return $this->loaded[$row->id_fx];
    }

    /**
     * Create record in app database
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     */
    public function create(object $row, Processor $processor): array
    {
        return [];
    }

    /**
     * Update existing record in app database
     *
     * @param object $row
     * @param object $existing_row
     * @param Processor $processor
     * @return array
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        return [];
    }
}
