<?php

declare(strict_types=1);

namespace App\Services\Quickbooks\Classes;

use App\Services\Quickbooks\Exceptions\QueryException;
use App\Services\QuickbooksService;
use QuickBooksOnline\API\Data\IPPIntuitEntity;

/**
 * Class Query
 *
 * @package App\Services\Quickbooks\Classes
 */
class Query
{
    /**
     * @var QuickbooksService
     */
    protected $service;

    /**
     * @var string
     */
    protected $entity;

    /**
     * @var array list of where clauses
     */
    protected $where = [];

    /**
     * @var array list of sorts
     */
    protected $order_by = [];

    /**
     * @var int|null
     */
    protected $offset = null;

    /**
     * @var int|null
     */
    protected $limit = null;

    /**
     * @var bool determines if count query is run
     */
    protected $count = false;

    /**
     * Query constructor
     *
     * @param QuickbooksService $service
     * @param string $entity
     */
    public function __construct(QuickbooksService $service, string $entity)
    {
        $this->service = $service;
        $this->entity($entity);
    }

    /**
     * Set entity name
     *
     * @param string $name
     * @return $this
     */
    public function entity(string $name): Query
    {
        $this->entity = $name;
        return $this;
    }

    /**
     * Add where clause
     *
     * Only AND clauses are allowed by QB API so we don't have to worry about any of that complexity.
     *
     * If only one additional argument is passed, we assume the operator is =. Otherwise, two arguments will need to be
     * passed. The first being an operator and second being a value.
     *
     * @param string $attribute
     * @param mixed ...$args
     * @return $this
     */
    public function where(string $attribute, ...$args): Query
    {
        $operator = '=';
        $count = count($args);
        if ($count === 1) {
            [$value] = $args;
        } elseif ($count === 2) {
            [$operator, $value] = $args;
        }
        $this->where[] = compact('attribute', 'operator', 'value');
        return $this;
    }

    /**
     * Add IN() where clause
     *
     * @param string $attribute
     * @param array $values
     * @return $this
     */
    public function whereIn(string $attribute, array $values): Query
    {
        return $this->where($attribute, 'in', $values);
    }

    /**
     * Add LIKE where clause
     *
     * @param string $attribute
     * @param string $value
     * @return $this
     */
    public function whereLike(string $attribute, string $value): Query
    {
        return $this->where($attribute, 'like', $value);
    }

    /**
     * Add sort for attribute
     *
     * @param $attribute
     * @param string $direction asc or desc
     * @return Query
     */
    public function orderBy($attribute, $direction = 'ASC'): Query
    {
        $this->order_by[$attribute] = $direction;
        return $this;
    }

    /**
     * Set start position (offset)
     *
     * If null is passed, the start position is removed.
     *
     * @param int|null $offset
     * @return $this
     */
    public function offset(?int $offset): Query
    {
        $this->offset = $offset;
        return $this;
    }

    /**
     * Set max results (limit)
     *
     * If null is passed, the max results limitation is removed.
     *
     * @param int|null $limit
     * @return $this
     */
    public function limit(?int $limit): Query
    {
        $this->limit = $limit;
        return $this;
    }

    /**
     * Set pagination config for query
     *
     * @param int $offset
     * @param int $limit
     * @return $this
     */
    public function paginate(int $offset = 1, int $limit = 100): Query
    {
        $this->offset($offset);
        $this->limit($limit);
        return $this;
    }

    /**
     * Get QB SQL ready value
     *
     * @param mixed $value
     * @return string
     */
    protected function getFilterValue($value): string
    {
        if ($value === null) {
            $value = "' '";
        } elseif (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        } else {
            $value = '\'' . str_replace('\'', '\\\'', $value) . '\'';
        }
        return $value;
    }

    /**
     * Build SQL query to send to QB
     *
     * @return string
     * @throws QueryException
     */
    protected function build(): string
    {
        $query = 'SELECT ';
        if ($this->count) {
            $query .= 'COUNT(*)';
        } else {
            $query .= '*';
        }
        $query .= " FROM {$this->entity}";
        if (count($this->where) > 0) {
            $query .= ' WHERE ';
            $first = true;
            foreach ($this->where as $where) {
                $query .= ($first ? '' : ' AND ') . "{$where['attribute']} ";
                if ($first) {
                    $first = false;
                }
                if ($where['operator'] === 'in') {
                    if (!is_array($where['value'])) {
                        throw new QueryException('Where clause with IN operator must have array value');
                    }
                    $in = array_map([$this, 'getFilterValue'], $where['value']);
                    $query .= 'IN (' . implode(',', $in) . ')';
                } elseif ($where['operator'] === 'like') {
                    $query .= 'LIKE ' . $this->getFilterValue($where['value']);
                } else {
                    $query .= "{$where['operator']} {$this->getFilterValue($where['value'])}";
                }
            }
        }
        if (count($this->order_by) > 0) {
            $query .= ' ORDERBY ';
            $first = true;
            foreach ($this->order_by as $attribute => $direction) {
                $query .= ($first ? '' : ', ') . "{$attribute} {$direction}";
            }
        }
        if ($this->offset !== null) {
            $query .= " STARTPOSITION {$this->offset}";
        }
        if ($this->limit !== null) {
            $query .= " MAXRESULTS {$this->limit}";
        }
        return $query;
    }

    /**
     * Run query
     *
     * @return array
     * @throws QueryException
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function run(): array
    {
        return $this->service->query($this->build());
    }

    /**
     * Get first result of query
     *
     * @return IPPIntuitEntity|null
     * @throws QueryException
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function first(): ?IPPIntuitEntity
    {
        return $this->service->first($this->build());
    }

    /**
     * Get count
     *
     * @return int
     * @throws QueryException
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function count(): int
    {
        $this->count = true;
        return $this->service->query($this->build());
    }
}
