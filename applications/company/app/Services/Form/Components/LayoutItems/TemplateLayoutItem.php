<?php

declare(strict_types=1);

namespace App\Services\Form\Components\LayoutItems;

use App\Resources\Form\Item\Group\{Layout\ItemResource, LayoutResource, TemplateResource};
use App\Services\Form\Classes\Structure\Group\Layout\Item;
use App\Services\Form\Exceptions\{FormException, ValidationException};
use Core\Components\Resource\Interfaces\AclInterface;

/**
 * Class TemplateLayoutItem
 *
 * @package App\Services\Form\Components\LayoutItems
 */
class TemplateLayoutItem extends Item
{
    /**
     * @var int Layout item type
     */
    protected int $item_type = ItemResource::TYPE_TEMPLATE;

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'template' => ['string', 'setAlias']
        ], $data);
    }

    /**
     * Shorthand for setting type to template and assigning an id
     *
     * @param string $id
     * @return $this
     */
    public function setTemplateID(string $id): self
    {
        return $this->setItemID($id);
    }

    /**
     * Prepare item for saving
     *
     * If alias is defined, we try to find the associated template and assign its id to this layout item's item id
     * for saving.
     */
    public function prepare(): void
    {
        parent::prepare();
        if (($alias = $this->getAlias()) !== null && ($template = $this->layout->getGroup()->getTemplate($alias)) !== null) {
            $this->setTemplateID($template->getID());
        }
    }

    /**
     * Validate item before saving
     *
     * Verify item id points to existing template. Since server templates are never sent to the client side library, we
     * ensure they are not assigned to input templates.
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();

        if (($item_id = $this->getItemID()) === null) {
            throw new ValidationException('Valid template id is required [%s]', $this->getLocation());
        }
        if (($template = $this->layout->getGroup()->getTemplate($item_id)) === null) {
            throw new ValidationException('Unable to find template with id: %s [%s]', $item_id, $this->getLocation());
        }
        if (
            in_array($this->layout->getType(), [LayoutResource::TYPE_INPUT_SCREEN_LARGE, LayoutResource::TYPE_INPUT_TABLE_ROW]) &&
            $template->getType() !== TemplateResource::TYPE_CLIENT
        ) {
            throw new ValidationException('Only client templates allowed with input layouts [%s]', $this->getLocation());
        }
    }

    /**
     * Configure item to set it up for usage or output to array or client formats
     *
     * @param AclInterface $acl
     * @throws FormException
     */
    protected function configure(AclInterface $acl): void
    {
        if (($item_id = $this->getItemID()) === null) {
            throw new FormException('Template item id not defined [%s]', $this->getLocation());
        }
        if (($template = $this->layout->getGroup()->getTemplate($item_id)) === null) {
            throw new FormException('Unable to find template: %s [%s]', $item_id, $this->getLocation());
        }
        $this->setItem($template);
        if (($alias = $template->getAlias()) !== null) {
            $this->setAlias($alias);
        }
    }
}
