<?php

declare(strict_types=1);

namespace App\Services\Form\Components\TypeDependencies;

use App\Resources\Product\ItemResource;
use App\Services\Form\Classes\Type\Dependency;
use App\Services\Form\Exceptions\FormException;
use Core\Components\Resource\Interfaces\AclInterface;

/**
 * Class ProductItemTypeDependency
 *
 * @package App\Services\Form\Components\TypeDependencies
 */
class ProductItemTypeDependency extends Dependency
{
    /**
     * @var string|null Alias of product item to load in
     */
    protected ?string $product_item_alias = null;

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'product_item_alias' => ['string', 'setProductItemAlias']
        ], $data);
    }

    /**
     * Set product item id
     *
     * Just an alias for setItemID().
     *
     * @param string $product_item_id
     * @return $this
     */
    public function setProductItemID(string $product_item_id): self
    {
        return $this->setItemID($product_item_id);
    }

    /**
     * Set product item alias
     *
     * If alias is used, the id will be fetched from the database during validation.
     *
     * @param string $alias
     * @return $this
     */
    public function setProductItemAlias(string $alias): self
    {
        $this->product_item_alias = $alias;
        return $this;
    }

    /**
     * Get product item alias
     *
     * @return string|null
     */
    public function getProductItemAlias(): ?string
    {
        return $this->product_item_alias;
    }

    /**
     * Prepare type dependency for saving
     *
     * If product item alias is defined, we request the item id via the product info helper.
     */
    public function prepare(): void
    {
        if (($alias = $this->getProductItemAlias()) !== null) {
            $this->type->getProductInfoHelper()->requestItem($alias);
        }
    }

    /**
     * Validate type dependency before saving
     *
     * If product item alias is defined, we grab the product item id from the product info helper and assign to the
     * class.
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws \App\Services\Form\Exceptions\ValidationException
     */
    public function validate(AclInterface $acl): void
    {
        if (($alias = $this->getProductItemAlias()) !== null) {
            if (($item_id = $this->type->getProductInfoHelper()->getItem($alias)) === null) {
                throw new FormException('Unable to find product item with alias: %s', $alias);
            }
            $this->setItemID($item_id);
        } elseif (($item_id = $this->getItemID()) !== null && !ItemResource::make($acl)->entityExists($item_id)) {
            throw new FormException('Unable to find product item with id: %s', $item_id);
        }
        parent::validate($acl);
    }

    /**
     * Export dependency to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        return $this->exportToArray([
            'product_item_alias' => 'getProductItemAlias'
        ], [
            'initial' => parent::export()
        ]);
    }
}
