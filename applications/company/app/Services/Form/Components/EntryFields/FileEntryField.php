<?php

declare(strict_types=1);

namespace App\Services\Form\Components\EntryFields;

use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Services\Form\Classes\Entry\Group\{Field, Item};

/**
 * Class FileEntryField
 *
 * @package App\Services\Form\Components\EntryFields
 */
class FileEntryField extends Field
{
    /**
     * @var Field\File[] List of defined files
     */
    protected array $files = [];

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'files' => ['list', fn($file) => Field\File::make($this, $file)]
        ], $data);
    }

    /**
     * Reset class after clone operation
     */
    protected function __clone()
    {
        $this->files = [];
    }

    /**
     * Clone field and clone all nested files
     *
     * @param Item $item
     * @return $this
     */
    public function clone(Item $item): self
    {
        $field = parent::clone($item);
        foreach ($this->files as $file) {
            $field->addFile($file->clone($field));
        }
        return $field;
    }

    /**
     * Add file
     *
     * @param Field\File $file
     */
    public function addFile(Field\File $file): void
    {
        $this->files[] = $file;
    }

    /**
     * Get all files
     *
     * @return Field\File[]
     */
    public function getFiles(): array
    {
        return $this->files;
    }

    /**
     * Persist files
     *
     * Fields are just containers for values and aren't saved directly to keep database structure cleaner.
     *
     * @param FieldFileResource $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FieldFileResource $resource): void
    {
        $files = $this->getFiles();
        if (count($files) > 0) {
            foreach ($files as $file) {
                $file->persist($resource);
            }
        }
    }

    /**
     * Setup and configure group for use after loading from external source
     *
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function setup(): void
    {
        $helper = $this->getItem()->getcontainer()->getEntry()->getFileInfoHelper();
        if (count($this->files) > 0) {
            foreach ($this->files as $file) {
                $file->setup($helper);
            }
        }
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     */
    public function toClientFormat(): array
    {
        return [
            'files' => count($this->files) > 0 ? array_map(fn($file) => $file->toClientFormat(), $this->files) : []
        ];
    }
}
