<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionSideValues;

use App\Services\Form\Classes\Structure\Group\Rule\Condition\SideValue;
use App\Services\Form\Interfaces\Structure\Group\Rule\Condition\ScalarSideValueInterface;

/**
 * Class EmptySideValue
 *
 * @package App\Services\Form\Components\RuleConditionSideValues
 */
class EmptySideValue extends SideValue implements ScalarSideValueInterface
{
    public const KEY_LEFT = 'left';
    public const KEY_RIGHT = 'value';

    /**
     * @var string|null Key for left side of conditional
     */
    protected ?string $left_key = self::KEY_LEFT;

    /**
     * @var string|null Key for right side of conditional
     */
    protected ?string $right_key = self::KEY_RIGHT;

    /**
     * Determines if side value accepts data based on the side, operator, and value
     *
     * @param string $side
     * @param string $operator
     * @param mixed $value
     * @return bool
     */
    public static function accepts(string $side, string $operator, mixed $value): bool
    {
        return in_array($operator, ['equal', 'not-equal', 'contains', 'does-not-contain']) && $value === null;
    }

    /**
     * Helper method to create new instance of side value
     *
     * @return static
     */
    public static function make(): static
    {
        return new static();
    }

    /**
     * Create instance from conditional value
     *
     * @return static
     */
    public static function fromValue(): static
    {
        return static::make();
    }

    /**
     * Convert value into storage format for conditional
     *
     * @return null
     */
    public function toValue(): mixed
    {
        return null;
    }
}
