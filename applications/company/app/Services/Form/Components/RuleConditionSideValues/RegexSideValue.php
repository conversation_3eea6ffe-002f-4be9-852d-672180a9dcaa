<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionSideValues;

use App\Services\Form\Classes\Structure\Group\Rule\Condition\SideValue;
use App\Services\Form\Interfaces\Structure\Group\Rule\Condition\RegexSideValueInterface;

/**
 * Class RegexSideValue
 *
 * @package App\Services\Form\Components\RuleConditionSideValues
 */
class RegexSideValue extends SideValue implements RegexSideValueInterface
{
    public const KEY_RIGHT = 'value';

    /**
     * @var string|null Key for right side of conditional
     */
    protected ?string $right_key = self::KEY_RIGHT;

    /**
     * Determines if side value accepts data based on the side, operator, and value
     *
     * @param string $side
     * @param string $operator
     * @param mixed $value
     * @return bool
     */
    public static function accepts(string $side, string $operator, mixed $value): bool
    {
        return $side === 'right' && in_array($operator, ['matches', 'does-not-match']) && is_string($value);
    }

    /**
     * Helper method to create new instance of side value
     *
     * @param string $pattern
     * @return static
     */
    public static function make(string $pattern): static
    {
        return new static($pattern);
    }

    /**
     * Create instance from conditional value
     *
     * @param string $pattern
     * @return static
     */
    public static function fromValue(string $pattern): static
    {
        return static::make($pattern);
    }

    /**
     * RegexSideValue constructor
     *
     * @param string $pattern
     */
    public function __construct(protected string $pattern)
    {}

    /**
     * Convert value into storage format for conditional
     *
     * @return string
     */
    public function toValue(): string
    {
        return $this->pattern;
    }
}
