<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleEvents\LineItemAdd;

use App\Resources\Bid\Item\LineItemResource;
use App\Services\Form\Components\RuleEvents\LineItemAddRuleEvent;
use App\Services\Form\Traits\RuleEvent\LineItemAdd\PriceAdjustmentTrait;

/**
 * Class DiscountLineItemAddRuleEvent
 *
 * @package App\Services\Form\Components\RuleEvents\LineItemAdd
 */
class DiscountLineItemAddRuleEvent extends LineItemAddRuleEvent
{
    use PriceAdjustmentTrait;

    /**
     * @var int Line item type
     */
    protected int $line_item_type = LineItemResource::TYPE_DISCOUNT;
}
