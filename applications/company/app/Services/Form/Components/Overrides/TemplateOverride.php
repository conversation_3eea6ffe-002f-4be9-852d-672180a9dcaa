<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Overrides;

use App\Resources\Form\Item\OverrideResource;
use App\Services\Form\Classes\Structure\{Group\Template, Override};
use App\Services\Form\Exceptions\ValidationException;

/**
 * Class TemplateOverride
 *
 * @package App\Services\Form\Components\Overrides
 */
class TemplateOverride extends Override
{
    /**
     * @var int Override type
     */
    protected int $type = OverrideResource::TYPE_TEMPLATE;

    /**
     * Set associated template
     *
     * This is an alias for setItem().
     *
     * @param Template $template
     * @return $this
     */
    public function setTemplate(Template $template): self
    {
        return $this->setItem($template);
    }

    /**
     * Validate before save
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();
        if (($item_id = $this->getItemID()) !== null) {
            if (($template = $this->structure->getTemplate($item_id, deep: true)) === null) {
                throw new ValidationException('Unable to find template with id: %s', $item_id);
            }
            $this->setItemID($template->getID());
        }
    }

    /**
     * Configure override to set it up for usage or output to array or client formats
     *
     * If item id is available and associated template is found, we set the item instance.
     */
    protected function configure(): void
    {
        if (($item_id = $this->getItemID()) !== null && ($template = $this->structure->getTemplate($item_id, deep: true)) !== null) {
            $this->setItem($template);
        }
    }
}
