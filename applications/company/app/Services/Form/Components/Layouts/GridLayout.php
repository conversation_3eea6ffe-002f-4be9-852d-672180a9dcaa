<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Layouts;

use App\Services\Form\Classes\Structure\Group\Layout;
use Core\Classes\Str;
use Core\Components\Http\StaticAccessors\View;

/**
 * Class GridLayout
 *
 * @package App\Services\Form\Components\Layouts
 */
class GridLayout extends Layout
{
    /**
     * Build row/column grid out of list of items
     *
     * Uses the size of each item along with the last in row setting to build a list rows with the proper amount
     * of columns to fit a base 12 grid.
     *
     * @return array
     */
    protected function buildGrid(): array
    {
        $grid = [];
        $total = count($this->items);
        $row = [];
        $size = 0;
        $i = 0;
        foreach ($this->items as $item) {
            $i++;
            $size += $item->getSize();
            $row[] = $item;
            if ($item->getIsLastInRow() || $size >= 12 || $i === $total) {
                $grid[] = $row;
                $row = [];
                $size = 0;
            }
        }
        return $grid;
    }

    /**
     * Convert layout into format useful for client side form library to consume
     *
     * @return array
     */
    public function toClientFormat(): array
    {
        $layout = parent::toClientFormat();
        $grid = $this->buildGrid();
        array_walk($grid, function (&$row) {
            array_walk($row, fn(&$column) => $column = $column->toClientFormat());
        });
        $layout['grid'] = $grid;
        return $layout;
    }

    /**
     * Build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    public function buildTemplate(int $layout_type): array
    {
        $rows = [];
        $styles = [];
        foreach ($this->buildGrid() as $row) {
            $_row = [];
            foreach ($row as $column) {
                $template = $column->getItem()->buildTemplate($layout_type);
                if ($template['content'] === null) {
                    continue;
                }
                $_row[] = [
                    'size' => $column->getSize(),
                    'content' => Str::indent($template['content'], 3)
                ];
                $styles = array_merge($styles, $template['styles']);
            }
            if (count($_row) === 0) {
                continue;
            }
            $rows[] = $_row;
        }
        $content = null;
        if (count($rows) > 0) {
            $content = View::fetch('services.form.structure.layouts.grid', [
                'rows' => $rows
            ])->render();
        }
        return [
            'content' => $content,
            'styles' => $styles
        ];
    }
}
