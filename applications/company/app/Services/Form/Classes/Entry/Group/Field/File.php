<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Entry\Group\Field;

use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Services\Form\Components\EntryFields\FileEntryField;
use App\Services\Form\Exceptions\{FormException, PersistException};
use App\Services\Form\Helpers\EntryFileInfoHelper;
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait};
use Core\Components\Resource\Classes\Entity;
use Throwable;

/**
 * Class File
 *
 * @package App\Services\Form\Classes\Entry\Group\Field
 */
class File
{
    use ArrayImportExportTrait;
    use IdentifierTrait;

    /**
     * @var FileEntryField Parent field
     */
    protected FileEntryField $field;

    /**
     * @var string|null File id
     */
    protected ?string $file_id = null;

    /**
     * @var array|null File related info (name, extension, size, etc.)
     */
    protected ?array $file = null;

    /**
     * @var array|null List of URLs to access file
     */
    protected ?array $media_urls = null;

    /**
     * @var bool Determines if file is setup
     */
    protected bool $setup = false;

    /**
     * Create new instance and assign to field
     *
     * @param FileEntryField $field
     * @param array $data
     * @return static
     * @throws FormException
     */
    public static function make(FileEntryField $field, array $data = []): self
    {
        $file = new static($field, $data);
        $field->addFile($file);
        return $file;
    }

    /**
     * File constructor
     *
     * @param FileEntryField $field
     * @param array $data
     * @throws FormException
     */
    public function __construct(FileEntryField $field, array $data = [])
    {
        $this->setField($field);
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Reset class after clone operation
     */
    public function __clone()
    {
        $this->clearID();
    }

    /**
     * Clone file and set new parent field
     *
     * @param FileEntryField $field
     * @return $this
     */
    public function clone(FileEntryField $field): self
    {
        $file = clone $this;
        $file->setField($field);
        return $file;
    }

    /**
     * Disable automatic id generation
     *
     * @return bool
     */
    public function getGenerateID(): bool
    {
        return false;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'id' => ['string', 'setID'],
            'file_id' => ['string', 'setFileID']
        ], $data);
    }

    /**
     * Set parent field
     *
     * @param FileEntryField $field
     */
    public function setField(FileEntryField $field): void
    {
        $this->field = $field;
    }

    /**
     * Set file id
     *
     * @param string $file_id UUID
     * @return $this
     */
    public function setFileID(string $file_id): self
    {
        $this->file_id = $file_id;
        return $this;
    }

    /**
     * Get file id
     *
     * @return string|null
     */
    public function getFileID(): ?string
    {
        return $this->file_id;
    }

    /**
     * Set file data
     *
     * Currently is fetched via file info helper.
     *
     * @param array $file
     * @return $this
     */
    public function setFile(array $file): self
    {
        $this->file = $file;
        return $this;
    }

    /**
     * Get file data
     *
     * @return array|null
     */
    public function getFile(): ?array
    {
        return $this->file;
    }

    /**
     * Set media urls
     *
     * @param array $media_urls
     * @return $this
     */
    public function setMediaUrls(array $media_urls): self
    {
        $this->media_urls = $media_urls;
        return $this;
    }

    /**
     * Get media urls for file
     *
     * @return array|null
     */
    public function getMediaUrls(): ?array
    {
        return $this->media_urls;
    }

    /**
     * Get file entity to persist
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        $entity = $this->field->getEntity();
        $entity['file_id'] = $this->getFileID();
        return $entity;
    }

    /**
     * Persist file and set id
     *
     * @param FieldFileResource $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FieldFileResource $resource): void
    {
        try {
            $id = $resource->create($this->getEntity())->nested()->run();
            $this->setID($id);
        } catch (Throwable $e) {
            throw (new PersistException('Unable to persist entry field file'))->setLastException($e);
        }
    }

    /**
     * Setup and configure file for use after loading from external source
     *
     * @param EntryFileInfoHelper $helper
     * @throws FormException
     */
    public function setup(EntryFileInfoHelper $helper): void
    {
        if ($this->setup) {
            throw new FormException('Entry field file is already setup');
        }
        if (($file_id = $this->getFileID()) !== null) {
            $helper->fetchFile($file_id, function ($file) use ($helper) {
                $this->setFile($file);
                $this->setMediaUrls($helper->buildMediaUrls($this->getID()));
            });
        }
        $this->setup = true;
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     * @throws FormException
     */
    public function toClientFormat(): array
    {
        if (($id = $this->getID()) === null) {
            throw new FormException('ID not defined for entry field file');
        }
        if (($file = $this->getFile()) === null) {
            throw new FormException('File data not loaded for entry field file');
        }
        if (($media_urls = $this->getMediaUrls()) === null) {
            throw new FormException('Media urls not loaded for entry field file');
        }
        return [
            'id' => $id,
            'file_id' => $this->getFileID(),
            'file' => $file,
            'file_media_urls' => $media_urls
        ];
    }

    /**
     * Export to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        return $this->exportToArray([
            'file_id' => 'getFileID'
        ]);
    }
}
