<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Entry\Group\Field;

use App\Resources\Form\Item\Entry\Group\FieldProductResource;
use App\Services\Form\Components\EntryFields\ProductEntryField;
use App\Services\Form\Exceptions\PersistException;
use App\Services\Form\Helpers\EntryProductInfoHelper;
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait};
use Core\Components\Resource\Classes\Entity;
use Throwable;

/**
 * Class Product
 *
 * @package App\Services\Form\Classes\Entry\Group\Field
 */
class Product
{
    use ArrayImportExportTrait;
    use IdentifierTrait;

    /**
     * @var ProductEntryField Parent field
     */
    protected ProductEntryField $field;

    /**
     * @var string|null Product item id
     */
    protected ?string $product_item_id = null;

    /**
     * @var array|null Info about product
     */
    protected ?array $product = null;

    /**
     * Create new instance and assign to field
     *
     * @param ProductEntryField $field
     * @param array $data
     * @return static
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public static function make(ProductEntryField $field, array $data = []): self
    {
        $product = new static($field, $data);
        $field->addProduct($product);
        return $product;
    }

    /**
     * Product constructor
     *
     * @param ProductEntryField $field
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function __construct(ProductEntryField $field, array $data = [])
    {
        $this->setField($field);
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Reset class after clone operation
     */
    protected function __clone()
    {
        $this->clearID();
    }

    /**
     * Clone product and set new parent field
     *
     * @param ProductEntryField $field
     * @return $this
     */
    public function clone(ProductEntryField $field): self
    {
        $product = clone $this;
        $product->setField($field);
        return $product;
    }

    /**
     * Disable automatic id generation
     *
     * @return bool
     */
    public function getGenerateID(): bool
    {
        return false;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'id' => ['string', 'setID'],
            'product_item_id' => ['string', 'setProductItemID']
        ], $data);
    }

    /**
     * Set parent field
     *
     * @param ProductEntryField $field
     */
    public function setField(ProductEntryField $field): void
    {
        $this->field = $field;
    }

    /**
     * Set product item id
     *
     * @param string $product_item_id
     * @return $this
     */
    public function setProductItemID(string $product_item_id): self
    {
        $this->product_item_id = $product_item_id;
        return $this;
    }

    /**
     * Get product item id
     *
     * @return string|null
     */
    public function getProductItemID(): ?string
    {
        return $this->product_item_id;
    }

    /**
     * Set product info
     *
     * Currently pulled via product info helper.
     *
     * @param array $product
     * @return $this
     */
    public function setProduct(array $product): self
    {
        $this->product = $product;
        return $this;
    }

    /**
     * Get product info
     *
     * @return array|null
     */
    public function getProduct(): ?array
    {
        return $this->product;
    }

    /**
     * Get product entity to persist
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        $entity = $this->field->getEntity();
        $entity['product_item_id'] = $this->getProductItemID();
        return $entity;
    }

    /**
     * Persist product and set id
     *
     * @param FieldProductResource $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FieldProductResource $resource)
    {
        try {
            $id = $resource->create($this->getEntity())->nested()->run();
            $this->setID($id);
        } catch (Throwable $e) {
            throw (new PersistException('Unable to persist entry field product'))->setLastException($e);
        }
    }

    /**
     * Setup and configure group for use after loading from external source
     *
     * If product item id is defined, we request it's info using the product info helper.
     *
     * @param EntryProductInfoHelper $helper
     */
    public function setup(EntryProductInfoHelper $helper): void
    {
        if (($product_item_id = $this->getProductItemID()) !== null) {
            $helper->fetchProduct($product_item_id, function ($product) {
                $this->setProduct($product);
            });
        }
    }
}
