<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Structure\Group\Rule;

use App\Services\Form\Classes\Structure\Group\Rule;
use App\Services\Form\Components\RuleConditionGroups;
use App\Services\Form\Exceptions\ValidationException;
use App\Services\Form\Interfaces\Structure\Group\Rule\{ConditionInterface, ConditionGroupParentInterface};
use Closure;

/**
 * Class ConditionGroup
 *
 * @package App\Services\Form\Classes\Structure\Group\Rule
 */
abstract class ConditionGroup implements ConditionInterface, ConditionGroupParentInterface
{
    /**
     * @var string[] Mapping of aliases to condition group type used for exports
     */
    protected static array $type_alias_map = [
        'all' => RuleConditionGroups\AllRuleConditionGroup::class,
        'any' => RuleConditionGroups\AnyRuleConditionGroup::class
    ];

    /**
     * @var Rule Parent rule
     */
    protected Rule $rule;

    /**
     * @var ConditionGroupParentInterface|null Group parent (allows for infinite nesting)
     */
    protected ?ConditionGroupParentInterface $parent;

    /**
     * @var string Group type
     */
    protected string $type;

    /**
     * @var ConditionInterface[] List of defined conditions
     */
    protected array $conditions = [];

    /**
     * Create new instance and assign to parent (rule or another condition group)
     *
     * @param Rule $rule
     * @param ConditionGroup|null $parent
     * @return static
     */
    public static function make(Rule $rule, ?self $parent = null): self
    {
        $group = new static($rule, $parent);
        $parent ??= $rule;
        $parent->addCondition($group);
        return $group;
    }

    /**
     * ConditionGroup constructor
     *
     * Rule is passed to each group so we don't have to traverse up the hierarchy to get to the rule info.
     *
     * @param Rule $rule
     * @param ConditionGroup|null $parent
     */
    public function __construct(Rule $rule, ?self $parent = null)
    {
        $this->rule = $rule;
        $this->parent = $parent ?? $rule;
    }

    /**
     * Get rule
     *
     * @return Rule
     */
    public function getRule(): Rule
    {
        return $this->rule;
    }

    /**
     * Define related components within scoped closure
     *
     * @param Closure $with
     * @return $this
     */
    public function with(Closure $with): self
    {
        $with($this, $this->rule);
        return $this;
    }

    /**
     * Get condition group type
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Add condition (or another group) to group
     *
     * @param ConditionInterface $condition
     * @return $this
     */
    public function addCondition(ConditionInterface $condition): self
    {
        $this->conditions[] = $condition;
        return $this;
    }

    /**
     * Get index of specified condition or group
     *
     * @param ConditionInterface $condition
     * @return int
     */
    public function getConditionIndex(ConditionInterface $condition): int
    {
        return array_search($condition, $this->conditions, true);
    }

    /**
     * Get location within array format for use with debugging
     *
     * @return string
     */
    public function getLocation(): string
    {
        return $this->parent->getLocation() . ".condition_group.{$this->type}";
    }

    /**
     * Prepare condition group by running prepare on all defined conditions
     */
    public function prepare(): void
    {
        if (count($this->conditions) > 0) {
            foreach ($this->conditions as $condition) {
                $condition->prepare();
            }
        }
    }

    /**
     * Validate condition group
     *
     * Ensures at least on condition is defined.
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        if (count($this->conditions) === 0) {
            throw new ValidationException('At least one condition is required for group [%s]', $this->getLocation());
        }
        foreach ($this->conditions as $condition) {
            $condition->validate();
        }
    }

    /**
     * Convert condition group to an array
     *
     * If export is true, then any type id's are replaced with their defined alias.
     *
     * @param bool $export
     * @return array
     */
    public function toArray(bool $export = false): array
    {
        return [
            $this->getType() => array_map(fn($condition) => $condition->toArray($export), $this->conditions)
        ];
    }
}
