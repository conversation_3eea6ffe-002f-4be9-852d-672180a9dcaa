<?php

declare(strict_types=1);

namespace App\Services\Form\Helpers;

use App\Resources\Product\CategoryResource as ProductCategoryResource;
use App\Resources\Product\ItemResource as ProductItemResource;
use App\Services\Form\Exceptions\FormException;
use Core\Components\Resource\Interfaces\AclInterface;

/**
 * Class ProductInfoHelper
 *
 * @package App\Services\Form\Helpers
 */
class ProductInfoHelper
{
    /**
     * @var array List of category aliases to lookup
     */
    protected array $category_requests = [];

    /**
     * @var array Mapping of category alias to id
     */
    protected array $category_alias_map = [];

    /**
     * @var array List of item aliases to lookup
     */
    protected array $item_requests = [];

    /**
     * @var array Mapping of item alias to id
     */
    protected array $item_alias_map = [];

    /**
     * Request category alias to be looked up
     *
     * @param string $alias
     */
    public function requestCategory(string $alias): void
    {
        if (isset($this->category_alias_map[$alias])) {
            return;
        }
        $this->category_requests[$alias] = true;
    }

    /**
     * Set category alias map which is used to get id's from aliases
     *
     * @param array $aliases
     */
    public function setCategoryAliasMap(array $aliases): void
    {
        $this->category_alias_map = $aliases;
    }

    /**
     * Get category alias map
     *
     * @return array
     */
    public function getCategoryAliasMap(): array
    {
        return $this->category_alias_map;
    }

    /**
     * Get id from category alias if available
     *
     * @param string $alias
     * @return string|null
     */
    public function getCategory(string $alias): ?string
    {
        return $this->category_alias_map[$alias] ?? null;
    }

    /**
     * Request item alias to be looked up
     *
     * @param string $alias
     */
    public function requestItem(string $alias): void
    {
        if (isset($this->item_alias_map[$alias])) {
            return;
        }
        $this->item_requests[$alias] = true;
    }

    /**
     * Set item alias map which is used to get id's from aliases
     *
     * @param array $aliases
     */
    public function setItemAliasMap(array $aliases): void
    {
        $this->item_alias_map = $aliases;
    }

    /**
     * Get item alias map
     *
     * @return array
     */
    public function getItemAliasMap(): array
    {
        return $this->item_alias_map;
    }

    /**
     * Get id from item alias if available
     *
     * @param string $alias
     * @return string|null
     */
    public function getItem(string $alias): ?string
    {
        return $this->item_alias_map[$alias] ?? null;
    }

    /**
     * Fetch requested ids for category and item aliases
     *
     * Company ID must be available for loading to prevent cross company contamination.
     *
     * @param AclInterface $acl
     * @throws FormException
     */
    public function load(AclInterface $acl): void
    {
        $has_categories = count($this->category_requests) > 0;
        $has_items = count($this->item_requests) > 0;
        if ($has_categories || $has_items) {
            if (($company_id = $acl->companyID(true)) === null) {
                throw new FormException('Company ID must be defined on ACL instance to fetch product info');
            }
            if ($has_categories) {
                $aliases = ProductCategoryResource::make($acl)
                    ->getIdsFromAliases(
                        ProductCategoryResource::OWNER_TYPE_COMPANY,
                        $company_id,
                        array_keys($this->category_requests)
                    );
                $this->setCategoryAliasMap(array_merge($this->getCategoryAliasMap(), $aliases));
            }
            if ($has_items) {
                $aliases = ProductItemResource::make($acl)
                    ->getIdsFromAliases(
                        ProductItemResource::OWNER_TYPE_COMPANY,
                        $company_id,
                        array_keys($this->item_requests)
                    );
                $this->setItemAliasMap(array_merge($this->getItemAliasMap(), $aliases));
            }
        }
    }
}
