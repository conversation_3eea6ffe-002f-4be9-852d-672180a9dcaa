<?php

namespace App\Services\Form\Interfaces\Entry;

use App\Services\Form\Classes\Entry\Group;
use App\Services\Form\Classes\Structure\Group as StructureGroup;

/**
 * Interface GroupParentInterface
 *
 * @package App\Services\Form\Interfaces\Entry
 */
interface GroupParentInterface
{
    /**
     * Get UUID
     *
     * @return string|null
     */
    public function getID(): ?string;

    /**
     * Get group instance from structure by id or alias
     *
     * @param string $id
     * @return StructureGroup|null
     */
    public function getStructureGroup(string $id): ?StructureGroup;

    /**
     * Add group
     *
     * @param Group $group
     */
    public function addGroup(Group $group): void;

    /**
     * Get entry group by id
     *
     * @param string $id UUID
     * @return Group|null
     */
    public function getGroup(string $id): ?Group;
}
