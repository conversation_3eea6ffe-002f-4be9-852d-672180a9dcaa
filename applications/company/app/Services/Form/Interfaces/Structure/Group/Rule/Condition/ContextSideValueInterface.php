<?php

namespace App\Services\Form\Interfaces\Structure\Group\Rule\Condition;

/**
 * Interface ContextSideValueInterface
 *
 * Used for any values derived from the rule context. They can return any value so this is a combination of all.
 *
 * @package App\Services\Form\Interfaces\Structure\Group\Rule\Condition
 */
interface ContextSideValueInterface extends
    ArraySideValueInterface,
    EmptySideValueInterface,
    NumericSideValueInterface,
    RegexSideValueInterface,
    ScalarSideValueInterface
{
    //
}
