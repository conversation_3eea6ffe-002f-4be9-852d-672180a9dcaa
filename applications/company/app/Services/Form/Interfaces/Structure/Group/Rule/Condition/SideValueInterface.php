<?php

namespace App\Services\Form\Interfaces\Structure\Group\Rule\Condition;

use App\Services\Form\Classes\Structure\Group\Rule\Condition;

/**
 * Interface SideValueInterface
 *
 * @package App\Services\Form\Interfaces\Structure\Group\Rule\Condition
 */
interface SideValueInterface
{
    /**
     * Get left key
     *
     * @return string|null
     */
    public function getLeftKey(): ?string;

    /**
     * Get right key
     *
     * @return string|null
     */
    public function getRightKey(): ?string;

    /**
     * Convert side value to value for conditional
     *
     * @return mixed
     */
    public function toValue(): mixed;

    /**
     * Prepare side value
     *
     * @param Condition $condition
     */
    public function prepare(Condition $condition): void;

    /**
     * Validate side value
     *
     * @param Condition $condition
     */
    public function validate(Condition $condition): void;
}
