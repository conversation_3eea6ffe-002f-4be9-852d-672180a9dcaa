<?php

namespace App\Services\Form\Interfaces\Structure\Group\Rule;

/**
 * Interface ConditionInterface
 *
 * @package App\Services\Form\Interfaces\Structure\Group\Rule
 */
interface ConditionInterface
{
    /**
     * Prepare condition
     */
    public function prepare(): void;

    /**
     * Validate condition
     */
    public function validate(): void;

    /**
     * Convert to array, optionally setup for export
     *
     * @param bool $export
     * @return array
     */
    public function toArray(bool $export = false): array;
}
