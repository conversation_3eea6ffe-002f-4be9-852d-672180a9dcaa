<?php

declare(strict_types=1);

namespace App\Services\Form\Traits;

use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Trait IdentifierTrait
 *
 * @package App\Services\Form\Traits
 */
trait IdentifierTrait
{
    /**
     * @var string UUID of component
     */
    protected ?string $id = null;

    /**
     * @var bool Determines if we a UUID will be generated if asked for before it's set
     */
    protected bool $generate_id = true;

    /**
     * @var bool Determines if component has been persisted or not
     */
    public bool $exists = false;

    /**
     * Set UUID
     *
     * @param string|UuidInterface $id
     * @param bool $exists
     * @return $this
     */
    public function setID($id, bool $exists = true): self
    {
        if (is_object($id) && $id instanceof UuidInterface) {
            $id = $id->toString();
        }
        $this->id = $id;
        $this->exists = $exists;
        return $this;
    }

    /**
     * Determines if UUID will be generated if asked for before it's set
     *
     * @return bool
     */
    public function getGenerateID(): bool
    {
        return $this->generate_id;
    }

    /**
     * Get UUID
     *
     * @return string|null
     */
    public function getID(): ?string
    {
        if ($this->getGenerateID() && $this->id === null) {
            $this->setID(Uuid::uuid4()->toString(), false);
        }
        return $this->id;
    }

    /**
     * Reset ID info to default state
     */
    protected function clearID(): void
    {
        $this->id = null;
        $this->exists = false;
    }
}
