<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\RuleEvent;

use App\Services\Form\Exceptions\ValidationException;

/**
 * Trait GroupTrait
 *
 * @package App\Services\Form\Traits\RuleEvent
 */
trait GroupTrait
{
    /**
     * @var string|null Group id or alias
     */
    protected ?string $group_id;

    /**
     * Set group id or alias
     *
     * @param string|null $id
     * @return $this
     */
    public function setGroup(?string $id): self
    {
        $this->group_id = $id;
        return $this;
    }

    /**
     * Get group id or alias
     *
     * @return string|null
     */
    public function getGroup(): ?string
    {
        return $this->group_id;
    }

    /**
     * Set event params
     *
     * Group based events only store the group id/alias as the first param.
     *
     * @param array $params
     * @return $this
     */
    public function setParams(array $params): self
    {
        $this->importFromArray([
            0 => ['string', 'setGroup']
        ], $params);
        return $this;
    }

    /**
     * Get event params
     *
     * @param bool $export
     * @return array
     */
    public function getParams(bool $export = false): array
    {
        $params = [];
        if (($field = $this->getGroup()) !== null) {
            $params[] = $field;
        }
        return $params;
    }

    /**
     * Validate event before saving
     *
     * Ensures group id exists within structure.
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();

        if (($group = $this->getGroup()) === null) {
            throw new ValidationException('Group param is required [%s]', $this->getLocation());
        }
        if ($this->rule->getGroup()->getGroup($group, deep: true) === null) {
            throw new ValidationException('Unable to find group: %s [%s]', $group, $this->getLocation());
        }
    }
}
