<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\Field\Option;

/**
 * Trait ColumnsTrait
 *
 * @package App\Services\Form\Traits\Field\Option
 */
trait ColumnsTrait
{
    /**
     * @var int|null Number of columns to divide options into in UI
     */
    protected ?int $display_columns = null;

    /**
     * Import nested array data with type check to prevent type errors
     *
     * @param array $config
     * @param array $data
     */
    abstract public function importFromArray(array $config, array $data): void;

    /**
     * Set number of columns to divide options into in UI
     *
     * Note: Default is set at 1 column.
     *
     * @param int|null $columns
     * @return $this
     */
    public function setDisplayColumns(?int $columns)
    {
        $this->display_columns = $columns;
        return $this;
    }

    /**
     * Get display columns config item
     *
     * @return int|null
     */
    public function getDisplayColumns(): ?int
    {
        return $this->display_columns;
    }

    /**
     * Set display columns from field config
     *
     * @param array $config
     */
    public function setColumnsConfig(array $config): void
    {
        $this->importFromArray([
            'display.columns' => ['int', 'setDisplayColumns']
        ], $config);
    }

    /**
     * Get field config, set columns as necessary
     *
     * @param array $config
     * @return array
     */
    public function getColumnsConfig(array $config): array
    {
        return $this->exportToArray([
            'display.columns' => 'getDisplayColumns'
        ], [
            'initial' => $config
        ]);
    }
}
