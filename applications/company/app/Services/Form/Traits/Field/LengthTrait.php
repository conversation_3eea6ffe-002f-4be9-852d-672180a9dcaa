<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\Field;

/**
 * Trait LengthTrait
 *
 * @package App\Services\Form\Traits\Field
 */
trait LengthTrait
{
    /**
     * @var int|null Max length for use with validation
     */
    protected ?int $max_length = null;

    /**
     * Import nested array data with type check to prevent type errors
     *
     * @param array $config
     * @param array $data
     */
    abstract public function importFromArray(array $config, array $data): void;

    /**
     * Export class data to array format
     *
     * @param array $fields
     * @param array $config
     * @return array|null
     */
    abstract public function exportToArray(array $fields, array $config = []): ?array;

    /**
     * Set max length for validation
     *
     * @param int $length
     * @return $this
     */
    public function setMaxLength(int $length)
    {
        $this->max_length = $length;
        return $this;
    }

    /**
     * Get max length for validation
     *
     * @return int|null
     */
    public function getMaxLength(): ?int
    {
        return $this->max_length;
    }

    /**
     * Set class data from config array
     *
     * @param array $config
     */
    public function setLengthConfig(array $config): void
    {
        $this->importFromArray([
            'validation.max_length' => ['int', 'setMaxLength']
        ], $config);
    }

    /**
     * Add length config items to existing list
     *
     * @param array $config
     * @return array
     */
    public function getLengthConfig(array $config): array
    {
        return $this->exportToArray([
            'validation.max_length' => 'getMaxLength'
        ], [
            'initial' => $config
        ]);
    }
}
