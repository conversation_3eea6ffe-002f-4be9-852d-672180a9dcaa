<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\Type;

use App\Resources\Form\ItemResource as FormItemResource;
use App\Services\Form\Components\Structures\BidStructure;
use Closure;

/**
 * Trait BidTrait
 *
 * @package App\Services\Form\Traits\Type
 */
trait BidTrait
{
    /**
     * Get list of allowed structure types for this form
     *
     * @return string[]
     */
    protected static function getAllowedStructures(): array
    {
        return [BidStructure::class];
    }

    /**
     * Get default structure type for this form
     *
     * Used when importing form where structure doesn't have a defined type.
     *
     * @return int
     */
    protected static function getDefaultStructureType(): int
    {
        return FormItemResource::TYPE_BID;
    }

    /**
     * @var bool Determines if form will be added to a bid by default (unnamed section will be made)
     */
    protected bool $is_default = false;

    /**
     * @var bool Determines if form will be added to every section created (include the one potentially created by a default form)
     */
    protected bool $is_section_default = false;

    /**
     * @var bool Determines if form will show in selection list in bid interface
     */
    protected bool $is_hidden_from_list = false;

    /**
     * @var bool Determines if form will show in bid document
     */
    protected bool $is_hidden_from_bid_document = false;

    /**
     * @var bool Determines if form will show in job document
     */
    protected bool $is_hidden_from_job_document = false;

    /**
     * @var bool Determines if form name is hidden when rendered within the bid and/or job documents
     */
    protected bool $is_form_name_hidden = false;

    /**
     * @var int Order of default forms which are automatically added to a bid section
     */
    protected int $default_order = 1;

    /**
     * Hydrate type from array
     *
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'bid' => ['array', 'setItem']
        ], $data);
    }

    /**
     * Add bid structure to form
     *
     * @param Closure $with
     * @return $this
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function withStructure(Closure $with): self
    {
        $structure = BidStructure::make();
        $with($structure);
        $this->setStructure($structure);
        return $this;
    }

    /**
     * Set if form should show by default on all bids
     *
     * An unnamed section is automatically added with this form.
     *
     * Note: A form can only be either a global or section default, not both.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsDefault(bool $status = true): self
    {
        $this->is_default = $status;
        return $this;
    }

    /**
     * Get is default setting
     *
     * @return bool
     */
    public function getIsDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Set if form should show by default for any new section
     *
     * This includes the unnamed section if a default form exists.
     *
     * Note: A form can only be either a global or section default, not both.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsSectionDefault(bool $status = true): self
    {
        $this->is_section_default = $status;
        return $this;
    }

    /**
     * Get is section default setting
     *
     * @return bool
     */
    public function getIsSectionDefault(): bool
    {
        return $this->is_section_default;
    }

    /**
     * Set if form is hidden from the selection list in the bid interface.
     *
     * Has no other consequences other than a user cannot select it while this is enabled. If a form was already in a
     * bid before this was enabled, it will be unaffected.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsHiddenFromList(bool $status = true): self
    {
        $this->is_hidden_from_list = $status;
        return $this;
    }

    /**
     * Get is hidden from list setting
     *
     * @return bool
     */
    public function getIsHiddenFromList(): bool
    {
        return $this->is_hidden_from_list;
    }

    /**
     * Set if form is hidden from the bid document
     *
     * Excludes this form when creating the PDF.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsHiddenFromBidDocument(bool $status = true): self
    {
        $this->is_hidden_from_bid_document = $status;
        return $this;
    }

    /**
     * Get is hidden from bid document setting
     *
     * @return bool
     */
    public function getIsHiddenFromBidDocument(): bool
    {
        return $this->is_hidden_from_bid_document;
    }

    /**
     * Set if form is hidden from job document
     *
     * Excludes this form when creating the PDF.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsHiddenFromJobDocument(bool $status = true): self
    {
        $this->is_hidden_from_job_document = $status;
        return $this;
    }

    /**
     * Get is hidden from job document setting
     *
     * @return bool
     */
    public function getIsHiddenFromJobDocument(): bool
    {
        return $this->is_hidden_from_job_document;
    }

    /**
     * Set if form name is hidden when rendering the form in documents
     *
     * Useful if a section or some other heading conveys the same info. Will make a cleaner looking result. This
     * setting is purely cosmetic and not necessary for a majority of forms.
     *
     * @param bool $status
     * @return $this
     */
    public function setIsFormNameHidden(bool $status = true): self
    {
        $this->is_form_name_hidden = $status;
        return $this;
    }

    /**
     * Get is form name hidden setting
     *
     * @return bool
     */
    public function getIsFormNameHidden(): bool
    {
        return $this->is_form_name_hidden;
    }

    /**
     * Set default order
     *
     * @param int $order
     * @return $this
     */
    public function setDefaultOrder(int $order): self
    {
        $this->default_order = $order;
        return $this;
    }

    /**
     * Get default order
     *
     * @return int
     */
    public function getDefaultOrder(): int
    {
        return $this->default_order;
    }

    /**
     * Set polymorphic item data
     *
     * @param array $item
     * @return $this
     */
    public function setItem(array $item): self
    {
        $this->importFromArray([
            'is_bid_default' => ['bool', 'setIsDefault'],
            'is_section_default' => ['bool', 'setIsSectionDefault'],
            'is_hidden_from_list' => ['bool', 'setIsHiddenFromList'],
            'is_hidden_from_bid' => ['bool', 'setIsHiddenFromBidDocument'],
            'is_hidden_from_scope_of_work' => ['bool', 'setIsHiddenFromJobDocument'],
            'is_form_name_hidden' => ['bool', 'setIsFormNameHidden'],
            'default_order' => ['int', 'setDefaultOrder']
        ], $item);
        return $this;
    }

    /**
     * Get polymorphic item data
     *
     * @return array
     */
    public function getItem(): array
    {
        return $this->exportToArray([
            'is_bid_default' => 'getIsDefault',
            'is_section_default' => 'getIsSectionDefault',
            'is_hidden_from_list' => 'getIsHiddenFromList',
            'is_hidden_from_bid' => 'getIsHiddenFromBidDocument',
            'is_hidden_from_scope_of_work' => 'getIsHiddenFromJobDocument',
            'is_form_name_hidden' => 'getIsFormNameHidden',
            'default_order' => 'getDefaultOrder'
        ]);
    }
}
