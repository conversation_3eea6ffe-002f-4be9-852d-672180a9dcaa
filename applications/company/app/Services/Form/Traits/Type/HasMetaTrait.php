<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\Type;

use App\Interfaces\Resource\TypeMetaResourceInterface;
use App\Services\Form\Classes\{Structure, Type, Type\Meta};
use App\Services\Form\Exceptions\ValidationException;

/**
 * Trait HasMetaTrait
 *
 * @package App\Services\Form\Traits\Type
 */
trait HasMetaTrait
{
    /**
     * @var array[] List of arrays containing a lookup name and closure
     */
    protected array $meta_callbacks = [];

    /**
     * @var Meta[] List of defined meta
     */
    protected array $meta = [];

    /**
     * Import data into class from array
     *
     * @param array $config
     * @param array $data
     * @param array $aliases
     */
    abstract protected function importFromArray(array $config, array $data, array $aliases = []): void;

    /**
     * Setup class data from array
     *
     * @param Type $type
     * @param array $data
     */
    protected function hydrateMeta(Type $type, array $data): void
    {
        $this->importFromArray([
            'meta' => ['list', fn($entity) => Meta::make($type, $entity)]
        ], $data);
    }

    /**
     * Set meta value for specified name
     *
     * @param string $name Reference to structure meta name
     * @param $value
     * @return $this
     */
    public function setMeta(string $name, $value)
    {
        $this->meta_callbacks[] = [$name, fn(Meta $meta) => $meta->setValue($value)];
        return $this;
    }

    /**
     * Add meta
     *
     * @param Meta $meta
     */
    public function addMeta(Meta $meta): void
    {
        $this->meta[] = $meta;
    }

    /**
     * Get index of specified meta instance
     *
     * @param Meta $meta
     * @return int
     */
    public function getMetaIndex(Meta $meta): int
    {
        return array_search($meta, $this->meta, true);
    }

    /**
     * Load meta using name and callback combinations defined by setMeta()
     *
     * Will create new type meta instance which is tied to the structure meta, then execute callback with new instance.
     *
     * @param Type $type
     */
    protected function loadMeta(Type $type): void
    {
        if (count($this->meta_callbacks) === 0) {
            return;
        }
        foreach ($this->meta_callbacks as [$name, $closure]) {
            $meta = Meta::make($type, ['form_item_meta_id' => $name]);
            $closure($meta);
        }
    }

    /**
     * Validate all defined meta before saving
     *
     * Will ensure meta values are only set once per structure meta. Will also ensure that all defined structure
     * meta are present in the form.
     *
     * @param Structure $structure
     * @throws ValidationException
     * @throws \App\Services\Form\Exceptions\ImportException
     */
    protected function validateMeta(Structure $structure): void
    {
        $names = [];
        if (count($this->meta) > 0) {
            foreach ($this->meta as $meta) {
                $meta->validate();
                $name = $meta->getSource()->getName();
                if (isset($names[$name])) {
                    throw new ValidationException('Meta value set twice for %s [%s]', $name, $meta->getLocation());
                }
                $names[$name] = true;
            }
        }
        $structure_names = array_map(fn($meta) => $meta->getName(), $structure->getMeta());
        if (count(($missing = array_diff($structure_names, array_keys($names)))) > 0) {
            throw new ValidationException('No meta value defined for the following names: %s', implode(', ', $missing));
        }
    }

    /**
     * Persist all defined meta
     *
     * @param TypeMetaResourceInterface $resource
     * @throws \Core\Exceptions\AppException
     */
    protected function persistMeta(TypeMetaResourceInterface $resource): void
    {
        if (count($this->meta) === 0) {
            return;
        }
        foreach ($this->meta as $meta) {
            $meta->persist($resource);
        }
    }

    /**
     * Setup and configure meta for use after loading from external source
     *
     * @throws \App\Services\Form\Exceptions\ImportException
     */
    protected function setupMeta(): void
    {
        if (count($this->meta) === 0) {
            return;
        }
        foreach ($this->meta as $meta) {
            $meta->setup();
        }
    }

    /**
     * Convert to array with any relationships
     *
     * Used as caching format.
     *
     * @return array
     */
    protected function metaToArray(): array
    {
        return count($this->meta) > 0 ? array_map(fn($meta) => $meta->toArray(), $this->meta) : [];
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     */
    protected function metaToClientFormat(): array
    {
        $meta = [];
        foreach ($this->meta as $item) {
            $meta[$item->getSource()->getName()] = $item->getValue();
        }
        return $meta;
    }

    /**
     * Export meta to user friendly format useful for transferring info between companies or environments
     *
     * @return array|null
     */
    protected function metaExport(): ?array
    {
        if (count($this->meta) === 0) {
            return null;
        }
        return array_map(fn($meta) => $meta->export(), $this->meta);
    }
}
