<?php

namespace App\Services\AppNotification\Types\User;

use App\Classes\Acl;
use App\Resources\AppNotificationDistributionResource;
use App\Resources\AppNotificationResource;
use App\Services\AppNotification\AppNotificationService;
use Common\Models\Evaluation;
use Common\Models\User;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;
use Throwable;

class BidRejectedType
{
    /**
     * Send an in-app notification when a bid has been rejected.
     *
     * @param Evaluation $evaluation
     * @return void
     * @throws AppException
     */
    public static function create(Evaluation $evaluation): void
    {

        $evaluation_item = Evaluation::query()
            ->withBidItem()
            ->withCustomer()
            ->whereKey($evaluation->evaluationID)
            ->first([
                'bidItems.*',
                'customBid.bidNumber as bidReferenceNumber',
                'customBid.bidTotal as bidTotal',
                'customer.firstName as customerFirstName',
                'customer.lastName as customerLastName',
                'customer.companyID as companyID',
                'project.projectID as project_id',
                'project.projectSalesperson as projectSalesperson',
                'evaluation.evaluationFinalizedByID as evaluationCreatedByID',
                'evaluation.evaluationDescription AS description']);

        if ($evaluation_item === null) {
            throw new AppException('BidRejectedType: Unable to find bid item with ID ' . $evaluation->evaluationID);
        }

        $user_id = $evaluation_item->projectSalesperson ?? $evaluation_item->evaluationCreatedByID;
        $user = User::where('userID', $user_id)->first();
        if ($user === null) {
            throw new \Exception('BidRejectedType: Unable to find user with ID ' . $user_id);
        }

        $title = "Bid Rejected";

        try {
            $summary = self::generateNotificationSummary($evaluation_item);
            $metadata = [
                'sub_type'   => "bid_rejected",
                'project_id' => $evaluation_item->project_id,
                'is_legacy'  => $evaluation_item->isLegacy(),
            ];

            DB::beginTransaction();

            $entity = [
                'title'              => $title,
                'summary'            => $summary,
                'association_type'   => AppNotificationResource::ASSOCIATION_TYPE_BID,
                'metadata'           => json_encode($metadata),
                'placement'          => AppNotificationResource::PLACEMENT_NOTIFICATION_CENTER,
                'type'               => AppNotificationResource::TYPE_INFO,
                'status'             => AppNotificationResource::STATUS_ACTIVE,
            ];

            if ($evaluation_item->isLegacy()) {
                $entity['association_id'] = $evaluation_item->evaluationID;
            } else {
                $entity['association_uuid'] = $evaluation_item->bidItemID;
            }

            $acl = Acl::make()->setUser($user);
            AppNotificationResource::make($acl)
                ->withAccessLevel(Field::ACCESS_LEVEL_PRIVATE, function ($resource) use (&$id, $entity) {
                    $id = $resource->create(Entity::make($entity))->run();
                });

            $distribution = [
                'app_notification_id' => $id,
                'user_id'             => $user_id,
                'company_id'          => $evaluation_item['companyID'],
                'status'              => AppNotificationDistributionResource::STATUS_UNREAD,
                'createdByUserID'     => $evaluation_item['evaluationCreatedByID'],
            ];

            AppNotificationDistributionResource::make($acl)
                ->withAccessLevel(Field::ACCESS_LEVEL_PRIVATE, function ($resource) use ($distribution) {
                    $resource->create(Entity::make($distribution))->run();
                });

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            throw new AppException('BidRejectedType: Unable to send notification', $e->getMessage());
        }
    }

    /**
     * Generates the content for the notification.
     *
     * @param Evaluation $item
     * @return string
     */
    private static function generateNotificationSummary(Evaluation $item): string
    {
        // Trim all data upfront
        $first_name = trim($item['customerFirstName'] ?? '');
        $last_name = trim($item['customerLastName'] ?? '');
        $bid_name = trim($item['description'] ?? 'Untitled Bid');
        $reference = trim($item['bidReferenceNumber'] ?? 'N/A');

        // Create full-name (handles cases where either name might be empty)
        $full_name = trim("{$first_name} {$last_name}") ?: 'Unknown Customer';

        // Determine the bid amount based on whether it's a legacy record or not
        // Legacy bids use the 'bidTotal' field while newer ones use 'total'
        $raw_value = $item->isLegacy()
            ? (float) ($item['bidTotal'] ?? 0)
            : (float) ($item['total'] ?? 0);

        // Format bid-value
        $bid_value = '$' . number_format($raw_value, 2, '.', ',');

        // Build HTML components
        $bid_div = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Bid:</span> {$bid_name}<span class=\"ns-imw-tag\">#{$reference}</span></div>";
        $customer_div = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Customer:</span> {$full_name}</div>";
        $value_div = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Bid Value:</span> <span class=\"ns-imw-tag\">{$bid_value}</span></div>";

        // Return the final summary
        return $bid_div . $customer_div . $value_div;
    }
}