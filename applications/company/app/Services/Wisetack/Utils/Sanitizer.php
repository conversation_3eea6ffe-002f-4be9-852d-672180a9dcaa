<?php

namespace App\Services\Wisetack\Utils;


class Sanitizer
{

    /**
     * Sanitize a name by removing any characters that are not
     * letters, spaces, commas, periods, apostrophes, or hyphens.
     *
     * @param string $name
     * @return string
     */
    public static function sanitize(string $name): string
    {
        // Replace & with 'and'
        $name = str_replace('&', 'and', $name);

        $wisetackPattern = "/[^a-zA-Z\s,.'-]/";
        return preg_replace($wisetackPattern, '', $name);
    }
}