<?php



namespace App\Services\Wisetack\DTOs;


use App\Services\Wisetack\Utils\Sanitizer;
use Common\Models\Customer;
use Common\Models\Project;
use Common\Models\WisetackMerchant;
use Core\Components\Http\StaticAccessors\URI;
use Ramsey\Uuid\Uuid;

class PrequalDTO
{

    private WisetackMerchant $merchant;

    private Project $project;

    private Customer $customer;

    public function __construct(array $data)
    {
        $this->project = $data['project'];
        $this->customer = $data['customer'];
        $this->merchant = $data['merchant'];
    }

    /**
     * Returns the merchant ID as a UUID with dashes.
     * @return string
     */
    public function getMerchantIdWithDashes(): string
    {
        return strval(UUID::fromBytes($this->merchant->wisetackMerchantID));
    }

    public function getProjectId(): string
    {
        return $this->project->projectID;
    }

    public function getWebhookUrl()
    {
        return URI::route('wisetack.webhooks.prequal-link')->build();
    }

    private function getCustomerPhone(): string
    {
        return $this->customer->primaryPhone->phoneNumber
            ? "+1" . preg_replace('/\D+/', '', $this->customer->primaryPhone->phoneNumber)
            : '';
    }

    public function getCustomerUUID()
    {
        return $this->customer->customerUUID;
    }

    public function getCustomerUUIDString(): string
    {
        return strval(Uuid::fromBytes($this->customer->customerUUID));
    }

    public function generateLoanPrequalData(): array
    {
        $first_name = Sanitizer::sanitize($this->customer->firstName);
        $last_name = Sanitizer::sanitize($this->customer->lastName);

        return [
            "callbackURL" => $this->getWebhookUrl(),
            "firstName" => $first_name,
            "lastName" => $last_name,
            "email" => $this->customer->email,
            "streetAddress1" => $this->customer->ownerAddress,
            "streetAddress2" => $this->customer->ownerAddress2,
            "city" => $this->customer->ownerCity,
            "stateCode" => $this->customer->ownerState,
            "zip" => $this->customer->ownerZip,
            "customerId" => $this->getCustomerUUIDString(),
            "mobileNumber"=> $this->getCustomerPhone(),
        ];
    }

}