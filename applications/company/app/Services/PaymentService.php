<?php

namespace App\Services;

use App\Classes\Log;
use App\Services\Payment\Exceptions\AuthorizationFailedException;
use App\Services\Payment\Exceptions\PaymentException;
use App\Services\Payment\Exceptions\TransactionDeclinedException;
use Core\Classes\Config;
use Core\Classes\Path;
use Core\StaticAccessors\App;
use Exception;
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;
use net\authorize\api\constants\ANetEnvironment;
use net\authorize\util\LogFactory;

/**
 * Class PaymentService
 *
 * @package App\Services
 */
class PaymentService
{
    /**
     * Configuration data
     *
     * @var array
     */
    protected $config;

    /**
     * Determines if test mode is enabled
     *
     * @var bool
     */
    protected $test_mode;

    /**
     * Payment gateway endpoint
     *
     * @var string
     */
    protected $endpoint;

    /**
     * @var null|\Monolog\Logger
     */
    protected $log = null;

    /**
     * PaymentService constructor
     *
     * @param Config $config
     * @param Path $path
     */
    public function __construct(Config $config, Path $path)
    {
        $this->config = $config->get('authorizenet', []);

        $this->test_mode = SERVER_ROLE !== 'PROD';
        $this->endpoint = (!$this->test_mode ? ANetEnvironment::PRODUCTION : ANetEnvironment::SANDBOX);

        $logger = LogFactory::getLog(static::class);
        $logger->setLogFile($path->logs($this->config['log']));
    }

    /**
     * Get log instance configured for service
     *
     * @return \Monolog\Logger
     */
    protected function getLog()
    {
        if ($this->log === null) {
            $this->log = Log::create('payment_service', [
                'email' => [
                    'subject' => 'System Error - Payment Service'
                ],
                'slack' => [
                    'username' => 'payment-service'
                ],
                'file' => 'payment_service.log',
                // ignore input since it can contain sensitive data
                'app_processor' => function (Log\AppProcessor $processor) {
                    return $processor->withInput(false);
                }
            ]);
        }
        return $this->log;
    }

    /**
     * Get merchant authentication
     *
     * @return AnetAPI\MerchantAuthenticationType
     */
    protected function getMerchantAuthentication()
    {
        return (new AnetAPI\MerchantAuthenticationType)
            ->setName($this->config['name'])
            ->setTransactionKey($this->config['key']);
    }

    /**
     * Executes call from controller and parses the response
     *
     * @param AnetController\base\ApiOperationBase $controller
     * @return AnetAPI\AnetApiResponseType
     * @throws PaymentException
     */
    protected function getResponseFromController(AnetController\base\ApiOperationBase $controller)
    {
        try {
            $response = $controller->executeWithApiResponse($this->endpoint);
            if (!is_object($response)) {
                throw new PaymentException('Invalid response returned from gateway');
            }
        } catch (Exception $e) {
            if ($e instanceof PaymentException) {
                throw $e;
            }
            throw new PaymentException('Unable to send request to Authorize');
        }
        return $response;
    }

    /**
     * Create customer profile for company
     *
     * @param int $company_id
     * @param string $name
     * @return string
     * @throws PaymentException
     * @throws \Core\Exceptions\AppException
     */
    public function createCustomerProfile($company_id, $name)
    {
        $profile = new AnetAPI\CustomerProfileType();
        $profile->setMerchantCustomerId($company_id);
        $profile->setDescription($name);

        $request = new AnetAPI\CreateCustomerProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setProfile($profile);

        $controller = new AnetController\CreateCustomerProfileController($request);
        /** @var AnetAPI\CreateCustomerProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        if ($response->getMessages()->getResultCode() === 'Error') {
            throw new PaymentException('Unable to create customer profile');
        }
        return $response->getCustomerProfileId();
    }

    /**
     * Delete customer profile
     *
     * @param string $profile_id
     * @throws PaymentException
     */
    public function deleteCustomerProfile($profile_id)
    {
        $request = new AnetAPI\DeleteCustomerProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setCustomerProfileId($profile_id);

        $controller = new AnetController\DeleteCustomerProfileController($request);
        /** @var AnetAPI\DeleteCustomerProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        if ($response->getMessages()->getResultCode() === 'Error') {
            throw new PaymentException('Unable to delete customer profile');
        }
    }

    /**
     * Handle response for payment profile request
     *
     * Checks error codes and throws proper exceptions if necessary
     *
     * @param AnetAPI\CreateCustomerPaymentProfileResponse|AnetAPI\UpdateCustomerPaymentProfileResponse $response
     * @throws AuthorizationFailedException
     * @throws PaymentException
     */
    protected function handlePaymentProfileResponse($response)
    {
        if ($response->getMessages()->getResultCode() !== 'Error') {
            return;
        }
        $validation_response = $response->getValidationDirectResponse();
        if (is_string($validation_response)) {
            $validation_response = explode(',', $validation_response);
            if (count($validation_response) >= 4) {
                [0 => $response_code, 2 => $reason_code] = $validation_response;
                switch ((int) $response_code) {
                    case 2: // declined
                        switch ($reason_code) {
                            case '8':
                                throw new AuthorizationFailedException('Card is expired');
                            case '27':
                                throw new AuthorizationFailedException('Address provided does not match billing address of cardholder');
                            case '45': // general error for AVS or card code mismatch based on our rejection settings in Authorize interface
                                throw new AuthorizationFailedException('Billing address and/or card code is incorrect');
                            case '65':
                                throw new AuthorizationFailedException('Card code is incorrect');
                            case '101':
                                throw new AuthorizationFailedException('Name on account and/or the account type is incorrect');
                            default:
                                $this->getLog()->error('Payment profile authorization declined', [
                                    'validation_response' => $validation_response
                                ]);
                                break;
                        }
                        break;
                    case 3: // error
                    case 4: // held for review
                        $this->getLog()->error('Unexpected payment profile response code', [
                            'validation_response' => $validation_response
                        ]);
                        break;
                }
            }
        }
        throw new PaymentException('Unable to process customer payment profile request');
    }

    /**
     * Create credit card payment profile
     *
     * @param string $customer_profile_id
     * @param array $card_info
     * @param array $billing_info
     * @return mixed
     * @throws PaymentException
     *
     * @todo validate info according to authorize.net restrictions
     */
    public function createCreditCardPaymentProfile($customer_profile_id, array $card_info, array $billing_info)
    {
        $credit_card = new AnetAPI\CreditCardType();
        $credit_card->setCardNumber($card_info['number']);
        $credit_card->setExpirationDate($card_info['expiration_date']);
        $credit_card->setCardCode($card_info['code']);

        $payment_type = new AnetAPI\PaymentType();
        $payment_type->setCreditCard($credit_card);

        // limit company name to 50 characters to prevent errors
        if (strlen($billing_info['company_name']) > 50) {
            $billing_info['company_name'] = trim(substr($billing_info['company_name'], 0, 50), ' ,&.');
        }

        $bill_to = new AnetAPI\CustomerAddressType();
        $bill_to->setCompany($billing_info['company_name']);
        $bill_to->setAddress($billing_info['address']);
        $bill_to->setCity($billing_info['city']);
        $bill_to->setState($billing_info['state']);
        $bill_to->setZip($billing_info['zip']);

        $profile = new AnetAPI\CustomerPaymentProfileType();

        $profile->setCustomerType('business');
        $profile->setBillTo($bill_to);
        $profile->setPayment($payment_type);

        $request = new AnetAPI\CreateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setCustomerProfileId($customer_profile_id);
        $request->setPaymentProfile($profile);
        $request->setValidationMode('liveMode');

        $controller = new AnetController\CreateCustomerPaymentProfileController($request);
        /** @var AnetAPI\CreateCustomerPaymentProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        $this->handlePaymentProfileResponse($response);
        return $response->getCustomerPaymentProfileId();
    }

    /**
     * Update credit card payment profile
     *
     * @param string $customer_profile_id
     * @param string $profile_id
     * @param array $card_info
     * @param array $billing_info
     * @return bool
     * @throws PaymentException
     */
    public function updateCreditCardPaymentProfile($customer_profile_id, $profile_id, array $card_info, array $billing_info)
    {
        // authorize.net requires all data to be passed when updating so the credit_card, payment_type, bill_to, and profile variables
        // should match the create method
        $credit_card = new AnetAPI\CreditCardType();
        $credit_card->setCardNumber($card_info['number']);
        $credit_card->setExpirationDate($card_info['expiration_date']);
        $credit_card->setCardCode($card_info['code']);

        $payment_type = new AnetAPI\PaymentType();
        $payment_type->setCreditCard($credit_card);

        $bill_to = new AnetAPI\CustomerAddressType();
        $bill_to->setCompany($billing_info['company_name']);
        $bill_to->setAddress($billing_info['address']);
        $bill_to->setCity($billing_info['city']);
        $bill_to->setState($billing_info['state']);
        $bill_to->setZip($billing_info['zip']);

        $profile = new AnetAPI\CustomerPaymentProfileExType();
        $profile->setCustomerType('business');
        $profile->setCustomerPaymentProfileId($profile_id);
        $profile->setBillTo($bill_to);
        $profile->setPayment($payment_type);

        $request = new AnetAPI\UpdateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setCustomerProfileId($customer_profile_id);
        $request->setPaymentProfile($profile);
        $request->setValidationMode('liveMode');

        $controller = new AnetController\UpdateCustomerPaymentProfileController($request);
        /** @var AnetAPI\UpdateCustomerPaymentProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        $this->handlePaymentProfileResponse($response);
        return true;
    }

    /**
     * Create ACH payment profile
     *
     * @param string $customer_profile_id
     * @param array $bank_info
     * @param array $billing_info
     * @return string
     * @throws PaymentException
     */
    public function createAchPaymentProfile($customer_profile_id, array $bank_info, array $billing_info)
    {
        $echeck_type = 'WEB';
        // switch echeck type when using business checking since it isn't supported by WEB
        if ($bank_info['account_type'] === 'businessChecking') {
            $echeck_type = 'CCD';
        }

        $bank_account = new AnetAPI\BankAccountType();
        // the ordering here is important, found out the hard way
        $bank_account->setAccountType($bank_info['account_type']);
        $bank_account->setRoutingNumber($bank_info['routing_number']);
        $bank_account->setAccountNumber($bank_info['account_number']);
        $bank_account->setNameOnAccount($bank_info['name_on_account']);
        $bank_account->setEcheckType($echeck_type);

        $payment_type = new AnetAPI\PaymentType();
        $payment_type->setBankAccount($bank_account);

        $bill_to = new AnetAPI\CustomerAddressType();
        $bill_to->setCompany($billing_info['company_name']);
        $bill_to->setAddress($billing_info['address']);
        $bill_to->setCity($billing_info['city']);
        $bill_to->setState($billing_info['state']);
        $bill_to->setZip($billing_info['zip']);

        $profile = new AnetAPI\CustomerPaymentProfileType();

        $profile->setCustomerType('business');
        $profile->setBillTo($bill_to);
        $profile->setPayment($payment_type);

        $request = new AnetAPI\CreateCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setCustomerProfileId($customer_profile_id);
        $request->setPaymentProfile($profile);
        $request->setValidationMode($this->test_mode ? 'testMode' : 'liveMode');

        $controller = new AnetController\CreateCustomerPaymentProfileController($request);
        /** @var AnetAPI\CreateCustomerPaymentProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        $this->handlePaymentProfileResponse($response);
        return $response->getCustomerPaymentProfileId();
    }

    /**
     * Delete payment profile
     *
     * @param string $customer_profile_id
     * @param string $profile_id
     * @throws PaymentException
     */
    public function deletePaymentProfile($customer_profile_id, $profile_id)
    {
        $request = new AnetAPI\DeleteCustomerPaymentProfileRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setCustomerProfileId($customer_profile_id);
        $request->setCustomerPaymentProfileId($profile_id);

        $controller = new AnetController\DeleteCustomerPaymentProfileController($request);
        /** @var AnetAPI\DeleteCustomerPaymentProfileResponse $response */
        $response = $this->getResponseFromController($controller);
        if ($response->getMessages()->getResultCode() === 'Error') {
            throw new PaymentException('Unable to delete customer payment profile');
        }
    }

    /**
     * Handle transaction error and throw appropriate exception based on error code
     *
     * @param AnetAPI\TransactionResponseType\ErrorsAType\ErrorAType $error
     * @throws TransactionDeclinedException
     */
    protected function handleTransactionError($error)
    {
        $code = $error->getErrorCode();
        switch ($code) {
            case '2':
                throw new TransactionDeclinedException('Transaction declined for unspecified reason');
            case '8':
                throw new TransactionDeclinedException('Card is expired');
            case '27':
                throw new TransactionDeclinedException('Address provided does not match billing address of cardholder');
            case '45': // general error for AVS or card code mismatch based on our rejection settings in Authorize interface
                throw new TransactionDeclinedException('Billing address and/or card code is incorrect');
            case '65':
                throw new TransactionDeclinedException('Card code is incorrect');
            case '101':
                throw new TransactionDeclinedException('Name on account and/or the account type is incorrect');
        }

        $this->getLog()->error('Transaction declined', [
            'code' => $code,
            'text' => $error->getErrorText()
        ]);

        throw new TransactionDeclinedException('Transaction declined for unspecified reason');
    }

    /**
     * Charge payment profile
     *
     * @param string $customer_profile_id
     * @param string $profile_id
     * @param array $info
     * @return array
     * @throws PaymentException
     * @throws TransactionDeclinedException
     */
    public function chargePaymentProfile($customer_profile_id, $profile_id, array $info)
    {
        $payment_profile = new AnetAPI\PaymentProfileType();
        $payment_profile->setPaymentProfileId($profile_id);

        $customer_profile = new AnetAPI\CustomerProfilePaymentType();
        $customer_profile->setCustomerProfileId($customer_profile_id);
        $customer_profile->setPaymentProfile($payment_profile);

        $duplicate_window = new AnetAPI\SettingType;
        $duplicate_window->setSettingName('duplicateWindow');
        $duplicate_window->setSettingValue(App::debugEnabled() ? 15 : 60); // in seconds

        $transaction_request = new AnetAPI\TransactionRequestType();
        $transaction_request->setTransactionType('authCaptureTransaction');
        $transaction_request->setAmount($info['amount']);
        $transaction_request->setProfile($customer_profile);
        $transaction_request->addToTransactionSettings($duplicate_window);

        $request = new AnetAPI\CreateTransactionRequest();
        $request->setMerchantAuthentication($this->getMerchantAuthentication());
        $request->setTransactionRequest($transaction_request);

        $controller = new AnetController\CreateTransactionController($request);
        /** @var AnetAPI\CreateTransactionResponse $response */
        $response = $this->getResponseFromController($controller);
        $messages = $response->getMessages();
        $trans_response = $response->getTransactionResponse();
        if ($messages->getResultCode() === 'Error') {
            if (!empty($trans_response) && !empty(($errors = $trans_response->getErrors()))) {
                $this->handleTransactionError($errors[0]);
            }
            $messages = $messages->getMessage();
            if (count($messages) > 0) {
                throw new PaymentException('Request failed: %s [%s]', $messages[0]->getText(), $messages[0]->getCode());
            }
            throw new PaymentException('Unknown error encountered');
        }
        if (!empty($trans_response) && !empty(($trans_messages = $trans_response->getMessages()))) {
            return [
                'id' => $trans_response->getTransId(),
                'response_code' => $trans_response->getResponseCode(),
                'auth_code' => $trans_response->getAuthCode(),
                'code' => $trans_messages[0]->getCode(),
                'description' => $trans_messages[0]->getDescription()
            ];
        }
        if (!empty(($errors = $trans_response->getErrors()))) {
            $this->handleTransactionError($errors[0]);
        }
        throw new TransactionDeclinedException('Transaction declined for unspecified reason');
    }
}
