<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Form\Classes\Structure;
use App\Services\Form\Types\{CompanyType, SystemType};
use Core\Classes\Directory;
use Core\Components\Http\Interfaces\RouterInterface;
use Core\Exceptions\AppException;
use Core\StaticAccessors\{App, Path};

use function Core\Functions\env;

/**
 * Class StorageService
 *
 * @package App\Services
 */
class StorageService
{
    /**
     * Create necessary storage directories
     *
     * @throws AppException
     */
    public function createPaths(): void
    {
        $asset_path = env('APP_ASSET_PATH');
        if ($asset_path === null || !is_dir($asset_path)) {
            throw new AppException('App asset path is not valid: %s', $asset_path);
        }
        $link_path = rtrim(Path::get('assets'), DIRECTORY_SEPARATOR);
        if (!symlink($asset_path, $link_path)) {
            throw new AppException('Unable to create asset symlink');
        }
        $paths = [
            // assets
            'company',
            'emailMessageRawHtml',
            'emailMessageHtml',
            'importIngest',
            'importLoad',
            'googleStaticImage',
            'systemMedia',
            // caches
            'brandCache',
            'calendarFeedCache',
            'companyFormItemCache',
            'companyFormItemTemplateCache',
            'companySettingCache',
            'contentTemplateCache',
            'contentPartialCache',
            'domainCache',
            'drawingNodeTypeCache',
            'featureCache',
            'formItemCache',
            'pageNumberStampCache',
            'systemFormItemCache',
            'systemFormItemTemplateCache',
            'userSettingCache',
            // framework
            'frameworkCache',
            // misc
            'customReports',
            'locks',
            'logs',
            'temp'
        ];
        foreach ($paths as $name) {
            $path = Path::get($name);
            if (is_dir($path)) {
                continue;
            }
            if (!mkdir($path, 0755, true)) {
                throw new AppException('Unable to create storage path: %s', $path);
            }
        }
    }

    /**
     * Delete HTTP route cache file
     *
     * @throws AppException
     * @todo should probably move this to the core framework
     */
    public function clearRouteCache(): void
    {
        /** @var RouterInterface $router */
        $router = App::get(RouterInterface::class);
        if (($cache_file = $router->getRouteCacheFile()) === null) {
            return;
        }
        if (file_exists($cache_file) && !unlink($cache_file)) {
            throw new AppException('Unable to delete cache file: %s', $cache_file);
        }
    }

    /**
     * Clear all application caches
     *
     * @throws AppException
     */
    public function clearCaches(): void
    {
        $this->clearRouteCache();
        BrandService::clearCache();
        CompanyDrawingNodeTypeService::clearEntireCache();
        CompanyFeatureService::clearEntireCache();
        CompanySettingService::clearEntireCache();
        ContentPartialService::clearCache();
        ContentTemplateService::clearCache();
        CompanyType::clearEntireCache();
        DomainService::clearCache();
        Structure::clearEntireCache();
        SystemType::clearEntireCache();
    }

    /**
     * Clear compiled handlebars templates
     *
     * Useful when upgrading lightncandy and all templates need to be recreated.
     *
     * @throws AppException
     */
    public function clearHandlebarsTemplateCache(): void
    {
        ContentPartialService::clearCache();
        ContentTemplateService::clearCache();
        CompanyType::clearTemplateCache();
        SystemType::clearTemplateCache();
    }

    /**
     * Clear all files out of defined path
     *
     * @param string $name
     * @throws AppException
     */
    public function emptyPath(string $name): void
    {
        Directory::clear(Path::get($name));
    }

    /**
     * Clear and recreate entire storage directory
     *
     * @throws AppException
     */
    public function resetStorage(): void
    {
        if (SERVER_ROLE === 'PROD') {
            throw new AppException('Clearing storage only allowed in debug mode');
        }
        $storage_path = Path::get('storage');
        if (is_dir($storage_path)) {
            Directory::clear($storage_path);
        }
        $this->createPaths();
    }
}
