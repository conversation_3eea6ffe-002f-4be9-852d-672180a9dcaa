<?php

namespace App\Services\Email\Interfaces;

use Common\Models\EmailMessage;
use Ramsey\Uuid\UuidInterface;

/**
 * Interface MessageInterface
 *
 * @package App\Services\Email\Interfaces
 */
interface MessageInterface
{
    /**
     * Fill out instance from email message model
     *
     * @param EmailMessage $message
     * @return MessageInterface
     */
    public function hydrate(EmailMessage $message);

    /**
     * Get id
     *
     * @return \Ramsey\Uuid\UuidInterface
     */
    public function getID();

    /**
     * Set type
     *
     * @param int $type
     * @return $this
     */
    public function type($type);

    /**
     * Get type
     *
     * @return int
     */
    public function getType();

    /**
     * Set item id
     *
     * @param UuidInterface $item_id
     * @return $this
     */
    public function itemID(UuidInterface $item_id);

    /**
     * Get item id
     *
     * @return UuidInterface
     */
    public function getItemID();

    /**
     * Add recipient
     *
     * @param int $address_type
     * @param AddressInterface $address
     * @return $this
     */
    public function addRecipient($address_type, AddressInterface $address);

    /**
     * Get recipients by type
     *
     * If address type is null, then all recipients will be returned
     *
     * @param null|int $address_type
     * @return array|mixed
     */
    public function getRecipients($address_type = null);

    /**
     * Add TO recipient
     *
     * @param AddressInterface $recipient
     * @return $this
     */
    public function to(AddressInterface $recipient);

    /**
     * Add CC recipient
     *
     * @param AddressInterface $recipient
     * @return $this
     */
    public function cc(AddressInterface $recipient);

    /**
     * Add BCC recipient
     *
     * @param AddressInterface $recipient
     * @return $this
     */
    public function bcc(AddressInterface $recipient);

    /**
     * Set from address
     *
     * @param MaskedAddressInterface $from
     * @return $this
     */
    public function from(MaskedAddressInterface $from);

    /**
     * Get from address
     *
     * @return MaskedAddressInterface
     */
    public function getFrom();

    /**
     * Set reply to address
     *
     * @param AddressInterface $reply_to
     * @return $this
     */
    public function replyTo(AddressInterface $reply_to);

    /**
     * Get reply to address
     *
     * @return null|AddressInterface
     */
    public function getReplyTo();

    /**
     * Set subject
     *
     * @param string $subject
     * @return $this
     */
    public function subject($subject);

    /**
     * Get subject
     *
     * @return string
     */
    public function getSubject();

    /**
     * Set text
     *
     * @param string $text
     * @return $this
     */
    public function text($text);

    /**
     * Set text based on html
     *
     * Runs html content through a library to convert it to readable text
     *
     * @param string $html
     * @return $this
     */
    public function textFromHtml($html);

    /**
     * Get text
     *
     * @return string|null
     */
    public function getText();

    /**
     * Set html
     *
     * @param string $html
     * @param null|string $text
     * @return $this
     */
    public function html($html, $text = null);

    /**
     * Get html
     *
     * @return string|null
     */
    public function getHtml();

    /**
     * Set emorgified html
     *
     * @param string $html
     * @return $this
     */
    public function emorgifiedHtml($html);

    /**
     * Get emorgified html
     *
     * @return string|null
     */
    public function getEmorgifiedHtml();

    /**
     * Set individual header
     *
     * @param string $name
     * @param string $value
     * @return $this
     */
    public function header($name, $value);

    /**
     * Set multiple headers
     *
     * @param array $headers
     * @param bool $overwrite
     * @return $this
     */
    public function headers(array $headers, $overwrite = true);

    /**
     * Get all headers
     *
     * @return array
     */
    public function getHeaders();

    /**
     * Persist message with addresses to database
     *
     * @return void
     */
    public function save();
}
