<?php

declare(strict_types=1);

namespace App\Services\Email\Types\System;

use App\Services\Email\Classes\Address;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\SystemType;
use App\Services\TimeService;
use Common\Models\Company;
use Common\Models\EmailMessageAddress;
use Common\Models\EmailMessageRecipient;
use Common\Models\User;
use Core\Components\Http\StaticAccessors\View;

/**
 * Class EmailMessageRecipientBounceType
 *
 * @package App\Services\Email\Types\System
 */
class EmailMessageRecipientBounceType extends SystemType
{
    use NotificationTrait;

    public const TYPE_SUPPRESS_BOUNCE = 1;
    public const TYPE_BOUNCE = 2;
    public const TYPE_GENERIC = 3;
    public const TYPE_SUPPRESS_COMPLAINT = 4;
    public const TYPE_INBOX_FULL = 5;

    /**
     * Get types
     *
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            static::TYPE_SUPPRESS_BOUNCE, static::TYPE_SUPPRESS_COMPLAINT, static::TYPE_BOUNCE, static::TYPE_GENERIC,
            static::TYPE_INBOX_FULL
        ];
    }

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message|\App\Services\Email\Interfaces\MessageInterface
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        if (!isset($payload['type'])) {
            throw new TypeException('Type not defined');
        }
        if (!in_array($payload['type'], static::getTypes())) {
            throw new TypeException('Type is not valid');
        }

        /** @var EmailMessageRecipient $recipient */
        $recipient = EmailMessageRecipient::query()
            ->select([
                'emailMessageRecipients.*', 'emailMessageAddresses.address as recipientAddress',
                'emailMessageAddresses.name as recipientName'
            ])
            ->join('emailMessageAddresses', 'emailMessageAddresses.emailMessageAddressID', '=', 'emailMessageRecipients.emailMessageAddressID')
            ->whereKey($this->getNotificationItemID())
            ->where('emailMessageRecipients.status', EmailMessageRecipient::STATUS_FAILED)
            ->first();
        if ($recipient === null) {
            throw new TypeException('Unable to find email message recipient');
        }

        /** @var \Common\Models\EmailMessage $original_message */
        $original_message = $recipient->message;
        $addresses = $original_message->addresses()
            ->whereIn('addressType', [
                EmailMessageAddress::ADDRESS_TYPE_FROM, EmailMessageAddress::ADDRESS_TYPE_REPLY_TO
            ])
            // only allow company and user address types
            ->whereIn('type', [
                EmailMessageAddress::TYPE_USER, EmailMessageAddress::TYPE_COMPANY_FROM,
                EmailMessageAddress::TYPE_COMPANY_REPLY
            ])
            ->get()
            ->keyBy('addressType');
        if (count($addresses) === 0) {
            throw new TypeException('No from addresses found');
        }

        if (isset($addresses[EmailMessageAddress::ADDRESS_TYPE_REPLY_TO])) {
            $address = $addresses[EmailMessageAddress::ADDRESS_TYPE_REPLY_TO];
        } elseif (isset($addresses[EmailMessageAddress::ADDRESS_TYPE_FROM])) {
            $address = $addresses[EmailMessageAddress::ADDRESS_TYPE_FROM];
        } else {
            throw new TypeException('Unable to find reply to or from address');
        }

        switch ($address->type) {
            case EmailMessageAddress::TYPE_COMPANY_FROM:
            case EmailMessageAddress::TYPE_COMPANY_REPLY:
                $item = Company::where('companyUUID', $address->itemID)
                    ->select('companies.companyID')
                    ->withTimezone()
                    ->first();
                break;
            case EmailMessageAddress::TYPE_USER:
                $item = User::where('userUUID', $address->itemID)
                    ->select('user.companyID')
                    ->withTimezone()
                    ->first();
                break;
        }

        $domain = $this->getDomainService()->findByCompanyID($item->companyID);

        $this->setup($domain);

        $message = $this->getMessage();
        $message->itemID($recipient->getUuidKey());
        $message->to(Address::makeFromModel($address)->replicate());
        $message->subject('Email Delivery Failed');

        $time_service = new TimeService();
        $time_service->setTimezone($item->timezone);

        $template_vars = [
            'name' => $recipient->recipientName,
            'email' => $recipient->recipientAddress,
            'date' => $time_service->getFromUtc($original_message->createdAt)->format('n/j/Y g:ia T'),
            'subject' => $original_message->subject
        ];

        $template_map = [
            static::TYPE_SUPPRESS_BOUNCE => 'bounce',
            static::TYPE_SUPPRESS_COMPLAINT => 'complaint',
            static::TYPE_BOUNCE => 'bounce',
            static::TYPE_GENERIC => 'generic',
            static::TYPE_INBOX_FULL => 'inbox_full'
        ];
        $template = $template_map[$payload['type']];

        $this->template->content = View::fetch("emails.html.system.email-message-bounce.{$template}", $template_vars)->render();

        $message->html($this->template->render(), View::fetch("emails.text.system.email-message-bounce.{$template}", $template_vars)->render());

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
