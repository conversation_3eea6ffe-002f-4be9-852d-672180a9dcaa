<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\CompanySubscriptionService;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Common\Models\CompanyInvoice;
use Ramsey\Uuid\Uuid;

/**
 * Class PaymentFailedType
 *
 * Notify all primary users of company their latest payment failed
 *
 * @package App\Services\Email\Types\User
 */
class PaymentFailedType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        if (!isset($payload['invoice_id'])) {
            throw new TypeException('Invoice ID not defined in payload');
        }
        if (!isset($payload['error'])) {
            throw new TypeException('No error reason defined in payload');
        }

        if (($invoice = CompanyInvoice::find($payload['invoice_id'])) === null) {
            throw new TypeException('Unable to find invoice: %s', $payload['invoice_id']);
        }

        $this->setup($invoice->company, $this->getPrimaryUsers($invoice->company));

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($invoice->companyInvoiceUUID));
        $message->subject('Payment Failed');

        $domain = $this->getDomain();
        $template_vars = [
            'error' => $payload['error'],
            'suspension_days' => CompanySubscriptionService::DAYS_UNTIL_SUSPENSION,
            'suspension_warning' => !isset($payload['suspension_warning']) || $payload['suspension_warning'],
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilder()->path('company/account')->build()
        ];
        $this->template->content = Template::fetch('emails.html.user.payment-failed', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.payment-failed', $template_vars));

        return $message;
    }
}
