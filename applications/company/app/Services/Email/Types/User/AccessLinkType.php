<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\AuthService;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\UserType;
use Common\Models\UserAccessToken;

/**
 * Class AccessLinkType
 *
 * @package App\Services\Email\Types\User
 */
class AccessLinkType extends UserType
{
    use NotificationTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message|\App\Services\Email\Interfaces\MessageInterface
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        if (($access_token = UserAccessToken::withTrashed()->whereKey($this->getNotificationItemID())->first()) === null) {
            throw new TypeException('Unable to find user access token');
        }

        $user = $access_token->user;
        $this->setup($user->company, [$user]);

        $message = $this->getMessage();
        $message->itemID($access_token->getUuidKey());

        $domain = $this->getDomain();
        $brand_name = $domain['brand']['name'];

        // if user doesn't have a password, they are new
        if ($user->userPassword === null) {
            $subject = 'Accept Your Invitation to ' . $brand_name;
            $template = 'new-user';
        } else {
            $subject = 'Please Create a New Password';
            $template = 'reset-password';
        }

        $message->subject($subject);

        $link = (new AuthService())->getUserAccessLink($access_token)
            ->host($domain['domain'])
            ->build();

        $template_vars = [
            'is_invited' => $user->isUserInvited,
            'first_name' => $access_token->user->userFirstName,
            'brand_name' => $brand_name,
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $link,
            'user_profile_settings_link' => 'https://cxlratr.to/hc-user-profile',
            'app_link' => $domain['domain'],
            'bookmark_link' => 'https://cxlratr.to/hc-bookmark'
        ];

        $this->template->content = Template::fetch("emails.html.user.access-link.{$template}", $template_vars);

        $message->html($this->template->render(), Template::fetch("emails.text.user.access-link.{$template}", $template_vars));

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
