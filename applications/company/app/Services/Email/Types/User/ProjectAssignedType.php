<?php

namespace App\Services\Email\Types\User;

use App\Classes\Acl;
use App\Classes\Template;
use App\Resources\ProjectResource;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Common\Models\Project;
use Common\Models\User;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Ramsey\Uuid\Uuid;

/**
 * Class ProjectAssignedType
 *
 * @package App\Services\Email\Types\User
 */
class ProjectAssignedType extends UserType
{
    use NotificationTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        $project_item_lookup = Project::query()->where('projectUUID', $this->getNotificationItemID())
            ->first(['projectID']);
        if ($project_item_lookup === null) {
            throw new TypeException('Unable to find project item id: %s', $this->getNotificationItemID());
        }

        try {
            $project_scope = Scope::make()
                ->fields([
                    'id', 'project_uuid', 'company_id', 'description', 'salesperson_user_id',
                    'updated_by_user_name', 'reference_id'
                ])
                ->with([
                    'property' => [
                        'fields' => ['property_id', 'customer_id'],
                        'with' => [
                            'customer' => [
                                'fields' => ['company_id', 'first_name', 'last_name', 'business_name']
                            ]
                        ]
                    ]
                ]);
            $project_item = ProjectResource::make(Acl::make())
                ->entity($project_item_lookup->projectID)
                ->scope($project_scope)
                ->run();
        } catch (EntityNotFoundException $e) {
            throw new TypeException('Unable to find project item: %s', $project_item_lookup->projectID);
        }

        $company = Company::find($project_item->get('property.customer.company_id'));
        if ($company === null) {
            throw new TypeException('Unable to find company');
        }

        $user = User::find($project_item->get('salesperson_user_id'));
        if ($user === null) {
            throw new TypeException('Unable to find user');
        }

        $this->setup($company, [$user]);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromString($project_item['project_uuid']));

        $message->subject("A Project Has Been Assigned To You:  {$project_item['description']} (#{$project_item['reference_id']})");

        $business_display = '';
        if ($project_item->get('property.customer.business_name') !== null) {
            $business_display = ' (' . $project_item->get('property.customer.business_name') . ')';
        }

        $domain = $this->getDomain();
        $template_vars = [
            'customer_name' => $project_item->get('property.customer.first_name') . ' ' . $project_item->get('property.customer.last_name') . $business_display,
            'project_name' => $project_item['description'] . ' (#' . $project_item['reference_id'] . ')',
            'assigned_by_user' => $project_item['updated_by_user_name'],
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilder()->path("projects/{$project_item_lookup->projectID}")->build(),
            'unsubscribe_link' => $this->newUrlBuilder()->path("user/profile")->build()
        ];

        $this->template->content = Template::fetch('emails.html.user.project-assigned', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.project-assigned', $template_vars));

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
