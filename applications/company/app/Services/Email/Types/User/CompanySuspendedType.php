<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\CompanyService;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

/**
 * Class CompanySuspendedType
 *
 * Notify all primary users of company their account is now suspended
 *
 * @package App\Services\Email\Types\User
 */
class CompanySuspendedType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws AppException
     * @throws TypeException
     */
    public function build(array $payload)
    {
        if (!isset($payload['company_id'])) {
            throw new TypeException('Company ID not defined in payload');
        }
        if (($company = Company::find($payload['company_id'])) === null) {
            throw new TypeException('Unable to find company: %s', $payload['company_id']);
        }

        $this->setup($company, $this->getPrimaryUsers($company));

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($company->companyUUID));
        $message->subject('Account Suspended');

        $domain = $this->getDomain();
        $template_vars = [
            'brand_name' => $domain['brand']['name'],
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'days' => CompanyService::DAYS_UNTIL_DORMANT,
            'link' => $this->newUrlBuilder()->path('company/account/subscription')->build()
        ];
        $this->template->content = Template::fetch('emails.html.user.company-suspended', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.company-suspended', $template_vars));

        return $message;
    }
}
