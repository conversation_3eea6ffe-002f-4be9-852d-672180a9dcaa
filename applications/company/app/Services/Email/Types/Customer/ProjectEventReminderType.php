<?php

namespace App\Services\Email\Types\Customer;

use App\Classes\Template;
use App\Services\CompanySettingService;
use App\Services\Email\Addresses\UserAddress;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Traits\Type\ProjectContactsTrait;
use App\Services\Email\Traits\Type\UserTrait;
use App\Services\Email\Types\CustomerType;
use App\Services\WisetackService;
use Carbon\Carbon;
use Common\Models\EmailTemplate;
use Common\Models\ProjectSchedule;
use Common\Models\WisetackMerchant;
use Core\Components\Http\StaticAccessors\URI;
use Ramsey\Uuid\Uuid;

/**
 * Class ProjectEventReminderType
 *
 * @package App\Services\Email\Types\Customer
 */
class ProjectEventReminderType extends CustomerType
{
    use NotificationTrait;
    use ProjectContactsTrait;
    use UserTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        $event = ProjectSchedule::where('projectScheduleUUID', $this->getNotificationItemID())->first();
        if ($event === null) {
            throw new TypeException('Unable to find event');
        }

        $property = $event->project->property;
        $customer = $property->customer;
        $scheduled_user = $event->scheduledUser;
        $primary_phone = $scheduled_user->primaryPhone;

        $this->setup($customer);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($event->projectScheduleUUID));

        $setting_service = new CompanySettingService($this->company->companyID);
        $email_greeting = $setting_service->get('email_greeting', null);

        $type = $event->scheduleType === ProjectSchedule::TYPE_EVALUATION ? EmailTemplate::TYPE_SALES_APPOINTMENT_REMINDER : EmailTemplate::TYPE_INSTALLATION_APPOINTMENT_REMINDER;

        $email_template_data = EmailTemplate::where('ownerID', $this->company->companyID)
            ->where('type', $type)
            ->first();

        if (
            $email_template_data->isSendFromSalesperson &&
            $event->project->salesperson !== null
        ) {
            $salesperson = (new UserAddress())->fromModel($event->project->salesperson);
            $message->from($salesperson);
            $message->replyTo(clone $salesperson);
        } else {
            $message->from($this->getCompanyFromAddress());
            $message->replyTo($this->getCompanyReplyAddress());
        }

        $this->addProjectContacts($event->project, $message);

        $property_address = $property->address;
        $property_address .= $property->address2 !== null ? ', ' . $property->address2 : '';

        $subject = Template::replace($email_template_data->subject, [
            'company_name' => $this->company->name,
            'address' => $property_address
        ]);
        $message->subject($subject);

        // @todo convert to scheduledUser timezone after scheduled start is converted to UTC
        $scheduled_start = Carbon::parse($event->scheduledStart);
        $start_midnight = $scheduled_start->copy()->startOfDay();
        $start_time = '';
        if ($scheduled_start->ne($start_midnight)) {
            $start_time = $scheduled_start->format('g:i a');
        }

        $scheduled_end = Carbon::parse($event->scheduledEnd);
        $end_midnight = $scheduled_start->copy()->endOfDay();
        $end_time = '';
        if ($scheduled_end->ne($end_midnight)) {
            $end_time = $scheduled_end->format('g:i a');
        }

        if (($user_photo = $this->getUserImageUrl($scheduled_user)) !== null) {
            $user_photo = '<img alt="Employee Photo" style="border:1px solid #151719;max-height:180px;" src="' . $user_photo . '" />';
        }

        $content = $email_template_data->content;

        switch ($event->scheduleType) {
            case ProjectSchedule::TYPE_EVALUATION:
                $vars = [
                    'evaluatorFirstName' => $scheduled_user->userFirstName,
                    'evaluatorLastName' => $scheduled_user->userLastName,
                    'evaluatorBio' => $scheduled_user->userBio,
                    'evaluatorEmail' => $scheduled_user->userEmail,
                    'evaluatorPhone' => $primary_phone->phoneNumber,
                    'evaluatorPicture' => $user_photo,
                    'date_time' => $scheduled_start->format('l, F j, Y') . ' at ' . $start_time,
                    'date' => $scheduled_start->format('l, F j, Y'),
                    'time' => $start_time,
                    'end_time' => $end_time,
                    'address' => "{$property->address}, {$property->city}, {$property->state} {$property->zip}"
                ];
                $template = 'emails.html.customer.project-event-reminder.evaluation';
                break;
            case ProjectSchedule::TYPE_INSTALLATION:
                $vars = [
                    'installerFirstName' => $scheduled_user->userFirstName,
                    'installerLastName' => $scheduled_user->userLastName,
                    'installerBio' => $scheduled_user->userBio,
                    'installerEmail' => $scheduled_user->userEmail,
                    'installerPhone' => $primary_phone->phoneNumber,
                    'installerPicture' => $user_photo,
                    'date_time' => $scheduled_start->format('l, F j, Y') . ' at ' . $start_time,
                    'date' => $scheduled_start->format('l, F j, Y'),
                    'time' => $start_time,
                    'end_time' => $end_time,
                    'address' => "{$property->address}, {$property->city}, {$property->state} {$property->zip}"
                ];
                $template = 'emails.html.customer.project-event-reminder.installation';
                break;
        }

        if (strpos($content, '{wisetack_prequal_link}') !== false) {
            $vars["wisetack_prequal_link"] = "";
            $merchant = WisetackMerchant::where('companyUUID', $this->company->companyUUID)->whereNull('deletedAt')->first();

            if ($merchant) {
                $is_wisetack_financing_enabled = WisetackService::isWisetackFinancingEnabled($this->company->companyID, $merchant, $event->project);

                if ($is_wisetack_financing_enabled) {
                    $url = URI::route('wisetack.prequals.project.redirect', ['id' => $event->project->getProjectUUIDString()])->build();
                    $vars["wisetack_prequal_link"] = "<p><a href=\"{$url}\">Click Here</a> to prequalify for financing through Wisetack.</p>";
                }
            }

        }

        $content = Template::replace($content, $vars);
        $this->template->content = Template::fetch($template, [
            'greeting' => $email_greeting ?: 'Hello',
            'first_name' => $customer->firstName,
            'content' => $content,
            'company_logo' => $this->getCompanyLogoUrl()
        ]);
        $message->html($this->template->render());
        $message->textFromHtml($content);

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
