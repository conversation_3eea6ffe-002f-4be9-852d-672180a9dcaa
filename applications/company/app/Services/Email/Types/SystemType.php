<?php

namespace App\Services\Email\Types;

use App\Classes\Template;
use App\Services\Email\Addresses\BrandFromAddress;
use App\Services\Email\Addresses\BrandReplyAddress;
use App\Services\Email\Classes\Type;
use App\Services\Email\Traits\Type\DomainTrait;
use Common\Models\Domain;
use Core\Exceptions\AppException;

/**
 * Class SystemType
 *
 * Base class for sending email to guests
 *
 * @package App\Services\Email\Types
 */
abstract class SystemType extends Type
{
    use DomainTrait;

    /**
     * @var Template
     */
    protected $template;

    /**
     * Since this email type gets a domain passed into setup, we shouldn't need to load it via other data
     *
     * @throws AppException
     */
    protected function loadDomain()
    {
        throw new AppException('Domain not loaded');
    }

    /**
     * @param Domain $domain Should be loaded from DomainService
     * @throws AppException
     */
    public function setup(Domain $domain)
    {
        $this->setDomain($domain);

        $message = $this->getMessage();

        $message->from((new BrandFromAddress())->fromModel($domain->brand));
        $message->replyTo((new BrandReplyAddress())->fromModel($domain->brand));

        $this->template = new Template('emails.html.layouts.system');
        $vars = [
            'brand' => [
                'color' => "#{$domain['brand']['mailColor']}",
                'name' => $domain['brand']['name'],
                'logo' => $this->newUrlBuilder()->path("assets/brands/{$domain['brand']['slug']}/email_logo.png")->build(),
                'base_url' => $this->newUrlBuilder()->build(),
                'domain' => $domain['domain']
            ]
        ];
        $this->template->setVars($vars);
    }
}
