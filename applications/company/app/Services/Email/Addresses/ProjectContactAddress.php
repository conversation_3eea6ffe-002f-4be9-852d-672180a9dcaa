<?php

namespace App\Services\Email\Addresses;

use App\Services\Email\Classes\Address;
use Common\Models\ProjectEmail;
use Ramsey\Uuid\Uuid;

/**
 * Class ProjectContactAddress
 *
 * @package App\Services\Email\Addresses
 */
class ProjectContactAddress extends Address
{
    /**
     * Fill out instance based on project email model
     *
     * @param ProjectEmail $contact
     * @return $this
     */
    public function fromModel(ProjectEmail $contact)
    {
        $this->setAddress($contact->email);
        $this->setName($contact->name);
        $this->setType(static::TYPE_PROJECT_CONTACT);
        $this->setItemID(Uuid::fromBytes($contact->projectEmailUUID));

        return $this;
    }
}
