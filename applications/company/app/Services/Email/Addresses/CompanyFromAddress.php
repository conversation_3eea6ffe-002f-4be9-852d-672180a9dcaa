<?php

namespace App\Services\Email\Addresses;

use App\Services\Email\Classes\MaskedAddress;
use App\Services\Email\Interfaces\SmtpSettingProviderInterface;
use App\Services\Email\Traits\Address\SmtpSettingProviderTrait;
use Common\Models\Company;
use Common\Models\EmailMessageAddress;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

/**
 * Class CompanyFromAddress
 *
 * @package App\Services\Email\Addresses
 */
class CompanyFromAddress extends MaskedAddress implements SmtpSettingProviderInterface
{
    use SmtpSettingProviderTrait;

    /**
     * @var null|Company
     */
    protected $company = null;

    /**
     * Set company
     *
     * @param Company $company
     */
    public function setCompany(Company $company)
    {
        $this->company = $company;
    }

    /**
     * Get company
     *
     * @return Company
     * @throws AppException
     */
    public function getCompany()
    {
        if ($this->company === null) {
            throw new AppException('No company defined');
        }
        return $this->company;
    }

    /**
     * Hydrate instance from email message address model
     *
     * Set company for use to source SMTP data as needed
     *
     * @param EmailMessageAddress $address
     * @return $this
     * @throws AppException
     */
    public function hydrate(EmailMessageAddress $address)
    {
        if (($company = Company::where('companyUUID', $address->itemID)->first()) === null) {
            throw new AppException('Unable to find company');
        }
        $this->setCompany($company);
        parent::hydrate($address);
        return $this;
    }

    /**
     * Fill in instance from company model
     *
     * @param Company $company
     * @return $this
     */
    public function fromModel(Company $company)
    {
        $this->setAddress($company->emailFrom);
        $this->setName($company->name);
        $this->setType(static::TYPE_COMPANY_FROM);
        $this->setItemID(Uuid::fromBytes($company->companyUUID));

        $this->setCompany($company);
        return $this;
    }

    /**
     * Load SMTP settings based on company data
     *
     * If company doesn't have a SMTP credential or it's disabled
     *
     * @return array
     * @throws AppException
     */
    protected function loadSmtpSettings()
    {
        $company = $this->getCompany();
        // if company as credentials assigned, use them. otherwise pull from brand through domain assignment for company
        if ($company->smtpCredentialID !== null && $company->smtpCredential->isEnabled) {
            $credential = $company->smtpCredential;
        } else {
            $domain = $this->getDomainService()->findByCompanyID($company->getKey());
            $credential = $domain->brand->smtpCredential;
        }
        return $this->getSmtpSettingsFromModel($credential);
    }

    /**
     * Get mail domain for masking
     *
     * @return string
     * @throws AppException
     */
    protected function getMailDomain(): string
    {
        $domain = $this->getDomainService()->findByCompanyID($this->getCompany()->getKey());
        return $domain->brand->mailDomain;
    }
}
