<?php

namespace App\Services;

use App\Classes\Acl;
use App\Exceptions\ServiceException;
use App\Resources\Bid\ContentResource;
use App\Resources\Bid\ItemResource;
use App\Resources\ContentTemplateResource;
use Common\Models\ContentTemplate;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\Scope;

/**
 * Class BidItemUpgradeService
 *
 * @package App\Services
 */
class BidItemUpgradeService
{
    /**
     * @var Acl
     */
    protected $acl;

    /**
     * @var string Bid item uuid
     */
    protected $item_id;

    /**
     * BidItemUpgradeService constructor
     *
     * @param Acl $acl
     * @param string $item_id
     */
    public function __construct(Acl $acl, $item_id)
    {
        $this->acl = $acl;
        $this->item_id = $item_id;
    }

    /**
     * Handle upgrade logic, separated into another method so we don't have to work inside the transaction closure
     *
     * @param ItemResource $resource
     * @param Entity $entity
     * @throws ServiceException
     * @throws \Core\Exceptions\AppException
     */
    protected function handleUpgrade(ItemResource $resource, Entity $entity)
    {
        if (!in_array($entity->status, [ItemResource::STATUS_INCOMPLETE, ItemResource::STATUS_SUBMITTED])) {
            throw new ServiceException('Upgrade only allowed for incomplete or submitted bids');
        }
        if ($entity->version === ItemResource::CURRENT_VERSION) {
            throw new ServiceException('Bid already updated to latest version');
        }

        $bid_item = [
            'id' => $this->item_id,
            'version' => ItemResource::CURRENT_VERSION
        ];

        // if version is less than two, then we need to load in the terms and conditions defaults
        if ($entity->version < 2) {
            // copy over default terms and condition template id
            $content_type = ContentTemplate::TYPE_BID_TERMS_CONDITIONS;
            $types = ContentTemplateResource::make($this->acl)->getDefaultsByType([$content_type]);
            if (isset($types[$content_type])) {
                $bid_item['terms_conditions_content_template_id'] = $types[$content_type];
            }

            // copy over default bid content
            $content_resource = ContentResource::make($this->acl);
            $content_resource->createDefaultsForBidItem($entity->id);
        }

        // update private version and any extra fields on bid item
        $resource->withAccessLevel(Field::ACCESS_LEVEL_PRIVATE, function (ItemResource $resource) use ($bid_item) {
            $resource->partialUpdate(Entity::make($bid_item))->run();
        });
    }

    /**
     * Run upgrade for bid
     *
     * @todo maybe add better exception handling
     *
     * @throws \Core\Components\Resource\Exceptions\ActionNotAllowedException
     * @throws \Core\Components\Resource\Exceptions\EntityNotFoundException
     */
    public function run()
    {
        $resource = ItemResource::make($this->acl);
        $scope = Scope::make()
            ->fields(['id', 'version', 'status']);
        $entity = $resource->entity($this->item_id)->scope($scope)->run();
        DB::transaction(function () use ($resource, $entity) {
            $this->handleUpgrade($resource, $entity);
        });
    }
}
