<?php

declare(strict_types=1);

namespace App\Services;

use App\Resources\Drawing\NodeResource;
use Common\Models\DrawingNodeType;
use Core\Classes\Directory;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Php<PERSON>ars<PERSON>\Node;

/**
 * Class CompanyDrawingNodeTypeService
 *
 * @package App\Services
 */
class CompanyDrawingNodeTypeService
{
    /**
     * @var int
     */
    protected int $company_id;

    /**
     * @var int Drawing version
     */
    protected int $version;

    /**
     * @var array|null Types cache
     */
    protected ?array $types = null;

    /**
     * Clear all drawing node type cache files
     *
     * @throws AppException
     */
    public static function clearEntireCache(): void
    {
        Directory::clear(Path::get('drawingNodeTypeCache'));
    }

    /**
     * CompanyDrawingNodeTypeService constructor
     *
     * @param int $company_id
     * @param int $version
     */
    public function __construct(int $company_id, int $version)
    {
        $this->company_id = $company_id;
        $this->version = $version;
    }

    /**
     * Get available node icons by version
     *
     * Not all nodes have associated icons, so this is the official list of which ones exist on the file system for
     * each version.
     *
     * @param int $version
     * @return array
     */
    protected function getAvailableNodeIconsByVersion(int $version): array
    {
        $type_icon_paths = [
            NodeResource::TYPE_CARBON_FIBER => 'carbon-fiber',
            NodeResource::TYPE_CUSTOM_SERVICE => 'custom-service',
            NodeResource::TYPE_DEADMAN => 'deadman',
            NodeResource::TYPE_PIER => 'exterior-pier',
            NodeResource::TYPE_FLOOR_CRACK => 'floor-crack',
            NodeResource::TYPE_INTERIOR_DRAIN_TILE => 'interior-drain-tile',
            NodeResource::TYPE_INTERIOR_PIER => 'interior-pier',
            NodeResource::TYPE_MUDJACKING => 'mudjacking',
            NodeResource::TYPE_POLYFOAM => 'polyfoam',
            NodeResource::TYPE_SOIL_ANCHOR => 'soil-anchor',
            NodeResource::TYPE_SUMP_PUMP => 'sump-pump',
            NodeResource::TYPE_SUPPORT_POST => 'support-post',
            NodeResource::TYPE_WALL => 'foundation',
            NodeResource::TYPE_WALL_BRACE => 'wall-brace',
            NodeResource::TYPE_WALL_CRACK => 'wall-crack'
        ];
        if ($version === 2) {
            $type_icon_paths = $type_icon_paths + [
                NodeResource::TYPE_EXTERIOR_DRAIN => 'exterior-drain',
                NodeResource::TYPE_INTERIOR_WALL => 'interior-wall',
                NodeResource::TYPE_FURNACE => 'furnace',
                NodeResource::TYPE_AC => 'ac',
                NodeResource::TYPE_WATER_HEATER => 'water-heater',
                NodeResource::TYPE_BEAM => 'beam',
                NodeResource::TYPE_LINTEL_REPAIR => 'lintel-repair',
                NodeResource::TYPE_STAIRS => 'stairs',
                NodeResource::TYPE_DEHUMIDIFIER => 'dehumidifier',
                NodeResource::TYPE_DOOR => 'door',
                NodeResource::TYPE_WINDOW => 'window',
                NodeResource::TYPE_EXTERIOR_WALL => 'exterior-wall',
                NodeResource::TYPE_FENCE => 'fence',
                NodeResource::TYPE_EXISTING_FENCE => 'existing-fence',
                NodeResource::TYPE_GATE => 'gate',
                NodeResource::TYPE_UTILITY => 'utility',
                NodeResource::TYPE_CONCRETE => 'concrete',
                NodeResource::TYPE_ENCAPSULATION => 'encapsulation',
                NodeResource::TYPE_COMPASS => 'compass',
                NodeResource::TYPE_VEGETATION => 'vegetation',
                NodeResource::TYPE_GARAGE_DOOR => 'garage-door',
                NodeResource::TYPE_DOUBLE_DOOR => 'double-door',
                NodeResource::TYPE_SLIDING_DOOR => 'sliding-door',
                NodeResource::TYPE_DOUBLE_GATE => 'double-gate',
                NodeResource::TYPE_POLYGON => 'polygon',
                NodeResource::TYPE_REFERENCE_POINT => 'reference-point',
                NodeResource::TYPE_DOWNSPOUT => 'downspout',
                NodeResource::TYPE_DRY_WELL => 'dry-well',
                NodeResource::TYPE_CATCH_BASIN => 'catch-basin',
                NodeResource::TYPE_CMU => 'cmu',
                NodeResource::TYPE_BASEMENT_WINDOW_WELL => 'basement-well',
                NodeResource::TYPE_EGRESS_WINDOW_WELL => 'egress-well',
                NodeResource::TYPE_FRENCH_DRAIN => 'french-drain',
                NodeResource::TYPE_VAPOR_BARRIER => 'vapor-barrier',
                NodeResource::TYPE_ROOT_BARRIER => 'root-barrier',
                NodeResource::TYPE_INTERIOR_DRAIN => 'interior-drain',
                NodeResource::TYPE_FLOOR_JOIST => 'floor-joist',
                NodeResource::TYPE_JOINT_SEALANT => 'joint-sealant',
                NodeResource::TYPE_FENCE_POST => 'fence-post',
                NodeResource::TYPE_SINGLE_CANTILEVER => 'single-cantilever',
                NodeResource::TYPE_DOUBLE_CANTILEVER => 'double-cantilever',
                NodeResource::TYPE_BEAM_REPLACEMENT => 'beam-replacement',
                NodeResource::TYPE_TRENCH_DRAIN => 'trench-drain',
                NodeResource::TYPE_TOILET => 'toilet',
                NodeResource::TYPE_SHOWER => 'shower',
                NodeResource::TYPE_BATH_TUB => 'bath-tub',
                NodeResource::TYPE_CABINET => 'cabinet',
                NodeResource::TYPE_KITCHEN_SINK => 'kitchen-sink',
                NodeResource::TYPE_BATTERY_BACKUP => 'battery-backup',
                NodeResource::TYPE_PATIO => 'patio',
                NodeResource::TYPE_DECK => 'deck',
                NodeResource::TYPE_SIDEWALK => 'sidewalk',
                NodeResource::TYPE_FLOOR_VENT => 'floor-vent',
                NodeResource::TYPE_AIR_DUCT => 'air-duct',
                NodeResource::TYPE_FOOTING => 'footing',
                NodeResource::TYPE_POOL_ROUND => 'pool-round',
                NodeResource::TYPE_POOL_RECTANGLE => 'pool-rectangle',
                NodeResource::TYPE_SPRINKLER_HEAD => 'sprinkler-head',
                NodeResource::TYPE_SPRINKLER_LINE => 'sprinkler-line',
                NodeResource::TYPE_RANGE => 'range',
                NodeResource::TYPE_WASHER_DRYER => 'washer-dryer',
                NodeResource::TYPE_DISHWASHER => 'dishwasher',
                NodeResource::TYPE_SINK => 'sink',
                NodeResource::TYPE_DOUBLE_SINK => 'double-sink',
                NodeResource::TYPE_FLOOR_DRAIN => 'floor-drain',
                NodeResource::TYPE_OPENING => 'opening',
                NodeResource::TYPE_FIREPLACE => 'fireplace',
                NodeResource::TYPE_UTIILTY_LINE => 'utility-line',
                NodeResource::TYPE_GAS => 'gas',
                NodeResource::TYPE_ELECTRICAL => 'electrical',
                NodeResource::TYPE_WATER => 'water',
                NodeResource::TYPE_RETAINING_WALL => 'retaining-wall'
            ];
        }
        return array_map(fn($path) => $path . ($this->version === 2 ? '.svg' : '.png'), $type_icon_paths);
    }

    /**
     * Get path to cache file
     *
     * @return string
     */
    protected function getCacheFile(): string
    {
        return Path::drawingNodeTypeCache("{$this->company_id}_{$this->version}.php");
    }

    /**
     * Get all drawing node types for company and drawing version
     *
     * @return array
     * @throws AppException
     */
    public function all(): array
    {
        if ($this->types === null) {
            // @todo implement proper caching layer in framework and move the data storage to a memory based cache
            $cache_file = $this->getCacheFile();
            if (file_exists($cache_file)) {
                $types = include $cache_file;
                if (!is_array($types)) {
                    throw new AppException('Drawing node types cache file corrupted: %s', $cache_file);
                }
                $this->types = $types;
            } else {
                $this->types = [];
                $type_icon_paths = $this->getAvailableNodeIconsByVersion($this->version);
                DrawingNodeType::query()
                    ->select(['drawingNodeTypes.drawingNodeTypeID'])
                    ->selectRaw('IFNULL(cdnt.name, drawingNodeTypes.name) as name')
                    ->selectRaw('IFNULL(cdnt.showInLegend, drawingNodeTypes.showInLegend) as showInLegend')
                    ->leftJoin('companyDrawingNodeTypes as cdnt', function ($join) {
                        $join->on('cdnt.drawingNodeTypeID', '=', 'drawingNodeTypes.drawingNodeTypeID')
                            ->where('cdnt.companyID', $this->company_id);
                    })
                    ->whereIn('drawingNodeTypes.drawingNodeTypeID', array_keys($type_icon_paths))
                    ->where('drawingNodeTypes.version', '<=', $this->version)
                    ->where('drawingNodeTypes.isInUse', true)
                    ->having('showInLegend', true)
                    ->each(function ($type) use ($type_icon_paths) {
                        $this->types[$type->drawingNodeTypeID] = [
                            'name' => $type->name,
                            'path' => $type_icon_paths[$type->drawingNodeTypeID]
                        ];
                    });
                $cache_data = '<' . '?php return ' . var_export($this->types, true) . ';' . PHP_EOL;
                if (file_put_contents($cache_file, $cache_data) === false) {
                    throw new AppException('Unable to save cache file: %s', $cache_file);
                }
            }
        }
        return $this->types;
    }

    /**
     * Clear cache for company
     *
     * Deletes cache file from system so it can be refreshed on next request
     *
     * @throws AppException
     */
    public function clearCache(): void
    {
        $cache_file = $this->getCacheFile();
        if (!file_exists($cache_file)) {
            return;
        }
        if (!unlink($cache_file)) {
            throw new AppException('Unable to delete cache file: %s', $cache_file);
        }
    }
}
