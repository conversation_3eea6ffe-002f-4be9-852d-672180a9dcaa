<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Objects;

use App\Services\HubspotApi\Classes\BaseObject;
use App\Services\HubspotApi\Entities\CompanyEntity;
use HubSpot\Client\Crm\Companies\Model\{Error, SimplePublicObject, SimplePublicObjectInput};
use HubSpot\Discovery\Crm\Companies\Discovery;

/**
 * Class CompanyObject
 *
 * @package App\Services\HubspotApi\Objects
 */
class CompanyObject extends BaseObject
{
    /**
     * Get client instance
     *
     * @return Discovery
     */
    protected function getClient(): Discovery
    {
        return $this->service->getClient()->crm()->companies();
    }

    /**
     * Determine if API response is an error
     *
     * @param mixed $response
     * @return bool
     */
    protected function isErrorResponse(mixed $response): bool
    {
        return is_object($response) && $response instanceof Error;
    }

    /**
     * Find individual company by id
     *
     * @param string $id
     * @param array|null $properties
     * @return CompanyEntity|null
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function find(string $id, array $properties = null): ?CompanyEntity
    {
        if ($properties !== null) {
            $properties = implode(',', $properties);
        }
        /** @var SimplePublicObject $object */
        $object = $this->callApi(fn(Discovery $client) => $client->basicApi()->getById($id, $properties));
        return CompanyEntity::fromSimplePublicObject($object);
    }

    /**
     * Create company
     *
     * @param array $properties
     * @return CompanyEntity
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function create(array $properties): CompanyEntity
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $object = $this->callApi(fn(Discovery $client) => $client->basicApi()->create($input));
        return CompanyEntity::fromSimplePublicObject($object);
    }

    /**
     * Update company
     *
     * @param string $id
     * @param array $properties
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function update(string $id, array $properties): void
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $this->callApi(fn(Discovery $client) => $client->basicApi()->update($id, $input));
    }
}
