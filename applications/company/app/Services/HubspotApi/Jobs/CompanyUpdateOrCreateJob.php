<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Jobs;

use App\Attributes\JobAttribute;
use App\Services\HubspotApi\Exceptions\Api\ServiceException;
use App\Services\HubspotApiService;
use Common\Models\Company;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Throwable;

/**
 * Class CompanyUpdateOrCreateJob
 *
 * @package App\Services\HubspotApi\Jobs
 */
#[JobAttribute(type: 39, channel: 'hubspot-api')]
class CompanyUpdateOrCreateJob extends Job
{
    /**
     * CompanyUpdateOrCreateJob constructor
     *
     * @param int $company_id
     */
    public function __construct(protected int $company_id)
    {}

    /**
     * Handle job
     *
     * @throws JobFailedException
     */
    public function handle(): void
    {
        if (($company = Company::find($this->company_id)) === null) {
            throw new JobFailedException('Unable to find company: %d', $this->company_id);
        }
        try {
            $service = new HubspotApiService();
            $service->pushCompany($company);
        } catch (ServiceException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw (new JobFailedException('Unable to push company: %d', $this->company_id))->setLastException($e);
        }
    }
}
