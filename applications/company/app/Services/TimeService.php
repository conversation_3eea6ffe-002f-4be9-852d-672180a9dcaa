<?php

namespace App\Services;

use App\Exceptions\TimeException;
use Carbon\Carbon;
use Common\Models\Company;
use Common\Models\Timezone;
use Common\Models\User;
use Core\Components\Cache\StaticAccessors\Cache;
use DateTimeZone;
use Exception;

/**
 * Class TimeService
 *
 * @package App\Services
 */
class TimeService
{
    /**
     * @var null|array
     */
    protected static $timezones = null;

    /**
     * @var array Cache of timezones by company id
     */
    protected static array $company_timezones = [];

    /**
     * @var array Cache of timezones by user id
     */
    protected static array $user_timezones = [];

    /**
     * DateTimeZone instance
     *
     * @var null|DateTimeZone
     */
    protected $timezone = null;

    /**
     * Get and cache all available timezones
     *
     * @return array
     */
    public static function getAllTimezones()
    {
        if (self::$timezones === null) {
            self::$timezones = Timezone::whereNull('parentTimezoneID')
                ->get(['timezoneID', 'label', 'timezone'])
                ->keyBy('timezoneID')
                ->toArray();
        }
        return self::$timezones;
    }

    /**
     * Get timezone for specified user and cache
     *
     * @param int $user_id
     * @return string
     */
    public static function getTimezoneForUser(int $user_id): string
    {
        if (!isset(static::$user_timezones[$user_id])) {
            $timezone = Cache::remember("timezone.user.{$user_id}", 3600, function () use ($user_id) {
                $user = User::withTimezone()->addSelect(['user.userID'])->whereKey($user_id)->first();
                if ($user === null) {
                    throw new TimeException('Unable to find user: %d', $user_id);
                }
                return $user->timezone;
            });
            static::$user_timezones[$user_id] = $timezone;
        }
        return static::$user_timezones[$user_id];
    }

    /**
     * Get timezone for specific company and cache
     *
     * @param int $company_id
     * @return string
     */
    public static function getTimezoneForCompany(int $company_id): string
    {
        if (!isset(static::$company_timezones[$company_id])) {
            $timezone = Cache::remember("timezone.company.{$company_id}", 3600, function () use ($company_id) {
                $company = Company::withTimezone()->addSelect(['companies.companyID'])->whereKey($company_id)->first();
                if ($company === null) {
                    throw new TimeException('Unable to find company: %d', $company_id);
                }
                return $company->timezone;
            });
            static::$company_timezones[$company_id] = $timezone;
        }
        return static::$company_timezones[$company_id];
    }

    /**
     * TimeService constructor
     */
    public function __construct()
    {
        $this->timezone = new DateTimeZone('UTC');
    }

    /**
     * Set timezone identifier
     *
     * @param string $timezone
     * @return $this
     * @throws TimeException
     */
    public function setTimezone($timezone)
    {
        try {
            $this->timezone = new DateTimeZone($timezone);
        } catch (Exception $e) {
            throw new TimeException('Unable to load timezone - Reason: %s', $e->getMessage());
        }
        return $this;
    }

    /**
     * Validate date and create carbon instance with proper timezone
     *
     * @param string|Carbon $date
     * @param string|DateTimeZone $timezone
     * @param boolean $clone
     * @return Carbon
     * @throws TimeException
     */
    protected function getCarbon($date, $timezone, $clone = false)
    {
        if ($date === null) {
            return Carbon::now($timezone);
        }
        if (!is_string($date) && (!is_object($date) || !($date instanceof Carbon))) {
            throw new TimeException('Invalid date, must be a string or Carbon instance');
        }
        if (is_string($date)) {
            return Carbon::parse($date, $timezone);
        }
        if ($clone) {
            $date = $date->copy();
        }
        return $date;
    }

    /**
     * Get date with proper timezone
     *
     * If string date is used, a Carbon instance with the services global timezone will be returned. Otherwise, the
     * passed Carbon date will be converted to the global timezone.
     *
     * @param string|Carbon $date
     * @param bool $clone
     * @return Carbon
     *
     * @throws TimeException
     */
    public function get($date, $clone = false)
    {
        return $this->getCarbon($date, $this->timezone, $clone)->timezone($this->timezone);
    }

    /**
     * Change UTC datetime to proper timezone
     *
     * If string date is used, a Carbon instance with UTC timezone will be created and then converted to global timezone.
     * Otherwise, the passed Carbon date will be converted to the global timezone.
     *
     * @param string $date
     * @param boolean $clone
     * @return Carbon
     * @throws TimeException
     */
    public function getFromUtc($date, $clone = false)
    {
        return $this->getCarbon($date, 'UTC', $clone)->timezone($this->timezone);
    }

    /**
     * Get UTC from date
     *
     * If string date is used, a Carbon instance with global timezone will be created and converted to UTC timezone.
     * Otherwise, the passed Carbon date will be converted to UTC timezone.
     *
     * @param string $date
     * @param bool $clone
     * @return Carbon
     * @throws TimeException
     */
    public function getUtc($date, $clone = false)
    {
        return $this->getCarbon($date, $this->timezone, $clone)->timezone('UTC');
    }
}
