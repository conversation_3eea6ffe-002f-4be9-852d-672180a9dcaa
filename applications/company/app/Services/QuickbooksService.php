<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Log;
use App\Services\Quickbooks\Exceptions\{
    BadRequestException,
    OAuth\AccessTokenRefreshException,
    OAuth\DisconnectedException,
    QueryException,
    QuickbooksException,
    ResourceForbiddenException,
    ResourceNotFoundException,
    ServiceInternalErrorException,
    ServiceUnavailableException,
    ThrottleException,
    UnauthorizedException
};
use Carbon\Carbon;
use Common\Models\{QuickbooksOAuth, User};
use Core\Components\Crypt\StaticAccessors\Crypt;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Config;
use QuickBooksOnline\API\Core\HttpClients\FaultHandler;
use QuickBooksOnline\API\Core\OAuth\OAuth2\{OAuth2AccessToken, OAuth2LoginHelper};
use QuickBooksOnline\API\Data\IPPIntuitEntity;
use QuickBooksOnline\API\DataService\DataService;
use QuickBooksOnline\API\Exception\ServiceException;
use Throwable;

define('QUICKBOOKS_API_TIMEOUT', '30');

/**
 * Class QuickbooksService
 *
 * Wrapper for Quickbooks SDK
 *
 * @package App\Services
 */
class QuickbooksService
{
    /**
     * @var int
     */
    protected $company_id;

    /**
     * @var null|\Monolog\Logger
     */
    protected $log = null;

    /**
     * @var null|QuickbooksOAuth
     */
    protected $oauth_config = null;

    /**
     * @var bool
     */
    protected $oauth_config_cached = false;

    /**
     * @var null|DataService
     */
    protected $data_service = null;

    /**
     * QuickbooksService constructor
     *
     * @param int $company_id
     */
    public function __construct(int $company_id)
    {
        $this->company_id = $company_id;
    }

    /**
     * Assign token data returned from Quickbooks SDK to OAuth model for storage
     *
     * @param QuickbooksOAuth $oauth_config
     * @param OAuth2AccessToken $access_token
     * @return QuickbooksOAuth
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public static function setAccessToken(QuickbooksOAuth $oauth_config, OAuth2AccessToken $access_token): QuickbooksOAuth
    {
        $now = Carbon::now('UTC');
        return $oauth_config->fill([
            'accessToken' => Crypt::encrypt($access_token->getAccessToken()),
            'accessTokenExpiresAt' => $now->copy()->addHour(),
            'refreshToken' => Crypt::encrypt($access_token->getRefreshToken()),
            'refreshTokenExpiresAt' => $now->copy()->addDays(100),
        ]);
    }

    /**
     * Get logger instance
     *
     * @return \Monolog\Logger
     */
    public function getLog(): \Monolog\Logger
    {
        if ($this->log === null) {
            $this->log = Log::create('quickbooks', [
                'email' => [
                    'subject' => 'Quickbooks'
                ],
                'slack' => [
                    'username' => 'quickbooks'
                ],
                'file' => 'quickbooks.log'
            ]);
        }
        return $this->log;
    }

    /**
     * Get company id
     *
     * @return int
     */
    public function getCompanyID(): int
    {
        return $this->company_id;
    }

    /**
     * Get base service config
     *
     * Used as base for authentication and requests
     *
     * @param array $settings
     * @return array
     */
    public function getServiceConfig(array $settings = []): array
    {
        $config = Config::get('quickbooks');
        return array_merge([
            'auth_mode' => 'oauth2',
            'ClientID' => $config['client_id'],
            'ClientSecret' => $config['client_secret'],
            'scope' => 'com.intuit.quickbooks.accounting',
            'baseUrl' => SERVER_ROLE !== 'PROD' ? 'Development' : 'Production'
        ], $settings);
    }

    /**
     * Get and store OAuth config data based on company
     *
     * @param bool $force
     * @return QuickbooksOAuth|null
     */
    public function getOAuthConfig(bool $force = false): ?QuickbooksOAuth
    {
        if ($force || !$this->oauth_config_cached) {
            $this->oauth_config = QuickbooksOAuth::where('companyID', $this->company_id)
                ->where('isCurrent', true)
                ->first();
            $this->oauth_config_cached = true;
        }
        return $this->oauth_config;
    }

    /**
     * Get request config needed to make authenticated calls to API
     *
     * If access token is about to expire in the next minute or has already expired, it will be renewed before config
     * is returned. This ensures other methods don't have to worry about if tokens are valid.
     *
     * @param bool $refresh_token
     * @param array $settings
     * @return array
     * @throws AppException
     * @throws DisconnectedException
     */
    public function getRequestConfig(bool $refresh_token = true, array $settings = []): array
    {
        $oauth_config = $this->getOAuthConfig();
        if ($oauth_config === null || $oauth_config->status !== QuickbooksOAuth::STATUS_CONNECTED) {
            throw new DisconnectedException('Company not connected to quickbooks');
        }
        $config = array_merge([
            'refreshTokenKey' => Crypt::decrypt($oauth_config->refreshToken),
            'QBORealmID' => $oauth_config->realmID
        ], $this->getServiceConfig($settings));
        // if access token will expire in less than a minute, we refresh it
        if ($refresh_token && $oauth_config->accessTokenExpiresAt->lte(Carbon::now('UTC')->addMinute())) {
            try {
                $helper = new OAuth2LoginHelper($config['ClientID'], $config['ClientSecret']);
                $access_token = $helper->refreshAccessTokenWithRefreshToken($config['refreshTokenKey']);
                $config['accessTokenKey'] = $access_token->getAccessToken();
                $config['refreshTokenKey'] = $access_token->getRefreshToken();
                $oauth_config = static::setAccessToken($oauth_config, $access_token);
                $oauth_config->save();
            } catch (ServiceException $e) {
                if ($e->getCode() === 401) {
                    $this->getLog()->error('Refresh token expired, disconnecting company', [
                        'exception' => $e,
                        'company_id' => $this->company_id
                    ]);
                    $this->disconnect(null, true);
                    throw new DisconnectedException('Refresh token expired');
                }
                throw (new AccessTokenRefreshException('Unable to refresh access token'))->setLastException($e);
            } catch (Throwable $e) {
                $this->getLog()->error('Unable to update access token for company', [
                    'exception' => $e,
                    'company_id' => $this->company_id
                ]);
                throw (new AccessTokenRefreshException('Unexpected error occurred while updating access token'))->setLastException($e);
            }
        } else {
            $config['accessTokenKey'] = Crypt::decrypt($oauth_config->accessToken);
        }
        return $config;
    }

    /**
     * Update refresh token
     *
     * Currently just a wrapper for getting the request config which will renew access tokens for us. We assume
     * if a refresh token is needing updated, the access token will definitely be expired.
     *
     * @throws AppException
     * @throws DisconnectedException
     */
    public function refreshToken(): void
    {
        $this->getRequestConfig();
    }

    /**
     * Get Quickbooks SDK data service instance based on request config
     *
     * @return DataService
     * @throws AppException
     * @throws DisconnectedException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function getDataService(): DataService
    {
        if ($this->data_service === null) {
            $this->data_service = DataService::Configure($this->getRequestConfig());
        }
        return $this->data_service;
    }

    /**
     * Determine if company is connected to quickbooks
     *
     * Note: this doesn't ensure they have valid tokens, just checks a database entry.
     *
     * @return bool
     */
    public function isConnected(): bool
    {
        $oauth_config = $this->getOAuthConfig();
        return $oauth_config !== null && $oauth_config->status === QuickbooksOAuth::STATUS_CONNECTED;
    }

    /**
     * Convert fault handler to array for use in error context
     *
     * @param FaultHandler $fault
     * @return array
     */
    protected function faultToArray(FaultHandler $fault): array
    {
        return [
            'status_code' => $fault->getHttpStatusCode(),
            'response_body' => $fault->getResponseBody(),
            'type' => $fault->getIntuitErrorType(),
            'code' => $fault->getIntuitErrorCode(),
            'element' => $fault->getIntuitErrorElement(),
            'message' => $fault->getIntuitErrorMessage(),
            'detail' => $fault->getIntuitErrorDetail(),
            'help_msg' => $fault->getOAuthHelperError(),
            'tid' => $fault->getIntuitTid()
        ];
    }

    /**
     * Handle fault returned from failed SDK call
     *
     * @param FaultHandler $fault
     * @throws AppException
     */
    protected function handleError(FaultHandler $fault): void
    {
        try {
            $code = $fault->getHttpStatusCode();
            throw match ($code) {
                400 => new BadRequestException('Bad request'),
                401 => new UnauthorizedException('Request is not authorized'),
                403 => new ResourceForbiddenException('Request for resource is forbidden'),
                404 => new ResourceNotFoundException('Request for resource not found'),
                429 => new ThrottleException('Request has been throttled'),
                500 => new ServiceInternalErrorException('Request resulted in internal server error'),
                503 => new ServiceUnavailableException('Quickbooks API is unavailable'),
                default => new QuickbooksException('Unexpected error encountered')
            };
        } catch (AppException $e) {
            $e->store('_context.fault', $this->faultToArray($fault));
            throw $e;
        }
    }

    /**
     * Run query against company QB account
     *
     * @param string $query
     * @return IPPIntuitEntity[]|int
     * @throws AppException
     * @throws DisconnectedException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function query(string $query)
    {
        $service = $this->getDataService();
        $results = $service->Query($query);
        if ($results === null) {
            if (($fault = $service->getLastError()) !== false) {
                $this->handleError($fault);
                throw (new QueryException('Unable to run query: %s', $query))
                    ->store('_context.fault', $this->faultToArray($fault));
            }
            // service returns null when no results are returned, we standardize to an empty array
            return [];
        }
        return $results;
    }

    /**
     * Run query and return the first result
     *
     * @param string $query
     * @return IPPIntuitEntity|null
     * @throws AppException
     * @throws DisconnectedException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function first(string $query): ?IPPIntuitEntity
    {
        $results = $this->query($query);
        return is_array($results) && isset($results[0]) ? $results[0] : null;
    }

    /**
     * Run entity related action and handle any failures by throwing exceptions
     *
     * @param string $action
     * @param IPPIntuitEntity $entity
     * @return mixed
     * @throws AppException
     * @throws DisconnectedException
     * @throws QuickbooksException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    protected function runEntityAction(string $action, IPPIntuitEntity $entity)
    {
        $service = $this->getDataService();
        $result = $service->{$action}($entity);
        if ($result === null) {
            $this->handleError($service->getLastError());
            throw new QuickbooksException('Unable to handle request');
        }
        return $result;
    }

    /**
     * Add entity to QB account
     *
     * @param IPPIntuitEntity $entity
     * @return IPPIntuitEntity
     * @throws AppException
     * @throws DisconnectedException
     * @throws QuickbooksException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function add(IPPIntuitEntity $entity): IPPIntuitEntity
    {
        return $this->runEntityAction('Add', $entity);
    }

    /**
     * Update entity on QB account
     *
     * @param IPPIntuitEntity $entity
     * @return mixed
     * @throws AppException
     * @throws DisconnectedException
     * @throws QuickbooksException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function update(IPPIntuitEntity $entity): IPPIntuitEntity
    {
        return $this->runEntityAction('Update', $entity);
    }

    /**
     * Delete entity on QB account
     *
     * @param IPPIntuitEntity $entity
     * @throws AppException
     * @throws DisconnectedException
     * @throws QuickbooksException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function delete(IPPIntuitEntity $entity): void
    {
        $this->runEntityAction('Delete', $entity);
    }

    /**
     * Download PDF of entity from Quickbooks server
     *
     * @param IPPIntuitEntity $entity
     * @return string path to temporary file
     * @throws AppException
     * @throws DisconnectedException
     * @throws QuickbooksException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function downloadPdf(IPPIntuitEntity $entity): string
    {
        return $this->runEntityAction('DownloadPDF', $entity);
    }

    /**
     * Disconnect company from Quickbooks
     *
     * @param User|null $user user doing action
     * @param bool $external determines if request originated from QB
     */
    public function disconnect(?User $user = null, bool $external = false): void
    {
        try {
            $config = $this->getRequestConfig();
            $helper = new OAuth2LoginHelper($config['ClientID'], $config['ClientSecret']);
            $helper->revokeToken($config['refreshTokenKey']);
        } catch (Throwable $e) {
            $this->getLog()->error('Unable to revoke token for company', [
                'exception' => $e,
                'company_id' => $this->company_id
            ]);
        }

        try {
            $now = Carbon::now('UTC');
            $this->getOAuthConfig()->fill([
                'status' => QuickbooksOAuth::STATUS_DISCONNECTED,
                'accessToken' => null,
                'accessTokenExpiresAt' => null,
                'refreshToken' => null,
                'refreshTokenExpiresAt' => null,
                'disconnectedAt' => $now,
                'disconnectedByUserID' => $user !== null ? $user->getKey() : null,
                'isExternalDisconnect' => $external,
                'updatedAt' => $now
            ])->save();
        } catch (Throwable $e) {
            $this->getLog()->error('Unable to disconnect company', [
                'exception' => $e,
                'company_id' => $this->company_id
            ]);
        }
    }
}
