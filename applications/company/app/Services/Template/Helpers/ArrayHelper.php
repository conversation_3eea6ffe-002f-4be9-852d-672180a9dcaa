<?php

declare(strict_types=1);

namespace App\Services\Template\Helpers;

use App\Services\TemplateService;

/**
 * Class ArrayHelper
 *
 * Handlebars helpers related to working with arrays.
 *
 * @package App\Services\Template\Helpers
 */
class ArrayHelper
{
    /**
     * Register helpers with template service
     *
     * @param TemplateService $service
     * @throws \App\Services\Template\Exceptions\TemplateException
     */
    public static function register(TemplateService $service): void
    {
        $helpers = [
            'chunk' => 'chunk'
        ];
        foreach ($helpers as $name => $method) {
            $service->registerHelper($name, [static::class, $method]);
        }
    }

    /**
     * Split array into chunks of specified size
     *
     * @param $array
     * @param $options
     * @return array
     */
    public static function chunk($array, $options)
    {
        if (!is_array($array)) {
            return [];
        }
        $size = isset($options['hash']['size']) ? (int)$options['hash']['size'] : 1;
        return array_chunk($array, $size);
    }
}
