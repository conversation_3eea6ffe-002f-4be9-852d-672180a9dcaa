<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Classes;

use App\Classes\{Func, Log};
use App\Services\GoogleApi\Classes\OAuth\AuthCode;
use App\Services\GoogleApi\Exceptions\{
    GoogleApiException,
    OAuth\AccessDeniedException,
    OAuth\AccountInUseException,
    OAuth\ConnectedException,
    OAuth\InvalidAuthCodeException
};
use App\Services\GoogleApiService;
use Carbon\Carbon;
use Common\Models\{GoogleAuthRequest, GoogleOAuth, GoogleService};
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\StaticAccessors\URI;
use Google_Client;
use Google_Service_Calendar;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class AuthHelper
 *
 * @package App\Services\GoogleApi\Classes
 */
class AuthHelper
{
    /**
     * @var GoogleApiService Google API service instance
     */
    protected GoogleApiService $service;

    /**
     * @var array Map of service id to Google Calendar API scopes
     */
    protected static array $service_scope_map = [
        Service::CALENDAR => Google_Service_Calendar::CALENDAR
    ];

    /**
     * AuthHelper constructor
     *
     * @param GoogleApiService $service
     */
    public function __construct(GoogleApiService $service)
    {
        $this->service = $service;
    }

    /**
     * Get google client instance with redirect URI
     *
     * @return Google_Client
     */
    protected function getClient(): Google_Client
    {
        $client = $this->service->newClient();
        $client->setRedirectUri(URI::route('google.oauth.auth-code')->build());
        return $client;
    }

    /**
     * Get OAuth connection URL for specified services
     *
     * Do not pass any sensitive data via state param as this is sent to Google.
     *
     * @param array $services
     * @param array $state Additional info to maintain state of application
     * @return string
     * @throws ConnectedException
     * @throws GoogleApiException
     */
    public function connect(array $services, array $state = []): string
    {
        if (count($services) === 0) {
            throw new GoogleApiException('At least one service is required to connect');
        }

        // determine if an existing connection is already made
        $oauth = $this->service->getOAuthConfig();
        if ($oauth !== null && $oauth->status === GoogleOAuth::STATUS_CONNECTED) {
            throw new ConnectedException('Google API is already connected for user');
        }

        $scopes = [];
        foreach ($services as $service) {
            if (!isset(static::$service_scope_map[$service])) {
                throw new GoogleApiException('Unable to find scope for service: %s', $service);
            }
            $scopes[] = static::$service_scope_map[$service];
        }

        $user_id = $this->service->getUser()->getKey();

        // determine if a pending auth request is active and delete if one is found
        $request = GoogleAuthRequest::query()->where('userID', $user_id)
            ->where('status', GoogleAuthRequest::STATUS_PENDING)
            ->first();
        if ($request !== null) {
            $request->delete();
        }

        // create new auth request with associated services
        $id = Uuid::uuid4();
        DB::transaction(function () use ($id, $user_id, $services) {
            GoogleAuthRequest::create([
                'googleAuthRequestID' => $id->getBytes(),
                'userID' => $user_id,
                'type' => GoogleAuthRequest::TYPE_INITIAL,
                'status' => GoogleAuthRequest::STATUS_PENDING
            ]);
            foreach ($services as $service) {
                Service::create($service, $id);
            }
        });

        $client = $this->getClient();
        $client->setScopes($scopes);
        $client->setAccessType('offline');
        $client->setState(Http::buildQueryString(array_merge($state, ['id' => $id->toString()])));
        return $client->createAuthUrl();
    }

    /**
     * Exchange OAuth auth code for an access token and save for user
     *
     * @param AuthCode $auth_code
     * @throws AccessDeniedException
     * @throws ConnectedException
     * @throws InvalidAuthCodeException
     * @throws Throwable
     */
    public function exchangeAuthCode(AuthCode $auth_code)
    {
        if (($id = $auth_code->getState('id')) === null || ($id = Func::uuidFromString($id)) === false) {
            throw new InvalidAuthCodeException('State is invalid');
        }
        $user_id = $this->service->getUser()->getKey();
        /** @var GoogleAuthRequest|null $auth_request */
        $auth_request = GoogleAuthRequest::query()
            ->where('userID', $user_id)
            ->whereKey($id->getBytes())
            ->first();
        if ($auth_request === null) {
            throw new InvalidAuthCodeException('Unable to find auth request for id: %s', $id->toString());
        }
        // if request has already been resulted, we just throw an error
        if ($auth_request->status !== GoogleAuthRequest::STATUS_PENDING) {
            throw new InvalidAuthCodeException('Auth request %s already resulted', $id->toString());
        }

        // check if there is a current connection
        $oauth_config = $this->service->getOAuthConfig();
        if ($oauth_config !== null && $oauth_config->status === GoogleOAuth::STATUS_CONNECTED) {
            throw new ConnectedException('Google API is already connected for user');
        }

        $now = Carbon::now('UTC');

        try {
            // see if an error was returned
            if (($error = $auth_code->getError()) !== null) {
                switch ($error) {
                    case 'access_denied':
                        $auth_request->status = GoogleAuthRequest::STATUS_ACCESS_DENIED;
                        $auth_request->accessDeniedAt = $now;
                        $auth_request->save();
                        throw new AccessDeniedException('User denied access');
                    case 'invalid_grant':
                    default:
                        throw new InvalidAuthCodeException('Error returned: %s', $error);
                }
            }

            $code = $auth_code->getCode();
            if (empty($code)) {
                throw new InvalidAuthCodeException('Code is missing from request');
            }

            try {
                $access_token = $this->getClient()->fetchAccessTokenWithAuthCode($code);
            } catch (Throwable $e) {
                throw (new InvalidAuthCodeException('Unable to exchange code for token'))->setLastException($e);
            }

            if ($auth_request->type === GoogleAuthRequest::TYPE_INITIAL && !isset($access_token['refresh_token'])) {
                throw new AccountInUseException();
            }

            DB::transaction(function () use ($oauth_config, $user_id, $access_token, $now, $auth_request) {
                if ($oauth_config !== null) {
                    $oauth_config->fill([
                        'isCurrent' => false
                    ])->save();
                }

                $new_oauth_id = Uuid::uuid4();
                $new_oauth_config = new GoogleOAuth([
                    'googleOAuthID' => $new_oauth_id->getBytes(),
                    'userID' => $user_id,
                    'status' => GoogleOAuth::STATUS_CONNECTED,
                    'isCurrent' => true
                ]);
                $new_oauth_config = GoogleApiService::setAccessToken($new_oauth_config, $access_token);
                $new_oauth_config->save();

                $auth_request->status = GoogleAuthRequest::STATUS_COMPLETED;
                $auth_request->completedAt = $now;
                $auth_request->googleOAuthID = $new_oauth_id->getBytes();
                $auth_request->save();

                $auth_request->services()->each(function (GoogleService $service) use ($new_oauth_id) {
                    Service::make($this->service, $service)->connect($new_oauth_id);
                });
            });
        } catch (AccessDeniedException $e) {
            throw $e;
        } catch (Throwable $e) {
            $auth_request->status = GoogleAuthRequest::STATUS_FAILED;
            $auth_request->failedAt = $now;
            $auth_request->save();
            $class_log_level_map = [
                AccountInUseException::class => Log::INFO
            ];
            GoogleApiService::getLog()->addRecord($class_log_level_map[get_class($e)] ?? Log::ERROR, 'Auth request failed', [
                'exception' => $e,
                'user_id' => $user_id
            ]);
            throw $e;
        }
    }

    /**
     * Disconnect user via queued job
     */
    public function disconnect(): void
    {
        $this->service->queueDisconnect();
    }
}
