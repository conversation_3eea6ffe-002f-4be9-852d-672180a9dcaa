<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Entities;

use Carbon\Carbon;

/**
 * Class EventEntity
 *
 * @package App\Services\GoogleApi\Calendar\Entities
 */
class EventEntity
{
    /**
     * @var string|null Event id
     */
    protected ?string $id = null;

    /**
     * @var string|null Event status (confirmed, cancelled, etc.)
     */
    protected ?string $status = null;

    /**
     * @var bool|null Determines if event is all day
     */
    protected ?bool $is_all_day = false;

    /**
     * @var Carbon|null Event start
     */
    protected ?Carbon $start = null;

    /**
     * @var Carbon|null Event end
     */
    protected ?Carbon $end = null;

    /**
     * @var string|null Event title
     */
    protected ?string $title = null;

    /**
     * Create event from Google API library class
     *
     * @param \Google_Service_Calendar_Event $entity
     * @return static
     */
    public static function fromGoogleEntity(\Google_Service_Calendar_Event $entity): self
    {
        $new_entity = new static();
        $new_entity->setID($entity->getId());
        $new_entity->setStatus($entity->getStatus());
        foreach (['start' => 'setStart', 'end' => 'setEnd'] as $key => $method) {
            if (($time = $entity->{$key}) === null) {
                continue;
            }
            if (isset($time->date)) {
                $time = Carbon::parse($time->date)->startOfDay();
                $new_entity->setIsAllDay();
            } else {
                $time = Carbon::parse($time->dateTime, $time->timeZone);
            }
            $new_entity->{$method}($time);
        }
        $new_entity->setTitle($entity->summary);
        return $new_entity;
    }

    /**
     * Set id
     *
     * @param string|null $id
     */
    public function setID(?string $id): void
    {
        $this->id = $id;
    }

    /**
     * Get id
     *
     * @return string|null
     */
    public function getID(): ?string
    {
        return $this->id;
    }

    /**
     * Set status
     *
     * @param string|null $status
     */
    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    /**
     * Get status
     *
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Determines if event is cancelled
     *
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->getStatus() === 'cancelled';
    }

    /**
     * Set if event is all day
     *
     * @param bool $is_all_day
     */
    public function setIsAllDay(bool $is_all_day = true): void
    {
        $this->is_all_day = $is_all_day;
    }

    /**
     * Determines if event is all day
     *
     * @return bool
     */
    public function isAllDay(): bool
    {
        return $this->is_all_day;
    }

    /**
     * Set start
     *
     * @param Carbon|null $start
     */
    public function setStart(?Carbon $start): void
    {
        $this->start = $start;
    }

    /**
     * Get start
     *
     * @return Carbon|null
     */
    public function getStart(): ?Carbon
    {
        return $this->start;
    }

    /**
     * Set end
     *
     * @param Carbon|null $end
     */
    public function setEnd(?Carbon $end): void
    {
        $this->end = $end;
    }

    /**
     * Get end
     *
     * @return Carbon|null
     */
    public function getEnd(): ?Carbon
    {
        return $this->end;
    }

    /**
     * Set title
     *
     * @param string|null $title
     */
    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    /**
     * Get title
     *
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }
}
