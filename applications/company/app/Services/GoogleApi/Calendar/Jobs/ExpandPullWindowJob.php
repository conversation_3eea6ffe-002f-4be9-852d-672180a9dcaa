<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Jobs;

use App\Attributes\GoogleApiJobAttribute;
use App\Services\GoogleApi\Calendar\Classes\Calendar;
use App\Services\GoogleApi\Calendar\Traits\Job\EventPullTrait;
use Carbon\Carbon;
use Common\Models\GoogleCalendarPull;
use Core\Attributes\SerializeIgnoreAttribute;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\UuidInterface;
use Throwable;

/**
 * Class ExpandPullWindowJob
 *
 * @package App\Services\GoogleApi\Calendar\Jobs
 */
#[GoogleApiJobAttribute(type: 27)]
class ExpandPullWindowJob extends Job
{
    use EventPullTrait;

    /**
     * @var Calendar|null Calendar instance cache
     */
    #[SerializeIgnoreAttribute]
    protected ?Calendar $calendar = null;

    /**
     * ExpandPullWindowJob constructor
     *
     * @param UuidInterface $calendar_id
     * @param Carbon $end_at
     */
    public function __construct(protected UuidInterface $calendar_id, protected Carbon $end_at)
    {}

    /**
     * Handle import of events into local storage
     *
     * @param Calendar $calendar
     * @throws JobFailedException
     * @throws \App\Exceptions\TimeException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\Api\ResourceNotFoundException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDeletedException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDetailMismatchException
     * @throws \App\Services\GoogleApi\Exceptions\CalendarException
     * @throws \App\Services\GoogleApi\Exceptions\GoogleApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    protected function handleImport(Calendar $calendar): void
    {
        $info = $this->importEvents($calendar, $calendar->getPullEventCursor(false, $this->end_at));
        $this->logPull($calendar, GoogleCalendarPull::TYPE_WINDOW_EXPANSION, $info);
    }

    /**
     * Handle job
     *
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        if (($this->calendar = Calendar::findByID($this->calendar_id)) === null) {
            throw new JobFailedException('Unable to find calendar with id: %s', $this->calendar_id->toString());
        }

        $this->runImport($this->calendar);
    }

    /**
     * Handle job failure
     *
     * @param Throwable $e
     * @param int $tries
     */
    public function failed(Throwable $e, int $tries): void
    {
        if ($this->calendar === null) {
            return;
        }
        $this->handleFailure($this->calendar);
    }
}
