<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Classes\Calendar;

use App\Services\GoogleApi\Calendar\EventDetails;
use App\Services\GoogleApi\Calendar\Interfaces\EventDetailInterface;
use Common\Classes\DB\Model;

/**
 * Class EventDetail
 *
 * @package App\Services\GoogleApi\Calendar\Classes\Calendar
 */
abstract class EventDetail implements EventDetailInterface
{
    /**
     * @var string[] Mapping of type to associated class
     */
    protected static array $map = [
        Event::TYPE_PROJECT => EventDetails\ProjectEventDetail::class,
        Event::TYPE_USER => EventDetails\UserEventDetail::class,
        Event::TYPE_COMPANY => EventDetails\CompanyEventDetail::class
    ];

    /**
     * @var int Event type
     */
    protected int $type;

    /**
     * Load proper class based on event type
     *
     * @param int $event_type
     * @param Model $model
     * @param string $timezone
     * @return static
     */
    public static function load(int $event_type, Model $model, string $timezone): self
    {
        return new self::$map[$event_type]($model, $timezone);
    }

    /**
     * Get type
     *
     * @return int
     */
    public function getType(): int
    {
        return $this->type;
    }
}
