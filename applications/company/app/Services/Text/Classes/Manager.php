<?php

declare(strict_types=1);

namespace App\Services\Text\Classes;

use App\Services\Text\Interfaces\TypeInterface;
use App\Services\Text\Jobs\BuildJob;
use Carbon\Carbon;
use Core\Classes\Config;
use Core\Exceptions\AppException;
use Core\Interfaces\KernelInterface;

/**
 * Class Manager
 *
 * @package App\Services\Type\Classes
 */
class Manager
{
    /**
     * @var bool
     */
    protected $enabled = true;

    /**
     * List of all text types
     *
     * Format: id => full class name
     *
     * @var array
     */
    protected $types = [];

    /**
     * @var KernelInterface
     */
    protected $kernel;

    /**
     * Manager constructor
     *
     * @param Config $config
     * @param KernelInterface $kernel
     */
    public function __construct(Config $config, KernelInterface $kernel)
    {
        $this->enabled = $config->get('text.enabled', true);
        $this->types = $config->get('text.types', []);
        $this->kernel = $kernel;
    }

    /**
     * Determines if texting is enabled at the application level
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Determine if type exists
     *
     * @param int $type
     * @return bool
     */
    public function hasType(int $type): bool
    {
        return isset($this->types[$type]);
    }

    /**
     * Get type by class name
     *
     * @param string $class
     * @return int
     * @throws AppException
     */
    public function getTypeByClass(string $class): int
    {
        if (($type = array_search($class, $this->types)) === false) {
            throw new AppException('Unable to find type for job: %s', $class);
        }
        return $type;
    }

    /**
     * Add send type by class
     *
     * Will lookup proper type based on class (determined from text.php config file mapping) and enqueue a build job
     *
     * @param string $type_class
     * @param array $payload
     * @param Carbon|null $handle_at
     * @return void
     * @throws AppException
     */
    public function sendType(string $type_class, array $payload, Carbon $handle_at = null): void
    {
        BuildJob::enqueue($this->getTypeByClass($type_class), $payload)->handleAt($handle_at);
    }

    /**
     * Get type instance by class name
     *
     * @param string $type_class
     * @return TypeInterface
     */
    public function getTypeInstance(string $type_class): TypeInterface
    {
        return $this->kernel->create($type_class);
    }

    /**
     * Get instance by type
     *
     * @param int $type
     * @return TypeInterface
     * @throws AppException
     */
    public function getByType(int $type): TypeInterface
    {
        if (!$this->hasType($type)) {
            throw new AppException('Unable to find type: %s', $type);
        }
        return $this->getTypeInstance($this->types[$type]);
    }
}
