<?php

declare(strict_types=1);

namespace App\Services\Text\Classes;

use App\Services\Text\Exceptions\MessageException;
use App\Services\Text\Interfaces\{MessageInterface, NumberInterface};
use App\Services\Text\Jobs\SendJob;
use Carbon\Carbon;
use Closure;
use Common\Models\{TextMessage, TextMessageNumber, TextMessageRecipient, TextNumberSuppression};
use Core\Components\DB\StaticAccessors\DB;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class Message
 *
 * @package App\Services\Text\Classes
 */
class Message implements MessageInterface
{
    const MESSAGE_TYPE_OUTBOUND = 1;
    const MESSAGE_TYPE_INBOUND = 2;

    /**
     * @var null|UuidInterface
     */
    protected $id = null;

    /**
     * @var null|int
     */
    protected $message_type = null;

    /**
     * @var null|int
     */
    protected $type = null;

    /**
     * @var null|UuidInterface
     */
    protected $item_id = null;

    /**
     * @var null|NumberInterface
     */
    protected $from = null;

    /**
     * @var NumberInterface[]
     */
    protected $recipients = [];

    /**
     * @var null|string
     */
    protected $body = null;

    /**
     * @var bool
     */
    protected $saved = false;

    /**
     * @var null|UuidInterface[]
     */
    protected $recipient_ids = null;

    /**
     * @var null|Carbon
     */
    protected $deliver_at = null;

    /**
     * @var bool
     */
    protected $sent = false;

    /**
     * Resend message by id
     *
     * @param UuidInterface|string $id
     * @param Closure|null $message_callback
     * @return MessageInterface
     * @throws MessageException
     */
    public static function resend($id, ?Closure $message_callback = null): MessageInterface
    {
        if (($message = TextMessage::findByUuid($id)) === null) {
            throw new MessageException('Unable to find message: %s', $id);
        }
        $message = (new static)->hydrate($message);
        $message = clone $message;

        if ($message_callback !== null) {
            $message = $message_callback($message);
        }

        $message->send();

        return $message;
    }

    /**
     * Handle cloning of message
     */
    public function __clone()
    {
        // clear out id so a new one will be generated
        $this->id = null;
        $this->saved = false;
        $this->recipient_ids = null;
        $this->sent = false;

        if ($this->from !== null) {
            $this->from = clone $this->from;
        }

        // clone all number instances
        foreach ($this->recipients as &$recipient) {
            $recipient = clone $recipient;
        }
    }

    /**
     * Fill out instance from text message model
     *
     * @param TextMessage $message
     * @return MessageInterface
     * @throws \App\Services\Text\Exceptions\NumberException
     */
    public function hydrate(TextMessage $message): MessageInterface
    {
        $this->id = $message->getUuidKey();
        $this->messageType($message->messageType);
        $this->type($message->type);
        $this->itemID($message->getUuid('itemID'));
        $this->body($message->body);

        foreach ($message->numbers as $model) {
            $number = Number::makeFromModel($model);
            switch ($model->numberType) {
                case TextMessageNumber::NUMBER_TYPE_FROM:
                    $this->from($number);
                    break;
                case TextMessageNumber::NUMBER_TYPE_TO:
                    $this->to($number);
                    break;
            }
        }

        $this->saved = true;

        return $this;
    }

    /**
     * Get ID
     *
     * @return UuidInterface
     * @throws \Exception
     */
    public function getID(): UuidInterface
    {
        if ($this->id === null) {
            $this->id = Uuid::uuid4();
        }
        return $this->id;
    }

    /**
     * Set message type
     *
     * @param int $type
     * @return MessageInterface
     */
    public function messageType(int $type): MessageInterface
    {
        $this->message_type = $type;
        return $this;
    }

    /**
     * Get message type
     *
     * @return int
     * @throws MessageException
     */
    public function getMessageType(): int
    {
        if ($this->message_type === null) {
            throw new MessageException('No message type defined');
        }
        return $this->message_type;
    }

    /**
     * Set type
     *
     * @param int $type
     * @return MessageInterface
     */
    public function type(int $type): MessageInterface
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Get type
     *
     * @return int
     * @throws MessageException
     */
    public function getType(): int
    {
        if ($this->type === null) {
            throw new MessageException('No type defined');
        }
        return $this->type;
    }

    /**
     * Set item ID
     *
     * @param UuidInterface $id
     * @return MessageInterface
     */
    public function itemID(UuidInterface $id): MessageInterface
    {
        $this->item_id = $id;
        return $this;
    }

    /**
     * Get item id
     *
     * @return UuidInterface
     * @throws MessageException
     */
    public function getItemID(): UuidInterface
    {
        if ($this->item_id === null) {
            throw new MessageException('No item id defined');
        }
        return $this->item_id;
    }

    /**
     * Set from number/service
     *
     * @param NumberInterface $number
     * @return MessageInterface
     */
    public function from(NumberInterface $number): MessageInterface
    {
        $this->from = $number;
        return $this;
    }

    /**
     * Get from number/service
     *
     * @return NumberInterface
     * @throws MessageException
     */
    public function getFrom(): NumberInterface
    {
        if ($this->from === null) {
            throw new MessageException('No from defined');
        }
        return $this->from;
    }

    /**
     * Clear all recipients
     *
     * @return MessageInterface
     */
    public function clearRecipients(): MessageInterface
    {
        $this->recipients = [];
        return $this;
    }

    /**
     * Add To number
     *
     * @param NumberInterface $number
     * @return MessageInterface
     */
    public function to(NumberInterface $number): MessageInterface
    {
        $this->recipients[] = $number;
        return $this;
    }

    /**
     * Add multiple recipients
     *
     * @param array $recipients
     * @param bool $overwrite
     * @return MessageInterface
     */
    public function recipients(array $recipients, bool $overwrite = false): MessageInterface
    {
        if ($overwrite) {
            $this->clearRecipients();
        }
        foreach ($recipients as $recipient) {
            $this->to($recipient);
        }
        return $this;
    }

    /**
     * Get recipients list
     *
     * @return NumberInterface[]
     */
    public function getRecipients(): array
    {
        return $this->recipients;
    }

    /**
     * Set message body
     *
     * @param string $body
     * @return MessageInterface
     */
    public function body(string $body): MessageInterface
    {
        $this->body = $body;
        return $this;
    }

    /**
     * Get message body
     *
     * @return string
     * @throws MessageException
     */
    public function getBody(): string
    {
        if ($this->body === null) {
            throw new MessageException('No body defined');
        }
        return $this->body;
    }

    /**
     * Handle saving of message
     *
     * Note: this is ran inside a DB transaction to be atomic
     *
     * @param array $recipients
     * @throws MessageException
     */
    protected function handleSave(array $recipients): void
    {
        /** @var TextMessage $message */
        $message = TextMessage::create([
            'textMessageID' => $this->getID()->getBytes(),
            'messageType' => $this->getMessageType(),
            'type' => $this->getType(),
            'itemID' => $this->getItemID()->getBytes(),
            'body' => $this->getBody()
        ]);

        $numbers = [
            // from message service entry, we don't currently know the from number since Twilio determines that after
            // we push. we leave it null to be filled out later
            [
                'number' => $this->getFrom(),
                'type' => TextMessageNumber::NUMBER_TYPE_FROM
            ]
        ];
        foreach ($recipients as $recipient) {
            $numbers[] = [
                'number' => $recipient,
                'type' => TextMessageNumber::NUMBER_TYPE_TO
            ];
        }

        $this->recipient_ids = [];
        foreach ($numbers as $info) {
            /** @var NumberInterface $number */
            $number = $info['number'];
            $number_id = $number->getID()->getBytes();
            $message->numbers()->create([
                'textMessageNumberID' => $number_id,
                'type' => $number->getType(),
                'itemID' => $number->getItemID()->getBytes(),
                'number' => $number->getNumber(),
                'numberType' => $info['type']
            ]);
            if ($info['type'] === TextMessageNumber::NUMBER_TYPE_TO) {
                $recipient_id = Uuid::uuid4();
                $message->recipients()->create([
                    'textMessageRecipientID' => $recipient_id->getBytes(),
                    'textMessageNumberID' => $number_id,
                    'status' => TextMessageRecipient::STATUS_PENDING
                ]);
                $this->recipient_ids[] = $recipient_id;
            }
        }
    }

    /**
     * Persist message to database
     *
     * @throws MessageException
     * @throws \App\Services\Text\Exceptions\NumberException
     */
    public function save(): void
    {
        if ($this->saved) {
            throw new MessageException('Message already saved');
        }

        $recipients = Number::filterSuppressed($this->recipients);
        if (count($recipients) === 0) {
            throw new MessageException('No recipients defined or all are unsubscribed');
        }

        DB::transaction(function () use ($recipients) {
            $this->handleSave($recipients);
        });

        $this->saved = true;
    }

    /**
     * Get recipient ids
     *
     * @return UuidInterface[]|null
     */
    public function getRecipientIDs(): ?array
    {
        return $this->recipient_ids;
    }

    /**
     * Date/time to deliver message
     *
     * @param Carbon|null $datetime
     * @return $this
     */
    public function deliverAt(?Carbon $datetime): MessageInterface
    {
        $this->deliver_at = $datetime;
        return $this;
    }

    /**
     * Get send at date/time
     *
     * @return Carbon|null
     */
    public function getDeliverAt(): ?Carbon
    {
        return $this->deliver_at;
    }

    /**
     * Enqueue send job for each message recipient
     *
     * @throws MessageException
     */
    public function send(): void
    {
        if (!$this->saved) {
            $this->save();
        }
        if ($this->sent) {
            throw new MessageException('Message already sent');
        }

        foreach ($this->getRecipientIDs() as $recipient_id) {
            SendJob::enqueue($recipient_id)->handleAt($this->getDeliverAt());
        }

        $this->sent = true;
    }
}
