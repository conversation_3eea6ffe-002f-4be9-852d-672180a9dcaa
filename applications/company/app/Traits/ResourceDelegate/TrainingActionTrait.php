<?php

declare(strict_types=1);

namespace App\Traits\ResourceDelegate;

use App\Services\TrainingService;
use Core\Components\Resource\Classes\Request;

/**
 * Trait TrainingActionTrait
 *
 * @package App\Traits\ResourceDelegate
 */
trait TrainingActionTrait
{
    /**
     * Determines if user in request has training enabled
     *
     * @param Request $request
     * @return bool
     */
    protected function isTraining(Request $request): bool
    {
        return TrainingService::isUserInTraining($request->resource()->acl()->user());
    }

    /**
     * Add training action to complete list to be recorded later (at close of request)
     *
     * @param Request $request
     * @param int|array $actions
     */
    protected function completeTrainingAction(Request $request, int|array $actions): void
    {
        $curr_actions = $request->storage('training_actions', []);
        if (!is_array($actions)) {
            $actions = [$actions];
        }
        $curr_actions = array_unique(array_merge($curr_actions, $actions));
        $request->store('training_actions', $curr_actions);
    }

    /**
     * Queue up any completed training actions within request for user
     *
     * @param Request $request
     */
    protected function recordCompletedTrainingActions(Request $request): void
    {
        if (($actions = $request->storage('training_actions')) === null) {
            return;
        }
        TrainingService::queueCompleteUserAction($request->resource()->acl()->user(), $actions);
    }
}
