<?php

namespace App\Traits\Console;

use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use SplFileObject;

/**
 * Trait LockTrait
 *
 * @package App\Traits\Console
 */
trait LockTrait
{
    /**
     * @var SplFileObject|null
     */
    protected $lock_file = null;

    /**
     * Determine if lock file instance has been defined
     *
     * @return bool
     */
    protected function hasLockFile()
    {
        return $this->lock_file !== null;
    }

    /**
     * Get lock file object
     *
     * @return SplFileObject
     * @throws AppException
     */
    protected function getLockFile()
    {
        if (!$this->hasLockFile()) {
            if (!isset($this->lock_id)) {
                throw new AppException('No lock id defined');
            }
            $this->lock_file = new SplFileObject(Path::locks("{$this->lock_id}.lock"), 'w+');
        }
        return $this->lock_file;
    }

    /**
     * Try to obtain lock
     *
     * @return bool
     * @throws AppException
     */
    protected function obtainLock()
    {
        return $this->getLockFile()->flock(LOCK_EX | LOCK_NB);
    }

    /**
     * Release lock
     *
     * @throws AppException
     */
    protected function releaseLock()
    {
        if (!$this->hasLockFile()) {
            return;
        }
        $this->getLockFile()->flock(LOCK_UN);
    }
}
