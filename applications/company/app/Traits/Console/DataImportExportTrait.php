<?php

declare(strict_types=1);

namespace App\Traits\Console;

use Core\Exceptions\AppException;
use JsonException;
use SplFileObject;

/**
 * Trait ArrayImportExportTrait
 *
 * Console helper to make working with array data easier.
 *
 * @package App\Traits\Console
 * @mixin \Core\Components\Console\Commands\BaseCommand
 */
trait DataImportExportTrait
{
    /**
     * Decode JSON and handle errors
     *
     * @param string $data
     * @return array
     * @throws AppException
     */
    protected function decodeJson(string $data): array
    {
        try {
            $data = json_decode($data, true, 512, JSON_THROW_ON_ERROR);
        } catch (JsonException $e) {
            throw (new AppException('Unable to parse JSON data'))->setLastException($e);
        }
        if (!is_array($data)) {
            throw new AppException('Parsed JSON data must be an array');
        }
        return $data;
    }

    /**
     * Get array of data from various sources
     *
     * @param array $paths List of paths to check for includes
     * @return mixed
     * @throws AppException
     */
    protected function fetchData(array $paths)
    {
        $methods = [
            'include' => 'Include',
            'file' => 'File',
            'url' => 'URL'
        ];

        $selected = null;
        foreach (array_keys($methods) as $method) {
            if (!$this->args->has($method)) {
                continue;
            }
            $selected = $method;
        }
        if ($selected === null) {
            $this->console->line('Choose an import method:');
            $this->console->line();
            $selected = $this->console->menu($methods, [
                'cancel' => true,
                'cancel_title' => '[cancel]'
            ]);
            if ($selected === false) {
                throw new AppException('Array import aborted');
            }
        }

        switch ($selected) {
            case 'include':
            case 'file':
                $path = $this->console->get($selected, 'Path');
                if (!file_exists($path)) {
                    if (str_starts_with($path, DIRECTORY_SEPARATOR)) {
                        throw new AppException('Unable to find file: %s', $path);
                    }
                    $found = false;
                    foreach ($paths as $basepath) {
                        $new_path = $basepath . $path;
                        if (!file_exists($new_path)) {
                            continue;
                        }
                        $path = $new_path;
                        $found = true;
                        break;
                    }
                    if (!$found) {
                        throw new AppException('Unable to find file: %s', $path);
                    }
                }
                switch ($selected) {
                    case 'include':
                        $data = include($path);
                        break;
                    case 'file':
                        if (($data = file_get_contents($path)) === false) {
                            throw new AppException('Unable to get contents of file: %s', $path);
                        }
                        $data = $this->decodeJson($data);
                        break;
                }
                return $data;
            case 'url':
                $url = $this->console->get('url', 'URL');
                if (($data = @file_get_contents($url)) === false) {
                    throw new AppException('Unable to download contents from URL: %s', $url);
                }
                return $this->decodeJson($data);
        }
    }

    /**
     * Output data to console or file based on user choice
     *
     * @param array $data
     * @throws AppException
     */
    protected function outputArray(array $data): void
    {
        $methods = [
            'console-output' => 'Output to Console',
            'file-output' => 'Output to File'
        ];

        $this->console->line('How would you like to export the data?');
        $this->console->line();
        $handle = $this->console->menu($methods);
        switch ($handle) {
            case 'console-output':
                $this->console->line();
                $this->console->out(json_encode($data, JSON_PRETTY_PRINT));
                $this->console->line();
                break;
            case 'file-output':
                $filename = $this->console->prompt('File path', [
                    'default' => 'file.json'
                ]);
                if (file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT)) === false) {
                    throw new AppException('Unable to write to file: %s', $filename);
                }
                $this->console->info('Data written to file: %s', $filename);
                break;
        }
    }

    /**
     * Output CSV data to console or file depending on user choice
     *
     * If lines is supplied, only the specified number of lines will be returned.
     *
     * @param string $path
     * @param int|null $lines
     */
    protected function outputCsv(string $path, ?int $lines = null)
    {
        $methods = [
            'console-output' => 'Output to Console',
            'file-output' => 'Output to File'
        ];

        $file = new SplFileObject($path);

        $this->console->line('How would you like to export the CSV?');
        $this->console->line();
        $handle = $this->console->menu($methods);
        switch ($handle) {
            case 'console-output':
                $this->console->line();
                $i = 0;
                while (!$file->eof() && ($line = $file->fgets()) !== false) {
                    $this->console->out($line);
                    if (++$i > $lines) {
                        break;
                    }
                }
                $this->console->line();
                break;
            case 'file-output':
                $filename = $this->console->prompt('File path', [
                    'default' => 'file.csv'
                ]);
                $new_file = new SplFileObject($filename, 'w');
                $i = 0;
                while (!$file->eof() && ($line = $file->fgets()) !== false) {
                    $new_file->fwrite($line);
                    if (++$i > $lines) {
                        break;
                    }
                }
                $this->console->info('Data written to file: %s', $filename);
                break;
        }
    }
}
