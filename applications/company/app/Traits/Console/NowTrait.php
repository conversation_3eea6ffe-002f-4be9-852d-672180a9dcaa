<?php

declare(strict_types=1);

namespace App\Traits\Console;

use Carbon\Carbon;
use Exception;
use Throwable;

/**
 * Trait NowTrait
 *
 * @package App\Traits\Console
 */
trait NowTrait
{
    /**
     * Get now
     *
     * If --now argument is passed, it is validated and turned into a Carbon instance. Otherwise, the current time
     * will be returned
     *
     * @return Carbon
     * @throws Exception
     */
    protected function getNow(): Carbon
    {
        if (($now = $this->args->get('now')) !== null) {
            try {
                $now = Carbon::createFromFormat('Y-m-d\TH:i:sT', $now)->utc();
                $this->console->info('Now: ' . $now->format('Y-m-d H:i:s T'));
            } catch (Throwable $e) {
                throw new Exception('--now must be a valid ISO 8601 date');
            }
        } else {
            $now = Carbon::now('UTC');
        }
        return $now;
    }
}
