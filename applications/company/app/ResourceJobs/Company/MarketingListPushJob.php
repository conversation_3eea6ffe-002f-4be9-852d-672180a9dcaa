<?php

declare(strict_types=1);

namespace App\ResourceJobs\Company;

use App\Attributes\JobAttribute;
use App\Services\HubspotApi\Jobs\CompanyUpdateOrCreateJob;
use App\Services\HubspotApiService;
use Core\Components\Queue\Classes\Job;

/**
 * Class MarketingListPushJob
 *
 * @package App\ResourceJobs\Company
 */
#[JobAttribute(type: 36)]
class MarketingListPushJob extends Job
{
    /**
     * MarketingListPushJob constructor
     *
     * @param int $company_id
     */
    public function __construct(protected int $company_id)
    {}

    /**
     * Handle job
     */
    public function handle(): void
    {
        $hubspot_service = new HubspotApiService();
        if ($hubspot_service->isEnabled()) {
            CompanyUpdateOrCreateJob::enqueue($this->company_id);
        }

        // add other marketing sources in the future here
    }
}
