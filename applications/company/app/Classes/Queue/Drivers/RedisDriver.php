<?php

declare(strict_types=1);

namespace App\Classes\Queue\Drivers;

use App\Attributes\JobAttribute;
use App\Classes\Log;
use App\Classes\Queue\JobEnvelopes\RedisJobEnvelope;
use App\Classes\Queue\JobProcessors\RedisJobProcessor;
use Carbon\Carbon;
use Common\Models\QueueJob;
use Core\Classes\Config;
use Core\Components\Queue\Exceptions\QueueException;
use Core\Components\Queue\Interfaces\{DriverInterface, JobBuilderInterface, JobEnvelopeInterface, ManagerInterface};
use Core\Interfaces\DBInterface;
use Core\StaticAccessors\App;
use Illuminate\Database\Connection;
use Monolog\Logger;
use Ramsey\Uuid\{Uuid, UuidInterface};
use Redis;
use stdClass;

/**
 * Class RedisDriver
 *
 * @package App\Classes\Queue\Drivers
 */
class RedisDriver implements DriverInterface
{
    /**
     * @var null|Logger
     */
    protected ?Logger $log = null;

    /**
     * @var string|null Name of db connection to use
     */
    protected ?string $db_connection_name = null;

    /**
     * @var string|null Name of redis connection to use
     */
    protected ?string $redis_connection_name = null;

    /**
     * @var string|null Default channel for jobs
     */
    protected ?string $default_channel = null;

    /**
     * @var Connection|null DB connection instance
     */
    protected ?Connection $db_connection = null;

    /**
     * @var Redis|null Redis client instance
     */
    protected ?Redis $redis_client = null;

    /**
     * Get log instance configured for redis queue
     *
     * @return Logger
     */
    public function getLog(): Logger
    {
        if ($this->log === null) {
            $this->log = Log::create('redis_queue', [
                'email' => [
                    'subject' => 'System Error - Redis Queue'
                ],
                'slack' => [
                    'username' => 'redis-queue'
                ],
                'file' => 'queue.log'
            ]);
        }
        return $this->log;
    }

    /**
     * RedisDriver constructor
     *
     * @param ManagerInterface $manager
     * @param Config $config
     * @param DBInterface $db
     */
    public function __construct(
        protected ManagerInterface $manager,
        protected Config $config,
        protected DBInterface $db
    ) {}

    /**
     * Get manager instance
     *
     * @return ManagerInterface
     */
    public function getManager(): ManagerInterface
    {
        return $this->manager;
    }

    /**
     * Configure driver
     *
     * @param array $config
     * @throws QueueException
     */
    public function configure(array $config): void
    {
        if (!isset($config['redis_connection'])) {
            throw new QueueException('No redis connection defined');
        }
        $this->redis_connection_name = $config['redis_connection'];
        if (!isset($config['db_connection'])) {
            throw new QueueException('No db connection defined');
        }
        $this->db_connection_name = $config['db_connection'];
        if (isset($config['default_channel'])) {
            $this->default_channel = $config['default_channel'];
        }
    }

    /**
     * Get and cache database connection instance
     *
     * @return Connection
     */
    public function getDbConnection(): Connection
    {
        if ($this->db_connection === null) {
            $this->db_connection = $this->db->getConnection($this->db_connection_name);
        }
        return $this->db_connection;
    }

    /**
     * Convert carbon date into SQL format with microseconds
     *
     * @param Carbon $date
     * @return string
     */
    public function getSqlDate(Carbon $date): string
    {
        return $date->format('Y-m-d H:i:s.u');
    }

    /**
     * Get now SQL timestamp
     *
     * @return string
     */
    public function getSqlDateNow(): string
    {
        return $this->getSqlDate(Carbon::now('UTC'));
    }

    /**
     * Configure, cache, and return redis client instance
     *
     * @return Redis
     */
    public function getRedisClient(): Redis
    {
        if ($this->redis_client === null) {
            $config = $this->config->get("database.redis.{$this->redis_connection_name}", []);
            $client = new Redis();
            $port = $config['port'] ?? 6379;
            $timeout = $config['timeout'] ?? 10;
            $client->connect($config['host'], (int) $port, (int) $timeout);
            if (!empty($config['password'])) {
                $client->auth($config['password']);
            }
            if (isset($config['database'])) {
                $client->select((int) $config['database']);
            }
            $this->redis_client = $client;
        }
        return $this->redis_client;
    }

    /**
     * Get default channel for driver or return the manager default
     *
     * @return string
     */
    public function getDefaultChannel(): string
    {
        return $this->default_channel ?? $this->manager->getDefaultChannel();
    }

    /**
     * Convert channel name into ID for database storage
     *
     * @param string $name
     * @return int|null
     * @throws QueueException
     */
    public function getChannelIDByName(string $name): ?int
    {
        if (($channel = $this->manager->getChannel($name)) === null) {
            throw new QueueException('Unable to find channel: %s', $name);
        }
        return $channel['id'] ?? null;
    }

    /**
     * Convert channel id into name
     *
     * @param int $id
     * @return string|null
     */
    public function getChannelNameByID(int $id): ?string
    {
        foreach ($this->manager->getChannels() as $name => $channel) {
            if (!isset($channel['id']) || $channel['id'] !== $id) {
                continue;
            }
            return $name;
        }
        return null;
    }

    /**
     * Convert all channel names to their associated id's as defined by queue config
     *
     * @param array $channels
     * @return array
     * @throws QueueException
     */
    protected function convertChannelNamesToIds(array $channels): array
    {
        $ids = [];
        foreach ($channels as $channel) {
            if (($channel_id = $this->getChannelIDByName($channel)) === null) {
                throw new QueueException('Unable to find id for channel: %s', $channel);
            }
            $ids[] = $channel_id;
        }
        return array_unique($ids);
    }

    /**
     * Push job builder to redis
     *
     * Converts builder to redis job envelope before pushing.
     *
     * @param JobBuilderInterface $job_builder
     * @throws QueueException
     */
    public function pushFromBuilder(JobBuilderInterface $job_builder): void
    {
        $job_envelope = RedisJobEnvelope::makeFromBuilder($job_builder);
        $this->push($job_envelope);
    }

    /**
     * Push job envelope onto queue
     *
     * @param JobEnvelopeInterface $job_envelope
     * @param bool $allow_delay
     * @throws QueueException
     */
    public function push(JobEnvelopeInterface $job_envelope, bool $allow_delay = true): void
    {
        $job = $job_envelope->getJob();
        if (!$this->manager->isJobRegistered(get_class($job))) {
            throw new QueueException('Job %s is not registered in queue.php config file', $job::class);
        }
        if (($attribute = $job->getAttribute()) === null || !($attribute instanceof JobAttribute)) {
            throw new QueueException('Job attribute %s not applied to job', JobAttribute::class);
        }
        if (($channel = $job_envelope->getChannel()) === null) {
            $channel = $attribute->getChannel() ?? $this->getDefaultChannel();
            $job_envelope->setChannel($channel);
        }
        if (!$this->manager->hasChannel($channel)) {
            throw new QueueException('Unable to find channel: %s', $channel);
        }

        $delay = true;
        $now = Carbon::now('UTC');
        if (!$allow_delay || ($handle_at = $job_envelope->getHandleAt()) === null || $handle_at->lt($now)) {
            $job_envelope->setHandleAt($now);
            $delay = false;
        }
        if (($max_tries = $job_envelope->getMaxTries()) === null) {
            $job_envelope->setMaxTries($attribute->getMaxTries());
        }

        if ($delay) {
            $now = $this->getSqlDateNow();
            $this->getDbConnection()->table(RedisJobProcessor::TABLE_JOBS)
                ->insert([
                    'queueJobID' => $job_envelope->getID()->getBytes(),
                    'channel' => $this->getChannelIDByName($channel) ?? throw new QueueException('No id defined for channel: %s', $channel),
                    'type' => $attribute->getType(),
                    'job' => igbinary_serialize($job),
                    'handleAt' => $this->getSqlDate($job_envelope->getHandleAt()),
                    'maxTries' => $job_envelope->getMaxTries(),
                    'status' => QueueJob::STATUS_PENDING,
                    'createdAt' => $now,
                    'updatedAt' => $now
                ]);
            return;
        }

        $job_envelope->setReservedAt($now);

        $id = $job_envelope->getID()->toString();
        $this->getRedisClient()->multi()
            ->hSet("channel-job:{$channel}", $id, igbinary_serialize($job_envelope))
            ->lPush("channel:{$channel}", $id)
            ->exec();
    }

    /**
     * Pop last job off of queue to be processed
     *
     * @param array|null $channels
     * @return RedisJobProcessor|null
     * @throws QueueException
     */
    public function pop(?array $channels = null): ?RedisJobProcessor
    {
        $channels ??= [$this->getDefaultChannel()];
        $client = $this->getRedisClient();
        foreach ($channels as $channel) {
            $id = $client->rpoplpush("channel:{$channel}", "channel-processing:{$channel}");
            if ($id === false) {
                continue;
            }
            if (($job_envelope = $client->hGet("channel-job:{$channel}", $id)) === false) {
                throw new QueueException('Unable to find channel %s job: %s', $channel, $id);
            }
            return new RedisJobProcessor($this, igbinary_unserialize($job_envelope));
        }
        return null;
    }

    /**
     * Convert DB result into configured job envelope
     *
     * @param stdClass $result
     * @return RedisJobEnvelope
     */
    protected function getJobEnvelopeFromDbResult(stdClass $result): RedisJobEnvelope
    {
        $envelope = new RedisJobEnvelope(igbinary_unserialize($result->job));
        $envelope->setID(Uuid::fromBytes($result->queueJobID));
        $envelope->setChannel($this->getChannelNameByID($result->channel));
        $envelope->setHandleAt(Carbon::parse($result->handleAt, 'UTC'));
        $envelope->setMaxTries($result->maxTries);
        $envelope->setTries($result->tries);
        $envelope->setReservedAt(Carbon::parse($result->reservedAt, 'UTC'));
        $envelope->setIsPersisted(true);
        return $envelope;
    }

    /**
     * Get all jobs which can be run before the passed timestamp
     *
     * @param Carbon $timestamp
     * @return RedisJobEnvelope[]
     * @throws \Throwable
     */
    public function getScheduledJobs(Carbon $timestamp): array
    {
        $db = $this->getDbConnection();
        return $db->transaction(function () use ($db, $timestamp) {
            $results = $db->table(RedisJobProcessor::TABLE_JOBS)
                ->where('status', QueueJob::STATUS_PENDING)
                ->where('handleAt', '<=', $timestamp)
                ->orderBy('handleAt')
                ->lockForUpdate()
                ->get();
            $jobs = [];
            if (count($results) > 0) {
                $now = Carbon::now('UTC');
                foreach ($results as $result) {
                    $job = $this->getJobEnvelopeFromDbResult($result);
                    $job->setReservedAt($now->copy());
                    $jobs[] = $job;
                }
                $db->table(RedisJobProcessor::TABLE_JOBS)
                    ->whereIn('queueJobID', array_map(fn(RedisJobEnvelope $envelope): string => $envelope->getID()->getBytes(), $jobs))
                    ->update([
                        'status' => QueueJob::STATUS_RESERVED,
                        'reservedAt' => $this->getSqlDate($now)
                    ]);
            }
            return $jobs;
        });
    }

    /**
     * Retry all failed jobs after a certain time
     *
     * If force is true, then all jobs will be retried regardless of status. Only works in debug mode.
     * If no after datetime is provide, a default of 5 mins ago is used to prevent accidentally retrying very old jobs.
     *
     * @param Carbon|null $after
     * @param bool $force
     * @return int
     * @throws QueueException
     */
    public function retryAll(?Carbon $after = null, bool $force = false): int
    {
        $after ??= Carbon::now('UTC')->subMinutes(5);
        $query = $this->getDbConnection()->table(RedisJobProcessor::TABLE_JOBS);
        if (!App::debugEnabled() || !$force) {
            $query->where('status', QueueJob::STATUS_FAILED);
            $query->where('failedAt', '>', $after);
        }
        $jobs = $query->get();
        $count = $jobs->count();
        if ($count === 0) {
            return 0;
        }
        foreach ($jobs as $job) {
            $this->push($this->getJobEnvelopeFromDbResult($job)->requeue());
        }
        return $count;
    }

    /**
     * Retry single job
     *
     * If force is true, then job will be retried even if it was not in failed state. Only works with debug mode enabled.
     *
     * @param UuidInterface $id - Job uuid
     * @param bool $force
     * @throws QueueException
     */
    public function retryOne(UuidInterface $id, bool $force = false): void
    {
        $queue_job = $this->getDbConnection()->table(RedisJobProcessor::TABLE_JOBS)
            ->where('queueJobID', $id->getBytes())
            ->first();
        if ($queue_job === null) {
            throw new QueueException('Unable to find job with id: %s', $id->toString());
        }

        if ((!App::debugEnabled() || !$force) && $queue_job->status !== QueueJob::STATUS_FAILED) {
            throw new QueueException('Only jobs with a status of failed can be retried');
        }

        $this->push($this->getJobEnvelopeFromDbResult($queue_job)->requeue());
    }

    /**
     * Get count of pending jobs in database for defined channels
     *
     * @param array|null $channels
     * @return int
     * @throws QueueException
     */
    public function getPendingJobCount(?array $channels): int
    {
        $query = $this->getDbConnection()->table(RedisJobProcessor::TABLE_JOBS)
            ->where('status', QueueJob::STATUS_PENDING);
        if ($channels !== null) {
            $query->whereIn('channel', $this->convertChannelNamesToIds($channels));
        }
        return $query->count();
    }

    /**
     * Get count of reserved jobs in redis queue list for defined channels
     *
     * @param array|null $channels
     * @return int
     */
    public function getReservedJobCount(?array $channels): int
    {
        $channels ??= array_keys($this->manager->getChannels());
        $count = 0;
        $client = $this->getRedisClient();
        foreach ($channels as $channel) {
            $count += $client->lLen("channel:{$channel}");
        }
        return $count;
    }

    /**
     * Remove all jobs for specified channel(s) or all if none specified
     *
     * @param array|null $channels
     * @throws QueueException
     */
    public function clearJobs(?array $channels = null): void
    {
        $channels ??= array_keys($this->manager->getChannels());
        $client = $this->getRedisClient();
        foreach ($channels as $channel) {
            $client->del("channel:{$channel}", "channel-processing:{$channel}", "channel-job:{$channel}");
        }
        $db = $this->getDbConnection();
        $channel_ids = $this->convertChannelNamesToIds($channels);
        $db->table(RedisJobProcessor::TABLE_JOB_LOG)
            ->join(RedisJobProcessor::TABLE_JOBS, RedisJobProcessor::TABLE_JOBS . '.queueJobID', '=', RedisJobProcessor::TABLE_JOB_LOG . '.queueJobID')
            ->whereIn(RedisJobProcessor::TABLE_JOBS . '.channel', $channel_ids)
            ->delete();
        $db->table(RedisJobProcessor::TABLE_JOBS)
            ->whereIn('channel', $channel_ids)
            ->delete();
    }
}
