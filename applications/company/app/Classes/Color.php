<?php

declare(strict_types=1);

namespace App\Classes;

/**
 * Class Color
 *
 * @package App\Classes
 */
class Color
{
    /**
     * Convert hex color string into RGB components
     *
     * @param string $hex Hexadecimal color (can include #)
     * @return array<int>
     */
    public static function hexToRgb(string $hex): array
    {
        $hex = ltrim($hex, '#');
        return array_map(fn(string $c): int => hexdec(str_pad($c, 2, $c)), str_split($hex, strlen($hex) > 3 ? 2 : 1));
    }

    /**
     * Get color which contrasts with the one passed
     *
     * Used to find proper text color to show on a background.
     *
     * @param string $hex_color
     * @param string $hex_black
     * @param string $hex_white
     * @return string
     */
    public static function getContrastColor(string $hex_color, string $hex_black = '#000000', string $hex_white = '#FFFFFF'): string
    {
        [$r1, $g1, $b1] = self::hexToRgb($hex_color);
        [$r2, $g2, $b2] = self::hexToRgb('000000');

        $l1 = 0.2126 * pow($r1 / 255, 2.2) +
            0.7152 * pow($g1 / 255, 2.2) +
            0.0722 * pow($b1 / 255, 2.2);

        $l2 = 0.2126 * pow($r2 / 255, 2.2) +
            0.7152 * pow($g2 / 255, 2.2) +
            0.0722 * pow($b2 / 255, 2.2);

        $contrast_ratio = (int) ($l1 > $l2 ? (($l1 + 0.05) / ($l2 + 0.05)) : (($l2 + 0.05) / ($l1 + 0.05)));
        return $contrast_ratio > 5 ? $hex_black : $hex_white;
    }
}
