<?php

namespace App\Classes;

use App\Exceptions\Email\SendFailureException;
use App\Exceptions\EmailException;
use Common\Models\Company;
use Common\Models\Customer;
use Common\Models\Project;
use Common\Models\User;
use Core\Classes\Config;
use Closure;
use PHPMailer\PHPMailer\PHPMailer;
use Throwable;

use function Core\Functions\env;

/**
 * Class Email
 *
 * @package App\Classes
 */
class Email
{
    /**
     * Email type constants
     */
    const TYPE_CUSTOMER = 1;
    const TYPE_SUPPORT = 3;
    const TYPE_SYSTEM = 4;

    /**
     * Address type constants
     */
    const ADDRESS_TYPE_TO = 1;
    const ADDRESS_TYPE_CC = 2;
    const ADDRESS_TYPE_BCC = 3;

    /**
     * Config instance
     *
     * @var Config
     */
    protected $config;

    /**
     * PHPMailer instance
     *
     * @var PHPMailer
     */
    protected $mailer;

    /**
     * PHPMailer options
     *
     * Maps to public properties of <PERSON><PERSON><PERSON>ail<PERSON> instance
     *
     * @var array
     */
    protected $mailer_options = [
        'Priority' => 3, // Highest priority - Email priority (1 = High, 3 = Normal, 5 = low)
        'CharSet' => 'UTF-8',
        'Encoding' => '8bit',
        'WordWrap' => 900, // RFC 2822 Compliant for Max 998 characters per line
        'XMailer' => ' ', // Whitespace means to leave the header out
        'SMTPDebug' => 3,
        'Timeout' => 10 // Seconds
    ];

    /**
     * Debug messages from PHPMailer
     *
     * @var array
     */
    protected $debug_messages = [];

    /**
     * Email type
     *
     * See email type constants
     *
     * @var null|int
     */
    protected $type = null;

    /**
     * Customer model instance
     *
     * @var null|Customer
     */
    protected $customer = null;

    /**
     * Company model instance
     *
     * @var null|Company
     */
    protected $company = null;

    /**
     * Project model instance
     *
     * @var null|Project
     */
    protected $project = null;

    /**
     * Salesperson User model instance
     *
     * @var null|User
     */
    protected $salesperson = null;

    /**
     * User model instance
     *
     * @var null|User
     */
    protected $user = null;

    /**
     * Collection of User model instances to also email
     *
     * @var array
     */
    protected $notify_users = [];

    /**
     * Email subject
     *
     * @var null|string
     */
    protected $subject = null;

    /**
     * Email recipients
     *
     * @var array
     */
    protected $addresses = [];

    /**
     * Email from value
     *
     * @var null|array
     */
    protected $from = null;

    /**
     * Email reply-to value
     *
     * @var null|array
     */
    protected $reply_to = null;

    /**
     * Determines if send() method has been called
     *
     * @var bool
     */
    protected $sent = false;

    /**
     * Get email types
     *
     * @return array
     */
    public static function getTypes()
    {
        return [
            self::TYPE_CUSTOMER => 'Customer',
            self::TYPE_SUPPORT => 'Support',
            self::TYPE_SYSTEM => 'System'
        ];
    }

    /**
     * Email constructor
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->mailer = new PHPMailer(true);
        if (count($this->mailer_options) > 0) {
            foreach ($this->mailer_options as $var => $value) {
                $this->mailer->{$var} = $value;
            }
        }
        $this->mailer->Debugoutput = function($message) {
            $this->debug_messages[] = $message;
        };
    }

    /**
     * Set email type
     *
     * @param int $type
     * @return $this
     *
     * @throws EmailException
     */
    public function type($type)
    {
        if (!array_key_exists($type, self::getTypes())) {
            throw new EmailException('Invalid email type');
        }
        $this->type = $type;
        return $this;
    }

    /**
     * Set company model instance
     *
     * @param Company $company
     * @return $this
     */
    public function company(Company $company)
    {
        $this->company = $company;
        return $this;
    }

    /**
     * Set company model instance by providing ID which is automatically loaded
     *
     * This was added to make it easier to work with legacy code, the company() method should be used when possible
     *
     * @param int $company_id
     * @return $this
     *
     * @throws EmailException
     */
    public function companyID($company_id)
    {
        $this->company = Company::find($company_id);
        if ($this->company === null) {
            throw new EmailException('Unable to find company');
        }
        return $this;
    }

    /**
     * Set customer model instance
     *
     * @param Customer $customer
     * @return $this
     */
    public function customer(Customer $customer)
    {
        $this->customer = $customer;
        return $this;
    }

    /**
     * Set customer model instance by providing ID which is automatically loaded
     *
     * This was added to make it easier to work with legacy code, the customer() method should be used when possible
     *
     * @param int $customer_id
     * @return $this
     * @throws EmailException
     */
    public function customerID($customer_id)
    {
        $this->customer = Customer::find($customer_id);
        if ($this->customer === null) {
            throw new EmailException('Unable to find customer');
        }
        return $this;
    }

    /**
     * Set project model instance
     *
     * @param Project $project
     * @return $this
     */
    public function project(Project $project)
    {
        $this->project = $project;
        return $this;
    }

    /**
     * Set project model instance by providing ID which is automatically loaded
     *
     * This was added to make it easier to work with legacy code, the project() method should be used when possible
     *
     * @param int $project_id
     * @return $this
     *
     * @throws EmailException
     */
    public function projectID($project_id)
    {
        $this->project = Project::find($project_id);
        if ($this->project === null) {
            throw new EmailException('Unable to find project');
        }
        return $this;
    }

    /**
     * Set salesperson User model instance
     *
     * @param User $salesperson
     * @return $this
     */
    public function salesperson(User $salesperson)
    {
        $this->salesperson = $salesperson;
        return $this;
    }

    /**
     * Set salesperson User model instance by providing ID which is automatically loaded
     *
     * This was added to make it easier to work with legacy code, the salesperson() method should be used when possible
     *
     * @param int $salespersonID
     * @return $this
     *
     * @throws EmailException
     */
    public function salespersonID($salespersonID)
    {
        $this->salesperson = User::find($salespersonID);
        if ($this->salesperson === null) {
            throw new EmailException('Unable to find salesperson');
        }
        return $this;
    }

    /**
     * Set User model instance
     *
     * @param User $user
     * @return $this
     */
    public function user(User $user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Set user model instance by providing ID which is automatically loaded
     *
     * This was added to make it easier to work with legacy code, the user() method should be used when possible
     *
     * @param int $user_id
     * @return $this
     *
     * @throws EmailException
     */
    public function userID($user_id)
    {
        $this->user = User::find($user_id);
        if ($this->user === null) {
            throw new EmailException('Unable to find user');
        }
        return $this;
    }

    /**
     * Add User instance to add as additional recipient to email
     *
     * User's will be added to the email based on the address type provided
     *
     * @param User $user
     * @param int $type
     * @return $this
     */
    public function notifyUser(User $user, $type = self::ADDRESS_TYPE_BCC)
    {
        $this->notify_users[] = compact('user', 'type');
        return $this;
    }

    /**
     * Add User instance by providing ID, which is automatically loaded, to add as additional recipient
     *
     * This was added to make it easier to work with legacy code, the notifyUser() method should be used when possible
     *
     * @param $user_id
     * @param int $type
     * @return Email
     * @throws EmailException
     */
    public function notifyUserID($user_id, $type = self::ADDRESS_TYPE_BCC)
    {
        $user = User::find($user_id);
        if ($user === null) {
            throw new EmailException('Unable to find user to notify');
        }
        return $this->notifyUser($user, $type);
    }

    /**
     * Get email type
     *
     * Validate to make sure it exists
     *
     * @return int
     *
     * @throws EmailException
     */
    protected function getType()
    {
        if ($this->type === null) {
            throw new EmailException('No email type defined');
        }
        return $this->type;
    }

    /**
     * Determines if email is allowed to be sent based on provided model data
     *
     * @return bool
     */
    public function canSend()
    {
        switch ($this->getType()) {
            case self::TYPE_CUSTOMER:
                return (!$this->customer->isUnsubscribed && $this->customer->email !== null);
        }
        return true;
    }

    /**
     * Add address by type
     *
     * @param int $type
     * @param string $email
     * @param string $name
     */
    protected function addAddress($type, $email, $name)
    {
        if (!isset($this->addresses[$type])) {
            $this->addresses[$type] = [];
        }
        $this->addresses[$type][] = compact('email', 'name');
    }

    /**
     * Check if address type has any addresses defined
     *
     * @param int $type
     * @return bool
     */
    protected function hasAddress($type)
    {
        return isset($this->addresses[$type]);
    }

    /**
     * Specific from value
     *
     * @param string $email
     * @param string $name
     * @return $this
     */
    public function from($email, $name = '')
    {
        $this->from = compact('email', 'name');
        return $this;
    }

    /**
     * Specify reply-to value
     *
     * @param string $email
     * @param string $name
     * @return $this
     */
    public function replyTo($email, $name = '')
    {
        $this->reply_to = compact('email', 'name');
        return $this;
    }

    /**
     * Add To recipient
     *
     * @param string $email
     * @param string $name
     * @return $this
     */
    public function to($email, $name = '')
    {
        $this->addAddress(self::ADDRESS_TYPE_TO, $email, $name);
        return $this;
    }

    /**
     * Add CC recipient
     *
     * @param string $email
     * @param string $name
     * @return $this
     */
    public function cc($email, $name = '')
    {
        $this->addAddress(self::ADDRESS_TYPE_CC, $email, $name);
        return $this;
    }

    /**
     * Add BBC recipient
     *
     * @param string $email
     * @param string $name
     * @return $this
     */
    public function bcc($email, $name = '')
    {
        $this->addAddress(self::ADDRESS_TYPE_BCC, $email, $name);
        return $this;
    }

    /**
     * Specify subject for email
     *
     * @param string $data
     * @return $this
     */
    public function subject($data)
    {
        $this->subject = $data;
        return $this;
    }

    /**
     * Add attachment
     *
     * @param string $path
     * @param string $name
     * @return $this
     */
    public function attach($path, $name = '')
    {
        $this->mailer->addAttachment($path, $name);
        return $this;
    }

    /**
     * Add custom header
     *
     * @param string $name
     * @param null|string $value
     * @return $this
     */
    public function customHeader($name, $value = null)
    {
        $this->mailer->addCustomHeader($name, $value);
        return $this;
    }

    /**
     * Allows for direct configuration of mailer by providing Closure
     *
     * PHPMailer instance is provided as a parameter
     *
     * @param Closure $closure
     * @return $this
     */
    public function configureMailer(Closure $closure)
    {
        call_user_func($closure, $this->mailer);
        return $this;
    }

    /**
     * Get PHPMailer instance
     *
     * @return PHPMailer
     */
    public function getMailer()
    {
        return $this->mailer;
    }

    /**
     * Enable HTML for email, add body and alternate body
     *
     * @param string $body
     * @param null|string $alt_body
     * @return $this
     */
    public function html($body, $alt_body = null)
    {
        $this->mailer->isHTML(true);
        $this->mailer->Body = $body;
        if ($alt_body !== null) {
            $this->mailer->AltBody = $alt_body;
        }
        return $this;
    }

    /**
     * Disable HTML for email, add text body
     *
     * @param string $body
     * @return $this
     */
    public function text($body)
    {
        $this->mailer->isHTML(false);
        $this->mailer->Body = $body;
        return $this;
    }

    /**
     * Add notify users to address list
     */
    protected function addNotifyUsers()
    {
        if (count($this->notify_users) === 0) {
            return;
        }
        foreach ($this->notify_users as $notify_user) {
            $this->addAddress(
                $notify_user['type'],
                $notify_user['user']->userEmail,
                $notify_user['user']->userFirstName . ' ' . $notify_user['user']->userLastName
            );
        }
    }

    /**
     * Configure PHPMailer instance using email type, model data, and send
     *
     * @return bool
     *
     * @throws EmailException
     */
    public function send()
    {
        if ($this->sent) {
            throw new EmailException('Email already sent, please use a new instance instead of reusing this one');
        }
        if ($this->subject === null) {
            throw new EmailException('Subject is required');
        }

        // validate passed data based on type
        $type = $this->getType();
        switch ($type) {
            case self::TYPE_CUSTOMER:
                if ($this->customer === null) {
                    throw new EmailException('Customer is required when using the customer email type');
                }

                // load company or make sure passed company matches customer
                if ($this->company === null) {
                    $this->company($this->customer->company);
                } elseif ($this->customer->companyID !== $this->company->companyID) {
                    throw new EmailException('Customer company ID does not match the passed Company');
                }

                // verify salesperson is part the passed company
                if ($this->salesperson !== null && $this->salesperson->companyID !== $this->company->companyID) {
                    throw new EmailException('Salesperson company ID does not match the customer company');
                }

                // verify notify users match company
                if (count($this->notify_users) > 0) {
                    foreach ($this->notify_users as $notify_user) {
                        if ($notify_user['user']->companyID === $this->company->companyID) {
                            continue;
                        }
                        throw new EmailException(
                            'Notify user %d is not associated the same company as customer', $notify_user['user']->userID
                        );
                    }
                }
                break;
        }

        $config = $this->config->get('mail');
        $this->customHeader('X-FX-Connection-Provider', 'system');
        switch ($config['driver']) {
            case 'smtp':
                if (!isset($config['smtp'])) {
                    throw new EmailException('SMTP setting not found in mail config');
                }
                $smtp_config = $config['smtp'];
                if (!isset($smtp_config['host'])) {
                    throw new EmailException('Host is required when using SMTP');
                }
                if (!isset($smtp_config['port'])) {
                    $smtp_config['port'] = 25;
                }
                $this->mailer->Mailer = 'smtp';
                $this->mailer->Host = $smtp_config['host'];
                $this->mailer->Port = $smtp_config['port'];
                if (
                    isset($smtp_config['user']) &&
                    $smtp_config['user'] !== '' &&
                    isset($smtp_config['pass']) &&
                    $smtp_config['pass'] !== ''
                ) {
                    $this->mailer->SMTPAuth = true;
                    $this->mailer->Username = $smtp_config['user'];
                    $this->mailer->Password = $smtp_config['pass'];
                }
                if (isset($smtp_config['secure']) && $smtp_config['secure']) {
                    $this->mailer->SMTPSecure = $smtp_config['secure'];
                }
                break;
        }

        switch ($type) {
            case self::TYPE_CUSTOMER:
                // handle from if not provided
                if ($this->from === null) {
                    if ($this->salesperson !== null) {
                        $from_name = $this->salesperson->userFirstName . ' ' . $this->salesperson->userLastName;
                        $from_email = $this->salesperson->userEmail;
                    } else {
                        $from_name = $this->company->name;
                        $from_email = $this->company->emailFrom;
                    }
                    $this->from($from_email, $from_name);
                }

                // handle reply to if not provided
                if ($this->reply_to === null) {
                    if ($this->salesperson !== null) {
                        $reply_name = $this->salesperson->userFirstName . ' ' . $this->salesperson->userLastName;
                        $reply_email = $this->salesperson->userEmail;
                    } else {
                        $reply_name = $this->company->name;
                        $reply_email = $this->company->emailReply;
                    }
                    $this->replyTo($reply_email, $reply_name);
                }

                // set recipient
                if (!$this->hasAddress(self::ADDRESS_TYPE_TO)) {
                    $this->to($this->customer->email, $this->customer->firstName . ' ' . $this->customer->lastName);
                }

                // add notify users
                $this->addNotifyUsers();

                // if project provided, add any additional contacts as CC recipients
                if ($this->project !== null && $this->project->emails !== null) {
                    foreach ($this->project->emails as $contact) {
                        if (empty($contact['email'])) {
                            continue;
                        }
                        $this->cc($contact['email'], (!empty($contact['name']) ? $contact['name'] : ''));
                    }
                }
                break;
            case self::TYPE_SUPPORT:
                $this->from('<EMAIL>', 'FX Application');
                if (!$this->hasAddress(self::ADDRESS_TYPE_TO)) {
                    $this->to(env('ERROR_EMAIL'));
                }
                break;
        }
        // disable internal tracking for emails sent with this class since they will not have proper data for
        // message tracking
        $this->customHeader('X-Mailgun-Track', 'no');
        $this->customHeader('X-Mailgun-Variables', json_encode(['track' => false]));

        // add addresses to mailer
        if (count($this->addresses) === 0) {
            throw new EmailException('No recipients defined');
        }
        foreach ($this->addresses as $type => $addresses) {
            foreach ($addresses as $address) {
                switch ($type) {
                    case self::ADDRESS_TYPE_TO:
                        $this->mailer->addAddress($address['email'], $address['name']);
                        break;
                    case self::ADDRESS_TYPE_CC:
                        $this->mailer->addCC($address['email'], $address['name']);
                        break;
                    case self::ADDRESS_TYPE_BCC:
                        $this->mailer->addBCC($address['email'], $address['name']);
                        break;
                }
            }
        }

        // handle from
        if ($this->from !== null) {
            $this->mailer->setFrom($this->from['email'], $this->from['name']);
        }

        // handle reply to
        if ($this->reply_to !== null) {
            $this->mailer->addReplyTo($this->reply_to['email'], $this->reply_to['name']);
        }

        // handle subject
        $this->mailer->Subject = $this->subject;

        // send email
        $this->sent = true;
        try {
            return $this->mailer->send();
        } catch (Throwable $e) {
            throw (new SendFailureException('Unable to send email - Reason: %s', $e->getMessage()))
                ->setLastException($e)
                ->store('_context', [
                    'mailer_debug' => $this->debug_messages
                ]);
        }
    }
}
