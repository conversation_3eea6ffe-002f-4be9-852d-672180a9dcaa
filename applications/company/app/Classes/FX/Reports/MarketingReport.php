<?php

namespace App\Classes\FX\Reports;

use App\Classes\FX\Report;
use App\Traits\FX\Report\StartEndDateTrait;
use Common\Models\ProjectSchedule;
use Core\Exceptions\AppException;

/**
 * Class MarketingReport
 *
 * Temporary report to replace old legacy class_Metrics_MarketingReport.php functionality. Returns the same payload
 * needed to drive the frontend since none of that was changed.
 *
 * @package App\Classes\FX\Reports
 */
class MarketingReport extends Report
{
    use StartEndDateTrait;

    /**
     * Get total marketing spends for specified time frame
     *
     * @return string
     * @throws \Exception
     */
    protected function getTotalMarketingCosts()
    {
        $sql =<<<SQL
SELECT IFNULL(SUM(spendAmount), 0) AS totalMarketingCosts 
FROM marketingSpend AS s
LEFT JOIN marketingType AS t ON t.marketingTypeID = s.marketingTypeID
WHERE t.companyID = :companyID 
AND s.startDate <= :endDate
AND s.endDate >=  :startDate
AND t.isDeleted IS NULL
AND s.isDeleted IS NULL
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        $row = $this->fetchResult($query);
        return $row['totalMarketingCosts'];
    }

    /**
     * Fetch all sources and build a hierarchy
     *
     * @return array
     * @throws AppException
     */
    protected function getSources()
    {
        $sql =<<<SQL
SELECT *
FROM `marketingType`
WHERE `companyID` = :companyID
ORDER BY `parentMarketingTypeID` ASC, `marketingTypeName` ASC
SQL;
        $query = $this->query($sql, [':companyID' => $this->getCompanyID()]);

        $source_default = [
            'spendAmount' => 0,
            'leads' => 0,
            'appointments' => 0,
            'bids' => 0,
            'sales' => 0,
            'grossSales' => 0,
            'subsources' => [],
            'unspecified' => false
        ];

        $sources = [
            null => array_merge($source_default, [
                'marketingTypeID' => null,
                'marketingTypeName' => 'Unspecified'
            ]),
            '__all__' => array_merge($source_default, [
                'marketingTypeID' => '__all__',
                'marketingTypeName' => 'All',
                'totalMarketingCosts' => $this->getTotalMarketingCosts(),
                'subsources' => [null]
            ])
        ];
        while ($source = $this->fetchResult($query)) {
            $source_name = $source['marketingTypeName'];
            if ($source['isDeleted']) {
                $source_name = $source_name . ' (deleted)';
            }
            $sources[$source['marketingTypeID']] = array_merge($source_default, [
                'marketingTypeID' => $source['marketingTypeID'],
                'marketingTypeName' => html_entity_decode($source_name, ENT_QUOTES, 'CP1252')
            ]);
            if ($source['parentMarketingTypeID'] === null) {
                $source['parentMarketingTypeID'] = '__all__';
            }
            if (!isset($sources[$source['parentMarketingTypeID']])) {
                throw new AppException('Parent marketing source should already exist');
            }
            $sources[$source['parentMarketingTypeID']]['subsources'][] = $source['marketingTypeID'];
        }
        return $sources;
    }

    /**
     * Get marketing spend per source within specified time frame and save data in global config
     *
     * @param array $sources
     * @throws \Exception
     */
    protected function injectMarketingCosts(&$sources)
    {
        $sql =<<<SQL
SELECT
    s.marketingTypeID,
    SUM(spendAmount) AS spendAmount
FROM marketingSpend AS s
JOIN marketingType AS t
    ON t.marketingTypeID = s.marketingTypeID
WHERE t.companyID = :companyID 
AND s.startDate <= :endDate
AND s.endDate >= :startDate
AND t.isDeleted IS NULL
AND s.isDeleted IS NULL
GROUP BY s.marketingTypeID
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        while ($row = $this->fetchResult($query)) {
            $sources[$row['marketingTypeID']]['spendAmount'] = $row['spendAmount'];
        }
    }

    /**
     * Get leads  per source within specified time frame and save data in global config
     *
     * @param array $sources
     * @throws \Exception
     */
    protected function injectLeads(&$sources)
    {
        $sql =<<<SQL
SELECT
    p.referralMarketingTypeID AS marketingTypeID,
    COUNT(*) AS leads 
FROM project AS p
JOIN property ON property.propertyID = p.propertyID
JOIN customer ON customer.customerID = property.customerID
WHERE customer.companyID = :companyID
AND p.createdAt >= :startDate 
AND p.createdAt <= :endDate
AND p.projectCancelled IS NULL
AND p.deletedAt IS NULL
GROUP BY p.referralMarketingTypeID
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        while ($row = $this->fetchResult($query)) {
            $sources[$row['marketingTypeID']]['leads'] = $row['leads'];
        }
    }

    /**
     * Get appointments per source within specified time frame and save data in global config
     *
     * @param array $sources
     * @throws \Exception
     */
    protected function injectAppointments(&$sources)
    {
        $sql =<<<SQL
SELECT
    p.referralMarketingTypeID AS marketingTypeID,
    COUNT(*) AS appointments
FROM projectSchedule AS s
JOIN project AS p ON p.projectID = s.projectID
JOIN property ON property.propertyID = p.propertyID
JOIN customer ON customer.customerID = property.customerID
WHERE customer.companyID = :companyID
AND p.createdAt >= :startDate
AND p.createdAt <= :endDate 
AND p.deletedAt IS NULL
AND s.scheduleType = :scheduleType 
AND s.cancelledAt IS NULL
AND s.replacedAt IS NULL
AND s.deletedAt IS NULL
AND p.projectCancelled IS NULL
GROUP BY p.referralMarketingTypeID
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':scheduleType' => ProjectSchedule::TYPE_EVALUATION,
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        while ($row = $this->fetchResult($query)) {
            $sources[$row['marketingTypeID']]['appointments'] = $row['appointments'];
        }
    }

    /**
     * Get sent bid count per source within specified time frame and save data in global config
     *
     * @param array $sources
     * @throws \Exception
     */
    protected function injectBids(&$sources)
    {
        $sql =<<<SQL
SELECT
    marketingTypeID,
    SUM(total) AS bids
FROM (
    SELECT
        p.referralMarketingTypeID AS marketingTypeID,
        COUNT(*) AS total
    FROM evaluationBid AS b
    JOIN evaluation AS e ON e.evaluationID = b.evaluationID
    JOIN project AS p ON p.projectID = e.projectID
    JOIN property ON property.propertyID = p.propertyID
    JOIN customer ON customer.customerID = property.customerID
    WHERE customer.companyID = :companyID
        AND b.bidFirstSent >= :startDate
        AND b.bidFirstSent <= :endDate
        AND b.bidFirstSent IS NOT NULL
        AND e.evaluationCancelled IS NULL
        AND e.deletedAt IS NULL
        AND p.projectCancelled IS NULL
        AND p.deletedAt IS NULL
    GROUP BY p.referralMarketingTypeID
    UNION ALL
    SELECT
        p.referralMarketingTypeID AS marketingTypeID,
        COUNT(*) AS total
    FROM customBid AS b
    JOIN evaluation AS e ON e.evaluationID = b.evaluationID
    JOIN project AS p ON p.projectID = e.projectID
    JOIN property ON property.propertyID = p.propertyID
    JOIN customer ON customer.customerID = property.customerID
    WHERE customer.companyID = :companyID
        AND b.bidFirstSent >= :startDate
        AND b.bidFirstSent <= :endDate
        AND b.bidFirstSent IS NOT NULL
        AND e.evaluationCancelled IS NULL
        AND e.deletedAt IS NULL
        AND p.projectCancelled IS NULL
        AND p.deletedAt IS NULL
    GROUP BY p.referralMarketingTypeID
) AS t
GROUP BY marketingTypeID
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        while ($row = $this->fetchResult($query)) {
            $sources[$row['marketingTypeID']]['bids'] = $row['bids'];
        }
    }

    /**
     * Get total bid sales and gross dollar amount per source within specified time frame and save data in global config
     *
     * @param array $sources
     * @throws \Exception
     */
    protected function injectSales(&$sources)
    {
        $sql =<<<SQL
SELECT
    marketingTypeID,
    SUM(total) AS sales,
    SUM(grossTotal) AS grossSales
FROM (
    SELECT
        p.referralMarketingTypeID AS marketingTypeID,
        COUNT(*) AS total,
        SUM(bidTotal + IFNULL(bidScopeChangeTotal, 0)) AS grossTotal
    FROM evaluationBid AS b
    JOIN evaluation AS e ON e.evaluationID = b.evaluationID
    JOIN project AS p ON p.projectID = e.projectID
    JOIN property ON property.propertyID = p.propertyID
    JOIN customer ON customer.customerID = property.customerID
    WHERE customer.companyID = :companyID
        AND b.bidAccepted >= :startDate
        AND b.bidAccepted <= :endDate
        AND b.bidFirstSent IS NOT NULL
        AND b.bidAccepted IS NOT NULL
        AND e.evaluationCancelled IS NULL
        AND e.deletedAt IS NULL
        AND p.projectCancelled IS NULL
        AND p.deletedAt IS NULL
    GROUP BY p.referralMarketingTypeID
    UNION ALL
    SELECT
        p.referralMarketingTypeID AS marketingTypeID,
        COUNT(*) AS total,
        SUM(bidTotal + IFNULL(bidScopeChangeTotal, 0)) AS grossTotal
    FROM customBid AS b
    JOIN evaluation AS e ON e.evaluationID = b.evaluationID
    JOIN project AS p ON p.projectID = e.projectID
    JOIN property ON property.propertyID = p.propertyID
    JOIN customer ON customer.customerID = property.customerID
    WHERE customer.companyID = :companyID
        AND b.bidAccepted >= :startDate
        AND b.bidAccepted <= :endDate
        AND b.bidFirstSent IS NOT NULL
        AND b.bidAccepted IS NOT NULL
        AND e.evaluationCancelled IS NULL
        AND e.deletedAt IS NULL
        AND p.projectCancelled IS NULL
        AND p.deletedAt IS NULL
    GROUP BY p.referralMarketingTypeID
) AS t
GROUP BY marketingTypeID
SQL;
        $query = $this->query($sql, [
            ':companyID' => $this->getCompanyID(),
            ':startDate' => $this->getStartDate()->toDateTimeString(),
            ':endDate' => $this->getEndDate()->toDateTimeString()
        ]);
        while ($row = $this->fetchResult($query)) {
            $sources[$row['marketingTypeID']]['sales'] = $row['sales'];
            $sources[$row['marketingTypeID']]['grossSales'] = $row['grossSales'];
        }
    }

    /**
     * Recursively compile source data
     *
     * Will compile subsource data if available and add it's data into the main source. Works to aggregate nested data
     * up the chain.
     *
     * @param $id
     * @param $sources
     * @return mixed
     */
    protected function compileSource($id, $sources)
    {
        $source = $sources[$id];

        $keys = ['spendAmount', 'leads', 'appointments', 'bids', 'sales', 'grossSales'];
        if (count($source['subsources']) > 0) {
            $subsources = [];
            foreach ($source['subsources'] as $subsource_id) {
                $subsource = $this->compileSource($subsource_id, $sources);
                foreach ($keys as $key) {
                    $source[$key] += $subsource[$key];
                }
                $subsources[$subsource_id] = $subsource;
            }
            $source['subsources'] = $subsources;
        }

        return $source;
    }

    /**
     * Run report
     *
     * Generates an array which can be used by the legacy marketing-report.php file. This was done to not have to
     * rework the frontend at this time.
     *
     * @return array
     * @throws \Exception
     */
    public function run()
    {
        $sources = $this->getSources();

        $this->injectMarketingCosts($sources);
        $this->injectLeads($sources);
        $this->injectAppointments($sources);
        $this->injectBids($sources);
        $this->injectSales($sources);

        $source = $this->compileSource('__all__', $sources);

        return $source;
    }
}
