<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllNotes extends Base
{
    private $results;

    public function getAllNotes()
    {
        $st = $this->db->prepare("SELECT HEX(drawingNotes.drawingID) AS drawingID, drawingNotes.nodeID, drawingNotes.xPos, drawingNotes.yPos, drawingNotes.zRotation, drawingNotes.text, drawingNotes.fontSize, drawingNotes.color, drawingNotes.createdAt, drawingNotes.updatedAt FROM drawingNotes
                                  JOIN appDrawing ON appDrawing.drawingID = drawingNotes.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'notes' => $returnObject);
        } else {
            $this->results = array('message' => 'No notes found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
