<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllSoilAnchors extends Base
{
    private $results;

    public function getAllSoilAnchors()
    {
        $st = $this->db->prepare("SELECT HEX(drawingSoilAnchors.drawingID) AS drawingID, drawingSoilAnchors.nodeID, drawingSoilAnchors.xPos, drawingSoilAnchors.yPos, drawingSoilAnchors.zRotation, drawingSoilAnchors.createdAt, drawingSoilAnchors.updatedAt FROM drawingSoilAnchors
                                  JOIN appDrawing ON appDrawing.drawingID = drawingSoilAnchors.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'soilAnchors' => $returnObject);
        } else {
            $this->results = array('message' => 'No soilAnchors found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
