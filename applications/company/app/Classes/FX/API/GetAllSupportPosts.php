<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllSupportPosts extends Base
{
    private $results;

    public function getAllSupportPosts()
    {
        $st = $this->db->prepare("SELECT HEX(drawingSupportPosts.drawingID) AS drawingID, drawingSupportPosts.nodeID, drawingSupportPosts.xPos, drawingSupportPosts.yPos, drawingSupportPosts.createdAt, drawingSupportPosts.updatedAt FROM drawingSupportPosts
                                  JOIN appDrawing ON appDrawing.drawingID = drawingSupportPosts.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'supportPosts' => $returnObject);
        } else {
            $this->results = array('message' => 'No supportPosts found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
