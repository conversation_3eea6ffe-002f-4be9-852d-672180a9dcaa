<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllMudjackings extends Base
{
    private $results;

    public function getAllMudjackings()
    {
        $st = $this->db->prepare("SELECT HEX(drawingMudjackings.drawingID) AS drawingID, drawingMudjackings.nodeID, drawingMudjackings.xPos, drawingMudjackings.yPos, drawingMudjackings.width, drawingMudjackings.height, drawingMudjackings.createdAt, drawingMudjackings.updatedAt FROM drawingMudjackings
                                  JOIN appDrawing ON appDrawing.drawingID = drawingMudjackings.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'mudjackings' => $returnObject);
        } else {
            $this->results = array('message' => 'No mudjackings found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
