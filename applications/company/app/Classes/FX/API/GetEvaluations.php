<?php

namespace App\Classes\FX\API;

use PDO;

class GetEvaluations extends Base
{
    private $projectID;
    private $results;

    public function setProjectID($projectID)
    {
        $this->projectID = $projectID;
    }

    public function getEvaluations()
    {
        if (!empty($this->projectID)) {

            $st = $this->db->prepare("SELECT evaluationDescription, evaluationFinalized, evaluationID FROM evaluation
								JOIN project ON project.projectID = evaluation.projectID
                                JOIN customer ON customer.customerID = project.customerID
                                JOIN user ON customer.companyID = user.companyID
                                JOIN companies ON user.companyID = companies.companyID
                                WHERE evaluation.projectID = :projectID AND evaluation.evaluationCancelled IS NULL AND user.token = :token AND companies.isActive = '1' AND user.userActive = '1' 
					");
            //write parameter query to avoid sql injections
            $st->bindParam(":token", $this->token);
            $st->bindParam(":projectID", $this->projectID);
            $st->execute();

            if ($st->rowCount() >= 1) {
                while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                    $returnProjects[] = $row;
                }
                $this->results = array('message' => 'Success',
                    'projectID' => $this->projectID,
                    'projectList' => $returnProjects);
            } else {
                $this->results = array('message' => 'No results',);
            }

        }
    }

    public function getPhoneNumbers($customerID)
    {
        $results = null;
        $st = $this->db->prepare("SELECT phoneDescription, phoneNumber, isPrimary FROM `customerPhone` WHERE customerID = :customerID");
        $st->bindParam(":customerID", $customerID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $phoneNumbers[] = $row;
                $results = $phoneNumbers;
            }
            return $results;
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
