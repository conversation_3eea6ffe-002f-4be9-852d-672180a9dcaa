<?php

namespace App\Classes\FX\API;

use PDO;

class GetProjects extends Base
{
    private $viewStartDate;
    private $viewEndDate;
    private $results;

    public function setViewStartDate($viewStartDate)
    {
        $this->viewStartDate = $viewStartDate;
    }

    public function setViewEndDate($viewEndDate)
    {
        $this->viewEndDate = $viewEndDate;
    }

    public function getProjects()
    {
        $st = $this->db->prepare("SELECT :viewStartDate, :viewEndDate, customer.customerID, project.projectID , project.projectDescription, CONCAT_WS(' ', customer.firstName, customer.lastName) AS customerName, property.address, property.address2, property.city, property.state, property.zip, projectSchedule.scheduledUserID, projectSchedule.scheduledStart, projectSchedule.scheduledEnd, DATE(projectSchedule.completedAt) AS installationComplete
                        
                        FROM project 
                        
                  JOIN property ON property.propertyID = project.propertyID
                  JOIN customer ON customer.customerID = property.customerID
                  JOIN projectSchedule ON projectSchedule.projectID = project.projectID
              	  JOIN user ON customer.companyID = user.companyID
            	  JOIN companies ON user.companyID = companies.companyID
      
                        WHERE project.projectCancelled IS NULL AND project.deletedAt IS NULL AND projectSchedule.scheduleType = 'installation' AND projectSchedule.scheduledStart < :viewEndDate AND projectSchedule.scheduledEnd >= :viewStartDate AND projectSchedule.deletedAt IS NULL AND user.token = :token AND companies.isActive = '1' AND user.userActive = '1'");
        //write parameter query to avoid sql injections
        $st->bindParam(":viewStartDate", $this->viewStartDate);
        $st->bindParam(":viewEndDate", $this->viewEndDate);
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $customerID = $row['customerID'];
                $phoneNumbers = $this->getPhoneNumbers($customerID);
                $projectID = $row['projectID'];
                $projectDescription = html_entity_decode($row['projectDescription'], ENT_QUOTES);
                $customerName = html_entity_decode($row['customerName'], ENT_QUOTES);
                $address = html_entity_decode($row['address'], ENT_QUOTES);
                $address2 = html_entity_decode($row['address2'], ENT_QUOTES);
                $city = html_entity_decode($row['city'], ENT_QUOTES);
                $state = $row['state'];
                $zip = $row['zip'];
                $scheduledUserID = $row['scheduledUserID'];
                $scheduledStart = $row['scheduledStart'];
                $scheduledEnd = $row['scheduledEnd'];
                $installationComplete = $row['installationComplete'];

                $data = array('projectID' => $projectID, 'projectDescription' => $projectDescription, 'customerName' => $customerName, 'address' => $address, 'address2' => $address2, 'city' => $city, 'state' => $state, 'zip' => $zip, 'installationComplete' => $installationComplete);

                $data['phoneNumbers'] = $phoneNumbers;
                $data['scheduledUserID'] = $scheduledUserID;
                $data['scheduledStart'] = $scheduledStart;
                $data['scheduledEnd'] = $scheduledEnd;

                $returnProjects[] = $data;
            }
            $this->results = array('message' => 'Success',
                'startDate' => $this->viewStartDate,
                'endDate' => $this->viewEndDate,
                'projectList' => $returnProjects);
        } else {
            $this->results = array('message' => 'No results',);
        }
    }

    public function getPhoneNumbers($customerID)
    {
        $results = null;
        $st = $this->db->prepare("SELECT phoneDescription, phoneNumber, isPrimary FROM `customerPhone` WHERE customerID = :customerID AND deletedAt IS NULL");
        $st->bindParam(":customerID", $customerID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $phoneNumbers[] = $row;
                $results = $phoneNumbers;
            }
            return $results;
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
