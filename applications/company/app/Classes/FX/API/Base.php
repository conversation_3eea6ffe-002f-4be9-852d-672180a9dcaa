<?php

namespace App\Classes\FX\API;

use App\Classes\Database;
use Common\Models\Company;
use Common\Models\User;

class Base
{
    protected $db;
    protected $token;
    protected $tokenUser;

    public function __construct()
    {
        $this->db = (new Database)->dbConnect();
    }

    public function setToken($token)
    {
        $this->token = $token;
    }

    protected function validateToken()
    {
        $this->tokenUser = User::query()
            ->join('companies', 'companies.companyID', '=', 'user.companyID')
            ->where('user.token', $this->token)
            ->where('user.userActive', '1')
            ->whereIn('companies.status', [
                Company::STATUS_SIGNUP, Company::STATUS_TRIAL, Company::STATUS_ACTIVE
            ])
            ->first();
        return $this->tokenUser !== null;
    }
}
