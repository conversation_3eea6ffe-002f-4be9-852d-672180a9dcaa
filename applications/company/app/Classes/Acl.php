<?php

namespace App\Classes;

use Common\Models\User;
use Common\Models\UserApiToken;
use Core\Components\Resource\Interfaces\AclInterface;
use Core\Exceptions\AppException;

class Acl implements AclInterface
{
    protected $is_admin = false;

    protected $user = null;

    protected $user_api_token = null;

    protected $company_id = null;

    public static function make(User $user = null)
    {
        return new static($user);
    }

    public function __construct(User $user = null)
    {
        if ($user !== null) {
            $this->setUser($user);
        }
    }

    public function setIsAdmin($bool)
    {
        $this->is_admin = $bool;
        return $this;
    }

    public function isAdmin()
    {
        return $this->is_admin;
    }

    public function setUser(User $user)
    {
        $this->user = $user;
        return $this;
    }

    public function user()
    {
        return $this->user;
    }

    public function hasCompanyID()
    {
        return $this->company_id !== null;
    }

    public function setCompanyID($company_id)
    {
        $this->company_id = $company_id;
        return $this;
    }

    public function companyID($allow_null = false)
    {
        // if no company id defined, pull from user
        if (!$this->hasCompanyID()) {
            $user = $this->user();
            if ($user !== null) {
                $this->company_id = $user->companyID;
            }
            if (!$allow_null && $this->company_id === null) {
                throw new AppException('Company id cannot be null');
            }
        }
        return $this->company_id;
    }

    public function setUserApiToken(UserApiToken $user_api_token)
    {
        $this->user_api_token = $user_api_token;
        return $this;
    }

    public function userApiToken()
    {
        return $this->user_api_token;
    }

    public function hasPermission($name)
    {
        // use this for future permissions check
    }
}
