<?php

namespace App\Classes;

use Core\StaticAccessors\Config;

/**
 * Class GoogleMap
 *
 * @package App\Classes
 */
abstract class GoogleMap
{
    /**
     * Directions url
     *
     * Generate Google map directions url
     *
     * @param string $address
     * @return string
     */
    public static function directionsUrl($address)
    {
        return 'https://www.google.com/maps/dir/?'.http_build_query(['api' => 1, 'destination' => $address]);
    }

    /**
     * Search url
     *
     * Generate Google map search url
     *
     * @param string $address
     * @return string
     */
    public static function searchUrl($address)
    {
        return 'https://www.google.com/maps/search/?'.http_build_query(['api' => 1, 'query' => $address]);
    }

    /**
     * Streetview url
     *
     * Generate Google map streetview url
     *
     * @param string $address
     * @param array $options
     * @return string
     */
    public static function streetviewImageUrl($address, $options = [])
    {
        $query = [
            'size' => isset($options['size']) ? $options['size'] : '640x320',
            'location' => $address,
            'key' => Config::get('google.map.api_key')
        ];
        return 'https://maps.googleapis.com/maps/api/streetview?' . http_build_query($query);
    }
}
