<?php

declare(strict_types=1);

namespace App\Http\ViewHelpers;

use App\Services\DomainService;
use Common\Models\{Brand, Domain};

/**
 * Class DomainHelper
 *
 * @package App\Http\ViewHelpers
 */
class DomainHelper
{
    /**
     * @var DomainService
     */
    protected $service;

    /**
     * DomainHelper constructor
     *
     * @param DomainService $service
     */
    public function __construct(DomainService $service)
    {
        $this->service = $service;
    }

    /**
     * Get current domain
     *
     * @return Domain|null
     */
    public function current(): ?Domain
    {
        return $this->service->current();
    }

    /**
     * Get brand from current domain if set
     *
     * @return Brand|null
     */
    public function brand(): ?Brand
    {
        $domain = $this->service->current();
        if ($domain === null) {
            return null;
        }
        return $domain['brand'];
    }
}
