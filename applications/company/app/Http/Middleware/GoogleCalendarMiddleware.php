<?php

namespace App\Http\Middleware;

use App\Exceptions\Api\ForbiddenException;
use App\Services\GoogleApi\Services\CalendarService;
use App\Services\GoogleApiService;
use Closure;
use Core\Components\Auth\StaticAccessors\Auth;

/**
 * Class GoogleCalendarMiddleware
 *
 * @package App\Http\Middleware
 */
class GoogleCalendarMiddleware
{
    /**
     * Handle request
     *
     * @param $request
     * @param Closure $next
     * @return mixed
     * @throws ForbiddenException
     */
    public function handle($request, Closure $next)
    {
        $google_api = new GoogleApiService(Auth::user());
        $calendar_service = new CalendarService($google_api);
        if (!$calendar_service->isConnected()) {
            throw new ForbiddenException(1009, 'Google Calendar is not connected');
        }
        return $next($request);
    }
}
