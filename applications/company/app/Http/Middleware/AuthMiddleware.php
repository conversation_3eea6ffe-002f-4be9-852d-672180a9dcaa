<?php

namespace App\Http\Middleware;

use App\Classes\Acl;
use App\Services\DomainService;
use Common\Models\User;
use App\Services\TimeService;
use Closure;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Http\StaticAccessors\Response;

class AuthMiddleware
{
    protected $auth;
    protected $time_service;
    protected $domain_service;

    public function __construct(Auth $auth, TimeService $time_service, DomainService $domain_service)
    {
        $this->auth = $auth;
        $this->time_service = $time_service;
        $this->domain_service = $domain_service;
    }

    public function handle($request, Closure $next)
    {
        $session = $request->session();

        $redirect = false;
        $route_name = ($route = $request->route()) !== null ? $route->getName() : null;
        if (!$session->has('userID')) {
            $redirect = 'page.auth.login';
        } else {
            $user = User::withSystemData(true)->active()->whereKey($session->get('userID'))->first();
            if ($user === null) {
                $redirect = 'page.auth.logout';
            } elseif ($user->systemPrimaryDomain !== $this->domain_service->current()->domain) {
                // if current domain isn't the primary domain for user, transfer session to primary
                // this functionality is primarily for impersonation as users would be redirected during login
                $url = $request->uri()->route('page.auth.transfer')->host($user->systemPrimaryDomain)->query([
                    'id' => session_id(),
                    'path' => $request->uri()->current()->build(URLBuilder::PART_NO_HOST)
                ]);
                return Response::redirect()->toURL($url->build());
            } elseif ($user->isUserInvited) {
                if (!in_array($route_name, ['page.app.set-user-details', 'page.app.set-user-details-process', 'page.auth.logout'], true)) {
                    $redirect = 'page.app.set-user-details';
                } else {
                    $this->auth->set('no-redirect', true);
                }
            } elseif (!$user->isPasswordValid) {
                if (!in_array($route_name, ['page.app.set-password', 'page.app.set-password-process', 'page.auth.logout', 'page.app.signup'], true)) {
                    $redirect = 'page.app.set-password';
                } else {
                    $this->auth->set('no-redirect', true);
                }
            }
        }

        if ($redirect !== false) {
            // only set return url to an application page, this prevents other pages caught in the catch-all route
            // (e.g. ajax/api related pages) from becoming a return URL
            if ($route_name !== null && str_starts_with($route_name, 'page.app.')) {
                $session->set('auth.return_url', $request->uri()->current()->build());
            }
            return Response::redirect()->toRoute($redirect);
        }

        $acl = new Acl($user);
        $acl->setIsAdmin($session->has('adminUserID'));
        $this->auth->setAcl($acl);

        $this->auth->setUser($user);
        $this->time_service->setTimezone($user->systemTimezone);

        return $next($request);
    }
}
