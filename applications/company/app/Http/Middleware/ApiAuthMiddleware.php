<?php

namespace App\Http\Middleware;

use App\Classes\Acl;
use App\Classes\Security;
use App\Exceptions\Api\ForbiddenException;
use App\Exceptions\Api\UnauthorizedException;
use Carbon\Carbon;
use Common\Components\Session\SaveHandler;
use Common\Models\Company;
use Common\Models\User;
use App\Services\TimeService;
use Closure;
use Common\Models\UserApiToken;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Ramsey\Uuid\Uuid;

class ApiAuthMiddleware
{
    protected $auth;
    protected $time_service;
    protected $save_handler;

    public function __construct(Auth $auth, TimeService $time_service, SaveHandler $save_handler)
    {
        $this->auth = $auth;
        $this->time_service = $time_service;
        $this->save_handler = $save_handler;
    }

    public function handle($request, Closure $next)
    {
        $now = Carbon::now('UTC');

        $token = trim($request->input()->header('Authorization', ''));
        if ($token !== '') {
            if (preg_match('#^[a-fA-F0-9]{64}$#', $token) !== 1) {
                throw new UnauthorizedException(1001);
            }
            $api_token = UserApiToken::with([
                'user' => function ($query) {
                    $query->withSystemData();
                }
            ])
                ->whereHas('user', function ($query) {
                    $query->active();
                })
                ->where('token', hex2bin($token))->first();
            if ($api_token === null || !$api_token->isActive) {
                throw new UnauthorizedException(1002);
            }
            $user = $api_token->user;
        } else if (
            trim($session_id = $request->input()->cookie(session_name(), '')) !== '' &&
            ($session = $this->save_handler->find($session_id)) !== null &&
            isset($session['userID'])
        ) {
            $user = User::active()->withSystemData()->where('userID', $session['userID'])->first();
            if ($user === null) {
                throw new UnauthorizedException(1002);
            }
            $api_token = $user->apiTokens()->where('tokenType', UserApiToken::TYPE_WEB)->first();
            if ($api_token === null) {
                $ip_type_map = [
                    RequestInterface::IP_TYPE_V4 => UserApiToken::IP_ADDRESS_TYPE_V4,
                    RequestInterface::IP_TYPE_V6 => UserApiToken::IP_ADDRESS_TYPE_V6
                ];

                list($ip_type, $ip) = $request->ip(true);
                $ip = inet_pton($ip);
                $api_token = new UserApiToken([
                    'userApiTokenID' => Uuid::uuid4()->getBytes(),
                    'ipAddressType' => $ip_type_map[$ip_type],
                    'ipAddress' => $ip,
                    'tokenType' => UserApiToken::TYPE_WEB,
                    'token' => Security::generateToken(),
                    'isActive' => true,
                    'lastLoginAt' => $now,
                    'lastActivityAt' => $now
                ]);
                $api_token->user()->associate($user);
                $api_token->save();
            }
        } else {
            throw new UnauthorizedException(1001);
        }

        if (
            (!in_array((int) $user->companyStatus, [Company::STATUS_SIGNUP, Company::STATUS_TRIAL, Company::STATUS_ACTIVE])) &&
            !$user->primary
        ) {
            throw new ForbiddenException(1012);
        }

        $api_token->fill(['lastActivityAt' => $now])->save();

        $acl = new Acl($user);
        if (isset($session['userID'])) {
            $acl->setIsAdmin(isset($session['adminUserID']));
        }
        $acl->setUserApiToken($api_token);

        $this->auth->setAcl($acl);

        $this->auth->setUser($user);
        $this->auth->setUserApiToken($api_token);
        $this->time_service->setTimezone($user->systemTimezone);

        return $next($request);
    }
}
