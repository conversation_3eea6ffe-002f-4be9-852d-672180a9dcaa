<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1\System\Form;

use App\Resources\System\Form\CategoryResource;
use App\Traits\Resource\Controller\ActionTrait;

/**
 * Class CategoryController
 *
 * @package App\Http\Controllers\ApiV1\System\Form
 */
class CategoryController
{
    use ActionTrait;

    /**
     * @var string Resource class
     */
    protected $resource = CategoryResource::class;

    /**
     * CategoryController constructor
     */
    public function __construct()
    {
        $this->registerFormat('list-v1', 'application/vnd.adg.fx.list-v1+json');
    }
}
