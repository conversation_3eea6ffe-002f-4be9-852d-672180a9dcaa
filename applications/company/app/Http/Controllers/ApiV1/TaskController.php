<?php

namespace App\Http\Controllers\ApiV1;

use App\Resources\TaskResource;
use App\Traits\Resource\Controller\ActionTrait;

class TaskController
{
    use ActionTrait;

    protected $resource = TaskResource::class;

    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
        $this->registerFormat('task-v1', 'application/vnd.adg.fx.task-v1+json');
    }
}
