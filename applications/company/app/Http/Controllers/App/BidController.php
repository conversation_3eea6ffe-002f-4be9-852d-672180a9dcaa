<?php

namespace App\Http\Controllers\App;

use App\Resources\Bid\ItemResource;
use App\Services\CompanyFeatureService;
use App\Services\CompanySettingService;
use App\Services\WisetackService;
use Common\Models\Feature;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Http\StaticAccessors\View;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\InvalidUuidException;
use Ramsey\Uuid\Uuid;

class BidController
{
    public function create($id)
    {
        $user = Auth::user();
        $company_id = $user->companyID;

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->bidCreation && !$user->bidVerification) {
            throw new HttpResponseException(403);
        }

        $bid_item = ItemResource::make(Auth::acl());
        try {
            if (($bid = $bid_item->find($id)) === null) {
                throw new HttpResponseException(404);
            }
            $bid_item->isModelMutable($bid);
            $bid_item->isModelUpdatable($bid);
        } catch (InvalidUuidException $e) {
            throw new HttpResponseException(404);
        } catch (ImmutableEntityException $e) {
            throw new HttpResponseException(403);
        }

        $setting_service = new CompanySettingService($company_id);
        $company_feature = new CompanyFeatureService($company_id);
        $wisetack_service = new WisetackService($user);
        $company_uuid = strval(Uuid::fromBytes($user->company->companyUUID));
        $wisetack_merchant = $wisetack_service->getMerchantByCompanyUUIDString($company_uuid) ?: null;

        $view = View::fetch('pages.app.bid.creator', [
            'info' => [
                'id' => $id,
                'current_version' => ItemResource::CURRENT_VERSION,
                'permissions' => [
                    'finalize' => $user->primary || $user->projectManagement || $user->bidVerification
                ],
                'settings' => [
                    'payment_term_one_time_due_time_frame' => $setting_service->get('payment_term_one_time_due_time_frame'),
                    'payment_term_installments' => $setting_service->get('payment_term_installments', []),
                    'bid_follow_up_notifications' => $setting_service->get('bid_follow_up_notifications', false),
                    'is_financing_required_for_all_projects' => $setting_service->get('wisetack_financing_required_for_all_projects', false),
                    'show_unit_with_product_selection' => $setting_service->get('show_unit_with_product_selection', true)
                ],
                'features' => [
                    'bid_follow_ups' => $company_feature->has(Feature::BID_FOLLOW_UPS, true),
                    'wisetack_api' => $company_feature->has(Feature::WISETACK_API, true),
                ],
                 'wisetack' => [
                    'merchant' => $wisetack_merchant,
                    'is_merchant_approved' => $wisetack_merchant ? $wisetack_merchant->isApproved() : false,
                ],
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }
}
