<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Classes\Log;
use App\Exceptions\{ApiException, Api\NotFoundException};
use App\Services\{DomainService, TrainingService};
use App\Services\Training\Exceptions\ModuleNotFoundException;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\{JSONResponse, RedirectResponse};
use Core\Components\Http\StaticAccessors\Response;
use Throwable;

/**
 * Class TrainingController
 *
 * @package App\Http\Controllers\App\API
 */
class TrainingController
{
    /**
     * Get content for training hub
     *
     * @param DomainService $domain_service
     * @return JSONResponse
     * @throws ApiException
     */
    public function content(DomainService $domain_service): JSONResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            return Response::json([
                'content' => $service->getContent($domain_service),
                'sections' => $service->getSections()['sections'],
                'completed_modules' => $service->getCompletedModules()
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Get all completed modules for a user
     *
     * @return JSONResponse
     * @throws ApiException
     */
    public function completedModules(): JSONResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            return Response::json([
                'completed_modules' => $service->getCompletedModules()
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Get individual module content
     *
     * @param int $id
     * @return JSONResponse
     * @throws ApiException
     */
    public function module($id): JSONResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            $module = $service->getModuleByID((int) $id);
            return Response::json([
                'module' => $module
            ]);
        } catch (ModuleNotFoundException $e) {
            throw (new NotFoundException(1004, 'Module not found'))->setLastException($e);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Complete training for the user
     *
     * @param RequestInterface $request
     * @return RedirectResponse
     */
    public function complete(RequestInterface $request): RedirectResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            $service->complete();
            $request->session()->setFlash('layout_message', [
                'type' => 'success',
                'message' => 'Congratulations, your training is complete!'
            ]);
        } catch (Throwable $e) {
            Log::create('training')->error('Unable to complete training', [
                'exception' => $e
            ]);
            $request->session()->setFlash('layout_message', [
                'type' => 'alert',
                'message' => 'Unable to complete training, please contact support'
            ]);
        }
        return Response::redirect()->toRoute('page.app.home');
    }

    /**
     * Enable user to have access to the training hub
     *
     * FOR USE WITH ADMIN SYSTEM ONLY
     *
     * @param int $id
     * @return JSONResponse
     * @throws ApiException
     */
    public function enableUser($id): JSONResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            $service->enableUser((int) $id);

            return Response::json([
                'user_enabled' => 'true'
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Enable company users to have access to the training hub
     *
     * FOR USE WITH ADMIN SYSTEM ONLY
     *
     * @param int $id
     * @return JSONResponse
     * @throws ApiException
     */
    public function enableCompanyUsers($id): JSONResponse
    {
        try {
            $user = Auth::user();
            $service = new TrainingService($user);
            $service->enableCompanyUsers((int) $id);

            return Response::json([
                'company_enabled' => 'true'
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }
}
