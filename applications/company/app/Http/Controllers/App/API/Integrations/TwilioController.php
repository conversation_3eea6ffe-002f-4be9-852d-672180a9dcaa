<?php

namespace App\Http\Controllers\App\API\Integrations;

use App\Classes\{Func, Log};
use App\Services\Text\Classes\{Message, Number};
use Carbon\Carbon;
use Common\Models\{TextMessage, TextMessageNumber, TextMessageRecipient, TextNumberSuppression, TwilioWebhookLog};
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Exceptions\AppException;
use Ramsey\Uuid\{Uuid, UuidInterface};
use Throwable;
use Twilio\TwiML\MessagingResponse;

/**
 * Class TwilioController
 *
 * @package App\Http\Controllers\App\API\Integrations
 */
class TwilioController
{
    /**
     * @var null|\Monolog\Logger
     */
    protected $log = null;

    /**
     * Get logger instance configured for Twilio
     *
     * @return \Monolog\Logger
     */
    protected function getLog(): \Monolog\Logger
    {
        if ($this->log === null) {
            $this->log = Log::create('twilio_webhook', [
                'email' => [
                    'subject' => 'Twilio Webhook'
                ],
                'slack' => [
                    'username' => 'twilio-webhook',
                    'config' => 'text'
                ],
                'file' => 'text.log'
            ]);
        }
        return $this->log;
    }

    /**
     * Log raw request to database for better debugging and return ID for quick lookup
     *
     * @param int $type
     * @param RequestInterface $request
     * @return UuidInterface
     * @throws \Exception
     */
    protected function logRequest(int $type, RequestInterface $request): UuidInterface
    {
        $id = Uuid::uuid4();
        try {
            (new TwilioWebhookLog([
                'twilioWebhookLogID' => $id->getBytes(),
                'type' => $type,
                'body' => $request->get(),
                'createdAt' => Carbon::now('UTC')
            ]))->quietSave();
        } catch (Throwable $e) {
            $this->getLog()->warning('Unable to log request to database', [
                'exception' => $e,
                'webhook_log_id' => $id->toString(),
                'type' => $type,
                'body' => $request->get()
            ]);
        }
        return $id;
    }

    /**
     * Handle message status updates from Twilio
     *
     * @param string $id
     * @param RequestInterface $request
     * @return \Core\Components\Http\Classes\Response|\Core\Components\Http\Responses\JSONResponse
     * @throws \Exception
     */
    public function messageStatusWebhook(string $id, RequestInterface $request)
    {
        $webhook_log_id = $this->logRequest(TwilioWebhookLog::TYPE_MESSAGE_STATUS, $request);

        try {
            if (!URLBuilder::verifyCsm($request->uri()->current())) {
                throw new AppException('Unable to verify URL checksum');
            }
            if (($id = Func::uuidFromString($id)) === false) {
                throw new AppException('ID is not a valid UUID');
            }
            $log = false;
            $log_level = Log::WARNING;
            DB::transaction(function () use ($id, $request, &$log, &$log_level) {
                if (($recipient = TextMessageRecipient::query()->whereKey($id->getBytes())->lockForUpdate()->first()) === null) {
                    throw new AppException('Unable to find recipient: %s', $id);
                }
                $status = $request->get('MessageStatus');
                $status_map = [
                    'queued' => [
                        'status' => TextMessageRecipient::STATUS_QUEUED,
                        'timestamp' => 'queuedAt'
                    ],
                    'sent' => [
                        'status' => TextMessageRecipient::STATUS_SENT,
                        'timestamp' => 'sentAt'
                    ],
                    'delivered' => [
                        'status' => TextMessageRecipient::STATUS_DELIVERED,
                        'timestamp' => 'deliveredAt'
                    ],
                    'undelivered' => [
                        'status' => TextMessageRecipient::STATUS_UNDELIVERED,
                        'timestamp' => 'undeliveredAt',
                        'log' => true
                    ],
                    'failed' => [
                        'status' => TextMessageRecipient::STATUS_FAILED,
                        'timestamp' => 'failedAt',
                        'log' => true
                    ]
                ];
                if (!isset($status_map[$status])) {
                    throw new AppException('Status not found in map: %s', $status);
                }
                $config = $status_map[$status];
                $recipient->status = $config['status'];
                $recipient->{$config['timestamp']} = Carbon::now('UTC');
                $recipient->save();
                if (isset($config['log']) && $config['log']) {
                    $log = true;
                }
                switch ($config['status']) {
                    case TextMessageRecipient::STATUS_QUEUED:
                        // store From value since we can't know this due to using the messaging service which selects a
                        // number to use for us
                        $number = TextMessageNumber::query()
                            ->where('textMessageID', $recipient->textMessageID)
                            ->where('numberType', TextMessageNumber::NUMBER_TYPE_FROM)
                            ->whereNull('number')
                            ->first();
                        if ($number !== null) {
                            $number->number = $request->get('From');
                            $number->save();
                        }
                        break;
                    case TextMessageRecipient::STATUS_UNDELIVERED:
                    case TextMessageRecipient::STATUS_FAILED:
                        $error_code = (int) $request->get('ErrorCode', 0);
                        switch ($error_code) {
                            case 30003: // unreachable destination handset
                            case 30004: // message blocked
                            case 30005: // unknown destination handset
                            case 30006: // landline or unreachable
                            case 30007: // carrier violation
                            case 30008: // unknown delivery error
                                $failures = Number::recordDeliveryFailure($recipient->number->number, $id, $error_code);
                                // if number of failures for the number is less than 2, we resend the message 1 hour from now
                                // otherwise, we add it the suppression list since we can't deliver to it
                                if ($failures < 2) {
                                    Message::resend(Uuid::fromBytes($recipient->textMessageID), function (Message $message) use ($recipient) {
                                        $message->clearRecipients();
                                        $message->to(clone Number::makeFromModel($recipient->number));
                                        $message->deliverAt(Carbon::now('UTC')->addHour());
                                        return $message;
                                    });
                                } else {
                                    Number::suppress($recipient->number->number);
                                }
                                if (in_array($error_code, [30004, 30007, 30008])) {
                                    $log_level = Log::ERROR;
                                }
                                break;
                            case 21610: // can't deliver due to unsubscribe (shouldn't happen, but handled for extra protection)
                            case 21614: // bad number (either landline or doesn't exist)
                                $source_map = [
                                    21610 => TextNumberSuppression::SOURCE_UNSUBSCRIBE,
                                    21614 => TextNumberSuppression::SOURCE_NUMBER_INVALID
                                ];
                                Number::suppress($recipient->number->number, $source_map[$error_code]);
                            break;
                        }
                        break;
                }
            });
            // if we should log, then we do that
            if ($log) {
                $this->getLog()->addRecord($log_level, 'Text message failed to deliver', [
                    'error_code' => $request->get('ErrorCode'),
                    'webhook_log_id' => $webhook_log_id->toString()
                ]);
            }
            return Response::create('', 204);
        } catch (Throwable $e) {
            $this->getLog()->error('Unable to process status update', [
                'exception' => $e,
                'webhook_log_id' => $webhook_log_id->toString()
            ]);
            return Response::json(['error' => 'Unable to process status update'], 406);
        }
    }

    /**
     * Handle incoming message from Twilio
     *
     * Note: we currently only support opt-in and opt-out messages, others will be added as needed in the future
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Classes\Response
     * @throws \Exception
     */
    public function incomingMessageWebhook(RequestInterface $request)
    {
        $webhook_log_id = $this->logRequest(TwilioWebhookLog::TYPE_INCOMING_MESSAGE, $request);

        $to = $request->get('To');
        $from = $request->get('From');

        $body = trim($request->get('Body', ''));

        try {
            // handle opt-out and opt-in
            $opt_out = in_array(strtolower($body), ['stop', 'stopall', 'unsubscribe', 'cancel', 'end', 'quit']);
            $opt_in = in_array(strtolower($body), ['start', 'yes', 'unstop']);
            if ($opt_out || $opt_in) {
                $suppression = TextNumberSuppression::query()->where('number', $from)->first();
                if ($opt_out && $suppression === null) {
                    TextNumberSuppression::create([
                        'source' => TextNumberSuppression::SOURCE_UNSUBSCRIBE,
                        'number' => $from
                    ]);
                } elseif ($opt_in && $suppression !== null) {
                    $suppression->delete();
                }
            }

            $prev_message = TextMessage::query()
                ->select(['textMessages.*'])
                ->join('textMessageNumbers as from', function ($join) use ($to) {
                    $join->on('from.textMessageID', '=', 'textMessages.textMessageID')
                        ->where('from.numberType', TextMessageNumber::NUMBER_TYPE_FROM)
                        ->where('from.number', $to);
                })
                ->join('textMessageRecipients as recipient', 'recipient.textMessageID', '=', 'textMessages.textMessageID')
                ->join('textMessageNumbers as to', function ($join) use ($from) {
                    $join->on('to.textMessageNumberID', '=', 'recipient.textMessageNumberID')
                        ->where('to.numberType', TextMessageNumber::NUMBER_TYPE_TO)
                        ->where('to.number', $from);
                })
                ->whereIn('recipient.status', [TextMessageRecipient::STATUS_SENT, TextMessageRecipient::STATUS_DELIVERED])
                ->orderBy('recipient.sentAt', 'desc')
                ->limit(1)
                ->first();
            // we currently don't use this prev message model, but left it for future reference, added logging to see
            // how many times we get texts which don't relate to something we sent
            if ($prev_message === null) {
                $this->getLog()->info('No prev message found for incoming message', [
                    'webhook_log_id' => $webhook_log_id->toString()
                ]);
            }
        } catch (Throwable $e) {
            $this->getLog()->error('Unable to handle incoming message', [
                'exception' => $e,
                'webhook_log_id' => $webhook_log_id->toString()
            ]);
        }

        return Response::create((string) new MessagingResponse())->contentType('text/xml');
    }
}
