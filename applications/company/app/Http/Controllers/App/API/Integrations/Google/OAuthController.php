<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API\Integrations\Google;

use App\Traits\Controller\Api\GoogleApiAuthTrait;
use App\Services\GoogleApi\Classes\OAuth\AuthCode;
use App\Services\GoogleApi\Exceptions\OAuth\{AccessDeniedException, AccountInUseException};
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Throwable;

/**
 * Class OAuthController
 *
 * Handles OAuth2.0 process for Google API
 *
 * @package App\Http\Controllers\App\API\Integrations\Quickbooks
 */
class OAuthController
{
    use GoogleApiAuthTrait;

    /**
     * Exchange auth code from access token
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\RedirectResponse
     */
    public function oauthAuthCode(RequestInterface $request): \Core\Components\Http\Responses\RedirectResponse
    {
        $user = Auth::user();
        $auth_code = AuthCode::fromRequest($request);
        try {
            $this->getAuthHelper($user)->exchangeAuthCode($auth_code);
            $request->session()->setFlash('layout_message', [
                'type' => 'success',
                'message' => 'Connection to Google successfully established'
            ]);
            return $this->redirectToDestination($auth_code->getState('destination', 'home'));
        } catch (Throwable $e) {
            $class = get_class($e);
            $messages = [
                AccessDeniedException::class => 'Unable to connect to Google - Access was denied',
                AccountInUseException::class => 'Your Google account is already in use with another user on the system. Go to https://cxlratr.to/faq-google-calendar for additional help.'
            ];
            $message = $messages[$class] ?? 'Unable to connect to Google, please contact support';
            $request->session()->setFlash('layout_message', [
                'type' => 'alert',
                'message' => $message,
                'auto_delete' => false
            ]);
            return $this->redirectToErrorDestination($auth_code->getState('destination', 'home'));
        }
    }

    /**
     * Disconnect user from Google and redirect to proper integrations page based on destination
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\RedirectResponse
     */
    public function disconnect(RequestInterface $request): \Core\Components\Http\Responses\RedirectResponse
    {
        try {
            $user = Auth::user();
            $this->getAuthHelper($user)->disconnect();
            $request->session()->setFlash('layout_message', [
                'type' => 'success',
                'message' => 'Google account is disconnecting...'
            ]);
            return $this->redirectToDestination($request->get('destination', 'home'));
        } catch (Throwable $e) {
            $request->session()->setFlash('layout_message', [
                'type' => 'alert',
                'message' => 'Unable to disconnect from Google, please contact support',
                'auto_delete' => false
            ]);
            return $this->redirectToErrorDestination($request->get('destination', 'home'));
        }
    }
}
