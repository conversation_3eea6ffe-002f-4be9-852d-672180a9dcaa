<?php

declare(strict_types=1);
namespace App\Http\Controllers\App\API\Integrations\Wisetack\Webhooks;


use App\Classes\Log;
use App\Services\CompanySettingService;
use App\Services\Wisetack\Helpers\WisetackPricingPlan;
use Common\Models\Company;
use Common\Models\WisetackMerchant;
use Common\Models\WisetackTransaction;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Exceptions\AppException;
use DateTime;
use Monolog\Logger;
use Ramsey\Uuid\Uuid;

class WisetackWebhookHelper
{

    /**
     * @var Logger
     */
    protected ?Logger $log = null;

    public function __construct()
    {
        $this->log = $this->getLog();
    }

    /**
     * Get logger instance configured for Google calendar
     *
     * @return Logger
     */
    protected function getLog(): Logger
    {
        if ($this->log === null) {
            $this->log = Log::create('wisetack_webhook', [
                'file' => 'wisetack_webhook.log'
            ]);
        }
        return $this->log;
    }


    /**
     * Get event timestamp from date string.
     *
     * @param string $date
     * @return \DateTime
     */
    public function getEventTimestamp(string $date): \DateTime
    {
        $date_format = strpos($date, ':') !== false ? 'm-d-Y H:i:s' : 'm-d-Y';
        return DateTime::createFromFormat($date_format, $date);
    }


    /**
     * Prepare transaction event data.
     *
     * @param array $payload
     * @param \DateTime $event_timestamp
     * @return array
     */
    public function prepareTransactionEventData(array $payload, \DateTime $event_timestamp): array
    {
        return [
            'wisetackTransactionID' => Uuid::fromString($payload['transactionId'])->getBytes(),
            'messageID' => $payload['messageId'],
            'event' => str_replace(' ', '_', $payload['changedStatus']),
            'eventTimestamp' => $event_timestamp,
            'requestedLoanAmount' => $this->getAmountFromPayload($payload, 'requestedLoanAmount'),
            'approvedLoanAmount' => $this->getAmountFromPayload($payload, 'approvedLoanAmount'),
            'settledLoanAmount' => $this->getAmountFromPayload($payload, 'settledLoanAmount'),
            'maximumAmount' => $this->getAmountFromPayload($payload, 'maximumAmount'),
            'processingFee' => $this->getAmountFromPayload($payload, 'processingFee'),
            'prequalificationID' => $payload['prequalId'] ?? null,
            'actionsRequired' => $payload['actionsRequired'] ?? [],
            'customerUUID' => isset($payload['customerId']) ? Uuid::fromString($payload['customerId'])->getBytes() : null,
            'payload' => json_encode($payload)
        ];
    }



    /**
     * Handle transaction status change.
     *
     * @param WisetackTransaction $transaction
     * @param array $payload
     * @return void
     */
    public function handleTransactionStatusChange(WisetackTransaction $transaction, array $payload): void
    {
        $status = str_replace(' ', '_', $payload['changedStatus']);
        $changed_status_code = WisetackTransaction::STATUS[$status];
        $actions_required_code = WisetackTransaction::STATUS['ACTIONS_REQUIRED'];

        if ($changed_status_code === $actions_required_code && !empty($payload['actionsRequired'])) {
            $transaction->actionsRequired = $this->mapActionsRequired($payload['actionsRequired']);
        }

        if ($changed_status_code > $actions_required_code && $transaction->actionsRequired !== null) {
            $transaction->actionsRequired = [];
        }

        if ($requested_loan_amount = $this->getAmountFromPayload($payload, 'requestedLoanAmount')) {
            $transaction->requestedLoanAmount = $requested_loan_amount;
        }

        if ($approved_loan_amount = $this->getAmountFromPayload($payload, 'approvedLoanAmount')) {
            $transaction->approvedLoanAmount = $approved_loan_amount;
        }

        if ($settled_loan_amount = $this->getAmountFromPayload($payload, 'settledLoanAmount')) {
            $transaction->settledLoanAmount = $settled_loan_amount;
        }

        if (isset($payload['expirationDate'])) {
            $transaction->expirationDate = $this->getEventTimestamp($payload['expirationDate']);
        }


        $transaction->setStatusAttributesBasedOnEvent(str_replace(' ', '_', $payload['changedStatus']));
        $transaction->save();
    }

    /**
     * Map actions required sub-items.
     *
     * @param array $actions_required
     * @return array
     */
    private function mapActionsRequired(array $actions_required): array
    {
        $mapped_actions = [];

        foreach ($actions_required as $full_action) {
            $action = str_replace(' ', '_', $full_action);
            if (isset(WisetackTransaction::ACTIONS_REQUIRED[$action])) {
                $mapped_actions[] = WisetackTransaction::ACTIONS_REQUIRED[$action];
            }
        }

        return $mapped_actions;
    }


    /**
     * Get amount from payload
     *
     * @param $payload
     * @param $key
     * @return array|string|string[]|null
     */
    public function getAmountFromPayload($payload, $key)
    {
        if (isset($payload[$key])) {
            return str_replace(['$', ','], '', $payload[$key]);
        }
        return null;
    }


    /**
     * Sets the default Wisetack current pricing plan when a merchant's application is approved for the first time.
     *
     * @param $merchant WisetackMerchant The Wisetack merchant object.
     * @throws AppException If the company is not found or other issues occur.
     */
    public function setDefaultWisetackPricingPlan(WisetackMerchant $merchant): void
    {
        $this->setWisetackPricingPlan($merchant, WisetackPricingPlan::PRICING_PLAN_STANDARD);
    }

    /**
     * Sets the Wisetack current pricing plan for a merchant.
     *
     * @throws BatchHandleException
     * @throws AppException
     */
    public function setWisetackPricingPlan(WisetackMerchant $merchant, int $pricingPlan): void
    {
        $company = Company::where('companyUUID', $merchant->companyUUID)->first();
        if (!$company) {
            throw new AppException('Unable to find company associated with the merchant.');
        }

        $settingService = new CompanySettingService($company->companyID);

        $this->getLog()->info('Setting Wisetack pricing plan for company ' . $company->companyID . ' to ' . $pricingPlan);
        $settingService->set('wisetack_current_pricing_plan', $pricingPlan);
        $settingService->save();
    }

}