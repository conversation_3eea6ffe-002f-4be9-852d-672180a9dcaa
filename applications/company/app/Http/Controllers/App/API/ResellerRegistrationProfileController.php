<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Exceptions\Api\UnprocessableEntityException;
use App\Exceptions\ApiException;
use Common\Models\Reseller;
use Common\Models\ResellerRegistrationProfile;
use Common\Models\Subscription;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Exceptions\AppException;
use Throwable;

/**
 * Class ResellerProfileController
 *
 * @package App\Http\Controllers\App
 */
class ResellerRegistrationProfileController
{
    /**
     * Create reseller registration profile code
     *
     * FOR USE WITH ADMIN SYSTEM ONLY
     *
     * @param Validation $validation
     * @return JSONResponse
     * @throws ApiException
     */
    public function create(Validation $validation): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user->admin) {
                throw new AppException('Only admins can create reseller registration profiles');
            }
            $config = FieldConfig::fromArray([
                'reseller_id' => [
                    'label' => 'Reseller ID',
                    'rules' => 'required|type[int]|in_array[reseller_ids]'
                ],
                'name' => [
                    'label' => 'Name',
                    'rules' => 'trim|required|max_length[100]'
                ],
                'description' => [
                    'label' => 'Description',
                    'rules' => 'trim|nullable|optional|max_length[200]'
                ],
                'code' => [
                    'label' => 'Code',
                    'rules' => 'trim|required|max_length[100]|not_in_array[codes]'
                ],
                'subscription_id' => [
                    'label' => 'Subscription ID',
                    'rules' => 'required|type[int]|in_array[subscription_ids]'
                ],
                'is_active' => [
                    'label' => 'Active',
                    'rules' => 'required|cast[bool]'
                ],
                'expires_at' => [
                    'label' => 'Expires At',
                    'rules' => 'nullable|optional|iso8601_date[date]'
                ]
            ]);
            $resellers = Reseller::all()->toArray();
            $codes = ResellerRegistrationProfile::get()->toArray();
            $subscriptions = Subscription::all()->toArray();
            $config->store('reseller_ids', array_map(fn(array $reseller): int => $reseller['resellerID'], $resellers));
            $config->store('codes', array_map(fn(array $code): string => $code['code'], $codes));
            $config->store('subscription_ids', array_map(fn(array $subscription): int => $subscription['subscriptionID'], $subscriptions));

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }
            $data = [
                'resellerID' => $validator->data('reseller_id'),
                'name' => $validator->data('name'),
                'description' => $validator->data('description'),
                'code' => $validator->data('code'),
                'subscriptionID' => $validator->data('subscription_id'),
                'isActive' => $validator->data('is_active'),
                'expiresAt' => $validator->data('expires_at')
            ];
            ResellerRegistrationProfile::create($data);

            return Response::json([
                'code_added' => 'true'
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Edit reseller registration profile code
     *
     * FOR USE WITH ADMIN SYSTEM ONLY
     *
     * @param int $id
     * @param Validation $validation
     * @return JSONResponse
     * @throws ApiException
     */
    public function update($id, Validation $validation): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user->admin) {
                throw new AppException('Only admins can edit reseller registration profiles');
            }
            $code_id = (int) $id;
            $registration_profile = ResellerRegistrationProfile::find($code_id);
            if ($registration_profile === null) {
                throw new AppException('Unable to find registration profile with ID: %s', $code_id);
            }
            $config = FieldConfig::fromArray([
                'reseller_id' => [
                    'label' => 'Reseller ID',
                    'rules' => 'required|type[int]|in_array[reseller_ids]'
                ],
                'name' => [
                    'label' => 'Name',
                    'rules' => 'trim|required|max_length[100]'
                ],
                'description' => [
                    'label' => 'Description',
                    'rules' => 'trim|nullable|optional|max_length[200]'
                ],
                'code' => [
                    'label' => 'Code',
                    'rules' => 'trim|required|max_length[100]|not_in_array[codes]'
                ],
                'subscription_id' => [
                    'label' => 'Subscription ID',
                    'rules' => 'required|type[int]|in_array[subscription_ids]'
                ],
                'is_active' => [
                    'label' => 'Active',
                    'rules' => 'required|cast[bool]'
                ],
                'expires_at' => [
                    'label' => 'Expires At',
                    'rules' => 'nullable|optional|iso8601_date[date]'
                ]
            ]);
            $resellers = Reseller::all()->toArray();
            $codes = ResellerRegistrationProfile::where('resellerRegistrationProfileID', '!=', $code_id)->get()->toArray();
            $subscriptions = Subscription::all()->toArray();
            $config->store('reseller_ids', array_map(fn(array $reseller): int => $reseller['resellerID'], $resellers));
            $config->store('codes', array_map(fn(array $code): string => $code['code'], $codes));
            $config->store('subscription_ids', array_map(fn(array $subscription): int => $subscription['subscriptionID'], $subscriptions));

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }
            $registration_profile->resellerID = $validator->data('reseller_id');
            $registration_profile->name = $validator->data('name');
            $registration_profile->description = $validator->data('description');
            $registration_profile->code = $validator->data('code');
            $registration_profile->subscriptionID = $validator->data('subscription_id');
            $registration_profile->isActive = $validator->data('is_active');
            $registration_profile->expiresAt = $validator->data('expires_at');
            $registration_profile->save();

            return Response::json([
                'code_updated' => 'true'
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }
}
