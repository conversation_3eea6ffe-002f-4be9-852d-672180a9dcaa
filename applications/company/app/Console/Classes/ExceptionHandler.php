<?php

declare(strict_types=1);

namespace App\Console\Classes;

use App\Classes\Log;
use Core\Components\Console\Classes\ExceptionHandler as ConsoleExceptionHandler;
use Throwable;

/**
 * Class ExceptionHandler
 *
 * @package App\Console\Classes
 */
class ExceptionHandler extends ConsoleExceptionHandler
{
    /**
     * Log exception using log library
     *
     * @param Throwable $exception
     */
    protected function log(Throwable $exception): void
    {
        Log::create('console', ['slack' => ['username' => 'console-error']])->error($exception->getMessage(), [
            'exception' => $exception
        ]);
    }
}
