<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\ResourceJobs\Company\SupportListPushJob;
use App\Traits\Console\{CompanyTrait, OutputExceptionTrait, UserTrait};
use Common\Models\Company;
use Core\Components\Console\Commands\BaseCommand;
use Throwable;

/**
 * Class ZendeskCommand
 *
 * @package App\Console\Commands
 */
class ZendeskCommand extends BaseCommand
{
    use CompanyTrait;
    use OutputExceptionTrait;
    use UserTrait;

    /**
     * Enqueue Zendesk update or create job for company
     */
    public function pushCompany(): void
    {
        try {
            if ($this->args->has('all')) {
                if (!$this->console->confirm('Are you sure you want to push all companies?')) {
                    return;
                }
                /** @var Company[] $companies */
                $companies = Company::query()->whereIn('status', [1,2,5])->get(['companyID']);
                foreach ($companies as $company) {
                    SupportListPushJob::enqueue($company->getKey());
                }
                $this->console->info('Pushing %d companies to Zendesk', count($companies));
                return;
            }
            $company = $this->getCompany();
            SupportListPushJob::enqueue($company->getKey());
            $this->console->info('Pushing company %s to Zendesk', $company->name);
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }
}
