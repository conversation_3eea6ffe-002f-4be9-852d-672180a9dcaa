<?php

namespace App\Console\Commands;

use App\Services\CompanyFeatureService;
use Common\Models\Company;
use Common\Models\CompanyFeature;
use Common\Models\Feature;
use Core\Components\Console\Commands\BaseCommand;

/**
 * Class CompanyFeatureCommand
 *
 * @package App\Console\Commands
 */
class CompanyFeatureCommand extends BaseCommand
{
    protected function getFeatures()
    {
        $features = [];
        Feature::all()->each(function ($feature) use (&$features) {
            $features[$feature->getKey()] = $feature->name;
        });
        return $features;
    }

    protected function getCompany()
    {
        $company_id = $this->console->get('company-id', 'Company ID');
        if (!is_numeric($company_id)) {
            $this->console->error('Company ID must be numeric');
            return null;
        }
        $company_id = (int) $company_id;
        $company = Company::find($company_id);
        if ($company === null) {
            $this->console->error('Unable to find company with ID: %s', $company_id);
            return null;
        }
        $this->console->info('Company: %s', $company->name);
        if (!$this->console->confirm('Is the above company correct?')) {
            $this->console->error('Aborted');
            return null;
        }
        return $company;
    }

    public function listAll()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        $service = new CompanyFeatureService($company->companyID);

        $company_features = $service->getFeatures();
        if (count($company_features) === 0) {
            $this->console->info('Company has no features configured');
            return;
        }
        $this->console->line('Company has the following feature settings:');
        $this->console->line();

        $features = $this->getFeatures();
        foreach ($company_features as $feature_id => $info) {
            $this->console->line(
                '%s: %s',
                $features[$feature_id],
                $info['status'] === CompanyFeature::STATUS_ENABLED ? 'Enabled' : 'Disabled'
            );
        }
    }

    public function enable()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        $service = new CompanyFeatureService($company->companyID);

        $features = $this->getFeatures();
        $menu = [];
        foreach ($features as $feature_id => $name) {
            if ($service->has($feature_id)) {
                continue;
            }
            $name .= $service->has($feature_id, null) === null ? ' (nonexistent)' : ' (disabled)';
            $menu[$feature_id] = $name;
        }
        if (count($menu) === 0) {
            $this->console->info('No features available to enable');
            return;
        }
        $this->console->line('Choose a feature to enable:');
        $this->console->line();
        $feature_id = $this->console->menu($menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($feature_id === false) {
            $this->console->error('Enable action aborted');
            return;
        }
        $service->enable($feature_id);
        $this->console->info('Feature \'%s\' enabled', $features[$feature_id]);
    }

    public function disable()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        $service = new CompanyFeatureService($company->companyID);

        $features = $this->getFeatures();
        $menu = [];
        foreach ($features as $feature_id => $name) {
            $name .= $service->has($feature_id) ? ' (enabled)' : ' (nonexistent)';
            $menu[$feature_id] = $name;
        }
        $this->console->line('Choose a feature to disable:');
        $this->console->line();
        $feature_id = $this->console->menu($menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($feature_id === false) {
            $this->console->error('Disable action aborted');
            return;
        }
        $service->disable($feature_id);
        $this->console->info('Feature \'%s\' disabled', $features[$feature_id]);
    }

    public function remove()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        $service = new CompanyFeatureService($company->companyID);

        $company_features = $service->getFeatures();
        if (count($company_features) === 0) {
            $this->console->info('No features to remove');
            return;
        }

        $features = $this->getFeatures();
        $menu = [];
        foreach ($company_features as $feature_id => $info) {
            if ($service->has($feature_id, null) === null) {
                continue;
            }
            $menu[$feature_id] = $features[$feature_id];
        }
        $this->console->line('Choose a feature to remove:');
        $this->console->line();
        $feature_id = $this->console->menu($menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($feature_id === false) {
            $this->console->error('Remove aborted');
            return;
        }
        $service->remove($feature_id);
        $this->console->info('Feature \'%s\' removed', $features[$feature_id]);
    }
}
