<?php

namespace App\Console\Commands\Seeders;

use App\Resources\Drawing\NodeResource;
use Common\Models\Drawing;
use Core\Components\Console\Commands\BaseCommand;
use Exception;
use Faker\Factory as Faker;
use const JSON_PRETTY_PRINT;

/**
 * Class DrawingSeeder
 *
 * Note: No longer functions, but kept for future seeder reference
 *
 * @package App\Console\Commands\Seeders
 */
class DrawingSeeder extends BaseCommand
{
    protected function getDrawing($valid = null)
    {
        $faker = Faker::create();

        $statuses = array_keys(Drawing::getStatuses());
        $grid_units = array_keys(Drawing::getGridUnits());

        $bool_chance = $valid === null ? 50 : ($valid ? 100 : 0);

        $drawing = [];

        // id
        if ($faker->boolean($bool_chance)) {
            $drawing['id'] = $faker->uuid;
        } else if ($faker->boolean) {
            $drawing['id'] = $faker->randomNumber();
        }

        // project id
        if ($faker->boolean($bool_chance)) {
            $drawing['project_id'] = $faker->optional()->randomNumber(6);
        } else if ($faker->boolean) {
            $drawing['project_id'] = $faker->optional()->uuid;
        }

        // status
        if ($faker->boolean($bool_chance)) {
            $drawing['status'] = $faker->randomElement($statuses);
        } else if ($faker->boolean) {
            $drawing['status'] = $faker->randomLetter;
        } else if ($faker->boolean) {
            $drawing['status'] = $faker->numberBetween(100, 200);
        }

        // name
        if ($faker->boolean($bool_chance)) {
            $drawing['name'] = $faker->text(100);
        } else if ($faker->boolean) {
            $drawing['name'] = $faker->text(200);
        }

        // zoom level
        if ($faker->boolean($bool_chance)) {
            $drawing['zoom_level'] = $faker->randomFloat(8, 0, 2);
        } else if ($faker->boolean) {
            $drawing['zoom_level'] = $faker->randomLetter;
        }

        // grid unit
        if ($faker->boolean($bool_chance)) {
            $drawing['grid_unit'] = $faker->randomElement($grid_units);
        } else if ($faker->boolean) {
            $drawing['grid_unit'] = $faker->randomLetter;
        } else if ($faker->boolean) {
            $drawing['grid_unit'] = $faker->numberBetween(100, 200);
        }

        // show wall lengths
        if ($faker->boolean($bool_chance)) {
            $drawing['show_wall_lengths'] = $faker->boolean;
        } else if ($faker->boolean) {
            $drawing['show_wall_lengths'] = $faker->randomLetter;
        }

        if ($this->args->has('nodes')) {
            if ($faker->boolean($bool_chance)) {
                $nodes = [];
                $types = NodeResource::getTypes();
                foreach (array_keys($types) as $type) {
                    // @todo generate random amounts
                    $nodes[] = $this->getNode($type, $valid);
                }
                $drawing['nodes'] = $nodes;
            } else if ($faker->boolean) {
                $drawing['nodes'] = 'asdlfkja';
            }
        }

        return $drawing;
    }

    public function getNode($type, $valid = null)
    {
        $faker = Faker::create();

        $node_resource = new NodeResource;
        $config = $node_resource->getNodeConfig($type);

        // skip fields (use action system later)
        $skip = ['drawing_id', 'data', 'created_at', 'created_by_user_id', 'updated_at', 'updated_by_user_id'];
        foreach ($skip as $field) {
            if (!array_key_exists($field, $config)) {
                continue;
            }
            unset($config[$field]);
        }

        $node = [];

        $bool_chance = $valid === null ? 50 : ($valid ? 100 : 0);

        foreach ($config as $key => $item) {
            switch ($key) {
                case 'id':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->uuid;
                    } else if ($faker->boolean) {
                        $value = $faker->randomNumber();
                    }
                    break;
                case 'type':
                    if ($faker->boolean($bool_chance)) {
                        $value = $type;
                    } else if ($faker->boolean) {
                        $value = $faker->numberBetween(100, 200);
                    }
                    break;
                case 'x_pos':
                case 'y_pos':
                case 'x_1':
                case 'x_2':
                case 'y_1':
                case 'y_2':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->numberBetween(-300, 300);
                    } else if ($faker->boolean) {
                        $value = $faker->randomLetter;
                    }
                    break;
                case 'z_rotation':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->randomFloat(8, 0, 2) * $faker->randomElement([1, -1]);
                    } else if ($faker->boolean) {
                        $value = $faker->randomLetter;
                    }
                    break;
                case 'width':
                case 'height':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->numberBetween(1, 400);
                    } else if ($faker->boolean) {
                        $value = $faker->randomLetter;
                    }
                    break;
                case 'pier_number':
                case 'font_size':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->numberBetween(1, 50);
                    } else if ($faker->boolean) {
                        $value = $faker->randomLetter;
                    }
                    break;
                case 'color':
                    if ($faker->boolean($bool_chance)) {
                        $value = str_replace('#', '', $faker->hexColor);
                    } else if ($faker->boolean) {
                        $value = 'asdlfkajsdf';
                    }
                    break;
                case 'text':
                    if ($faker->boolean($bool_chance)) {
                        $value = $faker->text(250);
                    } else if ($faker->boolean) {
                        $value = $faker->text(300);
                    }
                    break;
                default:
                    throw new Exception('No value defined for ' . $key);
            }
            if (isset($value)) {
                $node[$key] = $value;
            }
            unset($value);
        }

        return $node;
    }

    public function generatePayload()
    {
        $valid = null;
        if ($this->args->has('valid')) {
            $valid = true;
        } else if ($this->args->has('invalid')) {
            $valid = false;
        }

        $this->console->out(json_encode($this->getDrawing($valid), JSON_PRETTY_PRINT) . PHP_EOL);
    }
}
