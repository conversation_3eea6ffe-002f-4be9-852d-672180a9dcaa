<?php

namespace App\Console\Commands\Utilities;

use Core\Components\Console\Commands\BaseCommand;
use Core\Components\Http\Classes\URLBuilder;
use Throwable;

/**
 * Class UrlCommand
 *
 * @package App\Console\Commands\Utilities
 */
class UrlCommand extends BaseCommand
{
    /**
     * Parse URL from input and add checksum
     */
    public function sign()
    {
        $url = $this->console->get('url', 'URL');
        try {
            $builder = URLBuilder::fromString($url);
            $this->console->line('Signed URL: %s', $builder->csm()->build());
        } catch (Throwable $e) {
            $this->console->error('Unable to sign URL - Reason: %s', $e->getMessage());
        }
    }
}
