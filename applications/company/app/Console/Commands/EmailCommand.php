<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Func;
use App\Services\Email\Classes\Message;
use Common\Models\{EmailMessage, EmailMessageAddress};
use Core\Components\Console\Commands\BaseCommand;

/**
 * Class EmailCommand
 *
 * @package App\Console\Commands
 */
class EmailCommand extends BaseCommand
{
    /**
     * Resend message
     *
     * @throws \App\Services\Email\Exceptions\MessageException
     */
    public function resendMessage(): void
    {
        $message_id = $this->console->get('message-id', 'Message ID');

        if (($message_id = Func::uuidFromString($message_id)) === false) {
            $this->console->error('Invalid message id');
            return;
        }
        if (($message = EmailMessage::find($message_id->getBytes())) === null) {
            $this->console->error('Unable to find message: %s', $message_id);
            return;
        }

        $this->console->line('Found the following message:');
        $this->console->line();
        $this->console->line('Subject: %s', $message->subject);
        $this->console->line('Date: %s', $message->createdAt->toDateTimeString());
        $addresses = $message->addresses->groupBy('addressType');
        $address_type_order = [
            EmailMessageAddress::ADDRESS_TYPE_FROM => 'From',
            EmailMessageAddress::ADDRESS_TYPE_REPLY_TO => 'Reply To',
            EmailMessageAddress::ADDRESS_TYPE_TO => 'To',
            EmailMessageAddress::ADDRESS_TYPE_CC => 'CC',
            EmailMessageAddress::ADDRESS_TYPE_BCC => 'BCC'
        ];
        foreach ($address_type_order as $type => $label) {
            if (!isset($addresses[$type])) {
                continue;
            }
            $address_list = array_map(function ($address) {
                return ($address->name !== null ? "{$address->name} " : '') . "<{$address->address}>";
            }, $addresses[$type]->all());
            $this->console->line('%s: %s', $label, implode(', ', $address_list));
        }
        $this->console->line();
        if (!$this->console->confirm('Are you sure you want to resend this message?')) {
            $this->console->error('Aborted');
            return;
        }

        $new_message_id = Message::resend($message_id);

        $this->console->info('Message %s enqueued to be sent', $new_message_id->toString());
    }
}
