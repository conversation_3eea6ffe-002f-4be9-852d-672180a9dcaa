<?php

namespace App\Console\Commands;

use App\Classes\Acl;
use App\Services\CompanySubscriptionService;
use App\Traits\Console\NowTrait;
use Carbon\Carbon;
use Common\Models\Company;
use Core\Components\Console\Commands\BaseCommand;
use Exception;

/**
 * Class CompanySubscriptionCommand
 *
 * @package App\Console\Commands
 */
class CompanySubscriptionCommand extends BaseCommand
{
    use NowTrait;

    /**
     * Get company
     *
     * Pulls company id from input arguments or prompts user for id to create a Company model instance
     *
     * @return Company|null
     */
    protected function getCompany()
    {
        $company_id = $this->console->get('company-id', 'Company ID');
        if (!is_numeric($company_id)) {
            $this->console->error('Company ID must be numeric');
            return null;
        }
        $company_id = (int) $company_id;
        $company = Company::find($company_id);
        if ($company === null) {
            $this->console->error('Unable to find company with ID: %s', $company_id);
            return null;
        }
        $this->console->info('Company: %s', $company->name);
        if (!$this->console->confirm('Is the above company correct?')) {
            $this->console->error('Aborted');
            return null;
        }
        return $company;
    }

    /**
     * Bill all subscriptions which are at or past their next bill date
     */
    public function billAll()
    {
        try {
            $counts = (new CompanySubscriptionService)->billSubscriptions($this->getNow());
            $count_labels = [
                'billed_or_dormant' => 'Billed/Dormant',
                'failed' => 'Failed'
            ];
            $this->console->line('Stats:');
            $this->console->line();
            foreach ($count_labels as $key => $label) {
                $this->console->line('   %s: %d', $label, $counts[$key]);
            }
            $this->console->line();
            $this->console->info('Done');
        } catch (Exception $e) {
            $this->console->error($e->getMessage());
        }
    }

    /**
     * Suspend any active companies which have an invoice of their last subscription billing cycle past due by x number
     * of days (see CompanySubscriptionService::DAYS_UNTIL_SUSPENSION for day number)
     */
    public function suspendDelinquent()
    {
        try {
            $counts = (new CompanySubscriptionService)->suspendDelinquentCompanies($this->getNow());
            $count_labels = [
                'suspended' => 'Suspended',
                'marked_dormant' => 'Marked Dormant',
                'failed' => 'Failed'
            ];
            $this->console->line('Stats:');
            $this->console->line();
            foreach ($count_labels as $key => $label) {
                $this->console->line('   %s: %d', $label, $counts[$key]);
            }
            $this->console->line();
            $this->console->info('Done');
        } catch (Exception $e) {
            $this->console->error($e->getMessage());
        }
    }

    /**
     * Activate a subscription for a company. This will reverse a cancel for active companies which don't have any
     * outstanding invoices
     */
    public function activate()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        if (!$this->console->confirm('Are you sure you want to activate this subscription?')) {
            $this->console->error('Aborting');
            return;
        }

        try {
            (new CompanySubscriptionService)->activateSubscription(Acl::make(), $company->companyID);
            $this->console->info('Done');
        } catch (Exception $e) {
            $this->console->error($e->getMessage());
        }
    }

    /**
     * Cancel a subscription for a company, this will still allow them to use the software until the end of their
     * billing cycle (unless their last payment failed, in that case the suspend delinquent process should take over)
     */
    public function cancel()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        if (!$this->console->confirm('Are you sure you want to cancel this subscription?')) {
            $this->console->error('Aborting');
            return;
        }

        try {
            (new CompanySubscriptionService)->cancelSubscription(Acl::make(), $company->companyID);
            $this->console->info('Done');
        } catch (Exception $e) {
            $this->console->error($e->getMessage());
        }
    }

    public function changeTrialDate()
    {
        if (($company = $this->getCompany()) === null) {
            return;
        }

        $trial_date = $this->console->get('trial-date', 'Trial Date (YYYY-MM-DD)');
        if (!Carbon::hasFormat($trial_date, 'Y-m-d')) {
            $this->console->error('Invalid date format');
            return;
        }

        if (!$this->console->confirm('Are you sure you want to change the trial date for this company?')) {
            $this->console->error('Aborting');
            return;
        }

        try {
            $trial_date = Carbon::parse($trial_date);
            (new CompanySubscriptionService)->changeTrialDate(Acl::make(), $company->companyID, $trial_date);
            $this->console->info('Done');
        } catch (Exception $e) {
            $this->console->error($e->getMessage());
        }
    }
}
