<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Common\Models\Reseller;
use Common\Models\ResellerDomain;
use Core\Components\Console\Commands\BaseCommand;

/**
 * Class ResellerCommand
 *
 * @package App\Console\Commands
 */
class ResellerCommand extends BaseCommand
{
    /**
     * Allow user to see and change primary domain for a reseller
     */
    public function primaryDomain(): void
    {
        // choose reseller to work with
        $resellers = Reseller::all(['resellerID', 'name'])->keyBy('resellerID');
        $reseller_menu = [];
        foreach ($resellers as $id => $reseller) {
            $reseller_menu[$id] = $reseller->name;
        }
        $this->console->line('Choose a reseller:');
        $this->console->line();
        $reseller_id = $this->console->menu($reseller_menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($reseller_id === false) {
            $this->console->error('Aborted');
            return;
        }
        $reseller = $resellers[$reseller_id];
        $this->console->line('Reseller %s has the following domains:', $reseller->name);
        $this->console->line();
        $domains = $reseller->domains->keyBy('domainID');
        $primary_domain_id = null;
        $non_primary_domains = [];
        foreach ($domains as $id => $domain) {
            $primary = $domain->pivot->isResellerPrimaryDomain;
            $this->console->line($domain->domain . ($primary ? ' [primary]' : ''));
            if (!$primary) {
                $non_primary_domains[$id] = $domain->domain;
            } else {
                $primary_domain_id = $id;
            }
        }
        $this->console->line();
        if (!$this->console->confirm('Would you like the change the primary domain?')) {
            $this->console->error('Aborted');
            return;
        }
        $this->console->line('Choose a new domain to be the primary:');
        $this->console->line();
        $domain_id = $this->console->menu($non_primary_domains, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($domain_id === false) {
            $this->console->error('Aborted');
            return;
        }
        // update previous primary domain to no longer be marked as so
        ResellerDomain::query()
            ->where('resellerID', $reseller_id)
            ->where('domainID', $primary_domain_id)
            ->update([
                'isResellerPrimaryDomain' => false
            ]);
        // set new domain is primary for reseller
        ResellerDomain::query()
            ->where('resellerID', $reseller_id)
            ->where('domainID', $domain_id)
            ->update([
                'isResellerPrimaryDomain' => true
            ]);
        $this->console->info('Domain %s set as primary', $domains[$domain_id]->domain);
    }
}
