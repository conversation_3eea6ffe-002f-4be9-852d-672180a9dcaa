<?php

declare(strict_types=1);

namespace App\Extensions;

use Core\Classes\Path;
use Core\Components\Asset\Assets\File\{ScriptAsset, StyleAsset, SvgAsset};
use Core\Components\Asset\Classes\Manager;

/**
 * Class AssetExtension
 *
 * @package App\Extensions
 */
class AssetExtension
{
    /**
     * Load extension
     *
     * @param Manager $manager
     * @param Path $path
     * @throws \Core\Components\Asset\Exceptions\AssetException
     */
    public function load(Manager $manager, Path $path): void
    {
        // add our public asset paths
        $public_paths = [
            'base' => '',
            'style' => 'css',
            'script' => 'js',
            'brand' => 'brands',
            'image' => 'images',
            'common' => 'common',
            'module' => 'modules',
            'feature' => 'features',
            'vendor' => 'vendor',
            'animation' => 'animations'
        ];
        $public_path = $path->get('public');
        $public_base = 'assets/';
        foreach ($public_paths as $name => $directory) {
            $manager->addPath($name, $public_path . $public_base . $directory, $public_base . $directory);
        }

        // add our resource asset paths
        $resource_paths = [
            'resourceStyle' => 'css',
            'resourceScript' => 'js',
            'resourceImage' => 'images'
        ];
        $resource_path = $path->get('resource');
        foreach ($resource_paths as $name => $directory) {
            $manager->addPath($name, $resource_path . 'assets/' . $directory);
        }

        // define application script and style paths
        $manager->setAssetTypePath(StyleAsset::class, 'style');
        $manager->setAssetTypePath(ScriptAsset::class, 'script');
        $manager->setAssetTypePath(SvgAsset::class, 'image');
    }
}
