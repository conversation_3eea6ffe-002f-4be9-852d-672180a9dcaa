<?php

namespace App\Extensions;

use App\Services\Text\Classes\Manager;
use App\Services\Text\Classes\Type;
use Core\Interfaces\KernelInterface;

/**
 * Class TextExtension
 *
 * @package App\Extensions
 */
class TextExtension
{
    /**
     * Load extension
     *
     * @param KernelInterface $kernel
     */
    public function load(KernelInterface $kernel)
    {
        $kernel->singleton(Manager::class, function ($kernel) {
            return new Manager($kernel->get('config'), $kernel);
        });

        /** @var Manager $manager */
        $manager = $kernel->get(Manager::class);
        Type::setManager($manager);
    }
}
