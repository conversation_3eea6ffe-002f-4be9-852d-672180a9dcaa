<?php

namespace App\Extensions\Http;

use Core\Classes\Arr;
use Core\Components\Http\Interfaces\Route\BuilderFactoryInterface;
use Core\Components\Http\Interfaces\RouterInterface;
use Core\Classes\Path;

/**
 * Class RouteExtension
 *
 * @package App\Extensions\Http
 */
class RouteExtension
{
    /**
     * Configure the HTTP router for our application
     *
     * Set our available route files, route cache path, and register our custom route builders
     *
     * @param Path $path
     * @param RouterInterface $router
     * @param BuilderFactoryInterface $factory
     * @throws \Core\Exceptions\PathException
     */
    public function load(Path $path, RouterInterface $router, BuilderFactoryInterface $factory)
    {
        // add the application route files
        $router->addRouteFile($path->build('route','api.php'));
        $router->addRouteFile($path->build('route','web.php'));

        // set route cache path
        $router->setRouteCachePath($path->get('frameworkCache'));

        // add api resource route builder type
        $factory->register('apiResource', function (BuilderFactoryInterface $factory, $name, $controller, array $user_config = []) {
            $config = [
                'all' => [
                    'method' => 'get',
                    'action' => 'all'
                ],
                'store' => [
                    'method' => 'post',
                    'action' => 'store',
                    'name' => '.store'
                ],
                'retrieve' => [
                    'path' => '/{id}',
                    'method' => 'get',
                    'action' => 'retrieve',
                    'name' => '.retrieve'
                ],
                'update' => [
                    'path' => '/{id}',
                    'method' => 'put',
                    'action' => 'update',
                    'name' => '.update'
                ],
                'partial-update' => [
                    'path' => '/{id}',
                    'method' => 'patch',
                    'action' => 'partialUpdate',
                    'name' => '.partial-update'
                ],
                'delete' => [
                    'path' => '/{id}',
                    'method' => 'delete',
                    'action' => 'delete',
                    'name' => '.delete'
                ]
            ];
            $config = Arr::mergeRecursiveDistinct($config, $user_config);
            $config = array_filter($config);
            return $factory->group(function ($factory) use ($name, $controller, $config) {
                foreach ($config as $key => $info) {
                    if (isset($info['disable']) && $info['disable']) {
                        continue;
                    }
                    $info['path'] = $name . (isset($info['path']) ? $info['path'] : '');
                    if (!isset($info['controller'])) {
                        $info['controller'] = $controller;
                    }
                    $info['name'] = '.' . str_replace('/', '.', $name) . (isset($info['name']) ? $info['name'] : '');
                    $route = $factory->match($info['method'], $info['path'])
                        ->to($info['controller'], $info['action'])
                        ->name($info['name']);
                    if (isset($info['binds'])) {
                        foreach ($info['binds'] as $key => $value) {
                            $route->bind($key, $value);
                        }
                    }
                }
            });
        });
    }
}
