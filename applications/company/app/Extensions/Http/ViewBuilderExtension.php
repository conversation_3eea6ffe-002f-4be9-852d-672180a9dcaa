<?php

namespace App\Extensions\Http;

use Core\Components\Http\Interfaces\ViewFactoryInterface;

/**
 * Class ViewBuilderExtension
 *
 * @package App\Extensions\Http
 */
class ViewBuilderExtension
{
    /**
     * Register our applications view builders
     *
     * @param ViewFactoryInterface $factory
     */
    public function load(ViewFactoryInterface $factory)
    {
        $factory->builder(['layout.base'], \App\Http\ViewBuilders\BrandBuilder::class);
        $factory->builder('layout.app', \App\Http\ViewBuilders\LayoutAppBuilder::class);
    }
}
