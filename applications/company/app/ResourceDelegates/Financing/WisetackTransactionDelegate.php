<?php

namespace App\ResourceDelegates\Financing;

use App\Resources\Bid\ItemResource;
use App\Resources\Financing\WisetackTransactionResource;
use App\Services\Wisetack\Helpers\WisetackPricingPlan;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\WisetackTransaction;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;
use Ramsey\Uuid\UuidInterface;

class WisetackTransactionDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('bid')->resource(ItemResource::class);
        return $list;
    }

    public function buildFields(FieldList $list)
    {

        $list->field('id', true)
            ->primary()
            ->column('wisetackTransactionID')
            ->typeUuid()
            ->label('Id');

        $list->field('bid_item_id')
            ->typeUuid()
            ->column('bidItemID', true)
            ->label('Bid Item Id');

        $list->field('customer_name')
            ->onDemand()
            ->label('Customer Name')
            ->query(function ($query) {
                $query->leftJoin('property as p', 'p.propertyID', '=', 'project.propertyID');
                $query->leftJoin('customer as c', 'c.customerID', '=', 'p.customerID');
                return $query;
            })
            ->rawColumn('CONCAT(c.firstName, \' \', c.lastName)', 'customer_name');

        $list->field('project_name')
            ->onDemand()
            ->label('Project Name')
            ->rawColumn('project.projectDescription', 'project_name');

        $list->field('bid_description')
            ->onDemand()
            ->label('Bid Description')
            ->query(function ($query, $scope_builder) {
                $query->leftJoin('evaluation as eval', 'eval.bidItemID', '=', "bidItems.bidItemID");
                return $query->leftJoin('customBid as cb', 'cb.evaluationID', '=', "eval.evaluationID");
            })
            ->rawColumn('eval.evaluationDescription', 'bid_description');

        $list->field('bid_total')
            ->onDemand()
            ->label('Bid Total')
            ->rawColumn('bidItems.total', 'bid_total');

        $list->field('bid_accepted_at')
            ->onDemand()
            ->label('Bid Accepted')
            ->rawColumn('bidItems.acceptedAt', 'bid_accepted_at');

        $list->field('requested_loan_amount')
            ->column('requestedLoanAmount')
            ->label('Requested Amount');

        $list->field('approved_loan_amount')
            ->column('approvedLoanAmount')
            ->label('Approved Amount');

        $list->field('settled_loan_amount')
            ->column('settledLoanAmount')
            ->label('Settled Amount');

        $list->field('status')
            ->column('status')
            ->requireColumn()
            ->label('Status');

        $list->field('pricing_plan')
            ->column('pricingPlan')
            ->label('Pricing Plan');

        $list->field('pricing_plan_name')
            ->onDemand()
            ->label('Pricing Plan')
            ->column('pricingPlan')
            ->outputMutator(function ($value, WisetackTransaction $loan) {
                return $loan->pricingPlan ? WisetackPricingPlan::getDescriptiveName($loan->pricingPlan) : null;
            });

        $list->field('bid_status_name')
            ->onDemand()
            ->label('Bid Status')
            ->value(function (WisetackTransaction $loan) {
                return $loan->getFormattedStatusName();
            });

        $list->field('initiated_at')
            ->column('initiatedAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Initiated At');

        $list->field('actions_required_at')
            ->column('actionsRequiredAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Actions Required At');

        $list->field('authorized_at')
            ->column('authorizedAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Approved At');

        $list->field('loan_terms_accepted_at')
            ->column('loanTermsAcceptedAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Offer Locked At');

        $list->field('loan_confirmed_at')
            ->column('loanConfirmedAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Confirmed At');

        $list->field('settled_at')
            ->column('settledAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Paid At');

        $list->field('declined_at')
            ->column('declinedAt')
            ->typeDateTime()
            ->onDemand()
            ->label('Declined At');

        $list->field('expired_at')
            ->column('expiredAt')
            ->typeDateTime()
            ->label('Expiration');

        $list->field('canceled_at')
            ->column('canceledAt')
            ->typeDateTime()
            ->label('Canceled At');

        $list->field('refunded_at')
            ->column('refundedAt')
            ->typeDateTime()
            ->label('Refunded At');

        $list->field('link')
            ->column('paymentLink')
            ->label('Application Link');

        $list->field('expiration_date')
            ->column('expirationDate')
            ->typeDateTime()
            ->label('Expiration Date');

        $this->timestampFields($list, false, false, true);

        $list->modify([
            'status',
            'requested_loan_amount',
            'approved_loan_amount',
            'settled_loan_amount',
            'initiated_at',
            'actions_required_at',
            'authorized_at',
            'loan_terms_accepted_at',
            'loan_confirmed_at',
            'settled_at',
            'declined_at',
            'expired_at',
            'canceled_at',
            'refunded_at',
            'link',
            'expiration_date',
            'created_at', 'updated_at'
        ], function (Field $field) {
            return $field->onAction(Resource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false)->mutable();
            });
        });

        return $list;
    }

    public function queryScopeGlobal($query, WisetackTransactionResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }

        if (!$user->primary && !$user->projectManagement && $user->sales) {
            return $query->ofUser($user);
        }

        return $query->ofCompany($user->companyID);

    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param WisetackTransactionResource $resource
     * @return bool
     */
    public function actionAllowed($action, WisetackTransactionResource $resource)
    {
        if (in_array($action, [Resource::ACTION_GET_COLLECTION, Resource::ACTION_GET_ENTITY])) {
            return true;
        }

        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }

        if (($user->projectManagement || $user->sales)) {
            return true;
        }

        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'report-financing-transactions-v1':
                $scope->fields([
                    'id',
                    'bid',
                    'status',
                    'pricing_plan',
                    'pricing_plan_name',
                    'requested_loan_amount',
                    'approved_loan_amount',
                    'settled_loan_amount',
                    'initiated_at',
                    'actions_required_at',
                    'authorized_at',
                    'loan_terms_accepted_at',
                    'loan_confirmed_at',
                    'settled_at',
                    'declined_at',
                    'expired_at',
                    'canceled_at',
                    'refunded_at',
                    'link',
                    'expiration_date',
                    'created_at',
                    'updated_at',
                ])
                ->with([
                    'bid' => [
                        'fields' => ['id', 'total', 'accepted_at', 'status', 'description'],
                        'with' => [
                            'project' => [
                                'fields' => ['id', 'description'],
                                'with' => [
                                    'property' => [
                                        'fields' => ['id'],
                                        'with' => [
                                            'customer' => [
                                                'fields' => ['id', 'first_name', 'last_name'],
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]);
                break;
            case 'export-v1':
                $scope->fields([
                    'customer_name',
                    'project_name',
                    'bid_description',
                    'bid_total',
                    'bid_accepted_at',
                    'requested_loan_amount',
                    'approved_loan_amount',
                    'settled_loan_amount',
                    'bid_status_name',
                    'pricing_plan_name',
                    'created_at',
                    'updated_at',
                    'initiated_at',
                    'actions_required_at',
                    'authorized_at',
                    'loan_terms_accepted_at',
                    'loan_confirmed_at',
                    'settled_at',
                    'declined_at',
                    'expiration_date',
                ], true);
                break;
        }
    }
}
