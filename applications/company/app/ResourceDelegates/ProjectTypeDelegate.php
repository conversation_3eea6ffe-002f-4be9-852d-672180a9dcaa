<?php

namespace App\ResourceDelegates;

use App\Resources\CompanyResource;
use App\Resources\ProjectTypeResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\ProjectType;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;

class ProjectTypeDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('company')->resource(CompanyResource::class);
        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('projectTypeID')
            ->validation('Project Type Id', 'required|uuid')
            ->noSave()
            ->onAction(
                ProjectTypeResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|pk_available')
                    ->save();
            })
            ->enableAction(ProjectTypeResource::ACTION_FILTER)
            ->label('Id');

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id')
            ->onAction(ProjectTypeResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('status')
            ->label('Status')
            ->requireColumn()
            ->validationRules('required|type[int]|in_array[statuses]|check_status')
            ->onAction(ProjectTypeResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->column('name')
            ->label('Name')
            ->validationRules('trim|nullable|optional|max_length[100]');

        $list->field('inactive_at')
            ->typeDateTime()
            ->column('inactiveAt')
            ->label('Inactive At')
            ->immutable();

        $list->field('inactive_by_user_id')
            ->column('inactiveByUserID', true)
            ->immutable();

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->label('Archived At')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID', true)
            ->immutable();

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'status', 'name', 'inactive_at', 'archived_at'
        ], function (Field $field) {
            return $field->enableAction(ProjectTypeResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, ProjectTypeResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user !== null) {
            $list->get('company_id')->immutable()->hide();
        }
        return $list;
    }

    public function validationRules(Rules $rules, ProjectTypeResource $resource)
    {
        $rules->register('check_company_id', function ($id, $params, Validator $validator) use ($resource) {
            $company_resource = $resource->relationResource('company');
            if (($company = $company_resource->find($id)) === null) {
                return 'company_not_found';
            }
            try {
                $company_resource->isModelMutable($company);
            } catch (ImmutableEntityException $e) {
                return ['company_immutable', ['reason' => $e->getMessage()]];
            }
            return true;
        }, [
            'company_not_found' => 'Company not found',
            'company_immutable' => 'Company immutable{reason}'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [ProjectTypeResource::STATUS_ACTIVE, ProjectTypeResource::STATUS_INACTIVE],
                ProjectTypeResource::STATUS_ACTIVE => [ProjectTypeResource::STATUS_INACTIVE, ProjectTypeResource::STATUS_ARCHIVED],
                ProjectTypeResource::STATUS_INACTIVE => [ProjectTypeResource::STATUS_ACTIVE, ProjectTypeResource::STATUS_ARCHIVED]
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', ProjectTypeResource::getStatuses());

        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case ProjectType::STATUS_ACTIVE:
                    $model_data['inactiveAt'] = null;
                    $model_data['inactiveByUserID'] = null;
                    break;
                case ProjectType::STATUS_INACTIVE:
                    $model_data['inactiveAt'] = Carbon::now('UTC');
                    $model_data['inactiveByUserID'] = $resource->acl()->user()->getKey();
                    break;
                case ProjectType::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $model_data['archivedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        return $model_data;
    }

    public function queryScopeGlobal($query, ProjectTypeResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, ProjectTypeResource $resource)
    {
        if (($action & ProjectTypeResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        if (in_array($action, [ProjectTypeResource::ACTION_GET_COLLECTION, ProjectTypeResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->admin || $user->primary) {
            return true;
        }
        return false;
    }

    public function modelIsMutable(ProjectType $model, ProjectTypeResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null || $user->admin || ($user->primary && $user->companyID === $model->companyID)) {
            return true;
        }
        return false;
    }
}
