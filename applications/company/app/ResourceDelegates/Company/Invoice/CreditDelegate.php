<?php

namespace App\ResourceDelegates\Company\Invoice;

use App\Resources\Company\Invoice\CreditResource;
use App\Resources\Company\SubscriptionResource;
use App\Resources\CompanyResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\CompanyInvoiceCredit;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;

class CreditDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('company')->resource(CompanyResource::class);
        $list->oneOrMany('company_subscription')->modelRelation('companySubscription')
            ->resource(SubscriptionResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('companyInvoiceCreditID')
            ->noSave()
            ->onAction(CreditResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id')
            ->onAction(CreditResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('description')
            ->validation('Description', 'trim|required|max_length[200]');

        $list->field('amount')
            ->validation('Amount', 'trim|required|type[string]|numeric')
            ->immutable();

        $list->field('remaining_amount')
            ->column('remainingAmount')
            ->validation('Remaining Amount', 'trim|required|type[string]|numeric')
            ->immutable();

        $list->field('is_expended')
            ->column('isExpended')
            ->immutable();

        $list->field('expended_at')
            ->typeDateTime()
            ->column('expendedAt')
            ->immutable();

        $list->field('company_subscription_id')
            ->column('companySubscriptionID', true)
            ->validation('Company Subscription Id', 'nullable|required_if[is_subscription]|type[int]|check_company_subscription_id')
            ->onAction(CreditResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('nullable|required_if[is_subscription]|type[int]');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function configureFields(FieldList $list, CreditResource $resource)
    {
        $acl = $resource->acl();
        if ($acl->user() === null || $acl->isAdmin()) {
            $list->get('amount')
                ->onAction([CreditResource::ACTION_CREATE, CreditResource::ACTION_NESTED_CREATE], function (Field $field) {
                    return $field->mutable();
                });
            $list->get('remaining_amount')
                ->onAction([CreditResource::ACTION_UPDATE, CreditResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                    return $field->mutable();
                });
        } else {
            $list->get('company_id')->disable();
            $list->modify([
                'type', 'description', 'amount', 'company_subscription_id'
            ], function (Field $field) {
                return $field->immutable();
            });
        }

        return $list;
    }

    public function validationRules(Rules $rules, CreditResource $resource)
    {
        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        $rules->register('check_company_subscription_id', function ($id) use ($resource) {
            if ($resource->relationResource('company_subscription')->entityExists($id)) {
                return true;
            }
            return 'check_company_subscription_id';
        }, [
            'check_company_subscription_id' => 'Unable to find company subscription'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', CreditResource::getTypes());
        $config->store('is_subscription', function ($field, Validator $validator) {
            return $validator->data('type') === CreditResource::TYPE_SUBSCRIPTION;
        });
        return $config;
    }

    public function anyCreateModelDataAfter($model_data)
    {
        $model_data['remainingAmount'] = $model_data['amount'];
        $model_data['isExpended'] = 0;
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $bc_scale = 2;
        $model = $request->getModel();
        if (
            isset($model_data['remainingAmount']) &&
            bccomp($model_data['remainingAmount'], $model->remainingAmount, $bc_scale) !== 0 &&
            bccomp($model_data['remainingAmount'], '0.00', $bc_scale) <= 0
        ) {
            $model_data['remainingAmount'] = '0.00';
            $model_data['isExpended'] = 1;
            $model_data['expendedAt'] = Carbon::now('UTC');
        }

        return $model_data;
    }

    public function queryScopeGlobal($query, CreditResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        $query->ofCompany($user->companyID);
        return $query;
    }

    public function actionAllowed($action, CreditResource $resource)
    {
        if (($action & CreditResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $acl = $resource->acl();
        return $acl->user() === null || $acl->isAdmin();
    }

    public function modelIsMutable(CompanyInvoiceCredit $model)
    {
        if ($model->isExpended) {
            throw new ImmutableEntityException('Credit is no longer mutable since it is expended');
        }
        return true;
    }
}
