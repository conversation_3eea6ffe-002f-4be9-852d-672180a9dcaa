<?php

namespace App\ResourceDelegates\Company\Subscription;

use App\Resources\Company\Subscription\PriceAdjustmentResource;
use App\Resources\Company\SubscriptionResource;
use App\Resources\Subscription\Option\PriceAdjustmentResource as SubscriptionOptionPriceAdjustmentResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\{Addon, CompanySubscriptionPriceAdjustment};
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;

class PriceAdjustmentDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('subscription')->resource(SubscriptionResource::class);
        $list->oneOrMany('subscription_option_price_adjustment')
            ->modelRelation('subscriptionOptionPriceAdjustment')
            ->resource(SubscriptionOptionPriceAdjustmentResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('companySubscriptionPriceAdjustmentID')
            ->noSave()
            ->onAction(PriceAdjustmentResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('subscription_id')
            ->column('companySubscriptionID', true)
            ->validation('Subscription Id', 'required|type[int]|check_subscription_id')
            ->onAction(PriceAdjustmentResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('subscription_option_price_adjustment_id')
            ->column('subscriptionOptionPriceAdjustmentID', true)
            ->validation('Subscription Option Price Adjustment Id', 'nullable|optional|type[int]|check_subscription_option_price_adjustment_id')
            ->onAction(PriceAdjustmentResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $list->field('addon_id')
            ->column('addonID', true)
            ->validation('Addon Id', 'nullable|required_if[is_addon]|type[int]|check_addon_id')
            ->onAction(PriceAdjustmentResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('nullable|required_if[is_addon]|type[int]');
            });

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[100]');

        $list->field('amount_type')
            ->column('amountType')
            ->validation('Amount Type', 'required|type[int]|in_array[amount_types]');

        $list->field('amount')
            ->validation('Price', 'trim|required|type[string]|numeric|abs');

        $list->field('delay_count')
            ->column('delayCount')
            ->validation('Delay Count', 'required|type[int]|greater_than_equal[0]');

        $list->field('occurrence_count')
            ->column('occurrenceCount')
            ->validation('Occurrence Count', 'nullable|optional|type[int]|greater_than[0]');

        $list->field('cycle_count')
            ->column('cycleCount')
            ->validation('Cycle Count', 'required|type[int]');

        $list->field('billed_count')
            ->column('billedCount')
            ->validation('Billed Count', 'required|type[int]');

        $list->field('is_refundable')
            ->column('isRefundable')
            ->validation('Is Refundable', 'required|type[bool]');

        $list->field('is_transferable')
            ->column('isTransferable')
            ->validation('Is Transferable', 'required|type[bool]');

        $list->field('first_billed_at')
            ->typeDateTime()
            ->column('firstBilledAt')
            ->validation('First Billed At', 'nullable|optional|iso8601_date|to_carbon|to_utc');

        $list->field('last_billed_at')
            ->typeDateTime()
            ->column('lastBilledAt')
            ->validation('Last Billed At', 'nullable|optional|iso8601_date|to_carbon|to_utc');

        $this->timestampFields($list);

        return $list;
    }

    public function configureFields(FieldList $list, PriceAdjustmentResource $resource)
    {
        $acl = $resource->acl();
        $user = $acl->user();
        if ($user !== null && !$user->admin && !$acl->isAdmin()) {
            $list->modify([
                'subscription_id', 'subscription_option_price_adjustment_id', 'type', 'name', 'amount_type', 'amount',
                'delay_count', 'occurrence_count', 'is_refundable', 'is_transferable', 'cycle_count', 'billed_count',
                'first_billed_at', 'last_billed_at'
            ], function (Field $field) {
                $field->immutable();
                $field->onAction([
                    SubscriptionResource::ACTION_NESTED_CREATE, SubscriptionResource::ACTION_UPDATE,
                    SubscriptionResource::ACTION_PARTIAL_UPDATE
                ], function (Field $field) {
                    return $field->accessLevelPrivate()->mutable();
                });
            });
        }

        return $list;
    }

    public function validationRules(Rules $rules, PriceAdjustmentResource $resource)
    {
        $rules->register('check_subscription_id', function ($id) use ($resource) {
            if ($resource->relationResource('subscription')->entityExists($id)) {
                return true;
            }
            return 'check_subscription_id';
        }, [
            'check_subscription_id' => 'Unable to find subscription'
        ]);

        $rules->register('check_subscription_option_price_adjustment_id', function ($id) use ($resource) {
            if ($resource->relationResource('subscription_option_price_adjustment')->entityExists($id)) {
                return true;
            }
            return 'check_subscription_option_price_adjustment_id';
        }, [
            'check_subscription_option_price_adjustment_id' => 'Unable to find subscription option price adjustment'
        ]);

        $rules->register('check_addon_id', function ($id) use ($resource) {
            if (Addon::query()->whereKey($id)->count() === 1) {
                return true;
            }
            return 'check_addon_id';
        }, [
            'check_addon_id' => 'Unable to find addon'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('is_addon', function ($field, Validator $validator) {
            return in_array($validator->data('type'), [
                PriceAdjustmentResource::TYPE_ADD_ON
            ]);
        });
        $config->store('types', PriceAdjustmentResource::getTypes());
        $config->store('amount_types', PriceAdjustmentResource::getAmountTypes());
        return $config;
    }

    public function anyCreateModelDataAfter($model_data)
    {
        // discounts always need to be refundable since if they are removed we would end up giving back more than
        // they paid
        if ($model_data['type'] === CompanySubscriptionPriceAdjustment::TYPE_DISCOUNT) {
            $model_data['isRefundable'] = true;
        }

        return $model_data;
    }

    public function queryScopeGlobal($query, PriceAdjustmentResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null || $user->admin) {
            return $query;
        }
        $query->ofCompany($user->companyID);
        return $query;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        if ($scope->getFormat() === 'account-v1') {
            $scope->query(function ($query) {
                $query->where(function ($query) {
                    $query->whereColumn('occurrenceCount', '>=', 'billedCount')
                        ->orWhereNull('occurrenceCount');
                });
                return $query;
            });
            $scope->fields(['amount', 'amount_type', 'name', 'type', 'delay_count', 'occurrence_count']);
        }
    }

    public function actionAllowed($action, PriceAdjustmentResource $resource)
    {
        if (($action & PriceAdjustmentResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $acl = $resource->acl();
        $user = $acl->user();
        if (
            $user === null ||
            $user->admin ||
            $acl->isAdmin() ||
            (
                $user->primary &&
                in_array($action, [
                    PriceAdjustmentResource::ACTION_NESTED_CREATE, PriceAdjustmentResource::ACTION_PARTIAL_UPDATE
                ])
            )
        ) {
            return true;
        }
        return false;
    }
}
