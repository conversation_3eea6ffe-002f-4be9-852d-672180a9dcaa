<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Company\CustomReport;

use App\ResourceMediaHandlers\Company\CustomReport\ResultFileHandler;
use App\Resources\Company\{CustomReportResource, CustomReport\ResultResource};
use App\Resources\{FileResource, UserResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\{CompanyCustomReportResult, File};
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Resource\Classes\{Field, FieldList, MediaList, MediaType, RelationList, Scope};
use Core\Components\Validation\Classes\{FieldConfig, Validator};

/**
 * Class ResultDelegate
 *
 * @package App\ResourceDelegates\Company\CustomReport
 */
class ResultDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    /**
     * Build relations list
     *
     * @param RelationList $list
     * @return RelationList
     */
    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('custom_report')->modelRelation('customReport')->resource(CustomReportResource::class);
        $list->oneOrMany('file')->resource(FileResource::class);
        $list->oneOrMany('user')->resource(UserResource::class);
        return $list;
    }

    /**
     * Build field list
     *
     * @param FieldList $list
     * @return FieldList
     */
    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->column('companyCustomReportResultID')
            ->typeUuid()
            ->noSave()
            ->onAction([ResultResource::ACTION_CREATE, ResultResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|pk_available')
                    ->save();
            })
            ->onAction(ResultResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('custom_report_id')
            ->column('companyCustomReportID', true)
            ->typeUuid()
            ->validation('Custom Report Id', 'required|uuid|related_entity_exists[custom_report]')
            ->onAction(ResultResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('user_id')
            ->column('userID', true)
            ->validation('User Id', 'required|type[int]|related_entity_exists[user]')
            ->onAction(ResultResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('inputs')
            ->validation('Inputs', 'required|type[array]');

        $list->field('status')
            ->validation('Status', sprintf('required|type[int]|in_array:callable[%s][getStatuses]', ResultResource::class));

        $list->field('file_id')
            ->typeUuid()
            ->column('fileID', true)
            ->validation('File Id', sprintf('nullable|required_if[is_generated]|uuid|media_entity_exists[file][%d]', File::TYPE_CUSTOM_REPORT_RESULT));

        $list->field('row_count')
            ->column('rowCount')
            ->validation('Row Count', 'nullable|required_if[not_generating]|type[int]');

        $list->field('time')
            ->validation('Time', 'nullable|required_if[not_generating]|type[int]');

        $list->field('generated_at')
            ->column('generatedAt')
            ->typeDateTime()
            ->immutable();

        $list->field('expired_at')
            ->column('expiredAt')
            ->typeDateTime()
            ->immutable();

        $list->field('failed_at')
            ->column('failedAt')
            ->typeDateTime()
            ->immutable();

        $this->timestampFields($list, false);

        return $list;
    }

    /**
     * Configure field list utilizing the current ACL to determine what can be seen
     *
     * @param FieldList $list
     * @param ResultResource $resource
     * @return FieldList
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function configureFields(FieldList $list, ResultResource $resource): FieldList
    {
        if ($resource->acl()->user() !== null) {
            $list->get('user_id')->disable();
        }
        return $list;
    }

    /**
     * Modify global validation field config
     *
     * @param FieldConfig $config
     * @return FieldConfig
     */
    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('is_generated', function ($field, Validator $validator): bool {
            return $validator->data('status') === ResultResource::STATUS_GENERATED;
        });
        $config->store('not_generating', function ($field, Validator $validator): bool {
            return $validator->data('status') !== ResultResource::STATUS_GENERATING;
        });
        return $config;
    }

    /**
     * Build media list
     *
     * @param MediaList $list
     * @return MediaList
     */
    public function buildMedia(MediaList $list): MediaList
    {
        $list->type('file')
            ->directoryName('custom-report-files', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(ResultFileHandler::class);
            });
        return $list;
    }

    /**
     * Modify model data after any update request
     *
     * @param array $model_data
     * @param UpdateRequest $request
     * @return array
     */
    public function anyUpdateModelDataAfter(array $model_data, UpdateRequest $request): array
    {
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            $columns = [
                CompanyCustomReportResult::STATUS_GENERATED => 'generatedAt',
                CompanyCustomReportResult::STATUS_EXPIRED => 'expiredAt',
                CompanyCustomReportResult::STATUS_FAILED => 'failedAt'
            ];
            $model_data[$columns[$model_data['status']]] = Carbon::now('UTC');
            $request->store('is_expired', $model_data['status'] === CompanyCustomReportResult::STATUS_EXPIRED);
        }
        return $model_data;
    }

    /**
     * Handle actions after any update request is saved to database
     *
     * @param UpdateRequest $request
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        if ($request->storage('is_expired', false)) {
            /** @var ResultResource $resource */
            $resource = $request->resource();

            /** @var ResultFileHandler $file_handler */
            $file_handler = $resource->getMediaHandler('file');
            $file_handler->deleteByFileField('file_id', $request->getModel(), data: true);
        }
    }

    /**
     * Handle any actions which need to be completed after result is deleted from database
     *
     * @param DeleteRequest $request
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function deleteSaveAfter(DeleteRequest $request): void
    {
        /** @var ResultResource $resource */
        $resource = $request->resource();

        /** @var ResultFileHandler $file_handler */
        $file_handler = $resource->getMediaHandler('file');
        $file_handler->deleteByFileField('file_id', $request->getModel(), data: true);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed for the system
     *
     * @param int $action
     * @param ResultResource $resource
     * @return bool
     */
    public function actionAllowed(int $action, ResultResource $resource): bool
    {
        if (($action & ResultResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        return $resource->acl()->user() === null;
    }

    /**
     * Scoping applied to all queries
     *
     * Used to ensure all queries are scoped to a specific user.
     *
     * @param object $query
     * @param ResultResource $resource
     * @return object
     */
    public function queryScopeGlobal(object $query, ResultResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        $query->ofUser($user);
        return $query;
    }

    /**
     * Customize scope before it's built for use with a query
     *
     * @param Scope $scope
     */
    public function scopeBuildBefore(Scope $scope): void
    {
        if ($scope->getFormat() === 'detail-v1') {
            $scope->fields(['id', 'inputs', 'status', 'row_count', 'generated_at'], true);
            $scope->with(['file_media_urls']);
        }
    }
}
