<?php

namespace App\ResourceDelegates\User;

use App\Classes\Func;
use App\Resources\User\PhoneResource;
use App\Resources\UserResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\UserPhone;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;

class PhoneDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('user')->resource(UserResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('userPhoneID')
            ->noSave();

        $list->field('user_id')
            ->column('userID', true)
            ->validation('User Id', 'required|type[int]|check_user_id')
            ->onAction(PhoneResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('number')
            ->column('phoneNumber')
            ->validation('Number', 'trim|required|us_phone|us_phone_format|check_number')
            ->onAction(PhoneResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('trim|required|us_phone|us_phone_format');
            });

        $list->field('description')
            ->column('phoneDescription')
            ->validation('Description', 'trim|required|max_length[20]');

        $list->field('is_primary')
            ->column('isPrimary')
            ->validation('Is Primary', 'required|type[bool]|check_primary')
            ->onAction([PhoneResource::ACTION_CREATE, PhoneResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|type[bool]');
            })
            ->onAction(PhoneResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, PhoneResource $resource)
    {
        $rules->register('check_user_id', function ($id, $params, Validator $validator) use ($resource) {
            $user_resource = $resource->relationResource('user');
            if (($user = $user_resource->find($id)) === null) {
                return 'user_not_found';
            }
            try {
                $user_resource->isModelMutable($user);
            } catch (ImmutableEntityException $e) {
                return ['user_immutable', ['reason' => $e->getMessage()]];
            }

            $validator->getConfig()->store('user', $user);
            return true;
        }, [
            'user_not_found' => 'User not found',
            'user_immutable' => 'User immutable{reason}'
        ]);

        $rules->register('check_number', function ($number, $params, Validator $validator) {
            $user = $validator->getConfig()->storage('user');
            if ($user === null) {
                return Rules::STOP;
            }
            $model = $validator->getConfig()->storage('_model');
            if (
                ($model === null || $model->phoneNumber !== $number) &&
                $user->phones()->where('phoneNumber', $number)->count() !== 0
            ) {
                return 'number_exists';
            }
            return true;
        }, [
            'number_exists' => 'Number already in use'
        ]);

        $rules->register('check_primary', function ($primary, $params, Validator $validator) {
            $config = $validator->getConfig();
            if (
                $config->storage('check_primary', true) &&
                $config->storage('_model')->isPrimary &&
                !$primary
            ) {
                return 'check_primary';
            }
            return true;
        }, [
            'check_primary' => 'Cannot disable primary status since this phone is currently the primary'
        ]);

        return $rules;
    }

    protected function validatePhones(array $phones)
    {
        $primaries = 0;
        foreach ($phones as $phone) {
            if (!isset($phone['is_primary']) || !$phone['is_primary']) {
                continue;
            }
            $primaries++;
        }
        if ($primaries === 0) {
            throw new ValidationException('One phone must be marked primary');
        }
        if ($primaries > 1) {
            throw new ValidationException('Only one phone can be marked primary');
        }
    }

    public function batchAnyCreateValidateAfter(array $entities)
    {
        $this->validatePhones($entities);
    }

    public function batchAnyUpdateValidateAfter(array $entities)
    {
        $this->validatePhones($entities);
    }

    public function createSaveBefore(CreateRequest $request, $model_data)
    {
        // if model data contains a primary phone, then we remove the old one since there can only be one at a time
        if (isset($model_data['isPrimary']) && $model_data['isPrimary']) {
            /** @var PhoneResource $resource */
            $resource = $request->resource();
            $resource->removePrimary($model_data['userID']);
        }
    }

    public function anyUpdateSaveBefore(UpdateRequest $request, $model_data)
    {
        // if model data contains a primary phone and the current model isn't already a primary, then we remove the
        // existing primary since there can only be one at a time
        if (isset($model_data['isPrimary']) && $model_data['isPrimary'] && !$request->getModel()->isPrimary) {
            /** @var PhoneResource $resource */
            $resource = $request->resource();
            $resource->removePrimary($model_data['userID']);
        }
    }

    public function queryScopeGlobal($query, PhoneResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        $query->ofCompany($user->companyID);
        return $query;
    }

    public function modelIsMutable(UserPhone $model, PhoneResource $resource)
    {
        return $resource->relationResource('user')->isModelMutable($model->user);
    }

    public function modelIsDeletable(UserPhone $model)
    {
        return !$model->isPrimary;
    }
}
