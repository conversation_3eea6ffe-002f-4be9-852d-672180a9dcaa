<?php

namespace App\ResourceDelegates;

use App\Resources\CompanyResource;
use App\Resources\AdditionalCostResource;
use App\Resources\Product\Item\AdditionalCostResource as ProductItemAdditionalCostResource;
use App\Resources\Product\ItemResource;
use App\Resources\UnitResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\AdditionalCost;
use Common\Models\ProductItemAdditionalCost;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Relations\CustomRelation;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\UuidInterface;

class AdditionalCostDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->polymorphic('owner')->typeField('owner_type')->idField('owner_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(AdditionalCostResource::OWNER_TYPE_COMPANY)->resource(CompanyResource::class);
                // @todo figure out manufacturer
            });
        $list->oneOrMany('unit')->resource(UnitResource::class);
        $list->custom('product_items')
            ->dataCallback(function (AdditionalCost $additional_cost, Entity $entity, CustomRelation $relation) {
                $resource = ProductItemAdditionalCostResource::make($relation->getParentScopeBuilder()->getResource()->acl());
                $product_ids = $resource->newScopedQuery()
                    ->where('productItemAdditionalCosts.additionalCostID', $additional_cost->getKey())
                    ->where('productItemAdditionalCosts.status', ProductItemAdditionalCost::STATUS_ACTIVE)
                    ->get(['productItemAdditionalCosts.productItemID'])
                    ->pluck('productItemID')
                    ->toArray();
                if (count($product_ids) === 0) {
                    $products = new Collection();
                } else {
                    $product_resource = ItemResource::make($relation->getParentScopeBuilder()->getResource()->acl());
                    $primary_field = $product_resource->getPrimaryField();
                    $product_ids = array_map(function ($id) use ($primary_field) {
                        return $primary_field->outputValue($id);
                    }, $product_ids);
                    $product_scope = Scope::make()
                        ->fields(['id', 'name'])
                        ->filter('id', 'in', $product_ids)
                        ->query(function ($query) {
                            return $query->ordered();
                        });
                    $products = $product_resource->collection()->scope($product_scope)->run();
                }
                $entity->set('product_items', $products);
                return $entity;
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('additionalCostID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(AdditionalCostResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            })
            ->label('Id');

        $owner_type_map = AdditionalCostResource::getOwnerTypeMap();
        $list->field('owner_type')
            ->column('ownerType', true)
            ->validation('Owner Type', 'required|type[int]|in_array[owner_types]')
            ->saveMutator(function ($value) use ($owner_type_map) {
                return array_search($value, $owner_type_map);
            })
            ->outputMutator(function ($value) use ($owner_type_map) {
                return $owner_type_map[$value];
            });

        $list->field('owner_id')
            ->column('ownerID', true)
            ->validation('Owner Id', 'required|type[int]|check_owner_id');

        $list->field('name')
            ->validation('Name', 'required|max_length[100]')
            ->label('Name');

        $list->field('unit_id')
            ->typeUuid()
            ->column('unitID', true)
            ->validation('Unit Id', 'required|uuid|related_entity_exists[unit]')
            ->onAction(AdditionalCostResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('cost')
            ->validation('Cost', 'required|type[string]|numeric')
            ->label('Cost');

        $list->field('markup')
            ->validation('Markup', 'nullable|optional|type[string]|numeric')
            ->label('Markup');

        $list->field('markup_export')
            ->label('Markup')
            ->onDemand()
            ->rawColumn("IF(markup IS NULL, '0.0000', markup)", 'markup');

        $list->field('unit_price')
            ->column('unitPrice')
            ->validation('Unit Price', 'required|type[string]|numeric')
            ->label('Unit Price');

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([AdditionalCostResource::ACTION_CREATE, AdditionalCostResource::ACTION_NESTED_CREATE])
            ->onAction(AdditionalCostResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $list->field('unit')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('units', 'units.unitID', '=', 'additionalCosts.unitID');
            })
            ->rawColumn('units.name', 'unit_name')
            ->onAction(AdditionalCostResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('unit_abbreviation')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('units', 'units.unitID', '=', 'additionalCosts.unitID');
            })
            ->rawColumn('units.abbreviation', 'unit_abbreviation')
            ->onAction(AdditionalCostResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            })
            ->label('Unit Abbreviation');

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list, false, false, true);

        $list->modify(['name', 'unit_price', 'markup', 'cost'], function (Field $field) {
            return $field->enableAction(AdditionalCostResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, AdditionalCostResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('owner_type')->disable();
            $list->get('owner_id')->disable();
        }

        return $list;
    }

    public function validationRules(Rules $rules, AdditionalCostResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_owner_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('owner_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('owner_type');
            $owner_type_resource = $resource->polyRelationResource('owner', $type);

            if (!$owner_type_resource->entityExists($id)) {
                return 'check_owner_id';
            }
            return true;
        }, [
            'check_owner_id' => 'Unable to find owner'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('owner_types', AdditionalCostResource::getOwnerTypes());
        $config->store('statuses', AdditionalCostResource::getStatuses());
        return $config;
    }

    public function queryScopeGlobal($query, AdditionalCostResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->searchWithRank($term);
    }

    public function anyUpdateValidateAfter(Entity $entity, UpdateRequest $request)
    {
        /** @var AdditionalCostResource $resource */
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($entity['status'])) {
            $status = $resource->getFields()->get('status')->saveValue($entity['status']);
            if ($status !== $model->status) {
                if ($resource->hasActiveProducts($model->getKey())) {
                    throw new ActionNotAllowedException('Cannot archive an additional cost which is still in use by a product');
                }
            }
        }
        return $entity;
    }

    public function createModelDataAfter($model_data, Request $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['ownerType'] = AdditionalCost::OWNER_COMPANY;
            $model_data['ownerID'] = $user->companyID;
        }
        $model_data['status'] = AdditionalCost::STATUS_ACTIVE;
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case AdditionalCost::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case AdditionalCost::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $model_data['archivedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        if (
            (isset($model_data['cost']) && $model_data['cost'] != $model->cost) ||
            (isset($model_data['markup']) && $model_data['markup'] != $model->markup) ||
            (isset($model_data['unitPrice']) && $model_data['unitPrice'] != $model->unitPrice)
        ) {
            $request->store('is_price_changing', true);
        }
        return $model_data;
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        if ($request->storage('is_price_changing', false)) {
            $resource = $request->resource();
            $model = $request->getModel();

            $resource->updateProductsWithAdditionalCostID($model->getKey());
        }
    }

    public function actionAllowed($action, AdditionalCostResource $resource)
    {
        if (in_array($action, [AdditionalCostResource::ACTION_GET_COLLECTION, AdditionalCostResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function modelIsDeletable(AdditionalCost $model)
    {
        if ($model->productItems()->count() > 0) {
            throw new ImmutableEntityException('Cannot delete additional cost which is still in use by a product item');
        }
        return true;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
                $scope->fields(['id', 'name', 'cost', 'markup', 'unit_price', 'unit_id', 'unit_abbreviation'], true);
                $scope->with(['unit']);
                break;
            case 'export-v1':
                $scope->fields(['id', 'name', 'cost', 'markup_export', 'unit_price', 'unit_abbreviation', 'created_at', 'updated_at'], true);
                $scope->filter('status', 'eq', AdditionalCostResource::STATUS_ACTIVE);
                break;
            case 'item-v1':
                $scope->fields(['id', 'name', 'cost', 'markup', 'unit_price', 'unit_id', 'unit_abbreviation'], true);
                break;
        }
    }
}
