<?php

namespace App\ResourceDelegates\Bid\Item;

use App\Resources\Bid\Item\InstallmentPaymentTermResource;
use App\Resources\Bid\Item\OneTimePaymentTermResource;
use App\Resources\Bid\Item\PaymentTermResource;
use App\Resources\Bid\ItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\BidItemPaymentTerm;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\ImmutableRelationException;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class PaymentTermDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('bid_item')->modelRelation('bidItem')->resource(ItemResource::class);
        $list->polymorphic('item')
            ->typeField('type')
            ->types(function (PolyRelation $relation) {
                $relation->type(PaymentTermResource::TYPE_ONE_TIME)->resource(OneTimePaymentTermResource::class);
                $relation->type(PaymentTermResource::TYPE_INSTALLMENT)->resource(InstallmentPaymentTermResource::class);
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('bidItemPaymentTermID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([PaymentTermResource::ACTION_CREATE, PaymentTermResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('bid_item_id')
            ->typeUuid()
            ->column('bidItemID', true)
            ->validation('Item Id', 'required|uuid|check_bid_item_id')
            ->enableAction(PaymentTermResource::ACTION_FILTER)
            ->onAction(PaymentTermResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        // both type and item_id fields are disable during updates since you aren't allowed to change types
        // this is due to lack of support for changing poly relations within the resource system and the fact
        // it isn't really necessary for our setup
        $type_map = PaymentTermResource::getTypeMap();
        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->requireColumn()
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            })
            ->disableAction([PaymentTermResource::ACTION_UPDATE, PaymentTermResource::ACTION_PARTIAL_UPDATE])
            ->enableAction(PaymentTermResource::ACTION_FILTER);

        $list->field('item_id')
            ->typeUuid()
            ->column('itemID', true)
            ->validation('Item Id', 'required|uuid|check_item_id')
            ->disableAction([PaymentTermResource::ACTION_UPDATE, PaymentTermResource::ACTION_PARTIAL_UPDATE])
            ->onAction(PaymentTermResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->polyField('item')->typeField('type')->keyField('item_id');

        $list->field('order')
            ->validation('Order', 'required|type[int]')
            ->onAction([PaymentTermResource::ACTION_CREATE, PaymentTermResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, PaymentTermResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_bid_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('bid_item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_bid_item_id';
        }, [
            'check_bid_item_id' => 'Unable to find bid item'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('type')) {
                return Rules::STOP;
            }
            $type = $validator->data('type');
            $type_resource = $resource->polyRelationResource('item', $type);

            if (!$type_resource->entityExists($id->toString())) {
                return 'check_item_id';
            }
            return true;
        }, [
            'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', PaymentTermResource::getTypes());
        return $config;
    }

    public function anyCreateValidateAfter(Entity $entity)
    {
        if ($entity->get('order') === null) {
            $entity->set('order', 1);
        }
        return $entity;
    }

    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();
        $fields = $resource->getFields();
        $model = $request->getModel();

        try {
            // delete poly relation
            $type = $fields->get('type')->outputValueFromModel($model);
            $type_resource = $resource->polyRelationResource('item', $type);
            $type_resource->delete(Entity::make([
                'id' => $fields->get('item_id')->outputValueFromModel($model)
            ]))->run();
        } catch (ImmutableEntityException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    public function queryScopeGlobal($query, PaymentTermResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function modelIsMutable(BidItemPaymentTerm $model, PaymentTermResource $resource)
    {
        return $resource->relationResource('bid_item')->isModelMutable($model->bidItem);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
            case 'detail-v1':
                $scope->fields([
                    'id', 'type', 'item_id', 'order'
                ], true);
                $scope->query(fn($query) => $query->ordered());
                $scope->with(['item']);
                break;
        }
    }
}
