<?php

namespace App\ResourceDelegates\Bid;

use App\Classes\Acl;
use App\ResourceJobs\Bid\Item\GenerateJob;
use App\ResourceMediaHandlers\Bid\{ItemHandler, ScopeOfWorkHandler};
use App\Resources\{Bid\ContentResource as BidContentResource,
    Bid\ItemResource,
    ContentTemplateResource,
    FileResource,
    Company\Form\ItemResource as CompanyFormItemResource,
    Financing\WisetackTransactionResource,
    Product\ItemResource as ProductItemResource,
    ProjectResource,
    UserResource};
use App\Resources\Bid\Item\{
    CustomDrawingResource,
    DrawingResource,
    InstallmentPaymentTerm\InstallmentResource,
    LineItemResource,
    MediaResource,
    PaymentTermResource,
    SectionResource,
    ContentResource
};
use App\Services\Email\Types\User\BidSubmittedType;
use App\Services\{ReferenceIdentifierService, TimeService};
use App\Traits\Resource\{BulkActionTrait, MutableTrait};
use App\Traits\ResourceDelegate\{TimestampFieldsTrait, TrainingActionTrait};
use Brick\Math\{BigDecimal, RoundingMode};
use Carbon\Carbon;
use Common\Models\{BidItem, BidItemContent, BidItemLineItem, ContentTemplate, CustomBid, Evaluation, Notification, TrainingAction, WisetackPrequalLink};
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\{Collection,
    Entity,
    Field,
    FieldList,
    MediaList,
    MediaType,
    RelationList,
    Request,
    Resource,
    Scope};
use Core\Components\Resource\Exceptions\{
    ImmutableEntityException,
    ImmutableRelationException,
    RequestFailedException,
    ValidationException
};
use Core\Components\Resource\Relations\CustomRelation;
use Core\Components\Resource\Requests\{CreateRequest, DeleteRequest, UpdateRequest};
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validator};
use Core\Exceptions\AppException;
use Exception;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

class ItemDelegate
{
    use BulkActionTrait;
    use MutableTrait;
    use TimestampFieldsTrait;
    use TrainingActionTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('content')->resource(ContentResource::class);
        $list->oneOrMany('created_by_user')->modelRelation('createdByUser')
            ->resource(UserResource::class);
        $list->oneOrMany('custom_drawings')->modelRelation('customDrawings')
            ->resource(CustomDrawingResource::class);
        $list->oneOrMany('drawings')->resource(DrawingResource::class);
        $list->oneOrMany('file')->resource(FileResource::class);
        $list->oneOrMany('line_items')->modelRelation('lineItems')->resource(LineItemResource::class);
        $list->custom('line_item_products')
            ->dataCallback(function (BidItem $item, Entity $entity, CustomRelation $relation) {
                $product_ids = $item->lineItems()
                    ->join('bidItemProductLineItems', 'bidItemProductLineItems.bidItemProductLineItemID', '=', 'bidItemLineItems.itemID')
                    ->where('type', BidItemLineItem::TYPE_PRODUCT)
                    ->get(['bidItemProductLineItems.productItemID'])
                    ->pluck('productItemID')
                    ->toArray();

                if (count($product_ids) === 0) {
                    $products = new Collection;
                } else {
                    $product_resource = ProductItemResource::make($relation->getParentScopeBuilder()->getResource()->acl());
                    $primary_field = $product_resource->getPrimaryField();
                    $product_ids = array_map(function ($id) use ($primary_field) {
                        return $primary_field->outputValue($id);
                    }, $product_ids);
                    $product_scope = Scope::make()->format('bid-v1')
                        ->filter('id', 'in', $product_ids);
                    $products = $product_resource->collection()->scope($product_scope)->run();
                }
                $entity->set('line_item_products', $products);
                return $entity;
            });
        $list->oneOrMany('media')->resource(MediaResource::class);
        $list->oneOrMany('payment_terms')->modelRelation('paymentTerms')->resource(PaymentTermResource::class);
        $list->oneOrMany('project')->resource(ProjectResource::class);
        $list->oneOrMany('scope_of_work_file')->modelRelation('scopeOfWorkFile')->resource(FileResource::class);
        $list->oneOrMany('sections')->resource(SectionResource::class);
        $list->oneOrMany('submitted_by_user')->modelRelation('submittedByUser')->resource(UserResource::class);
        $list->custom('templates')
            ->dataCallback(function (BidItem $item, Entity $entity, CustomRelation $relation) {
                $template_scope = Scope::make()
                    ->fields(['id', 'type', 'name', 'is_default'])
                    ->filter('type', 'in', [
                        ContentTemplateResource::TYPE_BID_COVER, ContentTemplateResource::TYPE_BID_INTRO,
                        ContentTemplateResource::TYPE_BID_SECTIONS, ContentTemplateResource::TYPE_BID_LINE_ITEMS,
                        ContentTemplateResource::TYPE_BID_TERMS_CONDITIONS, ContentTemplateResource::TYPE_BID_IMAGES,
                        ContentTemplateResource::TYPE_BID_MEDIA
                    ])
                    ->sort('name', 'asc');
                $templates = ContentTemplateResource::make($relation->getParentScopeBuilder()->getResource()->acl())
                    ->collection()
                    ->scope($template_scope)
                    ->run();
                $entity->set('templates', $templates);
                return $entity;
            });

        $list->oneOrMany('wisetack_transactions')
            ->modelRelation('wisetackTransactions')
            ->resource(WisetackTransactionResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('bidItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([ItemResource::ACTION_CREATE, ItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('reference_id')
            ->column('referenceID')
            ->immutable();

        $list->field('version')
            ->validationRules('required|type[int]|check_version')
            ->immutable()
            ->onAction([ItemResource::ACTION_NESTED_CREATE, ItemResource::ACTION_NESTED_UPDATE], function (Field $field) {
                return $field->mutable();
            })
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->mutable()->accessLevelPrivate();
            });

        $list->field('project_id')
            ->column('projectID', true)
            ->validation('Project Id', 'required|type[int]|check_project_id')
            ->onAction(ItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('follow_up_notification_status')
            ->column('followUpNotificationStatus')
            ->validation('Follow-Up Notification Status', 'required|type[int]|in_array[follow_up_statuses]');

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('status')
            ->requireColumn()
            ->validation('Status', 'required|type[int]|in_array[statuses]|check_status')
            ->disableAction([ItemResource::ACTION_CREATE, ItemResource::ACTION_NESTED_CREATE])
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $templates = [
            'cover' => [
                'label' => 'Cover',
                'column' => 'cover',
                'type' => ContentTemplateResource::TYPE_BID_COVER
            ],
            'intro' => [
                'label' => 'Intro',
                'column' => 'intro',
                'type' => ContentTemplateResource::TYPE_BID_INTRO
            ],
            'sections' => [
                'label' => 'Sections',
                'column' => 'sections',
                'type' => ContentTemplateResource::TYPE_BID_SECTIONS
            ],
            'line_items' => [
                'label' => 'Line Items',
                'column' => 'lineItems',
                'type' => ContentTemplateResource::TYPE_BID_LINE_ITEMS
            ],
            'terms_conditions' => [
                'label' => 'Terms and Conditions',
                'column' => 'termsConditions',
                'type' => ContentTemplateResource::TYPE_BID_TERMS_CONDITIONS
            ],
            'images' => [
                'label' => 'Images',
                'column' => 'images',
                'type' => ContentTemplateResource::TYPE_BID_IMAGES
            ],
            'media' => [
                'label' => 'Media',
                'column' => 'media',
                'type' => ContentTemplateResource::TYPE_BID_MEDIA
            ]
        ];
        foreach ($templates as $name => $template) {
            $list->field("{$name}_content_template_id")
                ->typeUuid()
                ->column("{$template['column']}ContentTemplateID", true)
                ->validation("{$template['label']} Content Template Id", "nullable|optional|uuid|check_content_template[{$template['type']}")
                ->disableAction(ItemResource::ACTION_CREATE)
                ->onAction(ItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                    return $field->validationRules('nullable|optional|uuid');
                });
        }

        $list->field('file_id')
            ->typeUuid()
            ->column('fileID', true)
            ->validation('File Id', 'nullable|optional|uuid')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('is_file_valid')
            ->column('isFileValid')
            ->validation('Is File Valid', 'nullable|optional|type[bool]')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('is_file_generating')
            ->column('isFileGenerating')
            ->validation('Is File Generating', 'nullable|optional|type[bool]')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('scope_of_work_file_id')
            ->typeUuid()
            ->column('scopeOfWorkFileID', true)
            ->validation('Scope Of Work File Id', 'nullable|optional|uuid')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('is_scope_of_work_file_valid')
            ->column('isScopeOfWorkFileValid')
            ->validation('Is Scope Of Work File Valid', 'nullable|optional|type[bool]')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('is_scope_of_work_file_generating')
            ->column('isScopeOfWorkFileGenerating')
            ->validation('Is Scope Of Work File Generating', 'nullable|optional|type[bool]')
            ->immutable()
            ->onAction([ItemResource::ACTION_UPDATE, ItemResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable();
            });

        $list->field('is_locked')
            ->column('isLocked')
            ->validation('Is Locked', 'required|type[bool]');

        $list->field('locked_at')
            ->typeDateTime()
            ->column('lockedAt')
            ->immutable();

        $list->field('locked_by_user_api_token_id')
            ->typeUuid()
            ->column('lockedByUserApiTokenID')
            ->immutable();

        $list->field('submitted_at')
            ->typeDateTime()
            ->column('submittedAt')
            ->immutable();

        $list->field('submitted_by_user_id')
            ->column('submittedByUserID', true)
            ->immutable();

        $list->field('submitted_by_user_name')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('user as sbu', 'sbu.userID', '=', "{$table_alias}.submittedByUserID");
            })
            ->rawColumn("IF(sbu.userID IS NOT NULL, CONCAT(sbu.userFirstName, ' ', sbu.userLastName), NULL)", 'submitted_by_user_name')
            ->label('Submitted By User Name');

        $list->field('finalized_by_user_id')
            ->column('finalizedByUserID', true)
            ->immutable();

        $list->field('finalized_by_user_name')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('user as fbu', 'fbu.userID', '=', "{$table_alias}.finalizedByUserID");
            })
            ->rawColumn("IF(fbu.userID IS NOT NULL, CONCAT(fbu.userFirstName, ' ', fbu.userLastName), NULL)", 'finalized_by_user_name')
            ->label('Finalized By User Name');


        $list->field('accepted_signature')
            ->column('acceptedSignature')
            ->validation('Accepted Signature', 'trim|nullable|required_if[is_accepted]|max_length[100]');

        $list->field('accepted_by_user_id')
            ->column('acceptedByUserID', true)
            ->immutable();

        $list->field('accepted_by_user_name')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('user as abu', 'abu.userID', '=', "{$table_alias}.acceptedByUserID");
            })
            ->rawColumn("IF(abu.userID IS NOT NULL, CONCAT(abu.userFirstName, ' ', abu.userLastName), NULL)", 'accepted_by_user_name')
            ->label('Accepted By User Name');

        $list->field('cancelled_at')
            ->typeDateTime()
            ->column('cancelledAt')
            ->immutable();

        $list->field('cancelled_by_user_id')
            ->column('cancelledByUserID', true)
            ->immutable();

        $list->field('cancelled_by_user_name')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('user as clbu', 'clbu.userID', '=', "{$table_alias}.cancelledByUserID");
            })
            ->rawColumn("IF(clbu.userID IS NOT NULL, CONCAT(clbu.userFirstName, ' ', clbu.userLastName), NULL)", 'cancelled_by_user_name')
            ->label('Cancelled By User Name');

        $list->field('follow_up_notifications_disabled_at')
            ->typeDateTime()
            ->column('followUpNotificationsDisabledAt')
            ->immutable();

        $list->field('follow_up_notifications_disabled_by_user_id')
            ->column('followUpNotificationsDisabledByUserID', true)
            ->immutable();

        $list->field('follow_up_notifications_disabled_by_user_name')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('user as fundbu', 'fundbu.userID', '=', "{$table_alias}.followUpNotificationsDisabledByUserID");
            })
            ->rawColumn("IF(fundbu.userID IS NOT NULL, CONCAT(fundbu.userFirstName, ' ', fundbu.userLastName), NULL)", 'follow_up_notifications_disabled_by_user_name')
            ->label('Follow-Up Notifications Disabled By User Name');

        $list->field('follow_up_notification_1_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun1', function ($join) use ($table_alias) {
                    $join->on('fun1.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun1.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_1)
                        ->whereNull('fun1.deletedAt');
                });
            })
            ->rawColumn('fun1.createdAt', 'follow_up_notification_1')
            ->label('Follow Up Notification 1');

        $list->field('follow_up_notification_2_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun2', function ($join) use ($table_alias) {
                    $join->on('fun2.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun2.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_2)
                        ->whereNull('fun2.deletedAt');
                });
            })
            ->rawColumn('fun2.createdAt', 'follow_up_notification_2')
            ->label('Follow Up Notification 2');

        $list->field('follow_up_notification_3_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun3', function ($join) use ($table_alias) {
                    $join->on('fun3.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun3.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_3)
                        ->whereNull('fun3.deletedAt');
                });
            })
            ->rawColumn('fun3.createdAt', 'follow_up_notification_3')
            ->label('Follow Up Notification 3');

        $list->field('follow_up_notification_4_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun4', function ($join) use ($table_alias) {
                    $join->on('fun4.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun4.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_4)
                        ->whereNull('fun4.deletedAt');
                });
            })
            ->rawColumn('fun4.createdAt', 'follow_up_notification_4')
            ->label('Follow Up Notification 4');

        $list->field('follow_up_notification_5_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun5', function ($join) use ($table_alias) {
                    $join->on('fun5.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun5.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_5)
                        ->whereNull('fun5.deletedAt');
                });
            })
            ->rawColumn('fun5.createdAt', 'follow_up_notification_5')
            ->label('Follow Up Notification 5');

        $list->field('follow_up_notification_6_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun6', function ($join) use ($table_alias) {
                    $join->on('fun6.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun6.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_6)
                        ->whereNull('fun6.deletedAt');
                });
            })
            ->rawColumn('fun6.createdAt', 'follow_up_notification_6')
            ->label('Follow Up Notification 6');

        $list->field('follow_up_notification_7_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun7', function ($join) use ($table_alias) {
                    $join->on('fun7.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun7.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_7)
                        ->whereNull('fun7.deletedAt');
                });
            })
            ->rawColumn('fun7.createdAt', 'follow_up_notification_7')
            ->label('Follow Up Notification 7');

        $list->field('follow_up_notification_8_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun8', function ($join) use ($table_alias) {
                    $join->on('fun8.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun8.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_8)
                        ->whereNull('fun8.deletedAt');
                });
            })
            ->rawColumn('fun8.createdAt', 'follow_up_notification_8')
            ->label('Follow Up Notification 8');

        $list->field('follow_up_notification_9_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun9', function ($join) use ($table_alias) {
                    $join->on('fun9.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun9.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_9)
                        ->whereNull('fun9.deletedAt');
                });
            })
            ->rawColumn('fun9.createdAt', 'follow_up_notification_9')
            ->label('Follow Up Notification 9');

        $list->field('follow_up_notification_10_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('notifications as fun10', function ($join) use ($table_alias) {
                    $join->on('fun10.itemID', '=', "{$table_alias}.bidItemID")
                        ->where('fun10.type', Notification::TYPE_BID_FOLLOW_UP_NOTIFICATION_10)
                        ->whereNull('fun10.deletedAt');
                });
            })
            ->rawColumn('fun10.createdAt', 'follow_up_notification_10')
            ->label('Follow Up Notification 10');

        $list->field('rejected_at')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                $query->leftJoin('evaluation as eval', 'eval.bidItemID', '=', "{$table_alias}.bidItemID");
                return $query->leftJoin('customBid as cb', 'cb.evaluationID', '=', "eval.evaluationID");
            })
            ->rawColumn('cb.bidRejected', 'bid_rejected')
            ->label('Rejected At');

        $list->field('customer_name')
            ->onDemand()
            ->label('Customer Name')
            ->query(function ($query) {
                $query->leftJoin('property as p', 'p.propertyID', '=', 'project.propertyID');
                $query->leftJoin('customer as c', 'c.customerID', '=', 'p.customerID');
                return $query;
            })
            ->rawColumn('CONCAT(c.firstName, \' \', c.lastName)', 'customer_name');

        $list->field('project_name')
            ->onDemand()
            ->label('Project Name')
            ->rawColumn('project.projectDescription', 'project_name');

        $list->field('description')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                $query->leftJoin('evaluation as eval', 'eval.bidItemID', '=', "{$table_alias}.bidItemID");
                return $query->leftJoin('customBid as cb', 'cb.evaluationID', '=', "eval.evaluationID");
            })
            ->rawColumn('eval.evaluationDescription', 'bid_description')
            ->label('Bid Description');

        $status_names = ItemResource::getStatusNames();
        $list->field('bid_status_name')
            ->onDemand()
            ->label('Bid Status')
            ->value(function (BidItem $item) use ($status_names) {
                return $status_names[$item->status];
            });

        $list->field('has_wisetack_transaction')
            ->onDemand()
            ->internalFilter()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('wisetackTransactions as wt', function ($join) use ($table_alias) {
                    $join->on('wt.bidItemID', '=', "{$table_alias}.bidItemID")
                        ->whereNull('wt.deletedAt');
                });
            })
            ->rawColumn('IF(wt.wisetackTransactionID IS NOT NULL, 1, 0)', 'has_wisetack_transaction')
            ->label('Has Wisetack Transaction')
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('prequalification_amount')
            ->onDemand()
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query
                    ->leftJoin('project as pp', 'pp.projectID', '=', "{$table_alias}.projectID")
                    ->leftJoin('customer as cc', 'cc.customerID', '=', "pp.customerID")
                    ->leftJoin('wisetackPrequalLinks as wpl', function ($join) use ($table_alias) {
                        $join->on('wpl.projectID', '=', "{$table_alias}.projectID")
                            ->whereColumn('wpl.customerUUID', '=', 'cc.customerUUID')
                            ->where('wpl.status', '3', WisetackPrequalLink::STATUS['PREQUALIFIED'])
                            ->whereNotNull('wpl.maxQualifiedAmount');
                    });
            })
            ->rawColumn('wpl.maxQualifiedAmount', 'prequalification_amount')
            ->label('Prequalification Amount');

        $list->field('total')
            ->label('Total')
            ->immutable()
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            });

        $list->field('finalized_at')
            ->typeDateTime()
            ->column('finalizedAt')
            ->label('Bid Finalized At')
            ->immutable();

        $list->field('accepted_at')
            ->typeDateTime()
            ->column('acceptedAt')
            ->label('Bid Accepted At')
            ->immutable();


        $list->field('has_project_financing')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('project', 'project.projectID', '=', 'bidItems.projectID');
            })
            ->rawColumn('project.isFinancingEnabled', 'is_financing_enabled')
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'total',
            'finalized_at',
            'accepted_at',
            'created_at'
        ], function (Field $field) {
            return $field->onAction(Resource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false)->mutable();
            });
        });

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('file')
            ->directoryName('bids', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(ItemHandler::class);
            });

        $list->type('scope_of_work')
            ->directoryName('scope-of-work', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(ScopeOfWorkHandler::class);
            });

        return $list;
    }

    public function validationRules(Rules $rules, ItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_version', function ($version, $param, Validator $validator) {
            if ($version < 1 || $version > ItemResource::CURRENT_VERSION) {
                return 'version_invalid';
            }
            $model = $validator->getConfig()->storage('_model');
            if ($model !== null && $version < $model->version) {
                return 'version_decrease';
            }
            return true;
        }, [
            'version_invalid' => 'Version is not valid',
            'version_decrease' => 'Version cannot be decreased'
        ]);

        $rules->register('check_project_id', function ($id) use ($resource) {
            $project_resource = $resource->relationResource('project');
            if (($project = $project_resource->find($id)) === null) {
                return 'project_not_found';
            }
            // @todo maybe check if project is mutable here
            return true;
        }, [
            'project_not_found' => 'Project not found',
            'project_immutable' => 'Project immutable{reason}'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) use ($resource) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [BidItem::STATUS_INCOMPLETE],
                BidItem::STATUS_INCOMPLETE => [
                    BidItem::STATUS_SUBMITTED, BidItem::STATUS_FINALIZED, BidItem::STATUS_CANCELLED
                ],
                BidItem::STATUS_SUBMITTED => [BidItem::STATUS_FINALIZED, BidItem::STATUS_CANCELLED],
                BidItem::STATUS_FINALIZED => [BidItem::STATUS_ACCEPTED, BidItem::STATUS_CANCELLED],
                BidItem::STATUS_ACCEPTED => [BidItem::STATUS_CANCELLED],
                // once cancelled, status cannot be changed again
                BidItem::STATUS_CANCELLED => []
            ];
            $status_permissions = [
                BidItem::STATUS_FINALIZED => function () use ($resource) {
                    $user = $resource->acl()->user();
                    return $user === null || $user->primary || $user->projectManagement || $user->bidVerification;
                }
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                if (isset($status_permissions[$status]) && !$status_permissions[$status]()) {
                    return 'status_insufficient_privileges';
                }
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_insufficient_privileges' => 'Insufficient privileges to use status',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        $rules->register('check_content_template', function (UuidInterface $id, $type) use ($resource) {
            $template = ContentTemplateResource::make($resource->acl())->find($id->toString());
            if ($template === null) {
                return 'content_template_not_found';
            }
            if ($template->type !== (int) $type) {
                return 'content_template_invalid_type';
            }
            return true;
        }, [
            'content_template_not_found' => '{label} does not exist',
            'content_template_invalid_type' => '{label} is not valid'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', ItemResource::getTypes());
        $config->store('statuses', ItemResource::getStatuses());
        $config->store('follow_up_statuses', ItemResource::getFollowUpStatuses());
        $config->store('is_accepted', function ($field, Validator $validator) {
            return $validator->data('status') === ItemResource::STATUS_ACCEPTED;
        });
        return $config;
    }

    public function anyUpdateValidateAfter(Entity $entity, UpdateRequest $request)
    {
        $model = $request->getModel();
        // we are finalizing, then we need to make sure at least one payment term is defined
        if (
            isset($entity['status']) &&
            $model->status !== $entity['status'] &&
            $entity['status'] === ItemResource::STATUS_FINALIZED &&
            $model->paymentTerms()->count() === 0
        ) {
            throw new ValidationException('At least one payment term is required to finalize bid');
        }

        return $entity;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $acl = $request->resource()->acl();

        $id_service = new ReferenceIdentifierService($acl->user()->companyID, ReferenceIdentifierService::TYPE_BID);
        $model_data['referenceID'] = $id_service->getNext();

        $model_data['status'] = ItemResource::STATUS_INCOMPLETE;
        $model_data['followUpNotificationStatus'] = ItemResource::FOLLOW_UP_NOTIFICATION_STATUS_DISABLED;

        if ($model_data['isLocked']) {
            $model_data['lockedAt'] = Carbon::now('UTC');
            $model_data['lockedByUserApiTokenID'] = $acl->userApiToken()->userApiTokenID;
        } else {
            $model_data['lockedAt'] = null;
            $model_data['lockedByUserApiTokenID'] = null;
        }
        $model_data['isFileValid'] = false;
        $model_data['isScopeOfWorkFileValid'] = false;

        return $model_data;
    }

    /**
     * Only used when creating a new bid (not a nested request) to set the defaults
     *
     * @param array $model_data
     * @param CreateRequest $request
     * @return array
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function createModelDataAfter($model_data, CreateRequest $request)
    {
        $model_data['version'] = ItemResource::CURRENT_VERSION;

        $fields = $request->resource()->getFields();
        $defaults = [
            ContentTemplate::TYPE_BID_COVER => 'cover_content_template_id',
            ContentTemplate::TYPE_BID_INTRO => 'intro_content_template_id',
            ContentTemplate::TYPE_BID_SECTIONS => 'sections_content_template_id',
            ContentTemplate::TYPE_BID_LINE_ITEMS => 'line_items_content_template_id',
            ContentTemplate::TYPE_BID_TERMS_CONDITIONS => 'terms_conditions_content_template_id',
            ContentTemplate::TYPE_BID_IMAGES => 'images_content_template_id',
            ContentTemplate::TYPE_BID_MEDIA => 'media_content_template_id'
        ];
        $types = ContentTemplateResource::make($request->resource()->acl())->getDefaultsByType(array_keys($defaults));
        foreach ($defaults as $type => $field) {
            if (!isset($types[$type])) {
                continue;
            }
            $field = $fields->get($field);
            $model_data[$field->getColumn(null, false, false)] = $field->saveValue($types[$type]);
        }
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();

        if (isset($model_data['followUpNotificationStatus']) && $model_data['followUpNotificationStatus'] !== $model->followUpNotificationStatus) {
            switch ($model_data['followUpNotificationStatus']) {
                case BidItem::FOLLOW_UP_NOTIFICATION_STATUS_DISABLED:
                    $model_data['followUpNotificationsDisabledAt'] = Carbon::now('UTC');
                    $user = $resource->acl()->user();
                    if ($user !== null) {
                        $model_data['followUpNotificationsDisabledByUserID'] = $user->getKey();
                    }
                    break;
                case BidItem::FOLLOW_UP_NOTIFICATION_STATUS_ENABLED:
                case BidItem::FOLLOW_UP_NOTIFICATION_STATUS_COMPLETED:
                    break;
            }
        }

        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case BidItem::STATUS_SUBMITTED:
                case BidItem::STATUS_FINALIZED:
                case BidItem::STATUS_ACCEPTED:
                case BidItem::STATUS_CANCELLED:
                    if ($model_data['status'] === BidItem::STATUS_SUBMITTED) {
                        $request->store('is_submitting', true);
                    }
                    if ($model_data['status'] === BidItem::STATUS_FINALIZED) {
                        $model_data['isLocked'] = false;
                        $model_data['lockedAt'] = null;
                        $model_data['lockedByUserApiTokenID'] = null;
                        $request->store('is_finalizing', true);
                    }

                    $status_tracking_fields = [
                        BidItem::STATUS_SUBMITTED => [
                            'time' => 'submittedAt',
                            'user' => 'submittedByUserID'
                        ],
                        BidItem::STATUS_FINALIZED => [
                            'time' => 'finalizedAt',
                            'user' => 'finalizedByUserID'
                        ],
                        BidItem::STATUS_ACCEPTED => [
                            'time' => 'acceptedAt',
                            'user' => 'acceptedByUserID'
                        ],
                        BidItem::STATUS_CANCELLED => [
                            'time' => 'cancelledAt',
                            'user' => 'cancelledByUserID'
                        ]
                    ];

                    $fields = $status_tracking_fields[$model_data['status']];

                    $model_data[$fields['time']] = Carbon::now('UTC');
                    $user = $resource->acl()->user();
                    if ($user !== null) {
                        $model_data[$fields['user']] = $user->getKey();
                    }
                    switch ($model_data['status']) {
                        case BidItem::STATUS_ACCEPTED:
                            $request->store('is_accepting', true);
                            if (($ip_info = $request->storage('request_ip')) === null) {
                                break;
                            }
                            $model_data['acceptedIpAddressType'] = $ip_info['type'];
                            $model_data['acceptedIpAddress'] = inet_pton($ip_info['ip']);
                            break;
                    }
                    break;
            }
        }
        if (isset($model_data['isLocked']) && $model_data['isLocked'] !== $model->isLocked) {
            if ($model_data['isLocked']) {
                $model_data['lockedAt'] = Carbon::now('UTC');
                $model_data['lockedByUserApiTokenID'] = $resource->acl()->userApiToken()->userApiTokenID;
            } else {
                $model_data['lockedAt'] = null;
                $model_data['lockedByUserApiTokenID'] = null;
            }
        }
        return $model_data;
    }

    /**
     * Creates default sections, forms, terms and conditions, only used when creating a bid (not nested)
     *
     * @param CreateRequest $request
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function createSaveAfter(CreateRequest $request)
    {
        $resource = $request->resource();
        $acl = $resource->acl();
        $model = $request->getModel();

        $item_id = $request->getFields()->primaryField()->outputValueFromModel($model);

        // if default forms exist, we create a section with those forms
        $form = CompanyFormItemResource::make($acl);
        $forms = $form->getDefaultBidForms();
        if (count($forms) > 0) {
            /** @var SectionResource $section */
            $section = $resource->relationResource('sections');
            $form_entities = [];
            $order = 0;
            foreach ($forms as $form_id) {
                $form_entities[] = [
                    'id' => Uuid::uuid4()->toString(),
                    'company_form_item_id' => $form_id,
                    'order' => ++$order
                ];
            }
            $section->create(Entity::make([
                'item_id' => $item_id,
                'name' => 'Section 1', // Give the first section a default name
                'forms' => $form_entities
            ]))
                // enable creating default section forms since this section will not be created via the section manager
                // modal system in the bid create interface
                ->store('create_default_forms', true)
                ->run();
        }

        // add any bid content which is marked default
        BidContentResource::make($acl)->createDefaultsForBidItem($item_id);

        // if project is not active, we reopen it
        /** @var ProjectResource $project_resource */
        $project_resource = $resource->relationResource('project');
        if ($project_resource->isNotActive($model->projectID)) {
            $project_resource->partialUpdate(Entity::make([
                'id' => $model->projectID,
                'status' => ProjectResource::STATUS_ACTIVE
            ]))->run();
        }

        $this->completeTrainingAction($request, TrainingAction::CREATE_BID);
    }

    /**
     * Mark legacy bid as ready for review
     *
     * @param string $item_id
     * @throws AppException
     */
    protected function submitLegacyBidForReview($item_id)
    {
        $evaluation_id = Evaluation::select('evaluationID')
            ->where('bidItemID', Uuid::fromString($item_id)->getBytes())
            ->value('evaluationID');
        if ($evaluation_id === null) {
            throw new AppException('No evaluation id found for bid');
        }

        CustomBid::where('evaluationID', $evaluation_id)->update([
            'isBidCreated' => '1'
        ]);
    }

    /**
     * Finalize legacy bid
     *
     * Copy payment term info into proper columns of custom bid and evaluation invoice tables. Mark old evaluation row
     * as finalized.
     *
     * @param Acl $acl
     * @param string $item_id
     * @param BidItem $model
     * @return int
     * @throws AppException
     */
    protected function finalizeLegacyBid(Acl $acl, $item_id, BidItem $model)
    {
        $user = $acl->user();

        $now = Carbon::now('UTC');

        $evaluation_id = Evaluation::select('evaluationID')
            ->where('bidItemID', Uuid::fromString($item_id)->getBytes())
            ->value('evaluationID');
        if ($evaluation_id === null) {
            throw new AppException('No evaluation id found for bid');
        }

        $custom_bid_update = [
            'bidNumber' => $model->referenceID,
            'bidAcceptanceName' => null,
            'bidAcceptanceAmount' => '0.00',
            'bidAcceptanceNumber' => null,
            'bidAcceptanceSplit' => '0.0',
            'projectCompleteName' => null,
            'projectCompleteAmount' => '0.00',
            'projectCompleteNumber' => null,
            'projectCompleteSplit' => '0.0',
            'bidTotal' => $model->total,
            'isBidCreated' => '1',
            'bidId' => Uuid::uuid4()->toString(),
            'bidFirstSent' => $now,
            'bidFirstSentByID' => $user->userID,
            'contractID' => null
        ];

        // update main evaluation, set finalize columns
        $evaluation_update = [
            'evaluationFinalized' => $now,
            'evaluationFinalizedByID' => $user->userID
        ];

        // copy payment terms to old evaluation tables
        $payment_terms_resource = PaymentTermResource::make($acl);
        $payment_terms_scope = Scope::make()
            ->fields(['type', 'order'])
            ->filter('bid_item_id', 'eq', $item_id)
            ->with([
                'item' => [
                    'poly_scopes' => [
                        PaymentTermResource::TYPE_ONE_TIME => [
                            'fields' => ['due_time_frame']
                        ],
                        PaymentTermResource::TYPE_INSTALLMENT => [
                            'no_fields' => true,
                            'with' => [
                                'installments' => [
                                    'fields' => ['id', 'name', 'due_time_frame', 'amount_type', 'amount', 'order'],
                                    'query' => function ($query) {
                                        return $query->ordered();
                                    }
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $payment_terms = $payment_terms_resource->collection()->scope($payment_terms_scope)->run();
        if ($payment_terms->count() > 0) {
            $payments = [];
            foreach ($payment_terms as $payment_term) {
                switch ($payment_term->type) {
                    case PaymentTermResource::TYPE_ONE_TIME:
                        $payments[] = [
                            'name' => 'One-Time Payment',
                            'percentage' => '1',
                            'due_time_frame' => $payment_term->get('item.due_time_frame'),
                            'amount' => $model->total
                        ];
                        break;
                    case PaymentTermResource::TYPE_INSTALLMENT:
                        $installments = $payment_term->get('item.installments', []);
                        // loop through all installments and get total of all non-percentage based installments
                        $total_sum = BigDecimal::of('0');
                        $last_percentage_installment = null;
                        foreach ($installments as $installment) {
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_TOTAL) {
                                $total_sum = $total_sum->plus($installment->amount);
                            } elseif ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $last_percentage_installment = $installment->id;
                            }
                        }
                        // get difference of non-percentage installments and bid total to get sum for percentage
                        // installment calculations
                        $bid_total = BigDecimal::of($model->total);
                        $percentage_base_total = $bid_total->minus($total_sum);
                        if ($percentage_base_total->isNegativeOrZero()) {
                            throw new AppException('Sum of non-percentage installments is greater than or equal to bid total');
                        }
                        $percentage_total = BigDecimal::of('0');
                        foreach ($installments as $installment) {
                            $amount = BigDecimal::of($installment->amount);
                            // if amount type is percentage, then we need to multiply the amount by the base
                            // percentage total. if this is the last percentage, then we make any adjustments to the
                            // amount due to rounding errors
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $amount = $percentage_base_total->multipliedBy($amount)->toScale(2, RoundingMode::HALF_DOWN);
                                $percentage_total = $percentage_total->plus($amount);
                                if ($installment->id === $last_percentage_installment) {
                                    $diff = $percentage_base_total->minus($percentage_total);
                                    if (!$diff->isZero()) {
                                        $amount = $amount->plus($diff);
                                    }
                                }
                            }
                            $payments[] = [
                                'name' => $installment->name,
                                'percentage' => (string) $amount->dividedBy($bid_total, 2, RoundingMode::HALF_DOWN),
                                'due_time_frame' => $installment->due_time_frame,
                                'amount' => (string) $amount->toScale(2, RoundingMode::HALF_DOWN)
                            ];
                        }
                        break;
                }
            }
            $i = $s = 0;
            $c = count($payments);
            foreach ($payments as $payment) {
                $i++;
                // if first payment and it's due at bid acceptance then we set the proper custom bid columns
                if ($i === 1 && $payment['due_time_frame'] === InstallmentResource::DUE_TIME_FRAME_AT_BID_ACCEPTANCE) {
                    $custom_bid_update['bidAcceptanceName'] = $payment['name'];
                    $custom_bid_update['bidAcceptanceAmount'] = $payment['amount'];
                    $custom_bid_update['bidAcceptanceSplit'] = $payment['percentage'];
                    continue;
                }
                // if last payment, then we set the project completion custom bid columns
                if ($i === $c) {
                    $custom_bid_update['projectCompleteName'] = $payment['name'];
                    $custom_bid_update['projectCompleteAmount'] = $payment['amount'];
                    $custom_bid_update['projectCompleteSplit'] = $payment['percentage'];
                    break;
                }
                // otherwise, insert into separate invoice table from legacy system
                DB::table('evaluationInvoice')->insert([
                    'evaluationID' => $evaluation_id,
					'invoiceName' => $payment['name'],
					'invoiceSort' => ++$s,
					'invoiceSplit' => $payment['percentage'],
					'invoiceAmount' => $payment['amount'],
					'invoiceNumber' => null
                ]);
            }
        }

        // update tables with new values
        CustomBid::where('evaluationID', $evaluation_id)->update($custom_bid_update);
        Evaluation::where('evaluationID', $evaluation_id)->update($evaluation_update);

        return $evaluation_id;
    }

    public function anyUpdateSaveBefore(UpdateRequest $request)
    {
        $model = $request->getModel();

        if ($request->storage('is_submitting', false)) {
            $resource = $request->resource();
            $item_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());
            $this->submitLegacyBidForReview($item_id);
        }

        if ($request->storage('is_finalizing', false)) {
            $resource = $request->resource();
            $item_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

            /** @var LineItemResource $line_item_resource */
            $line_item_resource = $resource->relationResource('line_items');
            $model->total = $line_item_resource->finalizeByBidItemID($item_id);

            /** @var SectionResource $section_resource */
            $section_resource = $resource->relationResource('sections');
            $section_resource->finalizeByItemID($item_id);

            $evaluation_id = $this->finalizeLegacyBid($resource->acl(), $item_id, $model);
            $request->store('evaluation_id', $evaluation_id);
        }
    }

    protected function disableBidFollowUps($project_id, UpdateRequest $request)
    {
        /** @var ItemResource $bid_item_resource */
        $bid_item_resource = $request->resource();
        $bid_item_resource->disableFollowUpsByProjectID($project_id);
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        /** @var ItemResource $resource */
        $resource = $request->resource();
        /** @var BidItem $model */
        $model = $request->getModel();

        $bid_item_id = $resource->getPrimaryField()->outputValueFromModel($model);

        // if submitting, send notification to primary and bid verification users
        if ($request->storage('is_submitting', false)) {
            BidSubmittedType::send([
                'bid_item_id' => $bid_item_id
            ]);
        }

        // if finalizing, send bid email if possible for customer
        if ($request->storage('is_finalizing', false)) {
            GenerateJob::enqueue(Uuid::fromString($bid_item_id));
        }

        // if accepting, we get the finalized pdf and fill in the signature and date fields
        if ($model->version >= 2 && $request->storage('is_accepting', false)) {
            $company = $model->project()
                ->join('property', 'property.propertyID', '=', 'project.propertyID')
                ->join('customer', 'customer.customerID', '=', 'property.customerID')
                ->join('companies', 'companies.companyID', '=', 'customer.companyID')
                ->join('timezones', 'timezones.timezoneID', '=', 'companies.timezoneID')
                ->first(['companies.companyID', 'timezones.timezone']);
            if ($company === null) {
                throw new AppException('Unable to find company info for bid');
            }
            $resource->setMediaCompanyID($company->companyID);

            $time_service = new TimeService;
            $time_service->setTimezone($company->timezone);

            $accepted_at = $time_service->getFromUtc($model->acceptedAt, true);

            $data = [
                'accepted_signature' => $model->acceptedSignature,
                'accepted_date' => $accepted_at->format('n/d/Y'),
                'accepted_time' => $accepted_at->format('g:ia')
            ];

            $content = $model->content->where('type', BidItemContent::TYPE_ACKNOWLEDGEMENT)->toArray();
            foreach ($content as $item) {
                $name = str_replace(" ", "_", strtolower($item['name'])) . '_' . $item['order'];
                $data[$name] = $item['isAccepted'] ? 'Accept' : 'Decline';
            }

            /** @var ItemHandler $file_handler */
            $file_handler = $resource->getMediaHandler('file');
            $file_handler->finalize($model, $data);

            // We need to disable bid follow ups for any other bid on this project if follow ups are enabled
            $this->disableBidFollowUps($model->projectID, $request);
        }
    }

    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();
        $fields = $resource->getFields();
        $model = $request->getModel();

        $item_id = $resource->getPrimaryField()->outputValueFromModel($model);

        try {
            /** @var ContentResource $content_resource */
            $content_resource = $resource->relationResource('content');
            $content_resource->deleteByItemID($item_id);

            /** @var CustomDrawingResource $custom_drawing_resource */
            $custom_drawing_resource = $resource->relationResource('custom_drawings');
            $custom_drawing_resource->deleteByItemID($item_id);

            /** @var DrawingResource $drawing_resource */
            $drawing_resource = $resource->relationResource('drawings');
            $drawing_resource->deleteByItemID($item_id);

            // if we have a bid file which was generated, then we delete it
            if ($model->fileID !== null) {
                $file_id = $fields->get('file_id')->outputValueFromModel($model);
                try {
                    // delete using no user since file can be generated by job which will cause deletion failures due
                    // to missing user info for global query scope
                    FileResource::make(Acl::make())->delete(Entity::make([
                        'id' => $file_id
                    ]))->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('File is immutable'), $e, [
                        'bid_item_file_id' => $file_id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete file'), $e, [
                        'bid_item_file_id' => $file_id
                    ]);
                }
            }

            /** @var LineItemResource $line_item_resource */
            $line_item_resource = $resource->relationResource('line_items');
            $line_item_resource->deleteByBidItemID($item_id, true);

            /** @var MediaResource $media_resource */
            $media_resource = $resource->relationResource('media');
            $media_resource->deleteByBidItemID($item_id);

            /** @var PaymentTermResource $payment_term_resource */
            $payment_term_resource = $resource->relationResource('payment_terms');
            $payment_term_resource->deleteByBidItemID($item_id);

            // if we have a scope of work file which was generated, then we delete it
            if ($model->scopeOfWorkFileID !== null) {
                $scope_of_work_file_id = $fields->get('scope_of_work_file_id')->outputValueFromModel($model);
                try {
                    // delete using no user since file can be generated by job which will cause deletion failures due
                    // to missing user info for global query scope
                    FileResource::make(Acl::make())->delete(Entity::make([
                        'id' => $scope_of_work_file_id
                    ]))->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Scope of work file is immutable'), $e, [
                        'bid_item_scope_of_work_file_id' => $scope_of_work_file_id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete scope of work file'), $e, [
                        'bid_item_scope_of_work_file_id' => $scope_of_work_file_id
                    ]);
                }
            }

            /** @var SectionResource $section_resource */
            $section_resource = $resource->relationResource('sections');
            $section_resource->deleteByItemID($item_id);

            // delete associated legacy evaluation
            Evaluation::query()->where('bidItemID', $model->getKey())->delete();
        } catch (ImmutableEntityException | ImmutableRelationException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    public function close(Request $request)
    {
        $this->recordCompletedTrainingActions($request);
    }

    public function queryScopeGlobal($query, ItemResource $resource)
    {
        $user = $resource->acl()->user();
        // allow nullable user so this can be used when getting the proper media handler path for a bid
        if ($user === null) {
            return $query;
        }

        // @todo need to figure out a different solution to still show media even if person doesn't have access
        // @todo this was needed for the financing dashboard to view only bids that belonged to the user
//        if (!$user->primary && !$user->projectManagement && $user->sales) {
//            return $query->ofUser($user);
//        }

        return $query->ofCompany($user->companyID);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param ItemResource $resource
     * @return bool
     */
    public function actionAllowed($action, ItemResource $resource)
    {
        if (($action & ItemResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        if (
            ($user->projectManagement || $user->sales || $user->bidCreation || $user->bidVerification || $user->installation) &&
            (ItemResource::ACTION_GROUP_DELETE & $action) === 0
        ) {
            return true;
        }
        return false;
    }

    public function modelIsUpdatable(BidItem $model)
    {
        if ($model->followUpNotificationStatus !== ItemResource::FOLLOW_UP_NOTIFICATION_STATUS_DISABLED) {
            return true;
        }
        // only editable when in incomplete or submitted status
        if (!in_array($model->status, [BidItem::STATUS_INCOMPLETE, BidItem::STATUS_SUBMITTED])) {
            throw new ImmutableEntityException('Bid status is not incomplete or submitted');
        }
        return true;
    }

    /**
     * Determine if specific model is available for deletion
     *
     * @param BidItem $model
     * @return bool
     * @throws ImmutableEntityException
     */
    public function modelIsDeletable(BidItem $model)
    {
        if (in_array($model->status, [BidItem::STATUS_FINALIZED, BidItem::STATUS_ACCEPTED])) {
            throw new ImmutableEntityException('Cannot delete finalized or accepted bids');
        }
        return true;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
                $scope->fields([
                    'id', 'project_id', 'type', 'follow_up_notification_status', 'status', 'cover_content_template_id', 'intro_content_template_id',
                    'sections_content_template_id', 'line_items_content_template_id', 'terms_conditions_content_template_id',
                    'images_content_template_id', 'media_content_template_id'
                ], true);
                $scope->with([
                    'content',
                    'custom_drawings',
                    'drawings',
                    'file_media_urls',
                    'line_items',
                    'line_item_products',
                    'media',
                    'payment_terms',
                    'project' => [
                        'fields' => ['id', 'description', 'is_financing_enabled'],
                        'with' => [
                            'property' => [
                                'fields' => ['id', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township', 'image_file_id'],
                                'with' => [
                                    'image_media_urls',
                                    'customer' => [
                                        'fields' => ['business_name', 'first_name', 'last_name', 'email', 'is_unsubscribed'],
                                        'with' => [
                                            'phones' => [
                                                'fields' => ['description', 'number', 'is_primary']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'sections',
                    'templates'
                ]);
                break;
            case 'detail-v1':
                $scope->fields([
                    'reference_id', 'follow_up_notification_status', 'status', 'total', 'created_at', 'created_by_user_name',
                    'submitted_at', 'submitted_by_user_name', 'finalized_at', 'finalized_by_user_name', 'accepted_at',
                    'accepted_by_user_name', 'cancelled_at', 'cancelled_by_user_name', 'follow_up_notifications_disabled_at',
                    'follow_up_notifications_disabled_by_user_name', 'follow_up_notification_1_at', 'follow_up_notification_2_at',
                    'follow_up_notification_3_at', 'follow_up_notification_4_at', 'follow_up_notification_5_at',
                    'follow_up_notification_6_at', 'follow_up_notification_7_at', 'follow_up_notification_8_at',
                    'follow_up_notification_9_at', 'follow_up_notification_10_at', 'rejected_at'
                ], true);
                $scope->with([
                    'sections',
                    'file_media_urls',
                    'scope_of_work_media_urls',
                    'line_items',
                    'drawings',
                    'custom_drawings',
                    'project' => [
                        'fields' => ['id', 'description', 'is_financing_enabled'],
                    ],
                ]);
                break;
            case 'export-v1':
                $scope->fields([
                    'customer_name',
                    'created_at',
                    'project_name',
                    'description',
                    'bid_status_name',
                    'total',
                    'finalized_at',
                    'accepted_at',
                    'has_wisetack_transaction',
                    'prequalification_amount'
                ], true);
                $scope->filter('status', 'in', [
                    ItemResource::STATUS_FINALIZED,
                    ItemResource::STATUS_ACCEPTED
                ]);
                $scope->filter('total', 'gt', 500);
                $scope->filter('has_wisetack_transaction', 'eq', false);
                $scope->filter('has_project_financing', 'eq', true);
                break;
            case 'report-financing-opportunities-v1':
                $scope->fields([
                    'id',
                    'total',
                    'accepted_at',
                    'status',
                    'created_at',
                    'finalized_at',
                    'description',
                    'has_wisetack_transaction',
                    'prequalification_amount'
                ]);
                $scope->filter('status', 'in', [
                    ItemResource::STATUS_FINALIZED,
                    ItemResource::STATUS_ACCEPTED
                ]);
                $scope->filter('total', 'gt', 500);
                $scope->filter('has_wisetack_transaction', 'eq', false);
                $scope->filter('has_project_financing', 'eq', true);
                $scope->with([
                    'project' => [
                        'fields' => ['id', 'description'],
                        'with' => [
                            'property' => [
                                'fields' => ['id'],
                                'with' => [
                                    'customer' => [
                                        'fields' => ['id', 'first_name', 'last_name'],
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]);
        }
    }
}
