<?php

namespace App\ResourceDelegates\Form\Item\Group;

use App\Resources\Form\Item\Group\Rule\EventResource;
use App\Resources\Form\Item\Group\RuleResource;
use App\Resources\Form\Item\GroupResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class RuleDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('group')->resource(GroupResource::class);
        $list->oneOrMany('events')->resource(EventResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemGroupRuleID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([RuleResource::ACTION_CREATE, RuleResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('group_id')
            ->typeUuid()
            ->column('formItemGroupID', true)
            ->validation('Group Id', 'required|uuid|check_group_id')
            ->onAction(RuleResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('conditions')
            ->validation('Conditions', 'required|type[array]'); // @todo validate condition structure

        $list->field('priority')
            ->validation('Priority', 'required|type[int]'); // @todo handle float if needed

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, RuleResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_group_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('group')->entityExists($id->toString())) {
                return true;
            }
            return 'check_group_id';
        }, [
            'check_group_id' => 'Unable to find group'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', RuleResource::getTypes());
        return $config;
    }

    public function queryScopeGlobal($query, RuleResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, RuleResource $resource)
    {
        if (in_array($action, [RuleResource::ACTION_GET_COLLECTION, RuleResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'type', 'conditions', 'priority'], true);
                $scope->with(['events']);
                break;
        }
    }
}
