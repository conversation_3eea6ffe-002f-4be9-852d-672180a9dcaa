<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Form\Item;

use App\Resources\Form\{Item\OverrideResource, ItemResource};
use App\Resources\Form\Item\Group\{FieldResource, TemplateResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Field, FieldList, RelationList, Scope};
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validator};
use Ramsey\Uuid\UuidInterface;

class OverrideDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('form')->resource(ItemResource::class);
        $list->polymorphic('item')
            ->types(function (PolyRelation $relation) {
                $relation->type(OverrideResource::TYPE_FIELD)->resource(FieldResource::class);
                $relation->type(OverrideResource::TYPE_TEMPLATE)->resource(TemplateResource::class);
            });
        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemOverrideID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([OverrideResource::ACTION_CREATE, OverrideResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('form_item_id')
            ->typeUuid()
            ->column('formItemID', true)
            ->validation('Form Item Id', 'required|uuid|check_form_item_id')
            ->onAction(OverrideResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            })
            ->onAction(OverrideResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $type_map = OverrideResource::getTypeMap();
        $list->field('type')
            ->column('type', true)
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            })
            ->onAction(OverrideResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('item_id')
            ->typeUuid()
            ->column('itemID', true)
            ->enableAction(OverrideResource::ACTION_FILTER)
            ->validation('Item Id', 'nullable|required_if[item_required]|uuid|check_item_id')
            ->onAction(OverrideResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('nullable|required_if[item_required]|uuid');
            });

        $list->field('label')
            ->validation('Label', 'required|type[string]|max_length[200]');

        $list->field('description')
            ->validation('Description', 'nullable|optional|type[string]');

        $list->field('config')
            ->validation('Config', 'nullable|optional|type[array]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, OverrideResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_form_item_id', function (UuidInterface $id) use ($resource) {
            if (($form_item = $resource->relationResource('form')->find($id->toString())) === null) {
                return 'form_item_not_found';
            }
            return true;
        }, [
            'form_item_not_found' => '{label} does not exist'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('type')) {
                return Rules::STOP;
            }
            $type = $validator->data('type');
            $type_resource = $resource->polyRelationResource('item', $type);

            if (!$type_resource->entityExists($id->toString())) {
                return 'check_item_id';
            }
            return true;
        }, [
            'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('types', OverrideResource::getTypes());
        $config->store('item_required', function ($field, Validator $validator) {
            return in_array($validator->data('type'), [
                OverrideResource::TYPE_FIELD, OverrideResource::TYPE_TEMPLATE
            ]);
        });
        return $config;
    }

    public function queryScopeGlobal(object $query, OverrideResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed(int $action, OverrideResource $resource): bool
    {
        if (in_array($action, [OverrideResource::ACTION_GET_COLLECTION, OverrideResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'form_item_id', 'type', 'item_id', 'label', 'description', 'config'], true);
                break;
        }
    }
}
