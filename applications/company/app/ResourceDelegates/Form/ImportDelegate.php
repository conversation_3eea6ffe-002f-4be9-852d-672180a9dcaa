<?php

namespace App\ResourceDelegates\Form;

use App\Resources\Form\ImportResource;
use App\Resources\Form\ItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class ImportDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('item')->resource(ItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('formImportID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([ImportResource::ACTION_CREATE, ImportResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('item_id')
            ->typeUuid()
            ->column('formItemID', true)
            ->validation('Item Id', 'required|uuid|check_item_id')
            ->onAction(ImportResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('data')
            ->validation('Data', 'required|type[array]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, ImportResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_item_id';
        }, [
            'check_item_id' => 'Unable to find form item'
        ]);

        return $rules;
    }

    public function queryScopeGlobal($query, ImportResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, ImportResource $resource)
    {
        if (in_array($action, [ImportResource::ACTION_GET_COLLECTION, ImportResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }
}
