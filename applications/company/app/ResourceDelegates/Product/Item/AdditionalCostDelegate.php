<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Product\Item;

use App\Resources\Bid\ContentResource;
use App\Resources\Product\ItemResource;
use App\Resources\Product\Item\AdditionalCostResource as AdditionalCostItemResource;
use App\Resources\AdditionalCostResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Core\Components\Resource\Classes\{Collection, Entity, Field, FieldList, RelationList, Scope, ScopeBuilder};
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validator};
use Ramsey\Uuid\UuidInterface;

class AdditionalCostDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('item')->resource(ItemResource::class);
        $list->oneOrMany('additional_cost')->modelRelation('additionalCost')->resource(AdditionalCostResource::class);
        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('productItemAdditionalCostID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([AdditionalCostItemResource::ACTION_CREATE, AdditionalCostItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('item_id')
            ->typeUuid()
            ->column('productItemID', true)
            ->validation('Item Id', 'required|uuid|related_entity_exists[item]')
            ->onAction(AdditionalCostItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            })
            ->onAction(AdditionalCostItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('additional_cost_id')
            ->typeUuid()
            ->column('additionalCostID', true)
            ->validation('Additional Cost Id', 'required|uuid|related_entity_exists[additional_cost]')
            ->onAction(AdditionalCostItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            })
            ->onAction(AdditionalCostItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('unit_price')
            ->column('unitPrice')
            ->validation('Unit Price', 'required|type[string]|numeric');

        $list->field('quantity')
            ->label('Line Item Quantity')
            ->validation('Quantity', 'required|type[string]|numeric');

        $list->field('total')
            ->label('Line Item Total')
            ->validation('Total', 'required|type[string]|numeric');

        $list->field('order')
            ->validation('Order', 'required|type[int]');

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([AdditionalCostItemResource::ACTION_CREATE, AdditionalCostItemResource::ACTION_NESTED_CREATE])
            ->onAction(AdditionalCostItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list);

        return $list;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', AdditionalCostItemResource::getStatuses());
        return $config;
    }

    public function validationRules(Rules $rules, AdditionalCostItemResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
                'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_item_id';
        }, [
                'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    public function anyCreateModelDataAfter($model_data)
    {
        $model_data['status'] = AdditionalCostItemResource::STATUS_ACTIVE;
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case AdditionalCostItemResource::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case AdditionalCostItemResource::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $user = $resource->acl()->user();
                    $model_data['archivedByUserID'] = $user !== null ? $user->getKey() : null;
                    break;
            }
        }
        return $model_data;
    }

    public function queryScopeGlobal(object $query, AdditionalCostItemResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed(int $action, AdditionalCostItemResource $resource): bool
    {
        if (($action & AdditionalCostItemResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
                $scope->fields(['id', 'unit_id', 'order', 'quantity', 'total', 'unit_price']);
                $scope->filter('status', 'eq', AdditionalCostItemResource::STATUS_ACTIVE);
                $scope->query(function ($query) {
                    return $query->ordered();
                });
                $scope->with(['additional_cost']);
                break;
            case 'item-v1':
            case 'item-update-v1':
                $scope->filter('status', 'eq', AdditionalCostItemResource::STATUS_ACTIVE);
                $scope->query(function ($query) {
                    return $query->ordered();
                });
                $scope->with(['additional_cost']);
                break;
        }
    }
}
