<?php

namespace App\ResourceDelegates\Product;

use App\Resources\Product\CategoryItemResource;
use App\Resources\Product\CategoryResource;
use App\Resources\Product\ItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class CategoryItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('category')->resource(CategoryResource::class);
        $list->oneOrMany('item')->resource(ItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('productCategoryItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([CategoryItemResource::ACTION_CREATE, CategoryItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('category_id')
            ->typeUuid()
            ->column('productCategoryID', true)
            ->validation('Category Id', 'required|uuid|check_category_id')
            ->onAction(CategoryItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('item_id')
            ->typeUuid()
            ->column('productItemID', true)
            ->validation('Item Id', 'required|uuid|check_item_id')
            ->onAction(CategoryItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, CategoryItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_category_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('category')->entityExists($id->toString())) {
                return true;
            }
            return 'check_category_id';
        }, [
            'check_category_id' => 'Unable to find category'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_item_id';
        }, [
            'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    protected function checkEntityExists(Entity $entity, Request $request)
    {
        $resource = $request->resource();
        $table_alias = $resource->getTableAlias();
        $count = $resource->newScopedQuery()
            ->where("{$table_alias}.productCategoryID", $entity->get('category_id')->getBytes())
            ->where("{$table_alias}.productItemID", $entity->get('item_id')->getBytes())
            ->count();
        if ($count !== 0) {
            throw new ValidationException('Entity with this category and item combination already exists');
        }
    }

    public function anyCreateValidateAfter(Entity $entity, Request $request)
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function anyUpdateValidateAfter(Entity $entity, Request $request)
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function queryScopeGlobal($query, CategoryItemResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, CategoryItemResource $resource)
    {
        if (in_array($action, [CategoryItemResource::ACTION_GET_COLLECTION, CategoryItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }
}
