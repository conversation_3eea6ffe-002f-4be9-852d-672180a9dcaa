<?php

declare(strict_types=1);

namespace App\ResourceDelegates;

use App\Resources\{File\VariantResource, FileResource};
use App\Traits\{ResourceDelegate\HandleMediaTrait, ResourceDelegate\TimestampFieldsTrait, Resource\MutableTrait};
use Core\Components\Resource\Classes\{Field, FieldList, RelationList};
use Core\Components\Resource\Requests\{CreateRequest, DeleteRequest, UpdateRequest};
use Core\Components\Validation\Classes\{FieldConfig, Rules};
use Ramsey\Uuid\UuidInterface;

class FileDelegate
{
    use HandleMediaTrait;
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('variants')->resource(VariantResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('fileID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(FileResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            })
            ->enableAction(FileResource::ACTION_FILTER);

        $list->field('status')
            ->requireColumn()
            ->validation('Status', 'required|type[int]|in_array[statuses]');

        $list->field('type')
            ->requireColumn()
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('path')
            ->hide()
            ->validation('Path', 'required|is_file');

        $list->field('name')
            ->validation('Name', 'required|max_length[150]');

        $list->field('content_type')
            ->column('contentType')
            ->immutable();

        $list->field('extension')
            ->immutable();

        $list->field('size')
            ->immutable();

        $list->field('data')
            ->immutable();

        $list->field('time')
            ->validation('Time', 'required|type[int]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, FileResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('types', FileResource::getTypes());
        $config->store('statuses', FileResource::getStatuses());
        return $config;
    }

    public function anyCreateModelDataAfter(array $model_data, CreateRequest $request): array
    {
        $model_data['companyID'] = $request->resource()->acl()->companyID();
        return $this->buildFileModelData($model_data, $request);
    }

    public function anyUpdateModelDataAfter(array $model_data, UpdateRequest $request): array
    {
        return $this->buildFileModelData($model_data, $request);
    }

    public function deleteModelDataAfter(array $model_data, DeleteRequest $request): array
    {
        if ($this->shouldDeleteData($request)) {
            $model_data['isDataDeleted'] = true;
        }
        return $model_data;
    }

    public function anyCreateSaveAfter(CreateRequest $request): void
    {
        $request->attach('rollback', function (CreateRequest $request) {
            if (($file_path = $request->storage('file_path')) !== null) {
                unlink($file_path);
            }
        });
    }

    // @todo figure out update rollbacks

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        $resource = $request->resource();

        /** @var VariantResource $variant_resource */
        $variant_resource = $resource->relationResource('variants');
        $variant_resource->invalidateByFileID($resource->getPrimaryField()->outputValueFromModel($request->getModel()));
    }

    public function deleteSaveBefore(DeleteRequest $request): void
    {
        $resource = $request->resource();

        /** @var VariantResource $variant_resource */
        $variant_resource = $resource->relationResource('variants');
        $variant_resource->deleteByFileID(
            $resource->getPrimaryField()->outputValueFromModel($request->getModel()),
            delete_data: $this->shouldDeleteData($request, default: true)
        );
    }

    public function deleteSaveAfter(DeleteRequest $request): void
    {
        $this->deleteData($request);
    }

    public function queryScopeGlobal(object $query, FileResource $resource): object
    {
        $user = $resource->acl()->user();
        // Allow null to generate public urls/paths
        return $user !== null ? $query->ofCompany($user->companyID) : $query;
    }
}
