<?php

namespace App\ResourceDelegates;

use App\Resources\LeadFormFieldResource;
use App\Resources\LeadFormResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\LeadFormField;
use Common\Models\User;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use App\Models\LeadForm;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\{Uuid, UuidInterface};

class LeadFormDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    /**
     * Build relations for LeadForm.
     */
    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('fields')
            ->modelRelation('fields')
            ->resource(LeadFormFieldResource::class);
        return $list;
    }

    /**
     * Build fields for LeadForm.
     */
    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->primary()
            ->typeUuid()
            ->column('leadFormID')
            ->validation('Id', 'required|uuid')
            ->onAction(LeadFormResource::ACTION_CREATE, function (Field $field) {
                return $field->validation('Lead Form ID', 'required|uuid');
            })
            ->label('Id');

        $list->field('token')
            ->column('token')
            ->disableAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE]);

        $list->field('company_id')
            ->column('companyID')
            ->onAction(LeadFormResource::ACTION_CREATE, function (Field $field) {
                return $field->validation('Company ID', 'required|type[int]');
            })
            ->disableAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE]);

        $list->field('title')
            ->column('title')
            ->validation('Form Title', 'required|type[string]|max_length[50]');

        $list->field('save_button_label')
            ->column('saveButtonLabel')
            ->validation('Save Button Label', 'required|type[string]|max_length[50]');

        $list->field('default_assigned_to_user_id')
            ->column('defaultAssignedToUserID')
            ->validation('Default Assigned To User ID', 'nullable|optional|type[int]')
            ->onAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->validation('Default Assigned To User ID', 'nullable|optional|type[int]|check_user_id');
            })
            ->disableAction([LeadFormResource::ACTION_CREATE]);

        $list->field('is_active')
            ->column('isActive')
            ->validation('Is Active', 'required|type[bool]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, LeadFormResource $resource)
    {

        $rules->register('check_user_id', function ($id) use ($resource) {
            if (User::query()->whereKey($id)->count() === 1) {
                return true;
            }
            return 'check_user_id';
        }, [
            'check_user_id' => 'Unable to find user'
        ]);

        return $rules;
    }


    /**
     * Handle actions before LeadForm is created.
     *
     * @param $model_data
     * @param CreateRequest $request
     * @return mixed
     */
    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }

        $model_data['token'] = Uuid::uuid4()->toString();
        $model_data['title'] = LeadFormResource::LABEL_DEFAULT_FORM_TITLE;
        $model_data['saveButtonLabel'] = LeadFormResource::LABEL_DEFAULT_SAVE_BUTTON;
        $model_data['isActive'] = true;

        return $model_data;
    }


    /**
     * Handle update logic for LeadForm.
     *
     * @param $model_data
     * @param UpdateRequest $request
     * @return mixed
     */
    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $payload = $request->getEntity();

        // if attempt is to deactivate the form, other fields don't matter.
        if (!$payload->get('is_active')) {
            $model_data['isActive'] = 0;
            return $model_data;
        }

        $model_data['isActive'] = 1;
        $model_data['title'] = $payload->get('title');
        $model_data['saveButtonLabel'] = $payload->get('save_button_label');
        $model_data['defaultAssignedToUserID'] = $payload->get('default_assigned_to_user_id');

        // Get the associated fields from the request
        $fields = $payload->get('fields', []);

        foreach ($fields as $field_name => $field_data) {
            $lead_form_field = LeadFormField::where('leadFormID', $request->getModel()->leadFormID)
                ->where('reference', $field_name)
                ->first();

            if ($lead_form_field) {
                $lead_form_field->isEnabled = $field_data['is_enabled'] ? 1 : 0;
                $lead_form_field->isRequired = $field_data['is_required'] ? 1 : 0;

                $lead_form_field->label = $field_data['label'] ?? $lead_form_field->label;
                $lead_form_field->fieldType = $field_data['field_type'] ?? $lead_form_field->fieldType;

                $lead_form_field->save();
            }
        }

        return $model_data;
    }

    public function queryScopeGlobal($query, LeadFormResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    /**
     * Determine if a specific action is allowed for the LeadForm resource.
     */
    public function actionAllowed($action, LeadFormResource $resource)
    {
        if (in_array($action, [LeadFormResource::ACTION_GET_COLLECTION, LeadFormResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    /**
     * Configure the scope for LeadForm queries.
     */
    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields(['id', 'title', 'save_button_label', 'created_at', 'updated_at']);
                break;
            case 'form-v1':
                $scope->fields(['id', 'token', 'title', 'save_button_label', 'default_assigned_to_user_id', 'is_active']);
                $scope->with([
                    'fields' => [
                        'fields' => [
                            'field_type', 'label', 'is_enabled', 'is_required', 'reference'
                        ]
                    ]
                ]);
                break;
        }
    }
}
