<?php

declare(strict_types=1);

namespace App\ResourceDelegates\System\Form;

use App\Resources\System\Form\{BidItemResource, ItemResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Entity, Field, FieldList, RelationList, Scope};
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class BidItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('item')->resource(ItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('systemFormBidItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(BidItemResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('is_bid_default')
            ->column('isBidDefault')
            ->validation('Is Bid Default', 'required|type[bool]');

        $list->field('is_section_default')
            ->column('isSectionDefault')
            ->validation('Is Section Default', 'required|type[bool]');

        $list->field('is_hidden_from_list')
            ->column('isHiddenFromList')
            ->validation('Is Hidden From List', 'required|type[bool]');

        $list->field('is_hidden_from_bid')
            ->column('isHiddenFromBid')
            ->validation('Is Hidden From Bid', 'required|type[bool]');

        $list->field('is_hidden_from_scope_of_work')
            ->column('isHiddenFromScopeOfWork')
            ->validation('Is Hidden From Scope Of Work', 'required|type[bool]');

        $list->field('is_form_name_hidden')
            ->column('isFormNameHidden')
            ->validation('Is Form Name Hidden', 'required|type[bool]');

        $list->field('default_order')
            ->column('defaultOrder')
            ->validation('Default Order', 'required|type[int]')
            ->onAction([BidItemResource::ACTION_CREATE, BidItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, BidItemResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        return $rules;
    }

    protected function checkDefaults(Entity $entity): void
    {
        if (!$entity->get('is_bid_default') || !$entity->get('is_section_default')) {
            return;
        }
        throw new ValidationException('Form cannot be a bid and section default at the same time');
    }

    public function anyCreateValidateAfter(Entity $entity): Entity
    {
        if ($entity->get('default_order') === null) {
            $entity->set('default_order', 1);
        }
        $this->checkDefaults($entity);
        return $entity;
    }

    public function anyUpdateValidateAfter(Entity $entity): Entity
    {
        $this->checkDefaults($entity);
        return $entity;
    }

    public function actionAllowed(int $action, BidItemResource $resource): bool
    {
        if (in_array($action, [BidItemResource::ACTION_GET_COLLECTION, BidItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        if ($scope->getFormat() === 'form-v1') {
            $scope->fields([
                'is_bid_default', 'is_section_default', 'is_hidden_from_list', 'is_hidden_from_bid',
                'is_hidden_from_scope_of_work', 'is_form_name_hidden', 'default_order'
            ], true);
        }
    }
}
