<?php

namespace App\ResourceDelegates;

use App\Resources\{CompanyResource,
    Customer\PhoneResource,
    CustomerResource,
    LeadResource,
    ProjectResource,
    PropertyResource,
    TaskResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use App\Traits\ResourceDelegate\TrainingActionTrait;
use Carbon\Carbon;
use Common\Models\TrainingAction;
use Core\Components\Resource\Classes\{Collection, Entity, Field, FieldList, RelationList, Request, Scope};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, ImmutableRelationException, ValidationException};
use Core\Components\Resource\Requests\{CollectionRequest, CreateRequest, DeleteRequest, UpdateRequest};
use Core\Components\Validation\Classes\{FieldConfig, Rules};
use Ramsey\Uuid\Uuid;

class CustomerDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;
    use TrainingActionTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('company')->resource(CompanyResource::class);
        $list->oneOrMany('lead')->resource(LeadResource::class);
        $list->oneOrMany('phones')->resource(PhoneResource::class);
        $list->oneOrMany('properties')->resource(PropertyResource::class);
        $list->oneOrMany('tasks')->resource(TaskResource::class);
        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('customerID')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->noSave();

        $list->field('customer_uuid')
            ->typeUuid()
            ->column('customerUUID')
            ->immutable()
            ->enableAction(CustomerResource::ACTION_FILTER);

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company ID', 'required|type[int]|check_company_id')
            ->onAction(CustomerResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('lead_id')
            ->column('leadID', true)
            ->validation('Lead ID', 'nullable|optional|type[int]|check_lead_id')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('lead_name')
            ->onDemand()
            ->label('Lead Name')
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->leftJoin('leads as cl', function ($join) use ($table_alias) {
                    $join->on('cl.leadID', '=', "{$table_alias}.leadID")
                        ->whereNull('cl.deletedAt');
                });
            })
            ->rawColumn('IF(cl.leadID IS NOT NULL, CONCAT(cl.firstName, \' \', cl.lastName), NULL)', 'lead_name')
            ->onAction(CustomerResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });


        $list->field('quickbooks_id')
            ->onDemand()
            ->label('Quickbooks ID')
            ->rawColumn('quickbooksID', 'quickbooks_id')
            ->onAction(CustomerResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('business_name')
            ->column('businessName')
            ->label('Business Name')
            ->validationRules('trim|nullable|optional|max_length[200]');

        $list->field('first_name')
            ->column('firstName')
            ->label('First Name')
            ->validationRules('trim|required|max_length[50]');

        $list->field('last_name')
            ->column('lastName')
            ->label('Last Name')
            ->validationRules('trim|required|max_length[50]');

        $list->field('email')
            ->label('Email')
            ->validationRules('trim|nullable|optional|max_length[100]|email')
            ->enableAction(CustomerResource::ACTION_FILTER);

        $list->field('primary_phone_number')
            ->onDemand()
            ->label('Primary Phone')
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->join('customerPhone as cp', function ($join) use ($table_alias) {
                    $join->on('cp.customerID', '=', "{$table_alias}.customerID")
                        ->where('cp.isPrimary', true)
                        ->whereNull('cp.deletedAt');
                });
            })
            ->rawColumn('cp.phoneNumber', 'primary_phone_number')
            ->onAction(CustomerResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('address')
            ->column('ownerAddress')
            ->label('Address')
            ->validationRules('trim|required|max_length[100]');

        $list->field('address_2')
            ->column('ownerAddress2')
            ->label('Address 2')
            ->validationRules('trim|nullable|optional|max_length[100]');

        $list->field('city')
            ->column('ownerCity')
            ->label('City')
            ->validationRules('trim|required|max_length[50]')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('state')
            ->column('ownerState')
            ->label('State')
            ->validationRules('trim|required|max_length[15]')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('zip')
            ->column('ownerZip')
            ->label('Zip')
            ->validationRules('trim|required|max_length[12]')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        // only available during creation
        $list->field('is_duplicate_match_shown')
            ->column('isDuplicateMatchShown')
            ->validation('Is Duplicate Match Shown', 'nullable|optional|type[boolean]')
            ->disable()
            ->onAction(CustomerResource::ACTION_CREATE, function (Field $field) {
                return $field->enable();
            });

        $list->field('is_unsubscribed')
            ->column('isUnsubscribed')
            ->label('Is Unsubscribed')
            ->validationRules('required|type[boolean]')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $list->field('unsubscribed_at')
            ->typeDateTime()
            ->column('unsubscribedAt')
            ->immutable();

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'business_name', 'first_name', 'last_name', 'email', 'address', 'address_2', 'city', 'state', 'zip', 'is_unsubscribed'
        ], function (Field $field) {
            return $field->enableAction(CustomerResource::ACTION_SORT);
        });

        $list->field('created_at')
            ->onAction(CustomerResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            });

        return $list;
    }

    public function configureFields(FieldList $list, CustomerResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user !== null) {
            $list->get('company_id')->immutable()->hide();
        }
        return $list;
    }

    public function requestValidationFields(FieldList $list, Request $request)
    {
        $phones = $list->field('phones');
        $rules = 'nullable|optional|type[array]|min_count[1]';
        if ($request->isAction(CustomerResource::ACTION_CREATE | CustomerResource::ACTION_NESTED_CREATE)) {
            $rules = 'required|type[array]|min_count[1]';
        }
        $phones->validation('Phones', $rules);

        return $list;
    }

    public function validationRules(Rules $rules, CustomerResource $resource)
    {
        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        $rules->register('check_lead_id', function ($id) use ($resource) {
            // set access level to private so that if the lead isn't assigned to the user updating the customer we can still look it up in the query scope global
            if ($resource->relationResource('lead')->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->entityExists($id)) {
                return true;
            }
            return 'check_lead_id';
        }, [
             'check_lead_id' => 'Unable to find lead'
        ]);

        return $rules;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }
        $model_data['customerUUID'] = Uuid::uuid4()->getBytes();

        // if this field isn't provided, we set it to false by default
        if ($model_data['isDuplicateMatchShown'] === null) {
            $model_data['isDuplicateMatchShown'] = false;
        }
        if ($model_data['isUnsubscribed']) {
            $model_data['unsubscribedAt'] = Carbon::now('UTC');
        }

        if ($model_data['leadID']) {
            $request->store('is_from_lead', true);
        }

        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        if (isset($model_data['isUnsubscribed']) && $request->getModel()->isUnsubscribed !== $model_data['isUnsubscribed']) {
            $model_data['unsubscribedAt'] = $model_data['isUnsubscribed'] ? Carbon::now('UTC') : null;
        }

        return $model_data;
    }

    /**
     * Before delete is saved, we delete any related entities using their respective resources
     *
     * @param DeleteRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Exceptions\AppException
     */
    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();

        $customer_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        try {
            /** @var PhoneResource $phone_resource */
            $phone_resource = $resource->relationResource('phones');
            $phone_resource->deleteByCustomerID($customer_id, true);

            /** @var PropertyResource $property_resource */
            $property_resource = $resource->relationResource('properties');
            $property_resource->deleteByCustomerID($customer_id);
        } catch (ImmutableEntityException | ImmutableRelationException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    protected function createNestedPhones(CreateRequest $request)
    {
        $phones = $request->getValidatedEntity()->get('phones', []);
        try {
            $customer_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            $request->resource()->relationResource('phones')
                ->batchCreate(Collection::fromArray($phones))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($customer_id) {
                    $entity->set('customer_id', $customer_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create phones - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'phones' => $errors
                ]);
            }
            throw $exception;
        }
    }

    /**
     * If customer is created from a lead we need to update the lead status to converted because it has been used for a customer
     *
     * @param $lead_id
     * @param CreateRequest $request
     */
    public function changeLeadStatus($lead_id, CreateRequest $request)
    {
        /** @var LeadResource $lead_resource */
        $lead_resource = $request->resource()->relationResource('lead');
        $lead_resource->convertLead($lead_id);
    }

    public function anyCreateSaveAfter(CreateRequest $request)
    {
        $this->createNestedPhones($request);
        $this->completeTrainingAction($request, TrainingAction::CREATE_CUSTOMER);

        if ($request->storage('is_from_lead', false)) {
            $lead_id = $request->getFields()->get('lead_id')->outputValueFromModel($request->getModel());
            $this->changeLeadStatus($lead_id, $request);
        }
    }

    protected function updateNestedPhones(UpdateRequest $request)
    {
        $phones = $request->getValidatedEntity()->get('phones', []);
        if ($phones === null || count($phones) === 0) {
            return;
        }
        try {
            $customer_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            /** @var \App\Resources\Customer\PhoneResource $phone_resource */
            $phone_resource = $request->resource()->relationResource('phones');
            $ids = $phone_resource->batchUpdateOrCreate(Collection::fromArray($phones))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($customer_id) {
                    $entity->set('customer_id', $customer_id);
                    return $entity;
                })
                ->attach('request', function ($request) {
                    // since update or create requests are proxies you have to apply your request specific changes via
                    // another hook
                    $request->attach('request', function ($request) {
                        $request->attach('validation_field_config', function (FieldConfig $config) {
                            $config->store('check_number', false);
                            $config->store('check_primary', false);
                            return $config;
                        });
                        return $request;
                    });
                    return $request;
                })
                ->run();
            $phone_resource->deleteMissingPhonesByCustomerID($customer_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update phones - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'phones' => $errors
                ]);
            }
            throw $exception;
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        $this->updateNestedPhones($request);
    }

    public function deleteSaveAfter(DeleteRequest $request)
    {
        $resource = $request->resource();
        $customer_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        ProjectResource::make($resource->acl())->detachCustomer($customer_id);
        $resource->relationResource('tasks')->detachAssociation($customer_id);
    }

    public function close(Request $request)
    {
        $this->recordCompletedTrainingActions($request);
    }

    public function queryScopeGlobal($query, CustomerResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param CustomerResource $resource
     * @return bool
     */
    public function actionAllowed($action, CustomerResource $resource)
    {
        if (($action & CustomerResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        // if user is project management or sales, then we only allow them to do non-delete actions
        if (($user->projectManagement || $user->sales) && ($action & CustomerResource::ACTION_GROUP_DELETE) === 0) {
            return true;
        }
        return false;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && in_array($scope->getFormat(), ['collection-v1', 'list-v1', 'task-v1', 'export-v1'])) {
            $resource = $request->resource();
            $acl = $resource->acl();
            $term = $scope->getSearch();
            $customer_phone_query = PhoneResource::make($acl)->newScopedQuery()
                // customer already joined via scoped query
                ->search($term);
            $request->unionQuery($customer_phone_query);

            $property_query = PropertyResource::make($acl)->newScopedQuery()
                // customer is already joined in via scoped query
                ->search($term);
            $request->unionQuery($property_query);
            $request->enableChunking()->chunkLimit(300);
        }
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getformat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'lead_id', 'business_name', 'first_name', 'last_name', 'email', 'primary_phone_number',
                    'address', 'address_2', 'city', 'state', 'zip', 'created_at', 'created_by_user_name', 'updated_at',
                    'updated_by_user_name', 'is_unsubscribed', 'lead_name'
                ])
                ->with([
                    'lead'
                ]);
                break;
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                $scope->fields(['id', 'business_name', 'first_name', 'last_name'], true);
                $scope->query(function ($query) {
                    return $query->withTrashed();
                });
                break;
            case 'export-v1':
                $scope->fields([
                    'business_name', 'first_name', 'last_name', 'email', 'primary_phone_number', 'address', 'address_2', 'city',
                    'state', 'zip', 'is_unsubscribed', 'created_at', 'created_by_user_name'
                ], true);
                break;
            case 'list-v1':
                $scope->fields(['id', 'customer_uuid', 'business_name', 'first_name', 'last_name'], true);
                break;
            case 'task-v1':
                $scope->fields([
                    'id', 'business_name', 'first_name', 'last_name', 'email', 'primary_phone_number', 'address',
                    'address_2', 'city', 'state', 'zip', 'created_at', 'created_by_user_name'
                ], true);
                break;
        }
    }
}
