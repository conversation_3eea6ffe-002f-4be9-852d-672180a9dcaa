<?php

use App\Http\Controllers\App\API\LeadFormController;
use Core\Components\Http\StaticAccessors\Route;

Route::get('feed/calendar/{id}.ics')->to(App\Http\Controllers\FeedController::class, 'calendar')
    ->bind('id', '[a-f0-9\-]{36}')
    ->name('feed.calendar');

Route::group(function () {
    $app_controller = App\Http\Controllers\AppController::class;
    Route::get('/')->to($app_controller, 'home')->name('home');
    Route::get('index.php')->to($app_controller, 'home');
    Route::get('bids/create/{id}')->to(App\Http\Controllers\App\BidController::class, 'create')
        ->name('bids.create');
    Route::get('company/profile{path}')->to($app_controller, 'companyProfile')->bind('path', '.*')
        ->name('company.profile');
    Route::get('crew-management.php')->to($app_controller, 'crewManagement')->name('crew-management');
    Route::get('company/account{path}')->to(App\Http\Controllers\AppController::class, 'account')->bind('path', '.*')
        ->name('company.account');
    Route::get('custom-reports{path}')->to(App\Http\Controllers\AppController::class, 'customReports')->bind('path', '.*')
        ->name('custom-reports');
    Route::get('customers')->to($app_controller, 'customers')->name('customers');
    Route::get('customer-add.php')->to($app_controller, 'customerAdd')->name('customer-add');
    Route::get('customer-management.php')->to($app_controller, 'customerManagement')->name('customer-management');
    Route::get('drawings{path}')->to($app_controller, 'drawings')->bind('path', '.*')->name('drawings');
    Route::get('financing{path}')->to($app_controller, 'financing')->bind('path', '.*')->name('financing');
    Route::get('leads{path}')->to($app_controller, 'leads')->bind('path', '.*')->name('leads');
    Route::get('marketing')->to($app_controller, 'marketing')->name('marketing');
    Route::get('marketplace{path}')->to($app_controller, 'marketplace')->bind('path', '.*')->name('marketplace');
    Route::get('media-library')->to(App\Http\Controllers\App\MediaLibraryController::class, 'viewAll')
        ->name('media-library');
    Route::get('metrics')->to($app_controller, 'metrics')->name('metrics');
    Route::get('notifications{path}')->to($app_controller, 'notification')->bind('path', '.*')->name('notification');
    Route::get('properties')->to($app_controller, 'properties')->name('properties');
    Route::get('project-management.php')->to($app_controller, 'projectLegacy');
    Route::get('projects{path}')->to($app_controller, 'projects')->bind('path', '.*')->name('projects');
    Route::get('training{path}')->to($app_controller, 'training')->bind('path', '.*')->name('training');
    Route::get('setup-wizard{path}')->to($app_controller, 'setupWizard')->bind('path', '.*')->name('setup-wizard');
    Route::get('signup')->to($app_controller, 'signup')->name('signup');
    Route::get('tasks{path}')->to($app_controller, 'tasks')->bind('path', '.*')->name('tasks');
    $user_agreement_controller = App\Http\Controllers\App\UserAgreementController::class;
    Route::get('user-agreement')->to($user_agreement_controller, 'index')->name('user-agreement');
    Route::get('user-agreement/download')->to($user_agreement_controller, 'download')
        ->name('user-agreement.download');
    Route::get('user/profile{path}')->to($app_controller, 'userProfile')->bind('path', '.*')->name('user.profile');
    Route::get('auth/set-password')->to(App\Http\Controllers\AuthController::class, 'handle')->name('set-password');
    Route::post('auth/set-password')->to(App\Http\Controllers\Auth\SetPasswordController::class, 'process')
        ->name('set-password-process');
    Route::get('auth/set-user-details')->to(App\Http\Controllers\AuthController::class, 'handle')->name('set-user-details');
    Route::post('auth/set-user-details')->to(App\Http\Controllers\Auth\SetPasswordController::class, 'processInvitedUser')
        ->name('set-user-details-process');
    Route::get('demo/start')->to(App\Http\Controllers\App\ModeController::class, 'demoStart');
    Route::get('demo/end')->to(App\Http\Controllers\App\ModeController::class, 'demoEnd');
    Route::get('conference/start')->to(App\Http\Controllers\App\ModeController::class, 'conferenceStart');
    Route::get('conference/end')->to(App\Http\Controllers\App\ModeController::class, 'conferenceEnd');
})->name('page.app.')->middlewareGroup('app');

// public application routes
$auth_controller = App\Http\Controllers\AuthController::class;
Route::group(function () use ($auth_controller) {
    Route::post('auth/login')->to($auth_controller, 'handleLogin');
    Route::get('auth/access-token/{token}')->to($auth_controller, 'accessToken')->name('page.auth.access-token')
        ->bind('token', '[a-fA-F0-9]{64}');
    Route::post('auth/forgot-password')->to(App\Http\Controllers\Auth\ForgotPasswordController::class, 'process');
})->middlewareGroup('web');

Route::group(function () use ($auth_controller) {
    Route::get('auth/login')->to($auth_controller, 'handle')->name('page.auth.login');

    // auth forgot password
    Route::get('auth/forgot-password')->to($auth_controller, 'handle')
        ->name('page.auth.forgot-password');

    // auth reset password
    Route::get('auth/reset-password/{id}')->to($auth_controller, 'handle')
        ->name('page.auth.reset-password')
        ->bind('id', '[a-f0-9\-]{36}');

    // sign-up
    Route::post('sign-up')->to(App\Http\Controllers\SignUpController::class, 'handle');
    Route::get('registration/finalize')->to(App\Http\Controllers\SignUpController::class, 'finalize');
})->middlewareGroup('app-public');

Route::get('auth/transfer')->to(App\Http\Controllers\AuthController::class, 'transfer')->name('page.auth.transfer');

Route::get('auth/ping')->to($auth_controller, 'ping')->name('page.auth.ping');

// route logout only through domain and auth middleware to avoid redirects
Route::group(function () {
    Route::get('auth/logout')->to(App\Http\Controllers\AuthController::class, 'logout')->name('page.auth.logout');
})->middleware('domain')->middleware('auth');

Route::get('open-image.php')->to(App\Http\Controllers\EmailController::class, 'image')->name('page.open-image');

// route public pages with appropriate middleware
$public_pages = [
    'bid-accept' => 'bid-accept.php',
    'bid-accept-email' => 'bid-accept-email.php',
    'bid-reject' => 'bid-reject.php',
    'bid-reject-email' => 'bid-reject-email.php'
];
Route::group(function () use ($public_pages) {
    foreach ($public_pages as $name => $page) {
        Route::any($page)
            ->to(App\Http\Controllers\MainController::class, 'page')
            ->name($name);
    }
    Route::get('email/unsubscribe')->to(App\Http\Controllers\EmailController::class, 'unsubscribe')
        ->name('email.unsubscribe');

    Route::get('website-leads-form/{id}')->to(LeadFormController::class, 'render')
        ->name('lead-form.render');

    Route::get('view-bid.php')->to(App\Http\Controllers\AppController::class, 'viewBid')->name('view-bid');
})->name('page.')->middlewareGroup('web');

// route google webhook without any middleware
// @todo remove in future after all watches start using new route
Route::any('google-event-watch.php')->to(App\Http\Controllers\App\API\Integrations\GoogleCalendarController::class, 'eventWebhook');

// route any other call with application middleware
Route::any('{path}')->to(App\Http\Controllers\MainController::class, 'page')->bind('path', '.*')
    ->middlewareGroup('app');
