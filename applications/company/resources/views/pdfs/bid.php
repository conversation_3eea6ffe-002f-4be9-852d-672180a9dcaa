<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
<?php foreach ($style_embeds as $type => $paths): foreach ($paths as $path): ?>
        <?=$this->asset->embed($type, $path, false)?>
<?php endforeach; endforeach; ?>
        <?=$styles?>
    </style>
</head>
<body>
    <div class="page <?=$expandable ? 'expandable' : 'static'?>">
        <?=$content?>
    </div>
<?php if (isset($loaded_callback)): ?>
    <script>window.onload = function () {window.parent.<?=$loaded_callback?>();}</script>
<?php endif; ?>
</body>
</html>