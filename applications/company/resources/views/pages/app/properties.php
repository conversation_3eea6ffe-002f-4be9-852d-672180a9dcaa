<?php

$this->extend('layout.app');

$asset = $this->asset;
$manifest = $asset->manifest('module:property');

$manifest->style("css/{$this->domain->brand()->slug}");

$asset->scriptData('property_data', $script_data);
//
//$asset->scriptData('property_data', [
//    'export' => $can_export,
//    'delete' => $can_delete
//]);

$asset->scriptData('fx_pages', [
    'CUSTOMER_MANAGEMENT' => $this->uri->routeRaw('page.app.customer-management')->build() . '?cid={customer_id}',
    'CUSTOMERS' => $this->uri->routeRaw('page.app.customers')->build() . '?id={customer_id}',
    'PROJECTS' => $this->uri->routeRaw('page.app.projects', ['path' => ''])->build() . '?property_id={property_id}'
]);

$manifest->script('vendor');
$manifest->script('module');
$manifest->svg('sprite');
