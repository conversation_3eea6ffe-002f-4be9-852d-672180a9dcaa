<?php

$this->extend('layout.app');

$asset = $this->asset;
$manifest = $asset->manifest('module:customer');

$manifest->style("css/{$this->domain->brand()->slug}");

$asset->scriptData('customer_data', $script_data);

//$asset->scriptData('customer_data', [
//    'add' => $can_add,
//    'export' => $can_export,
//    'delete' => $can_delete
//]);

$asset->scriptData('fx_pages', [
    'CUSTOMER_ADD' => $this->uri->routeRaw('page.app.customer-add')->build(),
    'CUSTOMER_MANAGEMENT' => $this->uri->routeRaw('page.app.customer-management')->build() . '?cid={customer_id}',
    'LEAD' => $this->uri->routeRaw('page.app.leads', ['path' => '/details'])->build() . '/{lead_id}',
    'PROPERTIES' => $this->uri->routeRaw('page.app.properties')->build() . '?customer_id={customer_id}'
]);

$manifest->script('vendor');
$manifest->script('module');
$manifest->svg('sprite');
