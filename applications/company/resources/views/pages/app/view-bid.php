<?php

$this->extend('layout.base');

$this->asset->captureStyle([
    'priority' => 10.5
]); ?>
.button {
    background-color: <?=$companyColor?>;
    color: <?=$company_color_button_color?>;
}

.button:hover, .button:focus {
    background-color: <?=$companyColorHover?>;
    color: <?=$company_color_hover_button_color?>;
}

.button.secondary {
    color: <?=$companyColor?>;
    border: 1px solid <?=$companyColor?>;
    background-color: #ffffff;
}

.button.secondary:hover, .button.secondary:focus {
    color: <?=$companyColorHover?>;
}

a {
    color: <?=$companyColor?>;
}

a:hover, a:focus {
    color: <?=$companyColorHover?>;
}
<?php

$this->asset->endCaptureStyle();
$this->asset->style('view-bid');
$this->asset->scriptData('bid_info', [
    'id' => $bidID,
    'first_name' => $firstName,
    'last_name' => $lastName,
    'has_contract' => $contractID !== null
]);
$this->asset->script('view-bid');

?>
<div id="loading-image" class="loadingImage">
    <img src="<?=$this->asset->uri('image', 'ajax-loader.gif')?>" />
</div>
<?=$viewBidContentDisplay?>
