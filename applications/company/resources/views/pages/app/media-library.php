<?php

$this->extend('layout.app');

$this->asset->style('media-library');
$this->asset->script('media-library');

?>
<div class="row expanded" style="margin-bottom: 4rem;">
    <div class="medium-12 columns no-pad">
        <div class="p-media-library">
            <table class="e-ml-table">
                <tr class="e-mlt-row t-header">
                    <th class="e-mltr-header t-desc-control">&nbsp;</th>
                    <th class="e-mltr-header t-title">Title</th>
                    <th class="e-mltr-header t-file-size">File Size</th>
                    <th class="e-mltr-header t-action">Action</th>
                </tr>
<?php if (count($items) === 0): ?>
                <tr>
                    <td colspan="4"><em>No items found</em></td>
                </tr>
<?php else: foreach ($items as $item): ?>
                <tr class="e-mlt-row<?php if ($item['description'] === null): ?> t-no-desc<?php endif; ?>">
                    <td class="e-mltr-data t-desc-control">
<?php if ($item['description'] !== null): ?>
                        <span class="e-mltrd-icon icon-circle-down"></span>
<?php endif; ?>
                    </td>
                    <td class="e-mltr-data t-title">
                        <span class="e-mltrd-icon <?=$item['icon']?>"></span>
                        <?=$this->e($item['name'])?>
                    </td>
                    <td class="e-mltr-data t-file-size"><?=$item['file_size']?></td>
                    <td class="e-mltr-data t-action">
<?php if (isset($item['view_link'])): ?>
                        <a class="button small" href="<?=$item['view_link']?>" target="_blank">Download</a>
<?php endif; ?>
                    </td>
                </tr>
<?php if ($item['description'] !== null): ?>
                <tr class="e-mlt-row t-desc">
                    <td colspan="4" class="e-mltr-data t-desc">
                        <div class="e-mltrd-wrapper"><?=nl2br($this->e(trim($item['description'])))?></div>
                    </td>
                </tr>
<?php endif; endforeach; endif; ?>
            </table>
        </div>
    </div>
</div>
