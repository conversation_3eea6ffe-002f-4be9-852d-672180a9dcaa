'use strict';

import 'remixicon/icons/System/information-line.svg';
import 'remixicon/icons/System/checkbox-circle-line.svg';
import 'remixicon/icons/System/alert-line.svg';
import 'remixicon/icons/System/error-warning-line.svg';

import {Message as BaseMessage} from '@ca-package/notification';

import {Types} from './constants';

import message_tpl from '@cas-notification-toast-tpl/message.hbs';

const TypeClassMap = {
    [Types.INFO]: {
        title: 'Info',
        class: 't-info',
        icon: 'system--information-line'
    },
    [Types.SUCCESS]: {
        title: 'Success',
        class: 't-success',
        icon: 'system--checkbox-circle-line'
    },
    [Types.WARNING]: {
        title: 'Warning',
        class: 't-warning',
        icon: 'system--alert-line'
    },
    [Types.ERROR]: {
        title: 'Error',
        class: 't-error',
        icon: 'system--error-warning-line'
    },
};

/**
 * @memberof module:NotificationToast
 */
export class Message extends BaseMessage {
    /**
     * Constructor
     *
     * @param {string} message
     * @param {object} [config={}]
     */
    constructor(message, config = {}) {
        super(message, config);

        Object.assign(this.state, {
            type: null,
            delete_time: 8
        });
        this.configure(config);
    };

    /**
     * Configure message from passed config
     *
     * @param {object} config
     * @param {string} config.type
     */
    configure(config) {
        super.configure(config);
        this.state.type = config.type ?? null;
        if (this.state.type === null) {
            throw new Error('No type defined');
        }
    };

    /**
     * Render message
     *
     * @returns {string}
     */
    render() {
        return message_tpl({
            id: this.id,
            title: TypeClassMap[this.state.type].title,
            type_class: TypeClassMap[this.state.type].class,
            icon: TypeClassMap[this.state.type].icon,
            text: this.state.message,
            actions: Array.from(this.state.actions.values()).map(action => action.render())
        });
    };
}
