'use strict';

import {Types} from './constants';
import {createMessage} from './factory';
import {createCloseAction} from './action/icon/close';

/**
 * Factory to get info message
 *
 * @param {string} message
 * @param {object} [config={}]
 * @returns {module:NotificationToast.Message}
 */
export function createInfoMessage(message, config = {}) {
    return createMessage(message, Types.INFO, config).action(createCloseAction());
}
