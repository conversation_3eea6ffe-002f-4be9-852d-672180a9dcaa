@use '~@cac-sass/base';
@use '~@simonwep/pickr/src/scss/themes/nano';

@mixin pickr() {
    .pcr-button {
        display: block;
        width: base.$form-input-height;
        height: base.$form-input-height;
        border: base.unit-rem-calc(1px) base.$color-medium-grey solid;
        border-radius: base.$prop-border-radius;
        overflow: hidden;
        &::before,
        &::after {
            border-radius: 0;
        }
    }
}

.f-field {
    .f-f-color-picker {
        @include pickr;
    }
}
