@use '~@cac-sass/base';

.f-field {
    .f-f-password {
        position: relative;
        > .f-f-input {
            padding-right: base.unit-rem-calc(30px);
        }
    }
        .f-fp-view {
            display: block;
            position: absolute;
            top: 0;
            right: base.unit-rem-calc(2px);
            width: base.unit-rem-calc(30px);
            height: base.unit-rem-calc(30px);
            cursor: pointer;
        }
            .f-fpv-icon {
                @include base.svg-icon-center('view', 30px, 30px, 20px);
            }
        .f-fp-strength {
            display: none;
            &[data-score="0"] {
                .f-fpsm-bar {
                    width: 20%;
                    background-color: #8b0000;
                }
            }
            &[data-score="1"] {
                .f-fpsm-bar {
                    width: 40%;
                    background-color: #ff4500;
                }
            }
            &[data-score="2"] {
                .f-fpsm-bar {
                    width: 60%;
                    background-color: orange;
                }
            }
            &[data-score="3"] {
                .f-fpsm-bar {
                    width: 80%;
                    background-color: #9acd32;
                }
            }
            &[data-score="4"] {
                .f-fpsm-bar {
                    width: 100%;
                    background-color: green;
                }
            }
        }
            .f-fps-meter {
                position: relative;
                background-color: base.$color-light-grey;
                border-radius: base.$prop-border-radius;
                height: base.unit-rem-calc(4px);
                margin: base.unit-rem-calc(6px) 0 0;
                &::before,
                &::after {
                    display: block;
                    position: absolute;
                    width: calc(20% + #{base.unit-rem-calc(6px)});
                    height: inherit;
                    background: transparent;
                    border-color: base.$color-white-default;
                    border-style: solid;
                    border-width: 0 base.unit-rem-calc(5px);
                    content: '';
                    z-index: 10;
                }
                &::before {
                    left: calc(20% - #{base.unit-rem-calc(4px)});
                }
                &::after {
                    right: calc(20% - #{base.unit-rem-calc(4px)});
                }
            }
                .f-fpsm-bar {
                    position: absolute;
                    width: 0;
                    height: inherit;
                    border-radius: inherit;
                    transition: width .5s ease-in-out, background .25s;
                }
            .f-fps-info {
                margin: base.unit-rem-calc(10px) 0 0;
                font-size: base.unit-rem-calc(12px);
            }
                .f-fpsi-message {
                    margin-top: base.unit-rem-calc(6px);
                    line-height: 1.2;
                    &:first-child {
                        margin-top: 0;
                    }
                }
}
