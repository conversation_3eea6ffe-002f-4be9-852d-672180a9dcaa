'use strict';

require('remixicon/icons/System/add-circle-line.svg');
require('remixicon/icons/System/delete-bin-2-line.svg');

const autosize = require('autosize');

const FormInput = require('./index');

const Tooltip = require('@ca-submodule/tooltip');

const input_tpl = require('@cas-form-input-tpl/hidden_textarea.hbs');

/**
 * @memberof module:FormInput
 */
class HiddenTextarea extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     * @param {object} [config={}]
     */
    constructor(element, config = {}) {
        super(element);
        Object.assign(this.state, {
            opened: false
        });
        if (config.label === undefined || config.label === null) {
            throw new Error('Label is required for hidden textarea element');
        }

        autosize(element[0]);
        element.fxEvent('change', () => {
            autosize.update(element);
        });

        this.elem.root = $(input_tpl({
            label: config.label,
            show_info: config.info === undefined ? false : config.info,
            info: config.info,
            show_optional: config.show_optional === undefined ? true : config.show_optional
        }));
        this.elem.field = this.elem.root.fxFind('field');
        this.elem.add = this.elem.root.fxFind('add');
        this.elem.remove = this.elem.root.fxFind('remove');
        element.after(this.elem.root);
        this.elem.field.append(element);
        Tooltip.initAll(this.elem.root);

        this.elem.add.fxClick(() => {
            if (typeof config?.onAdd === 'function') {
                config.onAdd();
            }
            this.toggle()
        }, true);
        this.elem.remove.fxClick(() => {
            if (typeof config?.onRemove === 'function') {
                config.onRemove();
            }
            this.toggle()
        }, true);
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'hidden-textarea';
    };

    /**
     * Get if element is checked
     *
     * @returns {boolean}
     */
    get opened() {
        return this.state.opened;
    };

    /**
     * Toggle hidden textarea
     */
    toggle() {
        if (this.state.opened) {
            this.elem.root.removeClass('t-open');
            this.state.opened = false;
            return;
        }
        this.elem.root.addClass('t-open');
        this.state.opened = true;
    };

    /**
     * Destroy instance, reset UI back to default and unbind events
     */
    destroy() {
        this.elem.root.remove();
        this.elem.main.off('change.fx');
        super.destroy();
    };
}

module.exports = HiddenTextarea;
