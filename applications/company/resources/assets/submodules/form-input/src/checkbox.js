'use strict';

const FormInput = require('./index');

require('@cac-icon/input-radio-unchecked.svg');
require('@cac-icon/input-radio-checked.svg');
require('@cac-icon/input-radio-unchecked-disabled.svg');
require('@cac-icon/input-radio-checked-disabled.svg');

const input_tpl = require('@cas-form-input-tpl/checkbox.hbs');

/**
 * @memberof module:FormInput
 */
class Checkbox extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     */
    constructor(element) {
        super(element);
        Object.assign(this.state, {
            checked: element.is(':checked'),
            disabled: element.is(':disabled'),
            icon_unchecked: 'icon--input-radio-unchecked',
            icon_checked: 'icon--input-radio-checked',
            icon_unchecked_disabled: 'icon--input-radio-unchecked-disabled',
            icon_checked_disabled: 'icon--input-radio-checked-disabled',
        });

        this.elem.root = $(input_tpl({
            icon: this.getIcon()
        }));
        this.elem.icon = this.elem.root.find('use');
        element.after(this.elem.root);
        element.on('change.fx', () => {
            if (this.state.disabled) {
                return;
            }
            this.setChecked(element.is(':checked'), false);
        });
        this.elem.root.fxClick(() => this.toggle(), true);

        this.hideMainElem();
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'checkbox';
    };

    /**
     * Get if element is checked
     *
     * @returns {boolean}
     */
    get checked() {
        return this.state.checked;
    };

    /**
     * Get if element is disabled
     *
     * @returns {boolean}
     */
    get disabled() {
        return this.state.disabled;
    };

    /**
     * Get correct icon
     *
     * Options are checked, checked disabled, unchecked, unchecked disabled
     *
     * @returns {string}
     */
    getIcon () {
        let icon = '';
        if (this.state.checked) {
            icon = this.state.disabled ? this.state.icon_checked_disabled : this.state.icon_checked;
        } else {
            icon = this.state.disabled ? this.state.icon_unchecked_disabled : this.state.icon_unchecked;
        }
        return icon;
    }

    /**
     * Set checked status
     *
     * @param {boolean} checked
     * @param {boolean} [update_elem=true]
     */
    setChecked(checked, update_elem = true) {
        this.state.checked = checked;
        this.elem.icon[0].setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#${this.getIcon()}`);
        if (update_elem) {
            this.elem.main.prop('checked', this.state.checked);
        }
    };

    /**
     * Set disabled status
     *
     * @param {boolean} disabled
     * @param {boolean} [update_elem=true]
     */
    setDisabled(disabled, update_elem = true) {
        this.state.disabled = disabled;
        this.elem.icon[0].setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#${this.getIcon()}`);
        if (update_elem) {
            this.elem.main.prop('disabled', this.state.disabled);
        }
    };

    /**
     * Toggle checked status
     */
    toggle() {
        this.elem.main.prop('checked', !this.state.checked).trigger('change.fx');
    };

    /**
     * Destroy instance, reset UI back to default and unbind events
     */
    destroy() {
        this.elem.root.remove();
        this.elem.main.off('change.fx');
        super.destroy();
    };
}

module.exports = Checkbox;
