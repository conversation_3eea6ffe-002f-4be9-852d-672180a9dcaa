'use strict';

const Inputmask = require('inputmask');

const debounce = require('@cac-js/utils/debounce');

const FormInput = require('./index');

const input_tpl = require('@cas-form-input-tpl/number.hbs');

const Types = {
    FLOAT: 1,
    INT: 2,
    CURRENCY: 3,
    PERCENTAGE: 4,
    FEET: 5,
    INCHES: 6,
    NEGATIVE_CURRENCY: 7,
    NEGATIVE_PERCENTAGE: 8
};
const TypeConfig = {
    [Types.FLOAT]: {
        mask: {}
    },
    [Types.INT]: {
        mask: {
            digits: 0
        }
    },
    [Types.CURRENCY]: {
        class: 't-currency',
        mask: {
            digits: 2
        }
    },
    [Types.PERCENTAGE]: {
        class: 't-percentage',
        mask: {
            digits: 4
        }
    },
    [Types.FEET]: {
        class: 't-feet',
        mask: {
            digits: 0
        }
    },
    [Types.INCHES]: {
        class: 't-inches',
        mask: {
            digits: 0
        }
    },
    [Types.NEGATIVE_CURRENCY]: {
        class: 't-negative-currency',
        mask: {
            digits: 2
        }
    },
    [Types.NEGATIVE_PERCENTAGE]: {
        class: 't-negative-percentage',
        mask: {
            digits: 4
        }
    },
};

/**
 * @memberof module:FormInput
 */
class Number extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     * @param {object} [config={}]
     */
    constructor(element, config = {}) {
        super(element);
        if (!element.is('input')) {
            throw new Error('Number input only works with input elements');
        }
        Object.assign(this.state, {
            right_align: config.right_align || false,
            allow_minus: config.allow_minus || false,
            allow_plus: config.allow_plus || false,
            max_value: config.max_value !== undefined ? config.max_value : null,
            mask_config: config.mask_config || null,
            type_mask_configs: {},
            type: null,
            validation: typeof config.validation === 'function' ? config.validation : null,
            value: element.val(),
            disabled: element.is(':disabled'),
            warning: false
        });

        this.elem.root = $(input_tpl());
        element.after(this.elem.root);
        this.elem.root.append(element);

        if (this.state.disabled) {
            this.toggleDisabled(this.state.disabled);
        }

        // set the new field element so validators will consider the wrapper the actual field instead of the input.
        // helps get error messages to show in proper place.
        element.data('fx_field', this.elem.root);

        this.setType(config.type || Types.FLOAT);

        element.fxEvent('focusin', () => {
            this.elem.root.addClass('t-active');
            element.select();
        });
        element.fxEvent('focusout', () => {
            this.elem.root.removeClass('t-active');
        });
        element.fxEvent('mouseup', (e) => e.preventDefault());
        element.fxEvent('keyup', debounce(() => {
            this.setValue(element.val());
        }, 300));
        element.fxEvent('number:value-update', () => {
            this.setValue(element.val(), false, false);
        });
        element.fxEvent('change', () => {
            this.toggleDisabled(element.is(':disabled'));
        });
    };

    /**
     * Get available types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Type() {
        return Types;
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'number';
    };

    /**
     * Add or remove disabled
     *
     * @param {boolean} disabled
     */
    toggleDisabled(disabled) {
        if (!disabled) {
            this.elem.root.removeClass('t-disabled');
            this.state.disabled = false;
            return;
        }
        this.elem.root.addClass('t-disabled');
        this.state.disabled = true;
    };

    /**
     * Add or remove warning
     *
     * @param {boolean} show_warning
     */
    toggleWarning(show_warning) {
        if (!show_warning) {
            this.elem.root.removeClass('t-warning');
            this.state.warning = false;
            return;
        }
        this.elem.root.addClass('t-warning');
        this.state.warning = true;
    };

    /**
     * Get and cache masking config for type
     *
     * @param {number} type
     * @returns {object}
     */
    getMaskConfig(type) {
        if (this.state.type_mask_configs[type] === undefined) {
            let config = {
                alias: 'numeric',
                autoGroup: true,
                nullable: false,
                digitsOptional: true,
                rightAlign: this.state.right_align
            };
            if ([Types.CURRENCY, Types.PERCENTAGE, Types.FEET, Types.INCHES].includes(type)) {
                config['allowMinus'] = this.state.allow_minus;
                config['allowPlus'] = this.state.allow_plus;
                config['max'] = this.state.max_value;
            }
            config = Object.assign(config, TypeConfig[type].mask);
            if (typeof this.state.mask_config === 'function') {
                config = this.state.mask_config(config, type);
            }
            this.state.type_mask_configs[type] = config;
        }
        return this.state.type_mask_configs[type];
    };

    /**
     * Set number type
     *
     * @param {number} type
     */
    setType(type) {
        let config = TypeConfig[type];
        if (config === undefined) {
            throw new Error('Type not recognized');
        }
        if (this.state.type !== null) {
            let prev_config = TypeConfig[this.state.type];
            if (prev_config.class) {
                this.elem.root.removeClass(prev_config.class);
            }
        }
        if (config.class) {
            this.elem.root.addClass(config.class);
        }
        Inputmask(this.getMaskConfig(type)).mask(this.elem.main[0]);
        this.state.type = type;
    };

    /**
     * Set value of input
     *
     * @param {string|number} value
     * @param {boolean} [validate=true]
     * @param {boolean} [notify=true]
     */
    setValue(value, validate = true, notify = true) {
        let valid = true;
        if (validate && this.state.validation !== null) {
            valid = this.state.validation(value);
        }
        this.elem.root.toggleClass('t-error', !valid);
        if (valid) {
            this.state.value = value;
            if (notify) {
                this.elem.main.trigger('number:change', value);
            }
        }
    };

    /**
     * Destroy instance, reset UI back to default and unbind events
     */
    destroy() {
        this.elem.main[0].inputmask.remove();
        this.elem.root.after(this.elem.main);
        this.elem.root.remove();
        this.elem.main.removeData('fx_field').fxEventDestroy(['keyup', 'focusin', 'focusout', 'mouseup', 'number:value-update']);
        super.destroy();
    };
}

module.exports = Number;
