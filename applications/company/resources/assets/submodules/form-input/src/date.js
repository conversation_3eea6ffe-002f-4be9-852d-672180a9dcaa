'use strict';

const flatpickr = require('flatpickr');

const FormInput = require('./index');
const getIso8601DateTime = require('@cac-js/utils/iso8601_datetime');

const input_tpl = require('@cas-form-input-tpl/date.hbs');

// fix needed to tell flatpickr that it's an iPad due to iOS 13 update for iPads
function isMobile() {
    return function(instance) {
        return {
            onParseConfig: () => {
                if (instance.isMobile) {
                    return;
                }
                if (
                    window.navigator.userAgent.match(/iPad/i) ||
                    window.navigator.userAgent.match(/iPhone/i) ||
                    /iPad|iPhone|iPod/.test(navigator.platform) ||
                    (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
                ) {
                    instance.isMobile = true;
                }
            }
        };
    };
}

/**
 * @memberof module:FormInput
 */
class DateInput extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     * @param {object} config
     */
    constructor(element, config = {}) {
        super(element);
        if (!element.is('input')) {
            throw new Error('Date/Time input only works with input elements');
        }
        Object.assign(this.state, {
            pickr_config: config.pickr_config || {},
            pickr: null,
            ipad: null
        });
        this.elem.root = $(input_tpl({type: this.constructor.name}));
        element.after(this.elem.root);
        this.elem.pickr = this.elem.root.fxFind('input');
        this.elem.pickr.val(element.val());

        // set the new field element so validators will consider the wrapper the actual field instead of the input.
        // helps get error messages to show in proper place.
        element.data('fx_field', this.elem.root);

        this.hideMainElem();

        let pickr = this.getPickr();
        element.on('change', () => {
            pickr.setDate(element.val());
        });
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'date';
    };

    /**
     * Set value and trigger change so user can detect it
     *
     * @param {string} date
     */
    setDate(date) {
        this.elem.main.val(date).trigger(`${this.constructor.name}:change`);
    };

    /**
     * Reformat date to remove microseconds from date string
     *
     * @returns {string}
     */
    getIsoFormat() {
        return getIso8601DateTime(new Date(this.elem.pickr.val()));
    };

    /**
     * Get current date
     *
     * @returns {Date}
     */
    getCurrentDate() {
        return new Date();
    };

    /**
     * Get picker config
     *
     * @returns {object}
     */
    getPickrConfig() {
        let close_after_select = false,
            use_default_date = true,
            mode = 'single',
            default_date= this.state.pickr_config.defaultDate !== undefined ? this.state.pickr_config.defaultDate : this.getCurrentDate();

        if (this.state.pickr_config.use_default_date !== undefined && !this.state.pickr_config.use_default_date) {
            use_default_date = false;
        }

        if (this.state.pickr_config.close_after_select !== undefined && this.state.pickr_config.close_after_select) {
            close_after_select = true;
        }

        if (this.state.pickr_config.mode !== undefined) {
            mode = this.state.pickr_config.mode;
        }

        let config_object = Object.assign(this.state.pickr_config, {
            altInput: true,
            altFormat: this.state.pickr_config.altFormat ?? 'F j, Y',
            dateFormat: this.state.pickr_config.dateFormat ?? 'Y-m-d',
            monthSelectorType: 'static',
            yearSelectorType: 'static',
            allowInput: false,
            mode,
            locale: {
                weekdays: {
                    shorthand: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
                }
            },
            plugins: [
                isMobile()
            ]
        });

        if (use_default_date) {
            config_object['defaultDate'] = default_date;
        }

        if (close_after_select) {
            config_object['onChange'] = (selectedDates, dateStr, instance) => {
                // Close the Flatpickr instance when a date is selected
                instance.close();
            };
        }

        return config_object;
    };


    /**
     * Get pickr instance
     *
     * @returns {object}
     */
    getPickr() {
        if (this.state.pickr === null) {
            let config = Object.assign(this.getPickrConfig(), {
                onReady: (selected, date_str) => this.setDate(date_str),
                onChange: (selected, date_str) => {
                    this.setDate(date_str);
                    //@todo create option to close after choosing
                    // this.state.pickr.close();
                }
            });
            this.state.pickr = flatpickr(this.elem.pickr, config);
        }
        return this.state.pickr;
    };

    /**
     * Clear picker and reset to default state
     *
     * @param {boolean} set_default
     */
    clear(set_default = false) {
        this.state.pickr.clear();
        if (set_default) {
            this.state.pickr.setDate(this.getCurrentDate());
        }
    };

    /**
     * Destroy instance, reset UI back to default
     */
    destroy() {
        this.elem.root.after(this.elem.main);
        this.elem.root.remove();
        this.state.pickr.destroy();
        super.destroy();
    };
}

module.exports = DateInput;
