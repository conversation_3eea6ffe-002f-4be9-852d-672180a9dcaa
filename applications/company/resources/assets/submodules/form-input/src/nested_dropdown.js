'use strict';

const Dropdown = require('./dropdown');

/**
 * @memberof module:FormInput
 */
class NestedDropdown extends Dropdown {
    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'nested-dropdown';
    };

    /**
     * Handle initial input configuration
     *
     * @param {object} config
     */
    processInitConfig(config) {
        super.processInitConfig(config);
        Object.assign(this.state, {
            data_provider: config.data_provider || [],
            option_handler: config.option_handler || ((data, option) => {
                option(data, data.children);
            })
        });
    };

    /**
     * Get nested data needed to fill out select options
     *
     * @returns {Promise<array>}
     */
    async getData() {
        let data = this.state.data_provider;
        if (typeof data === 'function') {
            data = await data(this);
        }
        if (!Array.isArray(data)) {
            throw new Error('Data must be an array');
        }
        return data;
    };

    /**
     * Get configuration for underlying select2 instance
     *
     * @returns {object}
     */
    getSelect2Config() {
        let config = super.getSelect2Config();
        return Object.assign(config, {
            templateResult: node => {
                if (node.class !== undefined) {
                    return $(`<div class="${node.class}">${node.text}</div>`);
                }
                return $(`<div style="padding-left:${node.level * 25}px">${node.text}</div>`);
            }
        });
    };

    /**
     * Build option list for select2 jQuery plugin
     *
     * @param {array} data - Nested list of data to return
     * @param {number} [level=0]
     * @returns {array}
     */
    buildOptionList(data, level = 0) {
        let options = [];
        for (let datum of data) {
            if (datum.id === 'add') {
                this.state.has_add_button = true;
                datum.class = 't-add-button';
                datum.text = `<svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg><div data-text>${datum.text}</div>`;
                options.push(datum);
                continue;
            }
            this.state.option_handler(datum, (option, children = []) => {
                option.level = level;
                options.push(option);
                if (Array.isArray(children) && children.length > 0) {
                    options = options.concat(this.buildOptionList(children, level + 1));
                }
            });
        }
        return options;
    };

    /**
     * Build configuration for Select2
     *
     * @param {object} config
     * @returns {Promise<object>}
     */
    async buildConfig(config) {
        config.data = this.buildOptionList(await this.getData());
        return config;
    };
}

module.exports = NestedDropdown;
