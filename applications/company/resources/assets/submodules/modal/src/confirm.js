'use strict';

const Base = require('./base');

/**
 * @memberof module:Modal
 */
class Confirm extends Base {
    /**
     * Constructor
     *
     * @param {Object} [config={}]
     */
    constructor(config = {}) {
        super('', Object.assign({
            size: Base.Size.TINY,
        }, config));
        this.addAction({
            type: Base.Action.CANCEL,
            handler: () => this.no()
        });
        this.addAction({
            type: Base.Action.YES,
            handler: () => this.yes()
        });
        this.state.handlers = {
            yes: null,
            no: null
        };
    };

    /**
     * Open modal with specified content and action handlers
     *
     * @param {string} title
     * @param {string} content
     * @param {?function} yes_handler
     * @param {?function} no_handler
     */
    openWithContent(title, content, yes_handler = null, no_handler = null) {
        this.setTitle(title);
        this.setContent(content);
        this.state.handlers.yes = yes_handler;
        this.state.handlers.no = no_handler;
        this.open();
    };

    yes() {
        this.clearMessages();
        this.state.events.emit('yes');
        this.handleYes();
    };

    /**
     * Handle yes action
     *
     * Designed to be overridden
     */
    handleYes() {
        if (this.state.handlers.yes === null) {
            return;
        }
        this.state.handlers.yes();
    };

    no() {
        this.state.events.emit('no');
        this.handleNo();
    };

    /**
     * Handle no action
     *
     * Designed to be overridden
     */
    handleNo() {
        if (this.state.handlers.no === null) {
            return;
        }
        this.state.handlers.no();
    };
}

module.exports = Confirm;
