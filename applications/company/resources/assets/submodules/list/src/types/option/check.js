'use strict';

const Option = require('../option');

/**
 * @memberof module:List/Types/Option
 */
class Check extends Option {
    /**
     * Constructor
     *
     * @param {object} [config={}]
     */
    constructor(config = {}) {
        super(Object.assign({
            active_icon: 'icon--input-radio-checked'
        }, config));
    };

    /**
     * Set selected items
     *
     * @param {(null|number|number[])} id - Id(s) of items to select, passing null clears everything
     * @param {boolean} [overwrite=true] - Whether to clear all currently selected items
     * @param {boolean} [notify=true] - Whether to emit changed event
     */
    setSelected(id, overwrite = true, notify = true) {
        let clear = id === null,
            old_ids = this.state.selected;
        if (clear || overwrite) {
            this.deselectAll();
            if (clear) {
                if (notify) {
                    this.notifyChange(old_ids);
                }
                return;
            }
        }
        let ids = lang.isArray(id) ? id : [id];
        for (let id of ids) {
            let item = this.getItem(id);
            // toggle item state
            this.setItemState(item, !item.active);
            if (item.active) {
                this.state.selected.push(id);
            } else {
                let index = this.state.selected.indexOf(id);
                this.state.selected.splice(index, 1);
            }
        }
        if (notify) {
            this.notifyChange(old_ids);
        }
    };

    /**
     * Handle item action
     *
     * @protected
     *
     * @param {number} id
     */
    handleItemAction(id) {
        let item = this.getItem(id);
        // if item is locked, then no action is allowed
        if (item.locked) {
            return;
        }
        this.setSelected(id, false);
    };
}

module.exports = Check;
