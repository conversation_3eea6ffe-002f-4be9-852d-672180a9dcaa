<div class="m-table-settings-menu">
    <div class="c-tsm-header">
        <div class="c-tsmh-title">
            <p class="c-tsmht-name">Table Settings</p>
        </div>
        <div class="c-tsmh-actions">
            <button class="c-tsmha-reset" data-js="reset">Reset Defaults</button>
        </div>
    </div>
    {{#if table_options}}
    <div class="c-tsm-options">
        {{#if map_view}}
         <div class="f-field">
            <label class="f-f-label t-label" for="map_view">Map View
                <span data-tooltip data-type="info">
                    Show map on table
                </span>
            </label>
            <input class="f-f-input" type="checkbox" id="map_view" data-fx-form-input="switch" data-js="map-view"{{#if map_view_checked}} checked{{/if}}>
        </div>
        {{/if}}
        {{#if bulk_actions}}
         <div class="f-field">
            <label class="f-f-label t-label" for="bulk_actions">Bulk Actions
                <span data-tooltip data-type="info">
                    Show bulk actions on table for bulk editing
                </span>
            </label>
            <input class="f-f-input" type="checkbox" id="bulk_actions" data-fx-form-input="switch" data-js="bulk-actions"{{#if bulk_actions_checked}} checked{{/if}}>
        </div>
        {{/if}}
    </div>
    {{/if}}
    <div class="c-tsm-body">
        <div class="c-tsmb-title">Column Order &amp; Visibility</div>
        {{#if bulk_actions_checked}}<div class="c-tsmb-disabled-warning">Column ordering is disabled when bulk actions are turned on. Turn off bulk actions to reorder the columns.</div>{{/if}}
        <div class="c-tsmb-items" data-js="table-settings-body"></div>
    </div>
    <div class="c-tsm-footer">
        <button class="c-tsmf-close" data-js="close">Close</button>
    </div>
</div>