'use strict';

import {Layout} from './base';
import {ItemTypes} from './constants';

import grid_tpl from '@cas-form-tpl/layouts/grid.hbs';

/**
 * @memberof module:Form/Group/Layout
 */
export class Grid extends Layout {
    /**
     * Constructor
     *
     * @param {module:Form.Group} group - Parent group
     * @param {Object} data
     * @param {Array} data.grid - Grid structure
     */
    constructor(group, data) {
        super(group, data);
        this.state.grid = data.grid;
    };

    /**
     * Render grid layout
     *
     * @returns {string}
     */
    render() {
        let rows = [];
        for (let $row of this.state.grid) {
            let row = {
                columns: []
            };
            for (let $column of $row) {
                let item = null;
                switch ($column.item_type) {
                    case ItemTypes.GROUP:
                        item = this.state.group.child($column.item_id);
                        if (item === undefined) {
                            throw new Error(`Unable to get group '${$column.item_id}'`);
                        }
                        break;
                    case ItemTypes.FIELD:
                        item = this.state.group.field($column.item_id);
                        if (item === undefined) {
                            throw new Error(`Unable to get field '${$column.item_id}'`);
                        }
                        break;
                    case ItemTypes.TEMPLATE:
                        item = this.state.group.template($column.item_id);
                        if (item === undefined) {
                            throw new Error(`Unable to get template '${$column.item_id}'`);
                        }
                        break;
                    default:
                        throw new Error('Invalid column type');
                }
                row.columns.push({
                    size: $column.size,
                    content: item.render(this.state.group, this.state.type)
                });
            }
            rows.push(row);
        }
        return grid_tpl({
            rows: rows
        });
    };
}
