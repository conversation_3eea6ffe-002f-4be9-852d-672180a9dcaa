'use strict';

import InputMask from 'inputmask';

import {Field} from './base';
import {Types as LayoutTypes} from '../layout/constants';

const Tooltip = require('@ca-submodule/tooltip');

import text_tpl from '@cas-form-tpl/fields/text.hbs';

/**
 * @memberof module:Form/Group/Field
 */
export class Text extends Field {
    /**
     * Constructor
     *
     * @param {module:Form.Group} parent - Parent group
     * @param {Object} data
     * @param {Object} entry
     * @param {?string} entry.value
     */
    constructor(parent, data, entry) {
        super(parent, data, entry);
        this.state.has_value = entry.value !== undefined;
        if (this.state.has_value) {
            this.state.value = entry.value;
        }
        this.state.disabled = this.config('display.disabled', false);
        if (typeof this.state.disabled !== 'boolean') {
            this.state.disabled = false;
        }

        this.state.allowed_validators = ['max_length', 'less_than', 'less_than_equal', 'greater_than', 'greater_than_equal'];

        this.state.mask = null;
    };

    /**
     * Set input value
     *
     * @param {string} value
     * @param {boolean} [update=true]
     * @param {boolean} [rules=true]
     * @returns {boolean}
     */
    setValue(value, update = true, rules = true) {
        if (typeof value === 'string') {
            value = value.trim();
        }
        let updated = super.setValue(value === '' ? null : value, update, rules);
        if (this.isBooted()) {
            this.elem.input.val(value);
        }
        return updated;
    };

    /**
     * Get API request payload
     *
     * @returns {Object}
     */
    getPayload() {
        let payload = super.getPayload();
        payload.value = this.value;
        return payload;
    };

    /**
     * Get entry data
     */
    getEntry() {
        let entry = super.getEntry();
        entry.values = [{
            value: this.state.value
        }];
        return entry;
    };

    /**
     * Get default value (if available)
     *
     * @returns {Promise<?string>}
     */
    async getDefaultValue() {
        let default_value = this.config('default_value');
        if (default_value !== null) {
            default_value = await this.state.parent.rule_context.parse(default_value);
        }
        return default_value;
    };

    /**
     * Clear field value, optionally emit update
     *
     * @param {boolean} [update=true]
     * @param {boolean} [rules=true]
     */
    async clear(update = true, rules = true) {
        this.resetDisplay();
        this.setValue(await this.getDefaultValue(), update, rules);
    };

    /**
     * Boot text field
     */
    async boot() {
        await super.boot();
        Tooltip.initAll(this.elem.root);

        this.elem.input = this.elem.root.fxFind('input');
        if (this.elem.input.length !== 1) {
            throw new Error('Unable to find input element');
        }
        if (this.state.value !== null) {
            this.elem.input.val(this.state.value);
        }
        if (this.state.disabled) {
            this.elem.input.prop('disabled', true);
        } else {
            let mask = this.config('display.mask');
            if (mask !== null) {
                let mask_config = null;
                switch (mask.type) {
                    case 'integer':
                        mask_config = {
                            alias: 'numeric',
                            groupSeparator: '',
                            autoGroup: false,
                            digits: 0,
                            digitsOptional: true,
                            rightAlign: false
                        };
                        break;
                    case 'decimal':
                        mask_config = {
                            alias: 'numeric',
                            autoGroup: true,
                            digits: mask.digits || 0,
                            digitsOptional: typeof mask.digits_optional === 'boolean' ? mask.digits_optional : true,
                            rightAlign: false
                        };
                        break;
                    case 'currency':
                        mask_config = {
                            alias: 'numeric',
                            groupSeparator: ',',
                            autoGroup: true,
                            digits: 2,
                            digitsOptional: false,
                            rightAlign: false
                        };
                        break;
                }
                if (mask_config !== null) {
                    let mask = new InputMask(mask_config);
                    this.state.mask = mask.mask(this.elem.input[0]);
                }
            }
            this.elem.input.fxEvent('change', () => {
                // Commenting out the following code due to an issue with windows chrome not returning the unmasked value
                // and can't replicate on mac chrome
                // A better solution seems to just send the input value to the validate function
                // @todo look into removing the following commented code in the future, as it may not be necessary

                // let value;
                // if (this.state.mask !== null) {
                //     value = this.state.mask.unmaskedvalue();
                // } else {
                //     value = this.elem.input.val();
                // }
                // this.validate(value);
                this.validate(this.elem.input.val());
            });
        }
        this.state.booted = true;

        if (!this.state.has_value) {
            let default_value = await this.getDefaultValue();
            if (default_value !== null && this.setValue(default_value, false, false)) {
                this.save();
            }
        }
    };

    /**
     * Render text field
     *
     * @param {number} layout_type
     * @returns {string}
     */
    render(layout_type) {
        super.render();
        let classes = [];
        switch (layout_type) {
            case LayoutTypes.INPUT_TABLE_ROW:
                classes.push('t-layout-table-row');
                break;
        }
        let types = ['text', 'number', 'date', 'datetime-local', 'color', 'month', 'password', 'tel', 'time', 'url'];
        let type = this.config('display.type', 'text');
        if (types.indexOf(type) === -1) {
            type = 'text';
        }
        let internal = this.config('display.is_internal', false);
        if (internal) {
            classes.push('t-internal');
        }
        return text_tpl({
            path: this.path(),
            classes: classes,
            type: type,
            label: this.state.label,
            tooltip: this.state.tooltip,
            internal: internal
        });
    };
}
