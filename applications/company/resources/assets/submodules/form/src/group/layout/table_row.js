'use strict';

import {Layout} from './base';

import table_row_tpl from '@cas-form-tpl/layouts/table_row.hbs';

/**
 * @memberof module:Form/Group/Layout
 */
export class TableRow extends Layout {
    /**
     * Constructor
     *
     * @param {module:Form/Group} group - Parent group
     * @param {Object} data
     * @param {Array} data.columns - Table row columns
     */
    constructor(group, data) {
        super(group, data);
        this.state.columns = data.columns;
    };

    /**
     * Get header data
     *
     * @returns {Array}
     */
    headers() {
        let headers = [];
        for (let column of this.state.columns) {
            let field = this.state.group.field(column.item_id);
            headers.push({
                size: column.size,
                header: field.label,
                tooltip: field.tooltip,
                required: field.required
            });
        }
        return headers;
    };

    /**
     * Render table row
     *
     * @returns {string}
     */
    render() {
        let columns = [];
        for (let column of this.state.columns) {
            // only fields supported right now
            try {
                let field = this.state.group.field(column.item_id);
                if (field === undefined) {
                    throw new Error(`Unable to find field: ${column.item_id}`);
                }
                columns.push({
                    size: column.size,
                    content: field.render(this.type)
                });
            } catch (e) {
                throw new Error(`Unable to render table row: ${e.message}`);
            }
        }
        return table_row_tpl({
            columns: columns
        });
    };
}
