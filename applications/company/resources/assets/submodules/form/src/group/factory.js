'use strict';

import {Types} from './constants';
import {Group} from './base';
import {Default} from './default';
import {RepeatableTable} from './repeatable_table';

const ClassMap = new Map([
    [Types.DEFAULT, Default],
    [Types.REPEATABLE_TABLE, RepeatableTable]
]);

export function getClassByType(type) {
    return ClassMap.get(type);
}

/**
 * Load all groups with associated entries
 *
 * @param {module:Form.Controller} form
 * @param {GroupData[]} groups - Groups to load
 * @param {Object.<string, EntryData>} entries - Group entries
 * @param {module:Form.Group|null} [parent=null]
 * @param {object} aliases
 * @returns {module:Form.Group[]}
 */
export function loadAll(form, groups, entries, parent = null, aliases = {}) {
    let instances = [];
    if (groups.length > 0) {
        for (let group of groups) {
            let group_entry,
                type = getClassByType(group.type);
            switch (group.type) {
                case Types.DEFAULT:
                    // if default group type, then we just grab the first entry since there can only be one
                    group_entry = Array.isArray(entries[group.id]) ? entries[group.id][0] : {};
                    break;
                case Types.REPEATABLE_TABLE:
                    group_entry = Array.isArray(entries[group.id]) ? entries[group.id] : [];
                    break;
            }
            let instance = new type(form, group, group_entry, parent);
            instances.push(instance);
            aliases[group.id] = instance;
            if (group.alias !== null) {
                aliases[group.alias] = instance;
            }
        }
    }
    return instances;
}

/**
 * Determine if object is a Group instance
 *
 * @param {object} group
 * @returns {boolean}
 */
export function isGroup(group) {
    return group !== null && typeof group === 'object' && group instanceof Group;
}
