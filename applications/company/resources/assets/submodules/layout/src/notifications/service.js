import Api from '@ca-package/api';

class NotificationService {

    /**
    * Build the “primary” URL for a notification, based on its association_type
    *
    * @param {object} notification
    * @param {number} notification.association_type
    * @param {number} notification.association_id
    * @param {string} notification.association_uuid
    * @returns {string|null}
    */
    getNotificationLink(notification) {
        if (notification.link) {
            return notification.link;
        }

        const { association_type, association_id, association_uuid, metadata = '{}' } = notification;
        let parsed_metadata = {};

        try {
            parsed_metadata = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;


            const { project_id, sub_type, is_legacy } = parsed_metadata;
            const FX = window.fx_pages_urls;

            const replace = (template, values) =>
                Object.entries(values).reduce((url, [key, val]) => url.replace(`{${key}}`, val), template);

            switch (association_type) {
                case Api.Constants.AppNotification.AssociationType.PROPERTY:
                    return replace(FX.PROPERTIES_MANAGEMENT, { property_id: association_id });

                case Api.Constants.AppNotification.AssociationType.LEAD:
                    return replace(FX.LEAD_DETAILS, { lead_id: association_uuid });

                case Api.Constants.AppNotification.AssociationType.TASK:
                    return replace(FX.TASK_DETAILS, { task_id: association_uuid });

                case Api.Constants.AppNotification.AssociationType.PROJECT:
                    if (sub_type === 'project_event_created') {
                        return replace(FX.PROJECT_SCHEDULE, { project_id: association_id });
                    }
                    return replace(FX.PROJECT, { project_id: association_id });

                case Api.Constants.AppNotification.AssociationType.BID:
                    if (is_legacy) {
                        return replace(FX.PROJECT_BIDS, { project_id: project_id})
                    }

                    if (project_id && association_uuid) {
                        return replace(FX.PROJECT_BID, {
                            project_id: project_id,
                            bid_id: association_uuid
                        });
                    }
                    return null;

                default:
                    return null;
            }
        } catch (e) {
            console.warn(e);
            console.warn('Invalid metadata JSON:', metadata);
            return null;
        }
    }

    /**
     * Find a notification by its ID.
     *
     * @param notification_id
     * @returns {*}
     */
    findByNotificationId(notification_id) {
        let { banner, notification_center } = window.layout.user.notifications;

        let notification = notification_center.find(n => n.id === notification_id);

        if (!notification) {
            notification = banner.find(n => n.id === notification_id);
        }

        return notification;
    }

    /**
     * Remove a notification by its distribution id and placement.
     */
    removeNotification(distribution_id, placement) {
        let { banner, notification_center } = window.layout.user.notifications;

        switch (placement) {
            case Api.Constants.AppNotification.Placement.GLOBAL:
                window.layout.user.notifications.banner = banner.filter(
                    n => n?.distribution?.id !== distribution_id
                );
                window.layout.user.notifications.notification_center = notification_center.filter(
                    n => n?.distribution?.id !== distribution_id
                );
                break;
            case Api.Constants.AppNotification.Placement.NOTIFICATION_CENTER:
                window.layout.user.notifications.notification_center = notification_center.filter(
                    n => n?.distribution?.id !== distribution_id
                );
                break;
            case Api.Constants.AppNotification.Placement.BANNER:
                window.layout.user.notifications.banner = banner.filter(
                    n => n?.distribution?.id !== distribution_id
                );
                break;
        }
    }


    /**
     * Remove multiple distribution IDs from the notification center in one pass.
     *
     * @param {string[]} distributionIDs
     * @param {string} placement - One of Api.Constants.AppNotification.Placement.*
     */
    removeMultipleNotifications(distributionIDs, placement) {
        if (!Array.isArray(distributionIDs)) {
            console.warn('distributionIds is not an array!');
            return;
        }

        distributionIDs.map((id) => {
            this.removeNotification(id, placement);
        });
    }
}

export default new NotificationService();