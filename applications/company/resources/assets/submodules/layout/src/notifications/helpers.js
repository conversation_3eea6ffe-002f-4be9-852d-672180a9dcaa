import $ from "jquery";

const Api = require("@ca-package/api");
import moment from 'moment-timezone';

import section_tpl from '@cas-layout-tpl/notification/section.hbs';
import item_tpl from '@cas-layout-tpl/notification/item.hbs';

export async function acknowledge(distribution_id, status) {
    try {
        const payload = { status };

        const response = await Api.Resources.AppNotificationDistribution()
            .accept('application/vnd.adg.fx.form-v1+json')
            .partialUpdate(distribution_id, payload);

        if (response?.data) {
            $(document).trigger('notification:archive-update')
        } else {
            throw new Error('Unexpected API response format');
        }
    } catch (error) {
        console.error('Error acknowledging notification:', error);
    }
}

export function acknowledgeAll(distribution_ids) {
    return new Promise((resolve, reject) => {
        const updatedIds = [];

        try {
            const batch_request = new Api.BatchRequest.Multiple(false); // atomic = false

            distribution_ids.forEach((id) => {
                const singleRequest = new Api.BatchRequest.Single(
                    'app-notification-distribution',
                    'partial-update',
                    { id, status: Api.Constants.AppNotificationDistribution.Status.READ }
                );

                // Log each single request finishing
                singleRequest.promise.then(
                    (resp) => {
                        updatedIds.push(id);
                    },
                    (err) => {
                        console.warn(`[SingleRequest] ID ${id} error`, err);
                    }
                );

                batch_request.add(singleRequest);
            });

            // Listen for the entire batch finishing
            batch_request.promise.then(
                (result) => {
                    setTimeout(() => {
                        resolve(updatedIds);
                    }, 500);
                },
                (error) => {
                    console.error('[BatchRequest] batch_request.promise error:', error);
                    reject(error);
                }
            );

            // Start the request
            const queue = new Api.BatchQueue();
            queue.addRequest(batch_request);

        } catch (error) {
            console.error('[acknowledgeAll] Caught synchronous error:', error);
            reject(error);
        }
    });
}

export async function acknowledgeAndRedirect(notification) {
    return new Promise(async (resolve, reject) => {
        try {
            if (notification.distribution?.status !== Api.Constants.AppNotificationDistribution.Status.UNREAD) {
                window.location.href = notification.link;
                resolve();
            }

            await this.acknowledge(
                notification.distribution?.id,
                Api.Constants.AppNotificationDistribution.Status.SEEN
            );
            window.location.href = notification.link;
            resolve();
        } catch (error) {
            reject(error);
        }
    });
}

export async function fetchUserNotifications() {
    try {
        return await Api.Resources.AppNotification()
            .method(Api.Request.Method.GET)
            .custom('me')

    } catch (error) {
        console.error('Error fetching user notifications:', error);
        return [];
    }
}

/**
 * Group an array of notifications by a formatted date key.
 * @param {Array} notifications
 * @returns {Object} groupedNotifications
 */
export function groupNotificationsByDate(notifications) {
    const grouped = {};

    notifications.forEach(notification => {
        const created_at = new Date(notification.created_at);
        if (isNaN(created_at)) return; // skip invalid date

        // e.g. "Monday, October 9, 2025"
        const formattedDate = created_at.toLocaleString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            year: 'numeric',
        });

        if (!grouped[formattedDate]) {
            grouped[formattedDate] = [];
        }

        grouped[formattedDate].push(notification);
    });

    return grouped;
}

/**
 * Build an entire notification section for a given date and its notifications.
 * @param {String} date - Formatted date string (e.g., "Monday, October 9, 2025")
 * @param {Array} notifications - Array of notifications for that date
 * @returns {jQuery} section element
 */
 export function buildNotificationSectionHTML(date, notifications) {
    const html = section_tpl({ date })
    const section = $(html);
    const sectionItems = section.find('.ns-items');

    notifications.forEach(notification => {
        const itemHTML = buildNotificationItemHTML(notification);
        sectionItems.append(itemHTML);
    });

    return section;
}


/**
 * Build the HTML string for a single notification item.
 * @param {Object} notification
 * @returns {String} notification item HTML
 */
export function buildNotificationItemHTML(notification) {
    const { icon_type,
        icon_class,
        inner_icon_type} = getNotificationIcons(notification.type, notification.association_type, notification.metadata, notification?.content);

    // Format time
    const timezone = window.layout?.user?.timezone ?? 'America/Chicago';
    const momentUTC = moment.utc(notification.created_at);
    const formattedTime = momentUTC.tz(timezone).format('hh:mm A');

    return item_tpl({
        id:       notification.id || 'Unknown',
        title:    notification.title || 'Unknown',
        summary:  notification.summary || 'Unknown',
        icon_type,
        icon_class,
        inner_icon_type,
        datetime: notification.created_at || '',
        time:     formattedTime
    });
}

/**
 * Return icon information based on notification type.
 * @param {Number} type
 * @param {Number} association_type
 * @param {String} metadata
 * @param {String} content
 * @returns {{icon_type: string, icon_class: string, inner_icon_type: string}}
 */
export function getNotificationIcons(type, association_type, metadata = '{}', content = null) {
    let icon_type;
    let icon_class;
    let inner_icon_type = content ? '--media--picture-in-picture-fill' : '--system--share-circle-line';
    let FALLBACK_ICON = { icon: '--system--information-fill', class: 't-blue' };

    let BASE_ICON_MAP = {
        [Api.Constants.AppNotification.AssociationType.PROJECT]: {
            default:               { icon: '--document--clipboard-fill',      class: 't-yellow' },
            project_assigned:      { icon: '--document--clipboard-fill',     class: 't-yellow' },
            project_event_created: { icon: '--business--calendar-todo-fill', class: 't-purple' }
        },
        [Api.Constants.AppNotification.AssociationType.LEAD]: {
            default:          { icon: '--business--inbox-archive-fill', class: 't-orange' },
            lead_assigned:    { icon: '--business--inbox-archive-fill', class: 't-orange' }
        },
        [Api.Constants.AppNotification.AssociationType.TASK]: {
            default:        { icon: '--system--checkbox-fill', class: 't-green' },
            task_assigned: { icon: '--system--checkbox-fill', class: 't-green' }
        },
        [Api.Constants.AppNotification.AssociationType.BID]: {
            default:      { icon: '--document--file-list-2-fill',  class: 't-blue' },
            bid_viewed:   { icon: '--document--file-list-2-fill',class: 't-blue' },
            bid_accepted: { icon: '--document--file-check-fill', class: 't-green' },
            bid_rejected: { icon: '--document--file-close-fill', class: 't-red' }
        }
    };

    if (type === Api.Constants.AppNotification.Type.INFO) {
        let sub_type;
        try {
            sub_type = JSON.parse(metadata)?.sub_type;
        } catch (_) {
            sub_type = undefined;
        }

        const bucket = BASE_ICON_MAP[association_type] || {};
        ({ icon: icon_type, class: icon_class } = bucket[sub_type] || bucket.default || FALLBACK_ICON);
    }
    else if (type === Api.Constants.AppNotification.Type.MAINTENANCE) {
        icon_type  = '--design--tools-fill';
        icon_class = 't-yellow';
    } else if (type === Api.Constants.AppNotification.Type.ERROR) {
        icon_type  = '--system--error-warning-fill';
        icon_class = 't-red';
    } else {
        ({ icon: icon_type, class: icon_class } = FALLBACK_ICON);
    }

    return { icon_type, icon_class, inner_icon_type };
}
