import $ from 'jquery';
import {find, findChild, jsSelector} from '@ca-package/dom';
import {
    groupNotificationsByDate,
    buildNotificationSectionHTML,
    acknowledge, fetchUserNotifications
} from '@ca-submodule/layout/src/notifications/helpers';
import NotificationService from '@ca-submodule/layout/src/notifications/service';
import NotificationMenu from "@cas-layout-js/notifications/components/notification_menu";
import Api from "@cam-signup-js/api";

class NotificationCenter {
    /**
     * @param {HTMLElement|jQuery|string} root - The root element of the layout.
     */
    constructor(root) {
        this.root = root;
        this.elem = {};
        this.state = {
            selected_notifications: new Set(),
        };
        this.initializeElements();
        this.bindGlobalEvents()
        this.bindStaticEvents();
        this.notification_menu = new NotificationMenu(this.root);
    }

    initializeElements() {
        // Cache DOM elements within the layout.
        this.elem.notification_center_button = findChild(this.root, jsSelector('notification-center-button'));
        this.elem.notification_center = findChild(this.root, jsSelector('notification-center'));
        this.elem.notification_list = findChild(this.elem.notification_center, '.ns-wrapper');
        this.elem.notification_empty_tab = findChild(this.elem.notification_center, '#n-empty-tab');
        this.elem.notification_main_tab = findChild(this.elem.notification_center, '#n-main-tab');
        this.elem.notification_read_all_button = findChild(this.elem.notification_center, jsSelector('read-all-button'));

        const hasBannerNotification = $('.l-app.t-has-notification').length > 0;
        if (hasBannerNotification) {
            if (!this.elem.notification_center.hasClass('has-banner-notification')) {
                this.elem.notification_center.addClass('has-banner-notification');
            }
        }
    }

    bindGlobalEvents() {
        $(window).on('notification:fetch-all', () => {
            fetchUserNotifications().then((data) => {
                window.layout.user.notifications = data
                this.loadNotifications();
            })
        });

        // Notification center sidebar changes
        $(window).on('notification:sidebar-close', () => {
            const notification_center = find(jsSelector('notification-center'));
            notification_center?.removeClass('is-open');

            const $element = $('#notification-center-button');
            $element?.removeClass('active');
            const $mobile_element = $('#mobile-notification-center-button');
            $mobile_element?.removeClass('active');
            this.notification_menu.hide();
        });

        $(window).on('notification:sidebar-open', () => {
            const notification_center = find(jsSelector('notification-center'));
            if (notification_center) {
                $(notification_center).css('display', '');
                setTimeout(() => {
                    notification_center.addClass('is-open');
                }, 10);
            }

            const $element = $('#notification-center-button');
            $element?.addClass('active');
            const $mobile_element = $('#mobile-notification-center-button');
            $mobile_element?.addClass('active');

            $(window).trigger('notification:sidebar-style-update');
        });

        $(window).on('notification:count-update', () => {
            let count = window.layout?.user?.notifications?.notification_center?.length || 0;
            const $element = $('#notification-count');
            const mobile_element = $('#mobile-notification-count');

            if (count > 0) {
                if (count > 99) {
                    count = '99+';
                }
                $element.removeClass('t-hidden').html(count);
                mobile_element.removeClass('t-hidden').html(count);
            } else {
                $element.addClass('t-hidden').html('');
                mobile_element.addClass('t-hidden').html('')
            }
        });
    }

    bindStaticEvents() {
        const { notification_center_button, notification_center, notification_read_all_button } = this.elem;

        notification_center_button.on('click', () => {
            const event = notification_center.hasClass('is-open')
                ? 'notification:sidebar-close'
                : 'notification:sidebar-open';
            $(window).trigger(event);
        });

        notification_read_all_button.on('click', () => {
            $(window).trigger('notification:batch-read');
        });

        if (notification_center) {
            notification_center.on('transitionend', (event) => {
                if (event.originalEvent.propertyName === 'transform' && !notification_center.hasClass('is-open')) {
                    notification_center.css('display', 'none');
                }
            });
        }

        $(document).on('click', (event) => {
            if (notification_center && notification_center.hasClass('is-open')) {
                const clicked_on_button = notification_center_button[0].contains(event.target);
                const clicked_inside_center = notification_center[0].contains(event.target);

                if (!clicked_inside_center && !clicked_on_button) {
                    $(window).trigger('notification:sidebar-close');
                }
            }
        });

    }

    /**
     * Render notifications in the notification center.
     */
    loadNotifications() {
        $(window).trigger('notification:count-update');

        const notificationList = this.elem.notification_list;
        if (!notificationList) return;

        const notifications = window.layout?.user?.notifications?.notification_center || [];

        if (!notifications.length) {
            this.elem.notification_empty_tab.addClass('active');
            this.elem.notification_main_tab.removeClass('active');
            notificationList.html('');
            return;
        }

        // Clear previous notifications.
        notificationList.html('');
        this.elem.notification_empty_tab.removeClass('active');
        this.elem.notification_main_tab.addClass('active');

        const groupedNotifications = groupNotificationsByDate(notifications);

        Object.keys(groupedNotifications).forEach(date => {
            const sectionHTML = buildNotificationSectionHTML(date, groupedNotifications[date]);
            notificationList.append(sectionHTML);
        });

        // Now bind events to the freshly rendered items.
        this.bindNotificationItemClicks();
        this.bindCheckboxEvents();
    }

    /**
     * Binds click events on each notification item.
     */
    bindNotificationItemClicks() {
        this.elem.notification_list.find('.ns-item').on('click', (event) => {
            const $target = $(event.target);
            // If the click is on a checkbox, skip handling.
            if ($target.closest('.ns-ih-checkbox').length > 0) {
                const $notificationItem = $target.closest('.ns-item');
                const notificationId = $notificationItem.data('id');

                const notification = NotificationService.findByNotificationId(notificationId);
                const { distribution, placement } = notification;

                const is_processing_spinner = $('.loading-spinner-container');
                is_processing_spinner.addClass('active');

                acknowledge(distribution?.id, Api.Constants.AppNotificationDistribution.Status.READ).then(() => {
                    $(document).trigger('notification:read', { distribution_id: distribution?.id, placement });
                }).finally(() => {
                    is_processing_spinner.removeClass('active');
                });
                return;
            }

            const $notification = $(event.currentTarget);
            const notificationID = $notification.data('id');
            const notification = window.layout?.user?.notifications?.notification_center.find(
                n => n.id === notificationID
            );
            if (!notification) {
                console.warn('Notification data not found for ID:', notificationID);
                return;
            }

            const notificationData = {
                ...notification,
            };
            $(document).trigger('notification:clicked', [notificationData]);
        });
    }

    /**
     * Bind events for checkbox selection on notifications.
     */
    bindCheckboxEvents() {
        // Clear any previous bindings.
        $(document).off('click', '.ns-ih-checkbox');
        $(document).off('mouseenter', '.ns-ih-checkbox');
        $(document).off('mouseleave', '.ns-ih-checkbox');

        // Hover effects for checkboxes.
        $(document).on('mouseenter', '.ns-ih-checkbox', function () {
            const $checkbox = $(this);
            $checkbox.find('.blank').addClass('t-hidden');
            $checkbox.find('.checked').removeClass('t-hidden');
        });
        $(document).on('mouseleave', '.ns-ih-checkbox', function () {
            const $checkbox = $(this);
            $checkbox.find('.blank').removeClass('t-hidden');
            $checkbox.find('.checked').addClass('t-hidden');
        });
    }

    /**
     * Update the notification sidebar position and height.
     * This method can be bound to window resize events.
     */
    updateSidebar() {
        const headerSpacer = document.querySelector('.a-a-header-spacer');
        const sidebar = document.querySelector('.notification-sidebar-drawer');
        const sidebarPanel = document.querySelector('.s-sidebar-panel');
        const wrapperFlex = document.querySelector('.c-n-wrapper-flex');
        const notificationsTitle = document.querySelector('.ns-header');
        const mainTab = document.querySelector('#n-main-tab');

        if (!headerSpacer || !sidebar || !sidebarPanel || !notificationsTitle || !mainTab) return;

        const spacerHeight = headerSpacer.offsetHeight;
        const isSmallScreen = window.innerWidth <= 1200;
        let extraOffset = isSmallScreen && wrapperFlex ? wrapperFlex.offsetHeight : 0;
        const totalOffset = spacerHeight + extraOffset;

        // Adjust inner sidebar overflow to stick `Notifications` title.
        mainTab.style.height = `${window.innerHeight - totalOffset - notificationsTitle.offsetHeight}px`;
        mainTab.style.overflowY = 'auto'

        // Set sidebar position and height.
        sidebar.style.top = `${totalOffset}px`;
        sidebarPanel.style.height = `${window.innerHeight - totalOffset}px`;
    }
}

export default NotificationCenter;