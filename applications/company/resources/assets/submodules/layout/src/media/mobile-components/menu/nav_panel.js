'use strict';

import $ from 'jquery';

import {find, find<PERSON>hild, jsSelector, onClick, onClickWatcher, onEvent} from '@ca-package/dom';

import {Base} from './base';

import nav_panel_tpl from '@cas-layout-tpl/media/mobile-components/menu/nav_panel.hbs';

/**
 * @memberof module:Layout/Media/MobileComponents/Menu
 */
export class NavPanel extends Base {
    /**
     * Constructor
     *
     * @param {module:Layout/Media/MobileComponents.Menu} menu
     */
    constructor(menu) {
        super(menu);
        Object.assign(this.state, {
            new_menu_available: false,
            new_menu_open: false
        });
    };

    /**
     * Toggle new menu visibility
     *
     * @param {boolean|null} [open=null] - Determines state, if null toggle is used
     */
    toggleNewMenu(open = null) {
        if (open === null) {
            open = !this.state.new_menu_open;
        } else if (open === this.state.new_menu_open) {
            return;
        }
        this.elem.new_button_menu[open ? 'fadeIn' : 'fadeOut'](300);
        this.state.new_menu_open = open;
    };

    /**
     * Show new menu overlay
     */
    showNewMenu() {
        this.toggleNewMenu(true);
    };

    /**
     * Handle new menu overlay
     */
    hideNewMenu() {
        this.toggleNewMenu(false);
    };

    /**
     * Unload panel
     */
    unload() {
        if (this.menu.media.controller.restricted) {
            return;
        }
        let menu = findChild(this.elem.nav, jsSelector('menu-trigger'));
        menu.removeClass('t-open').next().hide();
        menu.parent().removeClass('t-open');
        this.hideNewMenu();

        if (this.elem.new_button.length > 0) {
            this.elem.new_button.removeClass('t-hidden-add');
            this.elem.new_button_menu_close.removeClass('t-show');
            this.elem.new_button_menu.removeClass('t-show');
        }
    };

    handleNotificationMenu() {
        const notification_center_button_mobile = $('#mobile-notification-center-button');

        notification_center_button_mobile.on('click', () => {
            this.elem.notification_center = find(jsSelector('notification-center'));
            const event = this.elem.notification_center.hasClass('is-open')
                ? 'notification:sidebar-close'
                : 'notification:sidebar-open';
            $(window).trigger(event);
        });

        const is_notification_center_enabled = window.layout.user?.notifications?.is_notification_center_enabled
        if (is_notification_center_enabled) {
            $(window).trigger('notification:count-update');
        }
    }

    /**
     * Boot panel
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.new_button = findChild(this.elem.root, jsSelector('new-button'));
        this.state.new_menu_available = this.elem.new_button.length === 1;
        if (this.state.new_menu_available) {
            this.elem.new_button_menu = findChild(this.elem.root, jsSelector('new-button-menu'));
            this.elem.new_button_menu_close = findChild(this.elem.new_button_menu, jsSelector('close'));
        }
        this.elem.nav_buttons = findChild(this.elem.root, jsSelector('nav-buttons'));
        this.elem.nav_actions = findChild(this.elem.root, jsSelector('nav-actions'));
        this.elem.search = findChild(this.elem.nav_actions, jsSelector('search'));
        this.elem.user = findChild(this.elem.root, jsSelector('user'));
        this.elem.nav = findChild(this.elem.root, jsSelector('nav'));

        this.handleNotificationMenu();

        onClick(this.elem.user, () => this.menu.showPanel('user'), true);

        let controller = this.menu.media.controller;
        if (controller.restricted) {
            this.elem.nav_actions.hide();
            this.elem.nav_buttons.hide();
            this.elem.nav.hide();
            if (this.state.new_menu_available) {
                this.elem.new_button.hide();
            }
            return;
        }

        if (this.state.new_menu_available) {
            onClick(this.elem.new_button, () => {
                this.showNewMenu();
                this.elem.new_button.toggleClass('t-hidden-add');
                this.elem.new_button_menu_close.toggleClass('t-show');
                this.elem.new_button_menu.toggleClass('t-show');
            }, true);
            onClick(this.elem.new_button_menu_close, () => {
                this.hideNewMenu();
                this.elem.new_button.toggleClass('t-hidden-add');
                this.elem.new_button_menu_close.toggleClass('t-show');
                this.elem.new_button_menu.toggleClass('t-show');
            }, true);
        }

        onEvent(this.elem.search, 'click', (e) => {
            e.preventDefault();
            controller.global_search.open();
            return false;
        });
        onClickWatcher(this.elem.nav, jsSelector('menu-trigger'), function () {
            let $this = $(this),
                menu = $this.next();
            $this.parent().toggleClass('t-open');
            $this.toggleClass('t-open');
            menu[menu.is(':visible') ? 'slideUp' : 'slideDown'](300);
        }, true);
    };

    /**
     * Render panel
     *
     * @returns {string}
     */
    render() {
        const controller = this.menu.media.controller;
        let user = controller.user;
        let menu = window.layout?.main_nav || {};
        let main_nav = {},
            button_nav = {},
            quick_links = {},
            menu_1 = {},
            menu_2 = {};

        for (let item in menu) {
            let menu_item = menu[item];
            if (menu_item.button !== undefined && menu_item.button) {
                button_nav[item] = menu_item;
                continue;
            }
            main_nav[item] = menu_item;
        }

        menu = window.layout?.user_menu ?? {};
        menu_2 = menu.menu_2 ?? {};

        if (!controller.restricted) {
            quick_links = menu.quick_links ?? {};
            menu_1 = menu.menu_1 ?? {};
        }

        return nav_panel_tpl({
            new_button_menu: Object.values(window.layout?.new_menu ?? {}),
            user_name: `${user.first_name} ${user.last_name}`,
            company_name: user.company_name,
            nav: main_nav,
            buttons_nav: Object.keys(button_nav).length > 0,
            buttons: button_nav,
            quick_links: quick_links,
            menu_1: menu_1,
            menu_2: menu_2,
        });
    };
}
