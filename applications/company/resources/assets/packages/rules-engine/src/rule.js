'use strict';

import includes from 'lodash/includes';
import clone from 'lodash/cloneDeep';
import get from 'lodash/get';
import set from 'lodash/set';

import Number from '@cac-js/utils/number';

export const Types = {
    GROUP_ANY: 'any',
    GROUP_ALL: 'all',
    CONDITIONAL: 'condition'
};
export const EventTypes = {
    PASS: 1,
    FAIL: 2
};
export const Operators = [
    'equal', 'not-equal', 'less-than', 'less-than-inclusive', 'greater-than', 'greater-than-inclusive',
    'in', 'not-in', 'contains', 'does-not-contain', 'matches', 'does-not-match'
];
export const SUBCONTEXT_MATCH = '__rule_matches';

/**
 * @memberof module:RulesEngine
 */
export class Rule {
    /**
     * Constructor
     */
    constructor() {
        /**
         * @protected
         */
        this.state = {
            items: [],
            priority: 5,
            events: new Map,
            storage: {}
        };
        this.state.item = this.state.items;
        for (let type of Object.values(EventTypes)) {
            this.state.events.set(type, new Map);
        }
    };

    /**
     * Get priority
     *
     * @readonly
     *
     * @returns {number}
     */
    get priority() {
        return this.state.priority;
    };

    /**
     * Store custom data by key
     *
     * Note: can use dot syntax to handle nested values
     *
     * @param {string} key
     * @param {*} value
     * @returns {module:RulesEngine.Rule}
     */
    store(key, value) {
        set(this.state.storage, key, value);
        return this;
    };

    /**
     * Get custom data from storage by key
     *
     * Note: can use dot syntax to handle nested values
     *
     * @param {?string} [key=null]
     * @param {*} [_default=null]
     * @returns {*}
     */
    storage(key = null, _default = null) {
        if (key === null) {
            return this.state.storage;
        }
        return get(this.state.storage, key, _default);
    };

    /**
     * Handle condition grouping
     *
     * @param {string} type - Any or all
     * @param {function} closure
     */
    handleGroup(type, closure) {
        let idx = this.state.item.length;
        this.state.item[idx] = {
            type: type,
            items: []
        };
        let last_item = this.state.item;
        this.state.item = this.state.item[idx].items;
        closure(this);
        this.state.item = last_item;
    };

    /**
     * Add conditions and groups defined in closure under an 'any' grouping
     *
     * If any condition passes, then this group will evaluate as true.
     *
     * @param {function} closure
     * @returns {module:RulesEngine.Rule}
     */
    any(closure) {
        this.handleGroup(Types.GROUP_ANY, closure);
        return this;
    };

    /**
     * Add conditions and groups defined in closure under an 'all' grouping
     *
     * Group will only evaluate as true if all conditions/groups defined under it pass.
     *
     * @param {function} closure
     * @returns {module:RulesEngine.Rule}
     */
    all(closure) {
        this.handleGroup(Types.GROUP_ALL, closure);
        return this;
    };

    /**
     * Add condition to rule
     *
     * @param {string|function} left
     * @param {string} operator
     * @param {*} right
     * @returns {module:RulesEngine.Rule}
     */
    condition(left, operator, right) {
        if (!includes(Operators, operator)) {
            throw new Error(`Invalid operator: ${operator}`);
        }
        switch (operator) {
            case 'in':
            case 'not-in':
                if (Array.isArray(right)) {
                    break;
                }
                throw new Error(`Operator '${operator}' can only be used with array values`);
        }
        this.state.item.push({
            type: Types.CONDITIONAL,
            conditional: {left, operator, right}
        });
        return this;
    };

    /**
     * Add event data to run if rule passes or fails
     *
     * @param {number} type - Event type (pass, fail)
     * @param {string|number} name
     * @param {array|undefined} params
     */
    addEvent(type, name, params) {
        this.state.events.get(type).set(name, params || []);
    };

    /**
     * Run event if rule passes
     *
     * @param {string|number} name
     * @param {array|undefined} params
     * @returns {module:RulesEngine.Rule}
     */
    addPassEvent(name, params) {
        this.addEvent(EventTypes.PASS, name, params);
        return this;
    };

    /**
     * Run event if rule fails
     *
     * @param {string|number} name
     * @param {array|undefined} params
     * @returns {module:RulesEngine.Rule}
     */
    addFailEvent(name, params) {
        this.addEvent(EventTypes.FAIL, name, params);
        return this;
    };

    /**
     * Get all events by type
     *
     * @param type
     * @returns {Map.<(string|number), array>}
     */
    getEvents(type) {
        let events = this.state.events.get(type);
        return events !== undefined ? events : new Map;
    };

    /**
     * Set rule priority
     *
     * The lower the number, the earlier the rule is ran
     *
     * @param {number} priority
     * @returns {module:RulesEngine.Rule}
     */
    setPriority(priority) {
        this.state.priority = priority;
        return this;
    };

    /**
     * Evaluate single conditional
     *
     * @param {object} item
     * @param {object} item.conditional
     * @param {module:RulesEngine.Context} context
     * @returns {Promise<boolean>}
     */
    async evaluateConditional(item, context) {
        let conditional = item.conditional,
            sides = {};
        for (let side of ['left', 'right']) {
            let side_type = conditional[side] === null ? 'null' : typeof conditional[side];
            if (side_type === 'function') {
                sides[side] = await conditional[side](context, this);
                continue;
            }
            let side_data = side_type === 'object' ? clone(conditional[side]) : conditional[side];
            if (side === 'left' && side_type === 'string') {
                side_data = await context.get(side_data);
            }
            sides[side] = side_data;
        }
        let negate = includes(['not-equal', 'not-in', 'does-not-contain', 'does-not-match'], conditional.operator),
            result = false;
        switch (conditional.operator) {
            case 'equal':
            case 'not-equal':
                result = (sides.left == sides.right);
                break;
            case 'less-than':
            case 'less-than-inclusive':
            case 'greater-than':
            case 'greater-than-inclusive':
                if (sides.left === null || sides.right === null) {
                    break;
                }
                let methods = {
                    'less-than': 'lessThan',
                    'less-than-inclusive': 'lessThanOrEqualTo',
                    'greater-than': 'greaterThan',
                    'greater-than-inclusive': 'greaterThanOrEqualTo'
                };
                result = Number.ofInput(sides.left)[methods[conditional.operator]](sides.right);
                break;
            case 'in':
            case 'not-in':
                result = includes(sides.right, sides.left);
                break;
            case 'contains':
            case 'does-not-contain':
                let side_type = sides.left === null ? 'null' : typeof sides.left;
                if (side_type === 'string') {
                    result = sides.left.indexOf(sides.right) !== -1;
                    break;
                }
                if (!Array.isArray(sides.left) && side_type !== 'object') {
                    break;
                }
                result = includes(sides.left, sides.right);
                break;
            case 'matches':
            case 'does-not-match':
                let regex = new RegExp(sides.right),
                    match = regex.exec(sides.left);
                if (match === null) {
                    break;
                }
                result = true;
                let matches = {},
                    i = 1;
                while (match[i] !== undefined) {
                    matches[i] = match[i];
                    i++;
                }
                context.set(SUBCONTEXT_MATCH, matches);
                break;
        }
        if (negate) {
            result = !result;
        }
        return result;
    };

    /**
     * Evaluate group of conditionals or nested groups
     *
     * @param {object} item
     * @param {string} item.type
     * @param {array} item.items
     * @param {module:RulesEngine.Context} context
     * @returns {Promise<boolean>}
     */
    async evaluateGroup(item, context) {
        let valid = false;
        for (let sub_item of item.items) {
            let result = false;
            switch (sub_item.type) {
                case Types.GROUP_ANY:
                case Types.GROUP_ALL:
                    result = await this.evaluateGroup(sub_item, context);
                    break;
                case Types.CONDITIONAL:
                    result = await this.evaluateConditional(sub_item, context);
                    break;
            }
            if (result && item.type === Types.GROUP_ANY) {
                return true;
            }
            if (!result && item.type === Types.GROUP_ALL) {
                return false;
            }
            valid = result;
        }
        return valid;
    };

    /**
     * Evaluate rule using context
     *
     * @param {module:RulesEngine.Context} context
     * @returns {Promise<boolean>}
     */
    evaluate(context) {
        context.set(SUBCONTEXT_MATCH, null);
        context.setSubcontext('rule-match', SUBCONTEXT_MATCH);
        return this.evaluateGroup({
            type: Types.GROUP_ANY,
            items: this.state.items
        }, context);
    };
}
