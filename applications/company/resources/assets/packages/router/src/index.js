/**
 * @module Router
 */

'use strict';

const $ = require('jquery');
const pagejs = require('page');
const qs = require('qs');
const escapeRegex = require('lodash/escapeRegExp');

const Route = require('./route');
const Page = require('./page');
const Modal = require('./modal');
const Callback = require('./callback');

/**
 * @typedef {object} Route
 * @property {number} type
 * @property {string} path
 * @property {string|RegExp} parsed_path
 * @property {number} segment_count
 * @property {array} id
 * @property {string} name
 * @property {string[]} bindings
 * @property {undefined|[number, string][]} binding_idx
 */

/**
 * @typedef {object} Request
 * @property {object} params - input params defined in route
 * @property {object} query - query string values
 * @property {object} data - object of data shared between requests
 */

/**
 * @typedef {object} RouteConfig
 * @property {string} name - route name
 * @property {module:Router.Route} instance
 * @property {Route} route
 * @property {module:Router.Route[]} route_instances - list of routes loaded
 * @property {Request} request
 * @property {boolean} is_initial - if true, signifies the route was first to be loaded on the page
 */

const RouteTypes = {
    STATIC: 1,
    REGEXP: 2
};

/**
 * @memberof module:Router
 * @mixes Observable
 */
class Controller {
    /**
     * Constructor
     *
     * @param {typeof Router.Route} main_route
     * @param {object} [config={}]
     */
    constructor(main_route, config = {}) {
        this.state = {
            main_route,
            main_route_callback: config.main_route_callback,
            base_path: config.base_path,
            routes: null,
            id_map: null,
            current: null,
            main_route_instance: null,
            before_unload_check: true
        };
        this.elem = {};
    };

    /**
     * Pull route class from routes config
     *
     * @param {object} config
     * @returns {typeof module:Router.Route}
     */
    getRouteClassFromConfig(config) {
        return config.route || config.page || config.modal;
    };

    /**
     * Properly initialize route instance based on type
     *
     * @param {module:Router.Route} instance
     * @param {module:Router.Page|null} [parent=null]
     * @returns {module:Router.Page|module:Router.Modal}
     */
    initRoute(instance, parent = null) {
        if (instance instanceof Page) {
            let elem = $(instance.render()),
                container = parent !== null ? parent.getPageContainer() : this.elem.root;
            container.append(elem);
            instance.boot(elem);
        } else if (instance instanceof Modal) {
            instance.boot();
        }
        return instance;
    };

    /**
     * Create, render, and boot route instance
     *
     * @param {typeof module:Router.Route|function} route
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     * @returns {module:Router.Page|module:Router.Modal}
     */
    createRoute(route, name, parent = null) {
        let instance;
        if (route.prototype instanceof Route) {
            instance = new route(this, name, parent);
        } else {
            instance = new Callback(route, this, name, parent);
        }
        return this.initRoute(instance, parent);
    };

    /**
     * Get main route instance
     *
     * @returns {module:Router.Route}
     */
    get main_route() {
        if (this.state.main_route_instance === null) {
            let instance = new this.state.main_route(this, 'main');
            if (this.state.main_route_callback) {
                instance = this.state.main_route_callback(instance);
            }
            this.state.main_route_instance = this.initRoute(instance);
        }
        return this.state.main_route_instance;
    };

    /**
     * Get current route data
     *
     * @returns {null|RouteConfig}
     */
    get current_route() {
        return this.state.current;
    };

    /**
     * Clear slashes from beginning and end of path
     *
     * @param {string} path
     * @returns {string}
     */
    clearSlashes(path) {
        return path.toString().replace(/\/$/, '').replace(/^\//, '');
    };

    /**
     * Parse route classes to build available routes
     *
     * @param {typeof module:Router.Route} route
     * @param {Route} prev_route
     * @returns {Route[]}
     */
    fetchRoutes(route, prev_route) {
        let routes = [],
            has_routes = false,
            has_default = false;
        if (route.prototype instanceof Page) {
            let page_routes = route.routes,
                route_names = Object.keys(page_routes);
            has_routes = route_names.length > 0;
            if (has_routes) {
                route_names.forEach(route_name => {
                    let route_config = page_routes[route_name];
                    if (route_config.default) {
                        if (has_default) {
                            throw new Error('Only one default route allowed per group');
                        }
                        has_default = true;
                    }
                    let route_path = prev_route.path.replace(/{_se_}/g, '') + (route_config.default ? '{_se_}' : `/${this.clearSlashes(route_config.path)}`),
                        segment_count = route_config.default ? 0 : (route_path.match(/\//g) || []).length,
                        id = [...prev_route.id, route_name];
                    routes = routes.concat(this.fetchRoutes(this.getRouteClassFromConfig(route_config), {
                        path: route_path,
                        segment_count: prev_route.segment_count + segment_count,
                        id,
                        name: id.slice(1).join('.'),
                        bindings: Object.assign({}, prev_route.bindings || {}, route_config.bindings || {})
                    }));
                });
            }
        }
        // if the route class doesn't have any routes defined, we just use the previous route for matching
        // if a page has routes, but one isn't defined as default this means they want the previous route to be the
        // main match for the page
        if (!has_routes || !has_default) {
            routes.push(prev_route);
        }
        return routes;
    };

    /**
     * Get list of route instances based on id name list
     *
     * @param {string[]} id - array of route names
     * @returns {module:Router.Route[]}
     */
    getRouteInstancesFromId(id) {
        let instances = [],
            instance = null;
        for (let name of id) {
            instance = instance === null ? this.main_route : instance.getRoute(name);
            instances.push(instance);
        }
        return instances;
    };

    /**
     * Run action for route using middleware pattern
     *
     * @param {module:Router.Route[]} routes
     * @param {string} action
     * @param args
     * @returns {Promise<void>}
     */
    async runRouteAction(routes, action, ...args) {
        routes = [...routes];
        let next = async (...args) => {
            if (routes.length === 0) {
                return;
            }
            let route = routes.pop();
            await route[action](...args, next);
        };
        await next(...args);
    };

    /**
     * Handle matched route from pagejs
     *
     * @param {Route} route
     * @param {object} context
     */
    handleMatchedRoute(route, context) {
        let previous = this.state.current,
            request = {
                params: {},
                query: Object.assign({}, context.query),
                data: previous !== null ? previous.request.data : {}
            };
        if (route.binding_idx) {
            for (let [idx, name] of route.binding_idx) {
                request.params[name] = context.params[idx] || null;
            }
        }

        // if the same route is loaded, we just immediately call refresh and update current with new request data
        if (this.state.current !== null && this.state.current.route === route) {
            Object.assign(this.state.current, {
                request,
                is_initial: false
            });
            this.state.current.instance.refresh(request);
            return;
        }

        let route_instances = this.getRouteInstancesFromId(route.id),
            instance = route_instances[route_instances.length - 1],
            load_routes = [],
            is_initial = this.state.current === null;
        if (is_initial) {
            load_routes = [...route_instances];
        } else {
            let prev_path = [...this.state.current.route.id],
                curr_path = [...route.id],
                load_count = 0,
                unload_count = 0;
            while (prev_path.length > 0 || curr_path.length > 0) {
                let prev = prev_path[0],
                    curr = curr_path[0];
                if (prev === curr) {
                    prev_path.shift();
                    curr_path.shift();
                    continue;
                }
                unload_count = prev_path.length;
                load_count = curr_path.length;
                break;
            }
            if (unload_count > 0) {
                let unload_routes = this.state.current.route_instances.slice(-unload_count).reverse();
                for (let unload_route of unload_routes) {
                    unload_route.beforeUnload(this.state.current.request);
                }
                this.runRouteAction(unload_routes, 'unload', this.state.current.request).catch((e) => console.error('route unload', e));
            }
            if (load_count > 0) {
                load_routes = route_instances.slice(-load_count);
            }
        }

        this.state.current = {name: route.name, route, instance, route_instances, request, is_initial};

        this.notify('route-changed', {
            previous,
            current: this.state.current
        });

        // if no routes are needing to be loaded, it means we navigated to different version of the same route, so we
        // call refresh on the current route instance because an unload and load is not necessary
        if (load_routes.length === 0) {
            this.state.current.instance.refresh(request);
            return;
        }
        // @todo use log package to log the exceptions
        this.runRouteAction(load_routes, 'load', request).catch((e) => console.error('route load', e));
    };

    /**
     * Load routes into pagejs library
     *
     * @param {Route[]} routes
     * @returns {{routes: Route[], id_map: object}}
     */
    loadRoutes(routes) {
        routes.sort(({segment_count: a_count}, {segment_count: b_count}) => b_count - a_count);
        let id_map = {},
            binding_defaults = {
                segment_end: '/?',
                int: '([0-9]+)',
                slug: '([a-z0-9\-]+)',
                uuid: '([a-zA-Z0-9\-]{32,36})'
            };
        for (let idx in routes) {
            let route = routes[idx],
                type = RouteTypes.STATIC,
                path = route.path;
            if (path.indexOf('{') !== -1 && path.indexOf('}') !== -1) {
                let i = 0,
                    binding_idx = [];
                path = path.replace(/{([a-z_0-9]+)(\?)?}/g, (match, name, optional) => {
                    let replacement = `@${i}@`;
                    binding_idx.push([i, name]);
                    i++;
                    return replacement;
                });
                path = `^${escapeRegex(path)}$`;
                for (let [i, name] of binding_idx) {
                    let binding = name === '_se_' ? 'segment_end' : route.bindings[name];
                    if (binding === undefined) {
                        throw new Error(`Unable to find binding value for ${name} in route: ${id}`);
                    }
                    if (binding_defaults[binding] !== undefined) {
                        binding = binding_defaults[binding];
                    }
                    path = path.replace(`@${i}@`, binding);
                }
                route.binding_idx = binding_idx;
                path = new RegExp(path);
                type = RouteTypes.REGEXP;
            }
            route.parsed_path = path;
            route.type = type;

            id_map[route.name] = idx;

            pagejs(route.parsed_path, (ctx) => {
                this.handleMatchedRoute(route, ctx);
            });
        }
        pagejs('*', () => {
            pagejs.redirect('/');
        });
        return {routes, id_map};
    };

    /**
     * Get route data by id
     *
     * @param {string} id
     * @returns {Route}
     */
    getRouteById(id) {
        let route_idx = this.state.id_map[id];
        if (route_idx === undefined) {
            throw new Error(`Unable to find route with id: ${id}`);
        }
        return this.state.routes[route_idx];
    };

    /**
     * Replace param names with their respective values in path
     *
     * Looks for {<key>} within path string and replaces it with the associated value.
     *
     * @protected
     * @param {string} path
     * @param {object} params
     * @returns {string}
     */
    interpolatePath(path, params) {
        for (let [name, value] of Object.entries(params)) {
            path = path.replace(`{${name}}`, value);
        }
        return path;
    };

    /**
     * Append query string to end of path
     *
     * @private
     * @param {string} path
     * @param {object} query
     * @returns {string}
     */
    addQueryString(path, query) {
        return `${path}?${qs.stringify(query, {encodeValuesOnly: true})}`;
    };

    /**
     * Build path from route id
     *
     * Finds route data associated with id, replaces any dynamic params in path, and optionally appends query data.
     *
     * @private
     * @param {Route} route
     * @param {object|null} [params={}]
     * @param {object|null} [query=null]
     * @returns {string}
     */
    buildPathFromRoute(route, params = {}, query = null) {
        let path_params = {_se_: '/'};
        if (params !== null) {
            path_params = Object.assign({}, this.state.current.request.params, params, path_params);
        }
        let path = this.interpolatePath(route.path, path_params);
        if (query !== null) {
            path = this.addQueryString(path, query);
        }
        return path;
    };

    /**
     * Get page URL to specified route
     *
     * @param {string} route_id
     * @param {object} [params={}]
     * @param {object|null} [query=null]
     */
    getPageUrl(route_id, params = {}, query = null) {
        return this.buildPathFromRoute(this.getRouteById(route_id), params, query);
    };

    /**
     * Get URL to specified route (including base path)
     *
     * @param {string} route_id
     * @param {object} [params={}]
     * @param {object|null} [query=null]
     */
    getUrl(route_id, params = {}, query = null) {
        return this.state.base_path + this.getPageUrl(route_id, params, query);
    };

    /**
     * Navigate to specified route, updating history
     *
     * This allows user to hit back button to come back to previous route.
     *
     * @param {string} route_id
     * @param {object} [params={}]
     * @param {object|null} [query=null]
     */
    navigate(route_id, params = {}, query = null) {
        pagejs(this.getPageUrl(route_id, params, query));
    };

    /**
     * Redirect to specified route, replacing history
     *
     * The last page in the browser history will be replaced with the new route, making it so the user cannot navigate
     * back to the last page.
     *
     * @param {string} route_id
     * @param {object} [params={}]
     * @param {object|null} [query=null]
     */
    redirect(route_id, params = {}, query = null) {
        pagejs.redirect(this.getPageUrl(route_id, params, query));
    };

    /**
     * Redirect to URL outside of module
     *
     * @param {string} url
     * @param {object|null} [params=null]
     * @param {object|null} [query=null]
     * @param {boolean} [force=false] - if true, beforeunload checks will be disabled
     */
    externalRedirect(url, params = null, query = null, force = false) {
        if (params !== null) {
            url = this.interpolatePath(url, params);
        }
        if (query !== null) {
            url = this.addQueryString(url, query);
        }
        if (force) {
            this.state.before_unload_check = false;
        }
        window.location.href = url;
    };

    /**
     * Forcefully redirect to external URL bypassing beforeunload checks
     *
     * @param {string} url
     * @param {object|null} [params=null]
     * @param {object|null} [query=null]
     */
    forceExternalRedirect(url, params = null, query = null) {
        return this.externalRedirect(url, params, query, true);
    };

    /**
     * Determines if current page can be unloaded based on current route rules
     *
     * Loops through all the loaded routes and asks if we can allow the user to leave the page. If all say yes, it
     * proceeds, otherwise the default functionality of the 'beforeunload' window event takes over.
     *
     * @returns {boolean}
     */
    allowUnload() {
        if (this.state.current !== null) {
            for (let route of [...this.state.current.route_instances].reverse()) {
                if (route.allowUnload()) {
                    continue;
                }
                return false;
            }
        }
        return true;
    };

    /**
     * Boot router
     *
     * @param {jQuery} root
     */
    boot(root) {
        this.elem.root = root;

        let routes = this.fetchRoutes(this.state.main_route, {
            path: '{_se_}',
            segment_count: 0,
            id: ['main'],
            name: 'main',
            bindings: {}
        });

        if (this.state.base_path) {
            pagejs.base(this.state.base_path);
        }
        pagejs('*', (ctx, next) => {
            ctx.query = qs.parse(ctx.querystring);
            next();
        });
        let info = this.loadRoutes(routes);
        this.state.routes = info.routes;
        this.state.id_map = info.id_map;
        pagejs.start({
            click: false
        });

        window.addEventListener('beforeunload', (e) => {
            if (
                !this.state.before_unload_check ||
                this.state.current === null ||
                this.allowUnload()
            ) {
                delete e['returnValue'];
                return undefined;
            }
            e.preventDefault();
            let text = 'Changes may be lost if you leave the page';
            e.returnValue = text;
            return text;
        });

        root.on('click', '[data-route]', function (e) {
            e.preventDefault();
            pagejs($(this).data('route'));
            return false;
        });
        const that = this;
        root.on('click', '[data-navigate]', function (e) {
            e.preventDefault();
            let $this = $(this),
                route = that.getRouteById($this.attr('data-navigate')),
                params = {};
            for (let param of Object.keys(route.bindings)) {
                let value = $this.attr(`data-${param}`);
                if (value === undefined) {
                    continue;
                }
                params[param] = value;
            }
            pagejs(that.buildPathFromRoute(route, params));
        });
    };
}

require('@cac-js/mixins/observable')(Controller);

module.exports = Controller;
