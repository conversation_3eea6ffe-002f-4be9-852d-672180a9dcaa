'use strict';

const Route = require('./route');

/**
 * @memberof module:Router
 */
class Callback extends Route {
    /**
     * Constructor
     *
     * @param {function} callback
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(callback, router, name, parent = null) {
        super(router, name, parent);
        this.properties.callback = callback;
    };

    /**
     * Load route
     *
     * Calls supplied callback with request and next to give the user control via a simple callback, but still give the
     * power of a route class. Also simplifies the router code to not check for functions everywhere.
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await this.properties.callback(request, next, this);
    };
}

module.exports = Callback;
