'use strict';

import EventEmitter from 'events';

import responsive from '@cac-js/data/responsive.json';

const Operators = ['>=', '>', '<=', '<', '≥', '≤'];

/**
 * Media query builder
 *
 * @memberof module:MediaQuery
 */
export class Query {
    /**
     * Constructor
     *
     * @param expressions
     */
    constructor(...expressions) {
        if (Array.isArray(expressions[0])) {
            expressions = expressions[0];
        }
        this.state = {
            events: null,
            query: this.buildQuery(expressions),
            event_callback: this.handleChange.bind(this),
            legacy: false
        };
        this.state.list = window.matchMedia(this.state.query);
        try {
            this.state.list.addEventListener('change', this.state.event_callback);
        } catch (e) {
            this.state.legacy = true;
            this.state.list.addListener(this.state.event_callback);
        }
    };

    /**
     * Get associated media query
     *
     * @returns {string}
     */
    get query() {
        return this.state.query;
    };

    /**
     * Get event handler
     *
     * @returns {NodeJS.EventEmitter}
     */
    get events() {
        if (this.state.events === null) {
            this.state.events = new EventEmitter();
        }
        return this.state.events;
    };

    /**
     * Determines if media query is currently matched
     *
     * @returns {boolean}
     */
    get matches() {
        return this.state.list.matches;
    };

    /**
     * Emit event is one is bound
     *
     * @protected
     * @param {string} name
     * @param {object} data
     */
    emit(name, data) {
        if (this.state.events === null) {
            return;
        }
        this.state.events.emit(name, data);
    };

    /**
     * Handle match media change
     *
     * @param {MediaQueryListEvent} e
     */
    handleChange(e) {
        this.emit('changed', {
            matches: e.matches
        });
    };

    /**
     * Get operator from expression
     *
     * @param {string} expression
     * @returns {string}
     */
    getOperator(expression) {
        for (let operator of Operators) {
            let index = expression.indexOf(operator);
            if (index === -1) {
                continue;
            }
            return operator;
        }
        throw new Error(`Unable to find operator in expression: ${expression}`);
    };

    /**
     * Parse individual expression into an operator, dimension, and value
     *
     * @param {string} expression
     * @returns {string}
     */
    parseExpression(expression) {
        if (responsive.expressions[expression] !== undefined) {
            return responsive.expressions[expression];
        }
        let dimension,
            value,
            operator = this.getOperator(expression);
        [dimension, value] = expression.split(operator, 2);
        if (dimension === '') {
            dimension = 'width';
        }
        let prefix = ['<', '<=', '≤'].indexOf(operator) !== -1 ? 'max' : 'min';
        if (responsive.breakpoints[value] !== undefined) {
            value = responsive.breakpoints[value];
        } else {
            value = parseInt(value.replace('[^0-9]', ''));
        }
        if (operator === '>') {
            value++;
        } else if (operator === '<') {
            value--;
        }
        return `(${prefix}-${dimension}: ${value}px)`;
    };

    /**
     * Flatten expressions into single nested media query
     *
     * @param {array} expressions
     * @returns {[][]}
     */
    flattenExpressions(expressions) {
        let parts = [],
            nested = null;
        for (let i = 0; i < expressions.length; i++) {
            let expression = expressions[i];
            if (!Array.isArray(expression)) {
                parts.push(expression);
                continue;
            }
            let remaining = expressions.slice(i + 1);
            nested = [];
            for (let part of expression) {
                nested.push(this.flattenExpressions([part].concat(remaining)));
            }
            break;
        }
        if (nested === null) {
            return [parts];
        }
        return nested.map(expressions => {
            return parts.slice(0).concat(expressions);
        });
    };

    /**
     * Build query from list of expressions
     *
     * @param {array} expressions
     * @returns {string}
     */
    buildQuery(expressions) {
        expressions = expressions.map(expr => this.parseExpression(expr));
        let queries = this.flattenExpressions(expressions).map(query => query.join(' and '));
        return queries.join(', ');
    };

    /**
     * Destroy media query watcher
     */
    destroy() {
        if (!this.state.legacy) {
            this.state.list.removeEventListener('change', this.state.event_callback);
        } else {
            this.state.list.removeListener(this.state.event_callback);
        }
    };
}
