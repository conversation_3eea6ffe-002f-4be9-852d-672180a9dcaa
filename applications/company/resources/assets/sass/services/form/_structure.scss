@use 'sass:math';

.m-form-structure {
    page-break-inside: avoid;
    margin-top: 20px;
    .c-fs-name {
        margin-bottom: 8px;
    }
    .c-fs-group {}
        .c-fsg-name {
            font-size: 12pt;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .c-fsg-grid {
            width: 99%;
            .c-fsg-grid {
                width: 100%;
            }
        }
            .c-fsgg-row {
                position: relative;
                width: 100%;
                padding: 0 0 1rem;
                &::after {
                    content: "";
                    display: table;
                    clear: both;
                }
                [class*="c-frggr-column"] {
                    padding-right: 20px;
                    &:last-of-type {
                        padding-right: 0;
                    }
                }
                &:last-child {
                    padding: 0;
                }
            }
                .c-fsggr-column {
                    float: left;
                    $grid-columns: 12;
                    @for $i from 1 through $grid-columns {
                        &.t-size-#{$i} {
                            width: percentage(math.div($i, $grid-columns));
                        }
                    }
                }
                    .c-fsggrc-field {
                        &.t-no-value {
                            .c-fsggrcf-value {
                                font-style: italic;
                            }
                        }
                        &.t-layout-table-row {
                            .c-fsggrcf-label {
                                display: none;
                            }
                        }
                        &.t-textarea {
                            .c-fsggrcf-value {
                                padding-right: 1%;
                            }
                        }
                        &.t-file {
                            .c-fsggrcf-value {
                                margin: 0 -5px -5px 0;
                                &::before,
                                &::after {
                                    display: table;
                                    content: ' ';
                                    clear: both;
                                }
                            }
                        }
                    }
                        .c-fsggrcf-label {
                            margin-bottom: 5px;
                            border-bottom: 1px #ccc solid;
                        }
                        .c-fsggrcf-value {
                            font-size: 10pt;
                            &::before,
                            &::after {
                                display: table;
                                content: ' ';
                                clear: both;
                            }
                        }
                            .c-fsggrcfv-product-name {

                            }
                            .c-fsggrcfv-product-desc {
                                margin: 6px 0 0;
                                font-size: 9pt;
                                font-style: italic;
                            }
                            .c-fsggrcfv-file {
                                display: block;
                                float: left;
                                width: 30px;
                                height: 30px;
                                margin: 0 5px 5px 0;
                                > img {
                                    display: block;
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                    .c-fsggrc-template {
                        %header {
                            margin: 0 0 8px;
                            font-weight: bold;
                        }
                        h1 {
                            font-size: 18px;
                            @extend %header;
                        }
                        h2 {
                            font-size: 16px;
                            @extend %header;
                        }
                        h3 {
                            font-size: 14px;
                            @extend %header;
                        }
                        h4, h5, h6 {
                            font-size: 16px;
                            @extend %header;
                        }
                        p:last-child {
                            margin-bottom: 0;
                        }
                    }
    .c-fs-repeatable-table {
        width: 100%;
        border-collapse: collapse;
        thead {
            display: table-header-group;
        }
        tfoot {
            display: table-row-group;
        }
        tr {
            page-break-inside: avoid;
        }
        %padding {
            padding: 5px;
        }
        th {
            @extend %padding;
            text-align: left;
            background-color: lightgrey;
        }
        td {
            @extend %padding;
            border-top: 1px solid #ccc;
        }
        $grid-columns: 12;
        @for $i from 1 through $grid-columns {
            .t-size-#{$i} {
                width: percentage(math.div($i, $grid-columns));
            }
        }
    }
}
