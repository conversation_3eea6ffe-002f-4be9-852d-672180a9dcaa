'use strict';

let iframe_api = null;

/**
 * Get YouTube iframe API instance
 *
 * Ensures script tag is grabbed only once and returns a singular instance.
 *
 * @returns {Promise<YT>}
 */
export function getIframeApi() {
    if (iframe_api === null) {
        iframe_api = new Promise(resolve => {
            let tag = document.createElement('script');
            tag.src = 'https://www.youtube.com/iframe_api';
            let first_tag = document.getElementsByTagName('script')[0];
            first_tag.parentNode.insertBefore(tag, first_tag);

            window.onYouTubeIframeAPIReady = () => {
                resolve(window.YT);
            };
        });
    }
    return iframe_api;
}

/**
 * Get player instance
 *
 * @param {HTMLElement|string} elem
 * @param {Object} config
 * @returns {Promise<YT.Player>}
 */
export async function getPlayer(elem, config) {
    const YT = await getIframeApi();
    return new YT.Player(elem, config);
}
