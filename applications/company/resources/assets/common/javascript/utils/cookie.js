'use strict';

/**
 * Expiration Type
 *
 * @type {{days: number, time: number}}
 */
const ExpirationTypes = {
    hours: 1,
    days: 2
}

export function expirationTypes() {
    return {
        Hours: ExpirationTypes.hours,
        Days: ExpirationTypes.days,
    };
}

/**
 * Set cookie
 *
 * @param {string} name
 * @param {string} value
 * @param {number} expiration_type
 * @param {number} expiration_length
 *
 * @returns {`${string}=${string}${string}`}
 */
export function setCookie(name, value, expiration_type, expiration_length) {
    let expires = '';
    if (expiration_length) {
        let date = new Date();
        switch(expiration_type) {
            case ExpirationTypes.days:
                date.setTime(date.getTime() + (expiration_length*24*60*60*1000));
                expires = "; expires=" + date.toUTCString();
                break;
            case ExpirationTypes.hours:
                date.setTime(date.getTime() + (expiration_length * 3600 * 1000));
                expires = "; expires=" + date.toUTCString();
                break;
        }
    }
    return window.document.cookie = `${name}=${value}${expires}`;
}

export function getCookie(name) {
    var name_equals = `${name}=`;
    var existing_cookies = document.cookie.split(';');
    for (let i=0; i < existing_cookies.length; i++) {
        let this_cookie = existing_cookies[i];
        while (this_cookie.charAt(0)===' ') this_cookie = this_cookie.substring(1,this_cookie.length);
        if (this_cookie.indexOf(name_equals) === 0) {
            return this_cookie.substring(name_equals.length,this_cookie.length);
        }
    }
    return null;
}