@use '../config/header';
@use 'functions/unit';
@use 'svg';

%header-base {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: unit.rem-calc(16px);
    border-bottom: unit.rem-calc(header.$bottom-border-height) solid header.$bottom-border-color;
}
%header-text-button {
    padding: unit.rem-calc(12px);
    > [data-title] {
        flex: 1;
        height: header.$title-height;
        margin-bottom: 0;
    }
    > [data-button] {
        flex: 0 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: unit.rem-calc(16px);
    }
}
%header-button-text-button {
    padding: unit.rem-calc(12px);
    > [data-button-left] {
        flex: 0 0 auto;
        order: 1;
    }
    > [data-title] {
        flex: 1;
        height: header.$title-height;
        padding-left: unit.rem-calc(4px);
    }
    > [data-button-right] {
        flex: 0 0 auto;
        order: 3;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: unit.rem-calc(16px);
    }
}

@mixin text-icon {
    @extend %header-base;
    @extend %header-text-button;
}

@mixin icon-text-icon {
    @extend %header-base;
    @extend %header-button-text-button;
}
