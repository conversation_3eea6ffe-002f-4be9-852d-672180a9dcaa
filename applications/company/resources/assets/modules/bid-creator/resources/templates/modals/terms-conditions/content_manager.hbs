<div class="m-terms-conditions-content-manager-modal">
    <div class="c-tccmm-header">
        <h1 class="c-tccmmh-text">Manage {{title.plural}}</h1>
        <a class="c-tccmmh-close" data-close>
            <svg class="c-tccmmhc-icon"><use xlink:href="#remix-icon--system--close-line"></use></svg>
        </a>
    </div>
    <div class="c-tccmm-messages">{{{messages}}}</div>
    <div class="c-tccmm-content">
        <h2 class="c-tccmmc-header">Available {{title.plural}}</h2>
        <div class="c-tccmmc-content">
            <div class="c-tccmmcc-message" data-js="available-message"></div>
            <div class="c-tccmmcc-accordion" data-js="accordion-items"></div>
        </div>
        <h2 class="c-tccmmc-header">Selected {{title.plural}}</h2>
        <div class="c-tccmmc-content">
            <div class="c-tccmmcc-message" data-js="selected-message">Select an item from above or use the button to the right to add your own</div>
            <div class="c-tccmmcc-selected" data-js="selected-items"></div>
            <div class="c-tccmmcc-container">
                <a class="c-tccmmccc-custom-add" data-js="custom-add">
                    <span data-text>Add Custom {{title.singular}}</span>
                    <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                </a>
            </div>
        </div>
    </div>
    <div class="c-tccmm-footer">
        <div class="c-tccmmf-actions" data-js="modal-actions">
            <div class="c-tccmmfa-working" data-js="modal-working"></div>
        </div>
    </div>
</div>
