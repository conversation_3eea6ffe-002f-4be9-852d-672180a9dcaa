/**
 * @module BidCreator/Section/Form
 */

'use strict';

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const FormController = require('@ca-submodule/form').Controller;

const DeleteModal = require('../modals/section/form/delete');
const FlashMessage = require('../flash-message');
const PubSub = require('../pubsub');

const form_tpl = require('@cam-bid-creator-tpl/sections/form.hbs');

/**
 * @alias module:BidCreator/Section/Form
 */
class Form {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Section} section
     * @param {Object} data
     * @param {string} data.id - Form uuid
     * @param {number} data.order - Form order
     * @param {Object} data.formatted_form - Form data needed to drive form module
     * @param {Object} data.formatted_form.structure - Raw form structure
     * @param {Object} data.formatted_form.info - User specific data used by structure
     * @param {Object} data.formatted_entry - Form entry data needed for form module
     */
    constructor(section, data) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            rendered: false,
            booted: false,
            section: section,
            id: data.id,
            form_id: data.formatted_form.id,
            name: data.formatted_form.name,
            order: data.order,
            files: [],
            messages: null
        };

        let form = new FormController();
        form.store('bid_form', this);
        this.state.form = form;

        // add queue to bid save handler, assign shared line item and product item helpers
        let bid = section.bid;
        form.save_queue = bid.save_handler.queue;
        form.line_item_helper = bid.line_item_helper;
        form.product_item_helper = bid.product_item_helper;

        // initially loaded files have to be cached since the section hasn't been published to external modules yet,
        // this is due to the form being fully rendered, then section form, then section, then published
        // @todo add jsdoc for events from form module
        form.on('file-loaded', (file) => {
            this.state.files.push(file);
        });
        form.on('file-added', (file) => {
            PubSub.Handler.publish(PubSub.Topics.Section.Form.FILE_ADD, {
                form: this,
                file: file
            });
        });

        form.load(data.formatted_form.structure, data.formatted_form.info, data.formatted_entry);
    };

    /**
     * Get delete modal instance
     *
     * If modal doesn't exist, it will be created and cached for future calls
     *
     * @readonly
     *
     * @returns {Delete}
     */
    static get delete_modal() {
        if (this._delete_modal === undefined) {
            this._delete_modal = new DeleteModal;
        }
        return this._delete_modal;
    };

    /**
     * Get section
     *
     * @returns {module:BidCreator/Section}
     */
    get section() {
        return this.state.section;
    };

    /**
     * Get form id
     *
     * @readonly
     *
     * @returns {string}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get internal form id
     *
     * @readonly
     *
     * @returns {string}
     */
    get form_id() {
        return this.state.form_id;
    };

    /**
     * Get name
     *
     * Note: currently pulls from nested form module since these forms aren't named currently. this could change in
     *       the future
     *
     * @returns {string}
     */
    get name() {
        return this.form_name;
    };

    /**
     * Get name of associated form
     *
     * @returns {string}
     */
    get form_name() {
        return this.state.name;
    };

    /**
     * Get form order
     *
     * @returns {number}
     */
    get order() {
        return this.state.order;
    };

    /**
     * Set form order
     *
     * @param {number} value
     */
    set order(value) {
        this.state.order = value;
    };

    /**
     * Get files
     *
     * @readonly
     *
     * @returns {Array}
     */
    get files() {
        return this.state.files;
    };

    /**
     * Get messages instance
     *
     * Will create one if it doesn't exist already
     *
     * @returns {FlashMessage.Controller}
     */
    getMessages() {
        if (this.state.messages === null) {
            let messages = new FlashMessage.Controller;
            this.elem.messages = this.elem.root.fxFind('messages');
            this.elem.messages.append(messages.render());
            messages.boot(this.elem.messages);
            this.state.messages = messages;
        }
        return this.state.messages;
    };

    /**
     * Clear all flash messages if one has already been defined
     */
    clearMessages() {
        if (this.state.messages === null) {
            return;
        }
        this.state.messages.deleteAllMessages();
    };

    /**
     * Get queue request for deletion
     *
     * @returns {Api.BatchRequest.Single}
     */
    getDeleteApiRequest() {
        return new Api.BatchRequest.Single('bid-item-section-form', 'delete', {
            id: this.state.id
        });
    };

    /**
     * Start delete process by opening confirmation modal
     */
    startDelete() {
        Form.delete_modal.open(this);
    };

    /**
     * Send delete request to server and remove from DOM if successful
     *
     * If removing a form in the middle of the section, then a batch request will be sent which contains the new
     * order for all surrounding forms. After which the section will reorder forms in the DOM.
     *
     * Note: this method is meant for one-off form deletes and shouldn't be used in any bulk operation, much more efficient
     *       means will be needed for that
     *
     * @returns {Promise}
     */
    delete() {
        return new Promise((resolve, reject) => {
            let request = this.getDeleteApiRequest();
            let forms = Array.from(this.section.forms.keys());
            let index = forms.indexOf(this.state.id);
            forms.splice(index, 1);
            let reorder_request = this.section.getReorderBatchRequest(forms);
            // if no reorder requests are needed, then we just send the single delete request
            if (reorder_request === false) {
                request.promise.then(() => {
                    this.destroy();
                    resolve();
                }, (result) => {
                    reject(result);
                });
                PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
                return;
            }
            // otherwise, we send an atomic batch request with the delete and form reorders, which upon success will
            // destroy this form and reorder the section forms
            let batch_request = new Api.BatchRequest.Multiple(true);
            batch_request.add(request);
            batch_request.merge(reorder_request);
            batch_request.promise.then(() => {
                this.destroy();
                this.section.setFormOrder(forms);
                resolve();
            }, (result) => {
                reject(result);
            });
            PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, batch_request);
        });
    };

    /**
     * Destroy form and associated form module, remove from DOM
     */
    destroy() {
        this.section.deleteForm(this.state.id);
        this.state.form.destroy();
        this.elem.root.remove();
    };

    /**
     * Trigger resize of form and nested form instance
     */
    resize() {
        this.state.form.resize();
    };

    /**
     * Determines if any form components have incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        return this.state.form.hasIncompleteUploads();
    };

    /**
     * Validate form and any child components
     *
     * @returns {Promise}
     */
    validate() {
        return new Promise((resolve, reject) => {
            this.clearMessages();
            let valid = true;
            if (this.hasIncompleteUploads()) {
                let message = FlashMessage.Message.make('One or more uploads are still in progress', FlashMessage.Message.Type.ERROR);
                this.getMessages().addMessage(message);
                valid = false;
            }
            if (!this.state.form.isValid()) {
                let message = FlashMessage.Message.make('Validation errors were found within form', FlashMessage.Message.Type.ERROR);
                this.getMessages().addMessage(message);
                valid = false;
            }
            if (valid) {
                resolve({
                    valid: true
                });
                return;
            }
            reject({
                valid: false
            });
        });
    };

    /**
     * Boot form and associated form module
     */
    async boot() {
        if (!this.state.rendered) {
            throw new Error('Cannot boot that which has not been rendered');
        }

        this.elem.root = this.section.elem.forms.fxFind('form', {id: this.state.id});

        await this.state.form.boot();

        this.state.booted = true;
    };

    /**
     * Render form
     *
     * @returns {string}
     */
    render() {
        this.state.rendered = true;
        return form_tpl({
            id: this.state.id,
            name: this.name,
            form: this.state.form.render()
        });
    };
}

module.exports = Form;
