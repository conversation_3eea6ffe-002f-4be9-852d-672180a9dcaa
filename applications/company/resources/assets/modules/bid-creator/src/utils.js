/**
 * @module BidCreator/Utils
 */

'use strict';

const accounting = require('accounting');
const lang = require('lodash/lang');
const Decimal = require('decimal.js');

/**
 * @alias module:BidCreator/Utils
 */
class Utils {
    /**
     * Build function which automatically debounces calls via a timer
     *
     * @param {function} func - Function to call when debounce finishes
     * @param {number} wait - Milliseconds to wait
     * @param {boolean} [immediate=false]
     * @returns {Function}
     */
    static debounce(func, wait, immediate = false) {
        let timeout;
        return function() {
            let context = this, args = arguments;
            let later = function() {
                timeout = null;
                if (!immediate) {
                    func.apply(context, args);
                }
            };
            let call_now = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (call_now) {
                func.apply(context, args);
            }
        };
    };

    /**
     * Get Decimal instance
     *
     * Removes comma from formatted numeric strings to prevent errors from being thrown. If a invalid value is passed,
     * the default value will be returned
     *
     * @param {*} value
     * @param {*} default_value
     * @returns {*}
     */
    static getDecimal(value, default_value = null) {
        if (typeof value === 'string') {
            value = value.replace(/,/g, '');
        }
        try {
            value = new Decimal(value);
        } catch (e) {
            console.log(e);
            value = default_value;
        }
        return value;
    };

    /**
     * Get absolute Decimal
     *
     * @param {*} value
     * @param {*} default_value
     * @returns {*}
     */
    static getAbsDecimal(value, default_value = null) {
        let decimal = this.getDecimal(value, null);
        if (decimal === null) {
            return default_value;
        }
        return decimal.abs();
    };

    /**
     * Get Decimal from input
     *
     * Will always return a decimal, if null or an empty string is passed 0 will be used
     *
     * @param {*} value
     * @returns {Decimal}
     */
    static getDecimalFromInput(value) {
        if (lang.isString(value)) {
            value = value.replace(/,/g, '');
        } else if (lang.isObject(value) && value instanceof Decimal) {
            return value;
        } else if (lang.isNil(value) || value === '') {
            value = '0';
        }
        return new Decimal(value);
    };

    /**
     * Format value as currency
     *
     * Will create a decimal using the getDecimalFromInput method if one isn't provided.
     *
     * @param {*} value
     * @returns {string}
     */
    static formatCurrency(value) {
        let decimal = this.getDecimalFromInput(value);
        return accounting.formatMoney(decimal.toFixed(2));
    };

    /**
     * Parse type, subtype, and params out of passed content type
     *
     * @param {string} type
     * @returns {{raw: string, type: string, subtype: string, params: {}}}
     */
    static parseContentType(type) {
        let parts = type.split(';');
        type = parts.shift().trim();
        let item = {
            raw: type,
            params: {}
        };
        [item.type, item.subtype] = type.split('/', 2);
        if (parts.length > 0) {
            for (let part of parts) {
                let [name, value] = part.split('=');
                item.params[name.trim()] = value.trim();
            }
        }
        return item;
    };

    /**
     * Check if content types match each other
     *
     * @param {(Object|string)} main
     * @param {(Object|string)} challenger
     * @param {boolean} [match_any=true] - Determines if it can match the all content type
     * @returns {boolean}
     */
    static contentTypeMatches(main, challenger, match_any = true) {
        if (!Array.isArray(main)) {
            main = this.parseContentType(main);
        }
        if (!Array.isArray(challenger)) {
            challenger = this.parseContentType(challenger);
        }
        if ((match_any && main.raw === '*/*') || main.raw === challenger.raw) {
            return true;
        }
        if (main.type !== challenger.type) {
            return false;
        }
        return main.subtype === '*' || main.subtype === challenger.subtype;
    };

    /**
     * Recursively compare and merge two objects, returns if anything was changed
     *
     * @param {Object} object
     * @param {Object} data
     * @returns {boolean}
     */
    static objectCompareAndSet(object, data) {
        let changed = false;
        for (let key in data) {
            if (!data.hasOwnProperty(key)) {
                continue;
            }
            if (lang.isPlainObject(data[key])) {
                if (!lang.isPlainObject(object[key])) {
                    object[key] = {};
                }
                changed = this.objectCompareAndSet(object[key], data[key]);
                continue;
            }
            if (!lang.isEqual(object[key], data[key])) {
                changed = true;
            }
            object[key] = data[key];
        }
        return changed;
    };
}

module.exports = Utils;
