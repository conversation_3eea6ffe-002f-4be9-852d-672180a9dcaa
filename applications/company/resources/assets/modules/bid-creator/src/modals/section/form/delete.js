/**
 * @module BidCreator/Modals/Section/Form/Delete
 */

'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

/**
 * @alias module:BidCreator/Modals/Section/Form/Delete
 */
class Delete extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Delete Form');
    };

    /**
     * Open modal
     *
     * @param {module:BidCreator/Section/Form} form
     */
    open(form) {
        this.state.form = form;
        this.setContent(`<p>Are you sure you want to delete the '${form.name}' form?</p>`);
        super.open();
    };

    /**
     * Handle yes which means the user wants to delete
     */
    handleYes() {
        this.startWorking();
        this.state.form.delete().then(() => {
            this.resetWorking();
            this.close();
        }, () => {
            // @todo implement proper flash message once they are built into modals
            alert('Unable to delete form, please contact support');
            this.resetWorking();
        });
    };

    /**
     * Handle no which means the user wants to abort
     */
    handleNo() {
        this.close();
    };
}

module.exports = Delete;
