/**
 * @module BidCreator/Modals/Section/Add
 */

'use strict';

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Base = require('./base');
const BidSection = require('../../section');
const FlashMessage = require('../../flash-message');
const PubSub = require('../../pubsub');

/**
 * @alias module:BidCreator/Modals/Section/Add
 */
class Add extends Base {
    /**
     * Constructor
     *
     * @param {module:BidCreator} bid
     */
    constructor(bid) {
        super(bid);
        this.addAction({
            type: Base.Action.SAVE,
            handler: () => this.save()
        });
    };

    /**
     * Add any forms automatically which were marked as a section default
     */
    addDefaultForms() {
        if (this.state.form_data.default_forms.length === 0) {
            return;
        }
        for (let form of this.state.form_data.default_forms) {
            this.addSelectedForm(form.id);
        }
    };

    /**
     * Open add modal
     *
     * Build and cache dropdown
     */
    open(section_number) {
        this.setSectionName(`Section ${section_number}`);
        this.startWorking();
        this.loadFormData().then(() => {
            this.buildDropdown();
            this.resetWorking();
            this.renderAllForms();
            this.addDefaultForms();
        }, () => {
            console.log('Unable to load form data');
        });
        super.open();
    };

    /**
     * Save new section
     */
    save() {
        if (this.isWorking() || !this.validate()) {
            return;
        }

        this.startWorking();

        let forms = [],
            order = 1;
        for (let id of this.getSelectedFormOrder()) {
            let form = this.getSelectedForm(id);
            forms.push({
                id: id,
                company_form_item_id: form.form_id,
                order: order
            });
            order++;
        }

        let request = new Api.BatchRequest.Single('bid-item-section', 'create', {
            item_id: this.bid.id,
            name: this.state.name,
            order: this.bid.sections.size + 1,
            forms: forms
        });
        request.scope({format: 'bid-v1'});
        request.promise.then((result) => {
            let section = new BidSection(this.bid, result.response.parsedData());
            this.bid.addSection(section);
            this.close();
        }, () => {
            this.state.messages.addMessage(FlashMessage.Message.make('Unable to save section, please contact support', FlashMessage.Message.Type.ERROR));
            this.resetWorking();
        });

        PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, request);
    };
}

module.exports = Add;
