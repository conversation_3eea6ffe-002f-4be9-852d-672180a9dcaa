/**
 * @module BidCreator/Sidebar/Components/Uploads
 */

'use strict';

const Accordion = require('../accordion');
const AppDrawings = require('./uploads/app_drawings');
const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');
const CustomDrawings = require('./uploads/custom_drawings');
const NoAppDrawingsModal = require('../../modals/no_app_drawings');
const PubSub = require('../../pubsub');
const Section = require('./uploads/section');

const uploads_tpl = require('@cam-bid-creator-tpl/sidebar/components/uploads/main.hbs');

/**
 * @alias module:BidCreator/Sidebar/Components/Uploads
 */
class Uploads extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     */
    constructor(sidebar, data) {
        super(sidebar, 'uploads', 'Drawings');
        Object.assign(this.state, {
            sections: new Map,
            accordions: {
                drawing: new Accordion.Controller,
                section: new Accordion.Controller({
                    no_items_text: 'No sections created'
                })
            },
            app_drawings: new AppDrawings(this, data),
            custom_drawings: new CustomDrawings(this, data)
        });

        this.state.action = {
            label: 'Refresh',
            icon: 'remix-icon--system--refresh-line',
            handler: () => {
                this.state.app_drawings.refreshDrawings();
            }
        };

        sidebar.bid.on('sections-reordered', (event) => {
            this.handleSectionReorder(event.order);
        });

        PubSub.Handler.subscribe(PubSub.Topics.Section.ADD, (message, section) => {
            this.addSection(section);
        });
        PubSub.Handler.subscribe(PubSub.Topics.Section.Form.FILE_ADD, (message, data) => {
            let info = this.getSection(data.form.section.id);
            info.item.addFile(data.file);
        });
    };

    /**
     * Get accordions
     *
     * @readonly
     *
     * @returns {{drawing: Accordion.Controller, section: Accordion.Controller}}
     */
    get accordions() {
        return this.state.accordions;
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--design--pencil-ruler-line',
            classes: ['t-uploads']
        });
    };

    /**
     * Get section by id
     *
     * @param {string} section_id - Section uuid
     * @returns {Object}
     */
    getSection(section_id) {
        return this.state.sections.get(section_id);
    };

    /**
     * Add section
     *
     * @param {module:BidCreator/Section} section
     */
    addSection(section) {
        let item = new Section(this, section);
        this.state.sections.set(section.id, {
            section: section,
            item: item
        });
        section.on('destroyed', () => {
            this.deleteSection(section.id);
        });
        if (this.isBooted()) {
            item.boot();
        }
    };

    /**
     * Handle section reordering from bid
     *
     * @param {string[]} list - Array of section uuid's
     */
    handleSectionReorder(list) {
        let sections = new Map;
        for (let section_id of list) {
            let info = this.getSection(section_id);
            sections.set(section_id, info);
        }
        this.state.sections = sections;
        if (this.isBooted()) {
            let order = [];
            for (let info of this.state.sections.values()) {
                order.push(info.item.item.id);
            }
            this.state.accordions.section.setItemOrder(order);
        }
    };

    /**
     * Delete section by id
     *
     * Delete associated group item and recalculate total of section group
     *
     * @param {string} section_id - Section uuid
     */
    deleteSection(section_id) {
        let section = this.getSection(section_id);
        section.item.delete();
        this.state.sections.delete(section_id);
    };

    /**
     * Determines if any uploads components have incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        // @todo expand to other items as needed
        return this.state.custom_drawings.hasIncompleteUploads();
    };

    /**
     * Validate upload component
     *
     * @returns {Promise}
     */
    validate() {
        return new Promise((resolve, reject) => {
            // if no app drawings are present or the user has selected one, we continue. otherwise, we confirm that
            // an action wasn't forgotten
            if (!this.state.app_drawings.hasDrawings() || this.state.app_drawings.hasSelectedDrawings()) {
                resolve();
                return;
            }
            let modal = new NoAppDrawingsModal;
            modal.on('yes', () => {
                modal.close().destroy();
                resolve();
            })
                .on('no', () => {
                    modal.close().destroy();
                    reject();
                });
            modal.open();
        });
    };

    /**
     * Boot uploads component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        this.elem.drawings = this.elem.content.fxFind('drawings');
        this.elem.sections = this.elem.content.fxFind('sections');

        this.state.accordions.drawing.boot(this.elem.drawings);
        this.state.accordions.section.boot(this.elem.sections);

        this.state.app_drawings.boot();
        this.state.custom_drawings.boot();

        // boot sections
        for (let section of this.state.sections.values()) {
            section.item.boot();
        }
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return uploads_tpl({
            drawings: this.state.accordions.drawing.render(),
            sections: this.state.accordions.section.render()
        });
    };
}

module.exports = Uploads;
