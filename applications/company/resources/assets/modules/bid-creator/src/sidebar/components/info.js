/**
 * @module BidCreator/Sidebar/Components/Info
 */

'use strict';

const Uppy = require('@uppy/core');
const Dashboard = require('@uppy/dashboard');
const XHRUpload = require('@uppy/xhr-upload');
const Webcam = require('@uppy/webcam');
const size = require('lodash/size');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');
const ConfirmModal = require('@ca-submodule/modal').Confirm;

const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');

const info_tpl = require('@cam-bid-creator-tpl/sidebar/components/info/main.hbs');

/**
 * @alias module:BidCreator/Sidebar/Components/Info
 */
class Info extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     */
    constructor(sidebar, data) {
        super(sidebar, 'info', 'Property Information');

        this.state.property = sidebar.bid.property;
        this.state.customer = sidebar.bid.customer;
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--system--information-line',
            classes: ['t-info']
        });
    };

    /**
     * Show image display container, create new img tag so we defer loading of image until it's needed
     *
     * Hides image uploading link
     */
    showImage() {
        this.elem.image_upload.hide();
        this.elem.image_container.append(`<img src="${this.state.property.image_media_urls.size_medium}?rand=${Math.round(new Date().getTime() / 1000)}" alt="Property Image">`);
        this.elem.image_display.show();
    };

    /**
     * Hide image display container, remove old img tag so we reload the image if one is uploaded again
     *
     * Shows image uploading link
     */
    hideImage() {
        this.elem.image_container.empty();
        this.elem.image_display.hide();
        this.elem.image_upload.show();
    };

    /**
     * Send API request to delete image from property
     *
     * Hides display image container and shows uploading link
     *
     * @returns {Promise<any>}
     */
    deleteImage() {
        return new Promise((resolve, reject) => {
            Api.Resources.Properties().partialUpdate(this.state.property.id, {
                image_file_id: null
            }).then(() => {
                this.hideImage();
                resolve();
            }, () => {
                reject();
            });
        });
    };

    /**
     * Open confirmation modal to verify image delete action
     */
    startDeleteImage() {
        let modal = new ConfirmModal();
        modal.openWithContent(
            'Delete Property Image',
            '<p>Are you sure you want to delete the image for this property? You cannot undo this action.</p>',
            () => {
                modal.startWorking();
                this.deleteImage().then(() => {
                    modal.close().destroy();
                }, () => {
                    alert('Unable to delete property image'); // @todo replace with flash message when implemented into modals
                    modal.close().destroy();
                });
            },
            () => {
                modal.close().destroy();
            }
        );
    };

    /**
     * Boot info component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        this.elem.image_upload = this.elem.content.fxFind('image-upload');
        this.elem.image_display = this.elem.content.fxFind('image-display');
        this.elem.image_container = this.elem.image_display.fxChildren('image-container');
        this.elem.image_delete = this.elem.image_display.fxFind('image-delete');

        if (this.state.property.image_file_id === null) {
            this.hideImage();
        } else {
            this.showImage();
        }

        let endpoint = Api.Resources.Properties().fields(['id']).endpoint((endpoint) => {
            return `${endpoint}${this.state.property.id}`;
        }).buildUrl();

        this.state.uppy = Uppy({
            id: 'property-image-upload',
            autoProceed: true,
            restrictions: {
                allowedFileTypes: ['image/jpeg', 'image/png'],
                maxNumberOfFiles: 1,
                maxFileSize: 10485760
            }
        })
            .use(Dashboard, {
                closeModalOnClickOutside: true,
                hideProgressAfterFinish: true,
                proudlyDisplayPoweredByUppy: false,
                locale: {
                    strings: {
                        done: 'Back',
                    }
                },
                onRequestCloseModal: () => {
                    this.state.dashboard.closeModal();
                    let files = Object.assign({}, this.state.uppy.getState().files);
                    if (size(files) === 0) {
                        this.state.uppy.reset();
                    }
                }
            })
            .use(XHRUpload, {
                endpoint: endpoint,
                method: 'PATCH',
                fieldName: 'image'
            })
            .use(Webcam, {
                target: Dashboard,
                modes: ['picture'],
                facingMode: 'environment',
                mirror: false
            });

        this.state.dashboard = this.state.uppy.getPlugin('Dashboard');

        this.state.uppy.on('upload-success', (file, property) => {
            this.showImage();
        })
            .on('complete', () => {
                setTimeout(() => {
                    let close_modal = true;
                    let files = this.state.uppy.getState().files;
                    for (let id in files) {
                        let file = files[id];
                        let is_processing = file.progress.preprocess || file.progress.postprocess;
                        if (file.progress.uploadComplete && !is_processing && !file.error) {
                            this.state.uppy.removeFile(file.id);
                            continue;
                        }
                        close_modal = false;
                    }
                    if (this.state.dashboard.isModalOpen() && close_modal) {
                        this.state.dashboard.closeModal();
                    }
                }, 1000);
            });

        this.elem.image_upload.fxClick((e) => {
            e.preventDefault();
            this.state.dashboard.openModal();
            return false;
        });
        this.elem.image_container.fxClick((e) => {
            e.preventDefault();
            $.fancybox.open({
                src: `${this.state.property.image_media_urls.original}`
            });
            return false;
        });
        this.elem.image_delete.fxClick((e) => {
            e.preventDefault();
            this.startDeleteImage();
            return false;
        });
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return info_tpl({
            property: this.state.property,
            customer: this.state.customer
        });
    };
}

module.exports = Info;
