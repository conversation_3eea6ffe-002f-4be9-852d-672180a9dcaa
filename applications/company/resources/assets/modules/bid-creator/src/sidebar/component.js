/**
 * @module BidCreator/Sidebar/Components
 */

'use strict';

/**
 * @type module:PanelStack/Panel
 */
const Panel = require('@ca-submodule/panel-stack').Panel;

const panel_tpl = require('@cam-bid-creator-tpl/sidebar/panel.hbs');

/**
 * @memberof module:BidCreator/Sidebar
 */
class Component extends Panel {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {string} name - Id of component
     * @param {string} title
     */
    constructor(sidebar, name, title) {
        super();
        this.state.sidebar = sidebar;
        this.state.name = name;
        this.state.title = title;
        this.state.action = null;

        this.state.nav_item = this.getNavItem();

        this.state.nav_item.addClickHandler(() => {
            this.state.nav_item.setActive(true);
        });

        sidebar.addNavItem(this.state.nav_item);

        sidebar.on('drawer-closed', () => {
            if (!this.isActive()) {
                return;
            }
            this.state.nav_item.setActive(false);
        });
    };

    /**
     * @abstract
     * @returns {module:BidCreator/Sidebar/NavItem}
     */
    getNavItem() {
        //
    };

    /**
     * Official name of component
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Get sidebar instance
     *
     * @readonly
     *
     * @returns {module:BidCreator.Sidebar}
     */
    get sidebar() {
        return this.state.sidebar;
    };

    /**
     * Change sidebar section title
     *
     * @param {string} title
     */
    changeSectionTitle(title) {
        this.elem.title.text(title);
    };

    /**
     * Unload component
     */
    unload() {
        super.unload();
        this.state.nav_item.setActive(false);
    };

    /**
     * Boot component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);
        this.elem.wrapper = this.elem.root.fxChildren('sidebar-panel');
        this.elem.title = this.elem.wrapper.fxFind('title');
        this.elem.content = this.elem.wrapper.fxChildren('content');

        let close = this.elem.root.fxFind('drawer-close');
        if (close.length > 0) {
            close.fxClick((e) => {
                e.preventDefault();
                this.sidebar.closeDrawer();
                return false;
            });
        }

        if (this.state.action !== null) {
            this.elem.action = this.elem.root.fxFind('panel-action').fxClick((e) => {
                e.preventDefault();
                this.state.action.handler();
            });
        }
    };

    /**
     * @abstract
     * @returns {string}
     */
    renderPanelContent() {
        return '';
    };

    /**
     * Render component
     *
     * @returns {string}
     */
    render() {
        super.render();

        let tpl_data = {
            title: this.state.title,
            content: this.renderPanelContent()
        };
        if (this.state.action !== null) {
            tpl_data.action = this.state.action;
        }

        return panel_tpl(tpl_data);
    };
}

module.exports = Component;
