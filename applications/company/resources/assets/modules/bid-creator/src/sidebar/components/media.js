/**
 * @module BidCreator/Sidebar/Components/Media
 */

'use strict';

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');
const DeleteModal = require('../../modals/media/delete');
const List = require('../list');
const MediaEntity = require('../../entities/media');
const PubSub = require('../../pubsub');

const media_tpl = require('@cam-bid-creator-tpl/sidebar/components/media/main.hbs');

/**
 * @typedef {Object} MediaConfig
 * @property {MediaEntity.Base} item
 * @property {number} list_item_id
 */

/**
 * @alias module:BidCreator/Sidebar/Components/Media
 */
class Media extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     * @param {Array} data.media
     */
    constructor(sidebar, data) {
        super(sidebar, 'media', 'Media');
        this.state.action = {
            label: 'Add Media',
            icon: 'remix-icon--system--add-circle-line',
            handler: () => {
                this.sidebar.bid.modals.media_library_add.open();
            }
        };
        let list = new List({
            type: List.Type.DELETE,
            sortable: true,
            no_items_text: 'No media selected',
            action_handler: (item) => {
                this.deleteMedia(item.storage.media_id);
            }
        });
        list.on('items-reordered', (event) => {
            let order = [];
            for (let item_id of event.order) {
                order.push(list.getItem(item_id).storage.media_id);
            }
            this.updateMediaOrder(order);
        });
        this.state.list = list;
        this.state.items = new Map;

        if (data.media.length > 0) {
            for (let media of data.media) {
                let item = MediaEntity.Base.getByData(media, false);
                this.handleMedia(item, false);
            }
        }

        this.state.last_order = this.state.items.size;
    };

    /**
     * Get delete modal instance
     *
     * If modal doesn't exist, it will be created and cached for future calls
     *
     * @readonly
     *
     * @returns {Delete}
     */
    static get delete_modal() {
        if (this._delete_modal === undefined) {
            this._delete_modal = new DeleteModal;
        }
        return this._delete_modal;
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--media--image-line',
            classes: ['t-media']
        });
    };

    /**
     * Get media item
     *
     * @param {string} id - Uuid
     * @returns {MediaConfig}
     */
    getMedia(id) {
        return this.state.items.get(id);
    };

    /**
     * Add media item
     *
     * @param {MediaEntity.Base} item
     */
    addMedia(item) {
        let title;
        switch (item.type) {
            case MediaEntity.Base.Type.LIBRARY_FILE:
                title = item.name;
                break;
        }
        item.on('destroyed', () => {
            this.destroyMedia(item.id);
        });
        this.state.items.set(item.id, {
            item: item,
            list_item_id: this.state.list.addItem(title, {
                media_id: item.id
            })
        });
    };

    /**
     * Handle media item
     *
     * If save is enabled and item state is changed, then we save. Otherwise media is added directly.
     *
     * @param {MediaEntity.Base} item - Media entity
     * @param {boolean} [save=true] - Determines if save is allowed
     * @returns {Promise<any>}
     */
    handleMedia(item, save = true) {
        return new Promise((resolve, reject) => {
            if (save && item.isStateChanged()) {
                item.order = ++this.state.last_order;
                item.save(this.sidebar.bid.id).then(() => {
                    this.addMedia(item);
                    resolve();
                }, reject);
                return;
            }
            this.addMedia(item);
            resolve();
        });
    };

    /**
     * Save media
     *
     * Waits for item to be full prepared, then send it to be handled
     *
     * @param item
     * @returns {Promise}
     */
    saveMedia(item) {
        return new Promise((resolve, reject) => {
            // run prepare on item to gather any prerequisite data before trying to save (this may make an API request to
            // fill in extra data, etc.) after which we send the media to be handled
            item.prepare().then((item) => {
                this.handleMedia(item).then(resolve, reject);
            }, (error) => {
                // @todo add better error handling
                console.log(error);
                reject(error);
            });
        });
    };

    /**
     * Get reorder batch request
     *
     * @param {string[]} list - Array of media item ids in the needed order
     * @returns {(boolean|Api.BatchRequest.Multiple)}
     */
    getReorderBatchRequest(list) {
        let requests = [];
        list.forEach((item_id, order) => {
            order++; // increment key since we start at 1 instead of zero
            let media = this.getMedia(item_id);
            if (media === undefined) {
                return;
            }
            // if order didn't change then do nothing
            if (media.item.order === order) {
                return;
            }
            let request = new Api.BatchRequest.Single('bid-item-media', 'partial-update', {
                id: media.item.id,
                order: order
            });
            request.promise.done(() => {
                // update media item order so it is up-to-date, use setState so we can prevent the state getting marked
                // as updated
                media.item.setState({order: order}, false);
            });
            requests.push(request);
        });
        if (requests.length === 0) {
            return false;
        }
        let batch_request = new Api.BatchRequest.Multiple(true);
        for (let request of requests) {
            batch_request.add(request);
        }
        return batch_request;
    };

    /**
     * Update media order
     *
     * Sends API request to update any media which order was changed in the provided list, sets the internal media order,
     * and reorders list items
     *
     * @param {string[]} list - Array of media uuid's
     */
    updateMediaOrder(list) {
        let batch_request = this.getReorderBatchRequest(list);
        // if batch_request is false, then nothing was reordered
        if (batch_request === false) {
            return;
        }
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, batch_request);
        this.setMediaOrder(list);
    };

    /**
     * Set media order
     *
     * Updates internal item map to be in the specified order
     *
     * @param {string[]} list - Array of media uuid's
     */
    setMediaOrder(list) {
        let items = new Map;
        for (let item_id of list) {
            let media = this.getMedia(item_id);
            items.set(media.item.id, media);
        }
        this.state.items = items;
    };

    /**
     * Delete media item
     *
     * @param {string} id - Uuid
     */
    deleteMedia(id) {
        Media.delete_modal.open(this.getMedia(id));
    };

    /**
     * Destroy media item
     *
     * Remove item from list and internal item cache, update media order
     *
     * @param {string} id - Uuid
     */
    destroyMedia(id) {
        let item = this.getMedia(id);
        this.state.list.deleteItem(item.list_item_id);
        this.state.items.delete(id);
        this.updateMediaOrder(Array.from(this.state.items.keys()));
    };

    /**
     * Boot media component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        this.elem.items = this.elem.content.fxFind('items');

        this.state.list.boot(this.elem.items);
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return media_tpl({
            list: this.state.list.render()
        });
    };
}

module.exports = Media;
