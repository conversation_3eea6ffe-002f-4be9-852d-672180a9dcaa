/**
* @module BidCreator/Sidebar/Components/Uploads/AppDrawings
*/

'use strict';

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Accordion = require('../../accordion');
const List = require('../../list');
const PubSub = require('../../../pubsub');

/**
 * @alias module:BidCreator/Sidebar/Components/Uploads/AppDrawings
 */
class AppDrawings {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Sidebar/Components/Uploads} component
     * @param {Object} data
     * @param {Array} data.drawings - List of selected drawings
     * @param {Object} data.project
     */
    constructor(component, data) {
        /**
         * @private
         */
        this.state = {
            booted: false,
            component: component,
            drawings: new Map,
            project: data.project,
            selected: []
        };

        // create array of selected drawings
        for (let drawing of data.drawings) {
            this.state.selected.push(drawing.drawing_id);
        }

        let list = new List({
            type: List.Type.CHECK,
            display_type: List.DisplayType.MEDIA,
            no_items_text: 'No drawings available',
            preview_handler: (item) => {
                let info = this.getDrawing(item.storage.drawing_id);
                $.fancybox.open({
                    src: `${info.drawing.repair_plan_media_urls.original}?viewer=true`,
                    type: 'iframe'
                });
            }
        });
        list.addAction({
            type: List.ActionType.ADD,
            label: 'Add',
            handler: () => {
                let link = window.fx_pages.DRAWINGS_CREATE.replace('{project_id}', this.state.project.id);
                window.open(link, '_blank');
            }
        });
        list.on('changed', (event) => {
            let selected = [];
            for (let id of event.new_ids) {
                selected.push(list.getItem(id).storage.drawing_id);
            }
            this.state.selected = selected;
            this.save();
        });

        this.state.list = list;

        this.addDrawings(data.project.drawings);
    };

    /**
     * Get component
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components/Uploads}
     */
    get component() {
        return this.state.component;
    };

    /**
     * Get accordion item title
     *
     * @returns {string}
     */
    getTitle() {
        return `Application Drawings (${this.state.selected.length}/${this.state.drawings.size})`;
    };

    /**
     * Set title of accordion item
     */
    renderTitle() {
        this.state.item.setTitle(this.getTitle());
    };

    /**
     * Determines if any app drawings are available
     *
     * @returns {boolean}
     */
    hasDrawings() {
        return this.state.drawings.size > 0;
    };

    /**
     * Get drawing
     *
     * @param {string} id - Drawing uuid
     * @returns {any}
     */
    getDrawing(id) {
        return this.state.drawings.get(id);
    };

    /**
     * Add drawing
     *
     * @param {Object} drawing
     * @returns {Object}
     */
    addDrawing(drawing) {
        let info = {
            drawing: drawing,
            list_item_id: this.state.list.addItem({
                name: drawing.name,
                thumbnail_url: drawing.image_media_urls.bid_thumbnail
            }, {
                drawing_id: drawing.id
            })
        };
        this.state.drawings.set(drawing.id, info);
        return info;
    };

    /**
     * Add multiple drawings and updated selected value
     *
     * @param {Array} drawings
     */
    addDrawings(drawings) {
        let selected = [];
        for (let drawing of drawings) {
            let info = this.addDrawing(drawing);
            if (this.state.selected.indexOf(drawing.id) !== -1) {
                selected.push(info.list_item_id);
            }
        }
        this.state.list.setSelected(selected, true, false);
    };

    /**
     * Refresh drawing list
     */
    refreshDrawings() {
        this.state.list.toggleWorking(true);
        this.state.list.deleteAllItems();
        this.state.drawings = new Map;
        Api.Resources.Drawings()
            .filter('project_id', this.state.project.id)
            .accept('application/vnd.adg.fx.bid-v1+json')
            .all()
            .then((collection) => {
                this.state.list.toggleWorking(false);
                let drawings = collection.entities;
                if (drawings.length === 0) {
                    return;
                }
                // remove entity class and just return object
                drawings = drawings.map((entity) => {
                    return entity.data;
                });
                this.addDrawings(drawings);
                this.renderTitle();
            }, (error) => {
                console.log(error);
                // @todo add proper error handling
            });
    };

    /**
     * Determines if any drawings have been selected
     *
     * @returns {boolean}
     */
    hasSelectedDrawings() {
        return this.state.selected.length > 0;
    };

    /**
     * Save drawing selections
     */
    save() {
        let request = new Api.BatchRequest.Single('bid-item-drawing', 'sync', {
            item_id: this.component.sidebar.bid.id,
            drawing_ids: this.state.selected
        });
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
        this.renderTitle();
    };

    /**
     * Determines if class is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot item
     *
     * Add accordion item to proper accordion of parent component
     */
    boot() {
        this.state.item = new Accordion.Item(this.getTitle(), this.state.list.render());
        this.state.item.on('booted', (event) => {
            this.state.list.boot(event.item.elem.panel);
        });

        this.component.accordions.drawing.addItem(this.state.item);

        this.state.booted = true;
    };

    /**
     * Render item
     *
     * @returns {string}
     */
    render() {
        return this.state.list.render();
    };
}

module.exports = AppDrawings;
