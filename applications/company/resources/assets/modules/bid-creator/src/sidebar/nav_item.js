/**
 * @module BidCreator/Sidebar/NavItem
 */

'use strict';

const nav_item = require('@cam-bid-creator-tpl/sidebar/nav_item.hbs');

/**
 * @alias module:BidCreator/Sidebar/NavItem
 */
class NavItem {
    /**
     * Constructor
     *
     * @param {Object} config
     */
    constructor(config) {
        NavItem.__idx++;
        this.elem = {};
        this.state = {
            rendered: false,
            booted: false,
            id: NavItem.__idx,
            active: false,
            classes: [],
            icon: null,
            elem: null,
            click_handlers: []
        };
        Object.assign(this.state, config);
    };

    /**
     * Get id
     *
     * @readonly
     *
     * @returns {number}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get sidebar instance
     *
     * @readonly
     *
     * @returns {module:BidCreator.Sidebar}
     */
    get sidebar() {
        return this.state.sidebar;
    };

    /**
     * Set active status of item
     *
     * @param {boolean} bool
     */
    setActive(bool) {
        if (this.state.active === bool) {
            return this;
        }
        this.state.active = bool;
        if (this.state.rendered) {
            let method = this.state.active ? 'addClass' : 'removeClass';
            this.elem.root[method]('t-active');
        }
        return this;
    };

    /**
     * Add click handler
     *
     * @param closure
     * @returns {NavItem}
     */
    addClickHandler(closure) {
        this.state.click_handlers.push(closure);
        return this;
    };

    /**
     * Trigger click event on nav item so click handlers run with proper browser event
     */
    triggerClick() {
        if (!this.state.rendered) {
            return;
        }
        this.elem.root.trigger('click');
    };

    /**
     * Handle click event
     *
     * @param event
     */
    handleClick(event) {
        this.state.click_handlers.forEach((handler) => {
            handler(event, this);
        });
    };

    /**
     * Render and boot nav item
     *
     * We render while booting since the container already exists and don't come from the rendered content
     *
     * @param {module:BidCreator.Sidebar} sidebar
     */
    boot(sidebar) {
        this.state.sidebar = sidebar;
        this.render(sidebar.elem.nav);
        this.state.booted = true;
    };

    /**
     * Render nav item and append to container
     *
     * @protected
     * @param {Object} container - jQuery object
     */
    render(container) {
        this.elem.root = $(nav_item({
            id: this.state.id,
            menu: this.state.id === 'menu',
            icon: this.state.icon,
            classes: this.state.classes.length > 0 ? ' ' + this.state.classes.join(' ') : ''
        }));
        container.append(this.elem.root);
        this.state.rendered = true;
    };
}

NavItem.__idx = 0;

module.exports = NavItem;
