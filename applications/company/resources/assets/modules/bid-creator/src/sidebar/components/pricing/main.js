/**
 * @module BidCreator/Sidebar/Components/Pricing/Main
 */

'use strict';

const EventEmitter = require('events');
const includes = require('lodash/includes');
const lang = require('lodash/lang');

/**
 * @type module:PanelStack/Panel
 */
const Panel = require('@ca-submodule/panel-stack').Panel;

const Group = require('./main/group');
const GroupItem = require('./main/group/item');
const LineItem = require('../../../entities/line_item');
const PaymentTerms = require('./payment_terms');
const PubSub = require('../../../pubsub');
const Utils = require('../../../utils');
const Tooltip = require('@ca-submodule/tooltip');

const main_tpl = require('@cam-bid-creator-tpl/sidebar/components/pricing/main.hbs');
const { getWisetackPromoMessage } = require("@cac-js/wisetack.js");



/**
 * @memberof module:BidCreator/Sidebar/Components/Pricing
 */
class Main extends Panel {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Sidebar/Components.Pricing} component
     * @param {Object} data
     * @param {Array} data.line_items - Initial line items for bid
     * @param {Array} data.payment_terms - Initial payment terms for bid
     * @param {Array} data.products - Array of all products used by line items
     */
    constructor(component, data) {
        super();
            const is_wisetack_enabled = window.bid_info.features.wisetack_api;
            const is_merchant_approved = window.bid_info.wisetack.is_merchant_approved;
            const is_financing_required_for_all_projects = window.bid_info.settings.is_financing_required_for_all_projects;

            Object.assign(this.state, {
            component: component,

            // events
            events: new EventEmitter,

            // groups
            groups: {},
            group_items: new Map,

            // line items
            line_items: new Map,
            event_line_items: new Map,
            group_item_line_items: new Map,

            // section data
            sections: new Map,

            // price adjustments
            price_adjustment_order: ['discount', 'fee'],

            // total information
            subtotal: Utils.getDecimal('0.00'),
            total: Utils.getDecimal('0.00'),
            has_total: false,

            // Wisetack info
            is_financing_enabled_for_project: window.BidCreator.state.is_financing_enabled_for_project,
            meet_wisetack_requirements: is_wisetack_enabled && is_merchant_approved && !is_financing_required_for_all_projects,
            wisetack_data: window.bid_info.wisetack.merchant,
            wisetack_promo: null,
        });


        // add price adjustment group
        let price_adjustment_group = new Group(this);
        price_adjustment_group.on('total-updated', (event) => {
            this.updateTotal(event.new_total);
        });
        this.state.groups.price_adjustment = price_adjustment_group;

        // add section group
        let section_group = new Group(this, {accordion: {no_items_text: 'No sections created'}});
        section_group.on('total-updated', (event) => {
            this.updateSubtotal(event.new_total);
            this.state.groups.price_adjustment.setBaseTotal(event.new_total);
        });
        this.state.groups.section = section_group;

        // load initial line items
        for (let item of data.line_items) {
            try {
                // set line item existing flag to true since this is the initial load of the line items and we
                // don't want it to trigger another save since nothing has been changed yet
                let $item = LineItem.Base.getByData(item, true);
                // handle line item so it gets sent to proper group to for display, disable updating totals
                this.handleLineItem($item, false, false);
            } catch (e) {
                console.log(e);
            }
        }

        // load initial products into cache
        for (let product of data.products) {
            LineItem.Product.cacheEntity(product);
        }

        // load price adjustment group items
        let price_adjustment_map = {
            discount: GroupItem.Discount,
            fee: GroupItem.Fee
        };
        for (let price_adjustment of this.state.price_adjustment_order) {
            this.addGroupItem(new price_adjustment_map[price_adjustment], 'price_adjustment');
        }

        // initialize payment terms separately since it needs access to the events
        this.state.payment_terms = new PaymentTerms(this, data.payment_terms);

        component.sidebar.bid.on('sections-reordered', (event) => {
            this.handleSectionReorder(event.order);
        });

        PubSub.Handler.subscribe(PubSub.Topics.Section.ADD, (message, section) => {
            this.addSection(section);
        });
        PubSub.Handler.subscribe(PubSub.Topics.LineItem.SAVE, (message, data) => {
            this.saveLineItem(data.line_item, data.update_total).catch((data) => console.log(data));
        });
    };

    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     * @returns {module:BidCreator/Sidebar/Components/Pricing.Main}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Get pricing component
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components.Pricing}
     */
    get component() {
        return this.state.component;
    };

    /**
     * Get payment terms
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components/Pricing.PaymentTerms}
     */
    get payment_terms() {
        return this.state.payment_terms;
    };

    /**
     * Get overall total
     *
     * @returns {Decimal}
     */
    get total() {
        this.calculateTotal();
        return this.state.total;
    };

    /**
     * Render subtotal
     */
    renderSubtotal() {
        this.elem.subtotal.text(Utils.formatCurrency(this.state.subtotal));
    };

    /**
     * Update subtotal for sections
     *
     * @param {Decimal} total
     */
    updateSubtotal(total) {
        this.state.subtotal = total;
        if (this.isBooted()) {
            this.renderSubtotal();
        }
    };

    /**
     * Render total
     */
    renderTotal() {
        this.elem.total.text(Utils.formatCurrency(this.state.total, '$ '));
    };

    /**
     * Set overall total
     *
     * @param {Decimal} total
     *
     * @emits module:BidCreator/Sidebar/Components/Pricing.Main~totalUpdated
     */
    updateTotal(total) {
        let prev_total = this.state.total;
        this.state.total = total;
        if (!this.state.has_total) {
            this.state.has_total = true;
        }
        this.state.events.emit('total-updated', {
            prev_total: prev_total,
            new_total: total
        });
        if (this.isBooted()) {
            this.renderTotal();
        }
    };

    /**
     * Calculate total only if it hasn't been done already
     *
     * Setting the base total on the section group will cause a cascade of total changes until it calls updateTotal()
     * which will mark has_total as true
     *
     * Used either when first booting to get the initial total, or when a total is requested (for finalization or
     * something, and the panel hasn't been booted)
     */
    calculateTotal() {
        if (this.state.has_total) {
            return;
        }
        this.state.groups.section.setBaseTotal('0.00');
    };

    /**
     * Get group item
     *
     * @param {string} id - Group item id
     * @returns {(module:BidCreator/Sidebar/Components/Pricing/Main/Group/Item.Base|undefined)}
     */
    getGroupItem(id) {
        return this.state.group_items.get(id);
    };

    /**
     * Add group item
     *
     * @param {module:BidCreator/Sidebar/Components/Pricing/Main/Group/Item.Base} item - Group item instance
     * @param {string} group - Group id
     */
    addGroupItem(item, group) {
        this.state.group_items.set(item.id, item);
        let line_items = this.state.group_item_line_items.get(item.id);
        if (line_items !== undefined) {
            for (let line_item_id of line_items) {
                // disable updating the total each time a line item is added
                item.handleLineItem(this.state.line_items.get(line_item_id), false);
            }
        }
        this.state.groups[group].addItem(item);
    };

    /**
     * Add line item to group item
     *
     * Keeps a map of what line items are associated with a group item (section or price adjustment). This is needed
     * for pre-populating sections with their line items during the initial loading process
     *
     * @param {string} id - Id of the group item
     * @param {LineItem.Base} item - Line item instance
     * @param {boolean} [update_total=true] - Determines if total should be updated when adding item
     */
    addGroupItemLineItem(id, item, update_total = true) {
        let line_items = this.state.group_item_line_items.get(id);
        if (line_items === undefined) {
            line_items = [];
            this.state.group_item_line_items.set(id, line_items);
        }
        if (!includes(line_items, item.id)) {
            line_items.push(item.id);
        }
        // if group already exists, then we just directly add the line item
        let group_item = this.getGroupItem(id);
        if (group_item !== undefined) {
            group_item.handleLineItem(item, update_total);
        }
    };

    /**
     * Determines if any line items exist
     *
     * @returns {boolean}
     */
    hasLineItems() {
        return this.state.line_items.size > 0;
    };

    /**
     * Takes a line item instance and distributes to the proper group for display
     *
     * Handles duplicate line items coming from form events by merging them into a single line item
     *
     * @param {LineItem.Base} item - Line item to be handled
     * @param {boolean} [save=true] - Whether or not to trigger a save for the line item
     * @param {boolean} [update_total=true] - Determines if total update should be triggered
     */
    handleLineItem(item, save = true, update_total = true) {
        // if line item is from a form event source, it may match another line item which already exists
        // we need to check and merge the line items if necessary
        if (item.source === LineItem.Source.FORM_EVENT) {
            let id = `${item.form_item_entry_group_id}-${item.form_item_group_rule_event_id}`;
            if (!this.state.event_line_items.has(id)) {
                this.state.event_line_items.set(id, item.id);
            } else {
                // merge item in into that one
                let existing_item = this.state.line_items.get(this.state.event_line_items.get(id));
                // if existing_item isn't the same instance of item, then merge them together
                if (existing_item !== item) {
                    // if merge returns true, then it means something changed and a save needs to happen, otherwise things
                    // are left alone
                    save = existing_item.merge(item);
                    // if new item isn't being saved (nothing changed), we assign the form item group if needed
                    // this associates existing line items from the api with their form group (needed when first loading bid)
                    if (!save && existing_item.form_item_group === undefined) {
                        existing_item.form_item_group = item.form_item_group;
                    }
                    item = existing_item;
                }
            }
        }
        let new_item = !this.state.line_items.has(item.id);
        if (new_item) {
            this.state.line_items.set(item.id, item);
            item.on('destroyed', () => {
                this.deleteLineItem(item.id);
            });
        } else {
            // if line item which is being edited, is currently being edited in a line item form, then we pop it
            if (this.isBooted() && this.controller.isActivePanel('line-item-edit')) {
                let panel = this.controller.getPanel('line-item-edit').instance;
                if (panel.allowExternalClose() && panel.line_item.id === item.id) {
                    this.controller.pop();
                }
            }
        }
        let saved = false;
        if (save && item.isStateChanged()) {
            item.save(this.state.component.sidebar.bid.id);
            saved = true;
        }
        // if line item has a section assigned to it then we send it to the appropriate group
        let section_id = item.bid_item_section_id;
        if (!lang.isNil(section_id)) {
            this.addGroupItemLineItem(`section-${section_id}`, item, update_total);
        } else {
            let item_type_map = {
                    [LineItem.Type.DISCOUNT]: 'discount',
                    [LineItem.Type.FEE]: 'fee'
                },
                group_item_id = item_type_map[item.type];
            if (group_item_id !== undefined) {
                this.addGroupItemLineItem(group_item_id, item, update_total);
            }
        }
        PubSub.Handler.publish(PubSub.Topics.LineItem.HANDLED, {
            line_item: item,
            saved: saved
        });
    };

    /**
     * Save line item
     *
     * Waits for item to be full prepared, then send it to be handled
     *
     * @param {LineItem.Base} item - Line item to be saved
     * @param {boolean} [update_total=true] - Determines if total is updated
     * @returns {Promise}
     */
    saveLineItem(item, update_total = true) {
        return new Promise((resolve, reject) => {
            // run prepare on item to gather any prerequisite data before trying to save (this may pull in a product to
            // fill in extra data, etc.) after which we send the line item to be handled
            let editing = this.state.line_items.has(item.id);

            item.prepare(editing).then((item) => {
                this.handleLineItem(item, true, update_total);
                resolve(item);
            }, (error) => {
                // @todo add better error handling
                console.log(error);
                reject(error);
            });
        });
    };

    /**
     * Delete line item
     *
     * @param item_id
     */
    deleteLineItem(item_id) {
        let item = this.state.line_items.get(item_id);
        if (item === undefined) {
            return;
        }
        // if the line item which is being deleted matches one being currently edited, then we pop the line item edit panel
        if (this.isBooted() && this.controller.isActivePanel('line-item-edit')) {
            let panel = this.controller.getPanel('line-item-edit').instance;
            if (panel.external_close && panel.line_item.id === item.id) {
                this.controller.pop();
            }
        }
        // if line item has a section assigned to it then we send the delete request to the appropriate group
        let group_item = null,
            section_id = item.bid_item_section_id;
        if (!lang.isNil(section_id)) {
            group_item = this.getGroupItem(`section-${section_id}`);
        } else {
            let item_type_map = {
                    [LineItem.Type.DISCOUNT]: 'discount',
                    [LineItem.Type.FEE]: 'fee'
                },
                group_item_id = item_type_map[item.type];
            if (group_item_id !== undefined) {
                group_item = this.getGroupItem(group_item_id);
            }
        }
        if (group_item !== null) {
            group_item.deleteLineItem(item.id);
        }
        if (item.source === LineItem.Source.FORM_EVENT) {
            let id = `${item.form_item_entry_group_id}-${item.form_item_group_rule_event_id}`;
            if (this.state.event_line_items.has(id)) {
                this.state.event_line_items.delete(id);
            }
        }
        this.state.line_items.delete(item_id);
    };

    /**
     * Get section by id
     *
     * @param {string} section_id - Section uuid
     * @returns {Object}
     */
    getSection(section_id) {
        return this.state.sections.get(section_id);
    };

    /**
     * Add section
     *
     * @param {module:BidCreator/Section} section
     */
    addSection(section) {
        let item = new GroupItem.Section(section);
        this.addGroupItem(item, 'section');
        this.state.sections.set(section.id, {
            section: section,
            item: item
        });

        section.on('destroyed', () => {
            this.deleteSection(section.id);
        });
    };

    /**
     * Handle section reordering from bid
     *
     * Converts section id's into group item id array and send to section group for reordering
     *
     * @param {string[]} items - Array of section uuid's
     */
    handleSectionReorder(items) {
        let list = [];
        for (let section_id of items) {
            let config = this.getSection(section_id);
            list.push(config.item.id);
        }
        this.state.groups.section.setItemOrder(list);
    };

    /**
     * Delete section by id
     *
     * Delete associated group item and recalculate total of section group
     *
     * @param {string} section_id - Section uuid
     */
    deleteSection(section_id) {
        let section = this.getSection(section_id);
        section.item.delete();
        this.state.sections.delete(section_id);
        section.item.group.recalculateTotal();
    };


    /**
     * Set financing state
     *
     */
    setFinancingState() {
        this.elem.project_financing_section.removeClass('t-hidden');

        const textMap = new Map([[
            true, { action: `Disable`, status: `Enabled` , class: 'link-red'}], [
            false, { action: `Enable`, status: `Disabled`, class: 'link'
        }]]);

        let text = textMap.get(false)

        // Backend already checks for wisetack feature flag.
        if (this.state.is_financing_enabled_for_project) {
            text = textMap.get(true)

            this.elem.project_financing.removeClass('disabled');
            this.elem.project_financing.addClass('enabled');
        }

        const destination = window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', window.BidCreator.state.project_id)
        let link = `<a class="${text.class}" href="${destination}">
                <span class="title">${text.action} Financing On Project</span>
                <svg data-icon><use xlink:href="#remix-icon--system--share-circle-line"></use></svg>
            </a>`

        this.elem.project_financing.html(text.status);
        this.elem.project_financing_link.html(link);

        this.setFinancingPromoState();
        this.state.events.on('total-updated', () => {
            this.setFinancingPromoState();
        });


    };

    /**
     * Set financing promo state
     *
     * @returns {Promise<void>}
     */
    async setFinancingPromoState() {
        try {

            // hide everything
            if (this.state.total < 500) {
                this.elem.project_financing_callout_disabled.addClass('t-hidden');
                this.elem.project_financing_callout_enabled.addClass('t-hidden');
                return
            }

            this.elem.promo_loading.removeClass('t-hidden');
            const merchant_id = this.state.wisetack_data.wisetackMerchantID;
            const amount = this.state.total;
            const promo = await getWisetackPromoMessage(merchant_id, amount);

            if (this.state.is_financing_enabled_for_project) {
                this.elem.project_financing_callout_disabled.addClass('t-hidden');
                this.elem.project_financing_callout_enabled.removeClass('t-hidden');
                const text = `As low as $${promo.monthlyPayment}/mo`
                this.elem.project_financing_promo_enabled.html(text);
            } else {
                this.elem.project_financing_callout_disabled.removeClass('t-hidden');
                this.elem.project_financing_callout_enabled.addClass('t-hidden');
                const text = `Enable financing with Wisetack and pay as low as $${promo.monthlyPayment}/mo`
                this.elem.project_financing_promo_disabled.html(text);
            }
        } catch (e) {
            console.log(e);
        } finally {
            this.elem.promo_loading.addClass('t-hidden');
        }
    }

    /**
     * Boot main panel
     *
     * @param {jQuery} root - Panel container jQuery element instance
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.subtotal = this.elem.root.fxFind('subtotal');
        this.elem.total = this.elem.root.fxFind('total');

        this.elem.payment_terms = this.elem.root.fxFind('payment-terms');
        this.elem.project_financing_section = this.elem.root.fxFind('project-financing-section');
        this.elem.project_financing = this.elem.root.fxFind('project-financing');
        this.elem.project_financing_link = this.elem.root.fxFind('project-financing-link');

        this.elem.promo_loading = this.elem.root.fxFind('promo-loading');
        this.elem.project_financing_callout_disabled = this.elem.root.fxFind('project-financing-callout-disabled');
        this.elem.project_financing_callout_enabled = this.elem.root.fxFind('project-financing-callout-enabled');
        this.elem.project_financing_promo_enabled = this.elem.root.fxFind('project-financing-promo-enabled');
        this.elem.project_financing_promo_disabled = this.elem.root.fxFind('project-financing-promo-disabled');

        // boot groups
        for (let [id, selector] of [['section', 'sections'], ['price_adjustment', 'price-adjustments']]) {
            this.elem[id] = this.elem.root.fxFind(selector);
            this.state.groups[id].boot(this.elem[id]);
        }

        // set total if it hasn't been set yet
        this.calculateTotal();

        // render subtotal and total in case total has already been calculated by another action before the boot
        // occurred which would result in it not being displayed
        this.renderSubtotal();
        this.renderTotal();

        this.state.payment_terms.boot(this.elem.payment_terms);
        if (this.state.meet_wisetack_requirements) {
            this.setFinancingState();
        }
    };

    /**
     * Render main panel
     *
     * @returns {string}
     */
    render() {
        return main_tpl({
            sections: this.state.groups.section.render(),
            price_adjustments: this.state.groups.price_adjustment.render(),
            payment_terms: this.state.payment_terms.render()
        });
    };
}

module.exports = Main;
