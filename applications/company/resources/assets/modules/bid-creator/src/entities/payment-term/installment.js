/**
 * @module BidCreator/Entities/PaymentTerm/Installment
 */

'use strict';

const lang = require('lodash/lang');

const Number = require('@cac-js/utils/number');

const Base = require('./base');
const InstallmentEntity = require('./installment/installment');

/**
 * @memberof module:BidCreator/Entities/PaymentTerm
 */
class Installment extends Base {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super(data, existing);
        this.state.type = Base.Type.INSTALLMENT;
        this.state.title = 'Installments';
        this.state.installments = [];

        if (data.item !== undefined) {
            // if installments exist, then we create necessary entities and store them
            if (lang.isArray(data.item.installments)) {
                for (let installment of data.item.installments) {
                    this.addInstallment(new InstallmentEntity(installment, existing));
                }
            }
        }

        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Get installments
     *
     * @readonly
     *
     * @returns {module:BidCreator/Entities/PaymentTerm/Installment.Installment[]}
     */
    get installments() {
        return this.state.installments;
    };

    /**
     * Add installment
     *
     * @param {module:BidCreator/Entities/PaymentTerm/Installment.Installment} entity
     */
    addInstallment(entity) {
        if (!(entity instanceof InstallmentEntity)) {
            throw new Error('Installment must be an entity');
        }
        this.state.installments.push(entity);
        this.setStateChanged(true);
    };

    /**
     * Set installments for payment term
     *
     * @param {module:BidCreator/Entities/PaymentTerm/Installment.Installment[]} entities
     */
    setInstallments(entities) {
        this.state.installments = [];
        for (let entity of entities) {
            this.addInstallment(entity);
        }
    };

    /**
     * Validate payment term
     *
     * @param {Decimal} bid_total
     * @returns {string[]}
     */
    validate(bid_total) {
        let errors = super.validate(bid_total);
        if (this.state.installments.length === 0) {
            errors.push('At least one installment is required');
        }
        return errors;
    };

    /**
     * Validate installments as a group to make sure the add up properly
     *
     * @param {module:BidCreator/Entities/PaymentTerm/Installment.Installment[]} installments
     * @param {Decimal} bid_total
     * @returns {Promise<void>}
     */
    async validateInstallments(installments, bid_total) {
        let promises = [],
            errors = [];
        // add installment prepare promises to array so we can validate all at once
        installments.forEach((installment, i) => {
            promises.push(installment.prepare().catch(data => {
                data = data.map((datum) => {
                    return `Installment ${++i} - ${datum}`;
                });
                errors = errors.concat(data);
            }));
        });
        if (promises.length > 0) {
            await Promise.all(promises);
        }
        if (errors.length > 0) {
            throw errors;
        }
        let total_sum = Number.of('0'),
            percentage_sum = Number.of('0'),
            percentage_count = 0;
        for (let installment of installments) {
            switch (installment.amount_type) {
                case InstallmentEntity.AmountType.TOTAL:
                    total_sum = total_sum.plus(installment.amount);
                    break;
                case InstallmentEntity.AmountType.PERCENTAGE:
                    percentage_sum = percentage_sum.plus(installment.amount);
                    percentage_count++;
                    break;
            }
        }
        if (percentage_count === 0) {
            throw ['At least one percentage installment is required'];
        }
        if (!percentage_sum.equals('100')) {
            throw ['Percentage sum must equal 100%'];
        }
        let percentage_total = bid_total.minus(total_sum);
        if (percentage_total.lessThanOrEqualTo('0')) {
            throw ['Sum of exact dollar installments is greater than or equal to bid total'];
        }
    };

    /**
     * Prepare payment term
     *
     * Fetch/prep any necessary data before saving
     *
     * @param {Decimal} bid_total
     * @returns {Promise<module:BidCreator/Entities/PaymentTerm.Installment>}
     */
    async prepare(bid_total) {
        let errors = this.validate(bid_total);
        if (errors.length > 0) {
            throw errors;
        }
        await this.validateInstallments(this.state.installments, bid_total);
        return this;
    };

    /**
     * Get payload for API save request
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {Object}
     */
    getPayload(bid_item_id) {
        let payload = super.getPayload(bid_item_id);
        payload.item = {
            id: this.item_id
        };
        if (this.state.installments.length > 0) {
            payload.item.installments = this.state.installments.map((entity) => {
                return entity.getPayload();
            });
        }
        return payload;
    };
}

module.exports = Installment;
