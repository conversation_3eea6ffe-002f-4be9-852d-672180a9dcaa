/**
 * @module BidCreator/Entities/Media/Base
 */

'use strict';

const lang = require('lodash/lang');
const uuid4 = require('uuid/v4');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Entity = require('../../entity');
const PubSub = require('../../pubsub');

const TypeClasses = new Map;

/**
 * @alias module:BidCreator/Entities/Media/Base
 */
class Base extends Entity {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super();
        Object.assign(this.state, {
            id: !lang.isNil(data.id) ? data.id : null,
            type: null,
            item_id: !lang.isNil(data.item_id) ? data.item_id : null,
            order: null
        });

        if (lang.isObject(data)) {
            // only fill certain fields since they need to be validated, we don't want users setting things like id's
            this.fill(['order'], data);
        }
        // if we are loading an existing entity, we don't want the state to be changed which could trigger an
        // unnecessary save
        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {{LIBRARY_FILE: number}}
     */
    static get Type() {
        return {
            LIBRARY_FILE: 1
        };
    };

    /**
     * Get class object by type
     *
     * @param {number} type
     * @returns {Object}
     */
    static getClassByType(type) {
        if (!TypeClasses.has(type)) {
            throw new Error(`Unable to find class for type: ${type}`);
        }
        return TypeClasses.get(type);
    };

    /**
     * Get proper type instance based on entity object
     *
     * @param {Object} data
     * @param {number} data.type
     * @param {boolean} [existing=false]
     */
    static getByData(data, existing = false) {
        let type = this.getClassByType(data.type);
        return new type(data, existing);
    };

    /**
     * Get id
     *
     * If id isn't defined, one is created
     *
     * @readonly
     *
     * @returns {string}
     */
    get id() {
        if (this.state.id === null) {
            this.setState({
                id: uuid4()
            }, false);
        }
        return this.state.id;
    };

    /**
     * Get type
     *
     * @readonly
     *
     * @returns {number}
     */
    get type() {
        return this.state.type;
    };

    /**
     * Get item id
     *
     * @readonly
     *
     * @returns {?string}
     */
    get item_id() {
        return this.state.item_id;
    };

    /**
     * Get order
     *
     * @readonly
     *
     * @returns {number}
     */
    get order() {
        return this.state.order;
    };

    /**
     * Set order
     *
     * @param {number} value
     */
    set order(value) {
        if (!lang.isNumber(value)) {
            throw new Error('Order must be a number');
        }
        this.setState({
            order: value
        });
    };

    /**
     * Get payload for API save request
     *
     * @param {string} bid_item_id - Bid id
     * @returns {Object}
     */
    getPayload(bid_item_id) {
        return {
            id: this.id,
            bid_item_id: bid_item_id,
            type: this.type,
            item_id: this.item_id,
            order: this.order
        };
    };

    /**
     * Save entity
     *
     * @param {string} bid_item_id - Bid id
     * @returns {*}
     */
    save(bid_item_id) {
        this.setStateChanged(false);
        let request = new Api.BatchRequest.Single('bid-item-media', 'poly-create', this.getPayload(bid_item_id));
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
        return request.promise;
    };

    /**
     * Delete entity
     *
     * @returns {Promise}
     *
     * @emits module:BidCreator/Entities/Media/Base~deleted
     */
    delete() {
        return new Promise((resolve, reject) => {
            let request = new Api.BatchRequest.Single('bid-item-media', 'delete', {
                id: this.state.id
            });
            request.promise.then((data) => {
                this.events.emit('deleted');
                this.destroy();
                resolve(data);
            }, (error) => {
                console.log(error);
                reject(error);
            });
            PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, request);
        });
    };

    /**
     * Destroy entity
     */
    destroy() {
        this.events.emit('destroyed');
    };
}

module.exports = Base;

TypeClasses.set(Base.Type.LIBRARY_FILE, require('./library_file'));
