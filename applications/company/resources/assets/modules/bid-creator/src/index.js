/**
 * @module BidCreator
 */

'use strict';

const $ = require('jquery');

const EventEmitter = require('events');
const truncate = require('lodash/truncate');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');
require('@ca-submodule/validator');

const PubSub = require('./pubsub');
const SaveHandler = require('./save');
const Section = require('./section');
const Utils = require('./utils');
const LineItemHelper = require('./form-helpers/line_item');
const ProductItemHelper = require('./form-helpers/product_item');

const Modal = {
    Upgrade: require('./modals/upgrade'),
    Section: require('./modals/section'),
    Media: require('./modals/media'),
    Message: require('@ca-submodule/modal').Message,
    Submit: require('./modals/submit'),
    Finalize: require('./modals/finalize')
};

const Sidebar = require('./sidebar');
const FlashMessage = require('./flash-message');

const layout_tpl = require('@cam-bid-creator-tpl/layout.hbs');

/**
 * @alias module:BidCreator
 */
class BidCreator {
    /**
     * Constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            layout,
            booted: false,
            mode: BidCreator.Mode.INITIALIZE,
            loading: true,
            /**
             * @type {?string}
             */
            id: null,
            entity: null,
            messages: new FlashMessage.Controller(),
            sidebar: new Sidebar(this),
            sections: new Map,
            line_item_helper: null,
            product_item_helper: null,
            save_handler: new SaveHandler(this),
            before_unload_check: true,
            modals: {},
            events: new EventEmitter,
            preview_url: null,
            changed: true,
            save_dependant_buttons: []
        };
    };

    /**
     * Modes
     *
     * @readonly
     *
     * @returns {{INITIALIZE: number, BUILDER: number, PREVIEW: number}}
     */
    static get Mode() {
        return {
            INITIALIZE: 1,
            BUILDER: 2,
            PREVIEW: 3
        };
    };

    /**
     * Get bid id
     *
     * @readonly
     *
     * @returns {?string}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get property info
     *
     * @readonly
     *
     * @returns {Object}
     */
    get property() {
        return this.state.property;
    };

    /**
     * Get customer info
     *
     * @readonly
     *
     * @returns {Object}
     */
    get customer() {
        return this.state.customer;
    };

    /**
     * Get project id
     *
     * @readonly
     *
     * @returns {string}
     */
    get project_id() {
        return this.state.project_id;
    };

    /**
     * Get sections
     *
     * @readonly
     *
     * @returns {Map<string, Section>}
     */
    get sections() {
        return this.state.sections;
    };

    /**
     * Get save handler
     *
     * @readonly
     *
     * @returns {SaveHandler}
     */
    get save_handler() {
        return this.state.save_handler;
    };

    /**
     * Get sidebar
     *
     * @readonly
     *
     * @returns {Sidebar}
     */
    get sidebar() {
        return this.state.sidebar;
    };

    /**
     * Get message controller
     *
     * @readonly
     *
     * @returns {FlashMessage.Controller}
     */
    get messages() {
        return this.state.messages;
    };

    /**
     * Get modals
     *
     * @readonly
     *
     * @returns {Object}
     */
    get modals() {
        return this.state.modals;
    };

    /**
     * Get line item helper for use with forms
     *
     * @returns {LineItemHelper}
     */
    get line_item_helper() {
        if (this.state.line_item_helper === null) {
            this.state.line_item_helper = new LineItemHelper();
        }
        return this.state.line_item_helper;
    };

    /**
     * Get product item helper for use with forms
     *
     * @returns {ProductItemHelper}
     */
    get product_item_helper() {
        if (this.state.product_item_helper === null) {
            this.state.product_item_helper = new ProductItemHelper();
        }
        return this.state.product_item_helper;
    };

    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     * @returns {BidCreator}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Redirect to specified URL
     *
     * Note: disables unload check before running
     *
     * @param {string} url
     */
    redirect(url) {
        this.state.before_unload_check = false;
        window.location.href = url;
    };

    /**
     * Add section
     *
     * @param {Section} section
     * @param {number} section_count
     * @param {boolean} [sync=false] - Determines if section should be published synchronously
     */
    async addSection(section, section_count, sync = false) {
        // if no sections existed before this one, then we set the mode to builder
        if (this.state.sections.size === 0) {
            this.setMode(BidCreator.Mode.BUILDER);
        }
        this.state.sections.set(section.id, section);
        $(section.render(section_count)).appendTo(this.elem.sections);
        await section.boot();
        PubSub.Handler[sync ? 'publishSync' : 'publish'](PubSub.Topics.Section.ADD, section);
    };

    /**
     * Get section
     *
     * @param {string} section_id - Section uuid
     * @returns {(Section|undefined)}
     */
    getSection(section_id) {
        return this.state.sections.get(section_id);
    };

    /**
     * Get reorder batch request
     *
     * @param {string[]} list - Array of section ids in the needed order
     * @returns {(boolean|Api.BatchRequest.Multiple)}
     */
    getReorderBatchRequest(list) {
        let requests = [];
        list.forEach((section_id, order) => {
            order++; // increment key since we start at 1 instead of zero
            let section = this.getSection(section_id);
            // section could be undefined when getting a batch request which contains new sections which haven't been added yet
            if (section === undefined) {
                return;
            }
            // if order didn't change then do nothing
            if (section.order === order) {
                return;
            }
            let request = new Api.BatchRequest.Single('bid-item-section', 'partial-update', {
                id: section_id,
                order: order
            });
            request.promise.done(() => {
                // update section order so it is up-to-date
                section.order = order;
            });
            requests.push(request);
        });
        if (requests.length === 0) {
            return false;
        }
        let batch_request = new Api.BatchRequest.Multiple(true);
        for (let request of requests) {
            batch_request.add(request);
        }
        return batch_request;
    };

    /**
     * Update section order
     *
     * Sends API request to update any sections which order was changed in the provided list, sets the internal section order,
     * and reorders section elements in the DOM
     *
     * @param {string[]} list - Array of section uuid's
     * @param {boolean} [reorder=true] - Determines if sections are reordered in the DOM
     * @param {boolean} [notify=true] - Determines if event is emitted
     */
    updateSectionOrder(list, reorder = true, notify = true) {
        let batch_request = this.getReorderBatchRequest(list);
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, batch_request);
        this.setSectionOrder(list, reorder, notify);
    };

    /**
     * Set section order
     *
     * Updates internal section map to be in the specified order and optionally reorders section elements in the DOM
     *
     * @param {string[]} list - Array of section uuid's
     * @param {boolean} [reorder=true] - Determines if sections are reordered in the DOM
     * @param {boolean} [notify=true] - Determines if event is emitted
     *
     * @emits module:BidCreator~sectionsReordered
     */
    setSectionOrder(list, reorder = true, notify = true) {
        let sections = new Map;
        for (let section_id of list) {
            let section = this.getSection(section_id);
            sections.set(section.id, section);
        }
        this.state.sections = sections;
        if (reorder) {
            this.reorderSections();
        }
        if (notify) {
            this.state.events.emit('sections-reordered', {
                order: list,
                bid: this
            });
        }
    };

    /**
     * Reorder sections in DOM according to the order they are in the sections map
     */
    reorderSections() {
        if (!this.isBooted()) {
            return;
        }
        // loop through map and adjust placement
        let first = true,
            last_elem;
        for (let section of this.state.sections.values()) {
            if (!first) {
                section.elem.root.insertAfter(last_elem);
            }
            last_elem = section.elem.root;
            if (first) {
                first = false;
            }
        }
    };

    /**
     * Delete section from internal cache and reorder remaining sections if necessary
     *
     * This is called from Section class to cleanup it's lingering instances
     *
     * @param {string} section_id - Section uuid
     */
    deleteSection(section_id) {
        this.state.sections.delete(section_id);
        if (this.state.sections.size === 0) {
            this.setMode(BidCreator.Mode.INITIALIZE);
        }
    };

    /**
     * Set bid mode
     *
     * Currently only handles setting the default component for the sidebar general menu button
     *
     * @param {number} mode
     */
    setMode(mode) {
        this.state.mode = mode;
        let default_component;
        switch (mode) {
            case BidCreator.Mode.INITIALIZE:
                default_component = 'section_manager';
                break;
            case BidCreator.Mode.BUILDER:
                default_component = 'pricing';
                break;
            case BidCreator.Mode.PREVIEW:
                default_component = 'templates';
                break;
        }
        this.sidebar.setDefaultComponent(default_component);
    };

    /**
     * Determines if any save requests have been made
     *
     * @returns {boolean}
     */
    isChanged() {
        return this.state.changed;
    };

    /**
     * Set changed status
     *
     * @param {boolean} bool
     */
    setChanged(bool) {
        this.state.changed = bool;
    };

    /**
     * Get version of bid
     *
     * @param {string} id - Bid item uuid
     * @returns {Promise}
     */
    getVersion(id) {
        return new Promise((resolve, reject) => {
            Api.Resources.BidItems()
                .fields(['version', 'project_id'])
                .retrieve(id)
                .then(resolve, reject);
        });
    };

    /**
     * Show upgrade modal to give the user the option to upgrade the bid
     *
     * @param {string} id - Bid item uuid
     * @param {number} project_id - Project id
     * @returns {Promise}
     */
    startUpgrade(id, project_id) {
        return new Promise((resolve, reject) => {
            let modal = new Modal.Upgrade;
            modal.on('yes', () => {
                Api.Resources.BidItems().method(Api.Request.Method.PUT)
                    .custom(`${id}/upgrade`)
                    .then(() => {
                        modal.close().destroy();
                        resolve();
                    }, (error) => {
                        reject(error);
                    });
            })
                .on('no', () => {
                    window.location.href = window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', project_id) + '/bids';
                });
            modal.open();
        });
    };

    /**
     * Fetch full bid info from the server
     *
     * @param {string} id - Bid item uuid
     */
    retrieveBid(id) {
        Api.Resources.BidItems()
            .accept('application/vnd.adg.fx.bid-v1+json')
            .retrieve(id)
            .then(entity => this.load(entity))
            .catch(error => this.handleError(error));
    };

    /**
     * Apply closure to one or more button elements
     *
     * @param {Array} names
     * @param {function} closure
     */
    updateButtons(names, closure) {
        for (let name of names) {
            closure(this.elem.buttons[name]);
        }
    };

    /**
     * Set save dependant buttons
     *
     * @param {(Array|string)} name
     */
    setSaveDependantButtons(name) {
        if (typeof name === 'string') {
            name = [name];
        }
        this.state.save_dependant_buttons = this.state.save_dependant_buttons.concat(name);
    };

    /**
     * Determines if bid is in a loading state
     *
     * @returns {boolean}
     */
    isLoading() {
        return this.state.loading;
    };

    /**
     * Setup bid from API entity
     *
     * @protected
     * @param {module:Api/Entity} entity
     */
    async load(entity) {
        try {
            this.state.entity = entity;
            this.state.property = entity.get('project.property');
            this.state.customer = entity.get('project.property.customer');
            this.state.project_id = entity.get('project.id');
            this.state.preview_url = entity.get('file_media_urls.original');
            this.state.is_financing_enabled_for_project = entity.get('project.is_financing_enabled')
            this.state.loading = true;

            // if user doesn't have permission to finalize, we check the status
            if (!window.bid_info.permissions.finalize) {
                // if status is submitted, we disable the submit button since we don't allow submitting again
                if (entity.get('status') === Api.Constants.BidItems.Status.SUBMITTED) {
                    this.updateButtons(['submit-builder', 'submit-preview'], (button) => {
                        button.text('Submitted for Approval').prop('disabled', true);
                    });
                } else {
                    // if status is incomplete, then we add the submit button to the save dependant list so it will be
                    // disabled during syncs
                    this.setSaveDependantButtons(['submit-builder', 'submit-preview']);
                }
            } else {
                // if user is allowed to finalize, we add the finalize button to the save dependant list so it will be
                // disabled during syncs
                this.setSaveDependantButtons(['finalize-builder', 'finalize-preview']);
            }

            let sections = entity.get('sections', []);

            this.elem.root.fxFind('project-link')
                .attr('href', window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', this.project_id) + '/bids')
                .text(truncate(entity.get('project.description', ''), {length: 25}));

            let active_templates = {},
                template_fields = [
                    'cover_content_template_id', 'intro_content_template_id', 'sections_content_template_id',
                    'line_items_content_template_id', 'terms_conditions_content_template_id',
                    'images_content_template_id', 'media_content_template_id'
                ];
            for (let template_field of template_fields) {
                active_templates[template_field] = entity.get(template_field);
            }

            this.state.sidebar.load({
                section_manager: {},
                pricing: {
                    line_items: entity.get('line_items', []),
                    payment_terms: entity.get('payment_terms', []),
                    products: entity.get('line_item_products', [])
                },
                terms_conditions: {
                    content: entity.get('content', [])
                },
                uploads: {
                    custom_drawings: entity.get('custom_drawings', []),
                    drawings: entity.get('drawings', []),
                    project: entity.get('project')
                },
                media: {
                    media: entity.get('media', [])
                },
                info: {},
                templates: {
                    templates: entity.get('templates', []),
                    active_templates: active_templates
                }
            }, 'pricing');

            this.state.modals.section_add = new Modal.Section.Add(this);
            this.state.modals.section_edit = new Modal.Section.Edit(this);
            this.state.modals.media_library_add = new Modal.Media.LibraryAdd(this);

            let section_count = 1;
            for (let section of sections) {
                let $section = new Section(this, section);
                // add sections synchronously so the loading boolean will be reliable
                await this.addSection($section, section_count, true);
                section_count++;
            }

            this.elem.loader.hide();

            this.state.loading = false;

            if (sections.length === 0) {
                this.handleNewSection();
            }
        } catch (e) {
            this.handleError(e);
        }
    };

    /**
     * Get message modal
     *
     * If modal hasn't been used before, one will be created and cached for future use
     *
     * @returns {Modal.Message}
     */
    getMessageModal() {
        if (this.state.modals.message === undefined) {
            this.state.modals.message = new Modal.Message;
        }
        return this.state.modals.message;
    };

    /**
     * Trigger resize of all bid components
     *
     * @param {Object} data
     * @param {number} data.width - Window width
     */
    resize(data) {
        for (let section of this.state.sections.values()) {
            section.resize(data);
        }
        this.sidebar.resize(data);
    };

    /**
     * Determines if bid is in preview mode
     *
     * @returns {boolean}
     */
    isPreviewing() {
        return this.state.mode === BidCreator.Mode.PREVIEW;
    };

    /**
     * Show loading image on preview overlay
     */
    showPreviewLoading() {
        this.elem.preview_loading.show();
    };

    /**
     * Hide loading image on preview overlay
     */
    hidePreviewLoading() {
        this.elem.preview_loading.hide();
    };

    /**
     * Update preview iframe
     *
     * Create iframe if it doesn't exist, otherwise reset the src to force a reload
     */
    updatePreviewIframe() {
        let src = `${this.state.preview_url}?viewer=true&loaded_callback=bidPreviewLoaded`;
        if (this.elem.preview_iframe === undefined) {
            this.elem.preview_iframe = $(`<iframe src="${src}"/>`).appendTo(this.elem.preview);
        } else {
            this.elem.preview_iframe.attr('src', src);
        }
        this.showPreviewLoading();
    };

    /**
     * Reload preview
     *
     * Reload iframe if validation passes, otherwise hide it.
     */
    reloadPreview() {
        this.validate().then(() => {
            this.updatePreviewIframe();
        }, () => {
            this.hidePreview();
        });

    };

    /**
     * Show preview overlay after validation passes
     */
    showPreview() {
        if (this.isPreviewing()) {
            return;
        }
        this.validate().then(() => {
            this.setMode(BidCreator.Mode.PREVIEW);
            this.elem.action_groups.builder.hide();
            this.elem.action_groups.preview.show();
            this.updatePreviewIframe();
            this.elem.preview.addClass('t-open');
            // set changed to false so we can track if anything changed while the preview was open, this helps us
            // determine if validation should be run again when reloading or finalizing
            this.setChanged(false);
        }, () => {
            // do nothing with rejection since it's already handled before we get here
        });
    };

    /**
     * Hide preview
     */
    hidePreview() {
        this.elem.preview.removeClass('t-open');
        this.elem.action_groups.builder.show();
        this.elem.action_groups.preview.hide();
        this.setMode(BidCreator.Mode.BUILDER);
    };

    /**
     * Submit bid for approval, if user doesn't have permission to finalize
     */
    submit() {
        if (window.bid_info.permissions.finalize) {
            return;
        }
        this.validate().then(() => {
            if (this.state.modals.submit === undefined) {
                this.modals.submit = new Modal.Submit(this);
            }
            this.modals.submit.open();
        }, () => {
            this.hidePreview();
        });
    };

    /**
     * Finalize bid if user has permission
     *
     * Rerun validation to make sure nothing was changed in the sidebar while preview was open which could cause issues.
     * If validation passed, then we open the confirmation modal. Otherwise, hide preview so the errors can be resolved.
     */
    finalize() {
        if (!window.bid_info.permissions.finalize) {
            return;
        }
        this.validate().then(() => {
            if (this.state.modals.finalize === undefined) {
                this.modals.finalize = new Modal.Finalize(this);
            }
            this.modals.finalize.open();
        }, () => {
            if (this.isPreviewing()) {
                this.hidePreview();
            }
        });
    };

    /**
     * Set action button state
     *
     * @param {boolean} state - True is enabled, false is disabled
     */
    setActionButtonState(state) {
        for (let button of this.state.save_dependant_buttons) {
            this.elem.buttons[button].prop('disabled', !state);
        }
    };

    /**
     * Handle top bar actions
     *
     * @param {string} action
     */
    handleAction(action) {
        switch (action) {
            case 'preview':
                this.showPreview();
                break;
            case 'close-preview':
                this.hidePreview();
                break;
            case 'submit-builder':
            case 'submit-preview':
                this.submit();
                break;
            case 'finalize-builder':
            case 'finalize-preview':
                this.finalize();
                break;
            case 'reload':
                this.reloadPreview();
                break;
        }
    };

    /**
     * Determines if any bid components have incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        if (this.sidebar.hasIncompleteUploads()) {
            return true;
        }
        for (let section of this.state.sections.values()) {
            if (!section.hasIncompleteUploads()) {
                continue;
            }
            return true;
        }
        return false;
    }

    /**
     * Validate all bid components
     *
     * @returns {Promise}
     */
    validate() {
        return new Promise((resolve, reject) => {
            // if nothing has changed, then we bypass validation. this is used for running validation before preview
            // reloading and finalizing which already had to pass validation once. don't want to annoy people.
            if (!this.isChanged()) {
                resolve();
                return;
            }
            let message = null;
            if (this.state.sections.size === 0) {
                message = 'At least one section is required';
            } else {
                let pending_requests = this.save_handler.hasPendingRequests(),
                    incomplete_uploads = this.hasIncompleteUploads();
                if (pending_requests && incomplete_uploads) {
                    message = 'Not all changes have been saved and one or more uploads are still in progress';
                } else if (pending_requests) {
                    message = 'Not all changes have been saved';
                } else if (incomplete_uploads) {
                    message = 'One or more uploads are still in progress';
                }
                if (message !== null) {
                    message += '. Please wait a few moments and try again.';
                }
            }
            if (message !== null) {
                this.getMessageModal().openWithContent('Unable to Preview', `<p>${message}</p>`);
                reject();
                return;
            }
            let promises = [];
            for (let section of this.state.sections.values()) {
                // catch any rejects so we can process all the sections together, otherwise one failure would stop the
                // next one from running. we want the validation process to happen for each section so the proper messages
                // and/or modals are presented
                promises.push(section.validate().catch(data => {return data;}));
            }
            Promise.all(promises)
            // handle sections
                .then((sections) => {
                    let valid = true;
                    for (let section of sections) {
                        if (section.valid) {
                            continue;
                        }
                        valid = false;
                        break;
                    }
                    if (!valid) {
                        this.getMessageModal().openWithContent(
                            'Unable to Preview',
                            '<p>One or more errors were encountered within the sections of the bid. Please review and correct any issues before continuing.</p>'
                        );
                        return Promise.reject(false);
                    }
                    return this.sidebar.validate();
                })
                // handle sidebar
                .then(() => {
                    resolve();
                }, () => {
                    reject();
                });
        });
    };

    /**
     * Handle error
     *
     * @param error
     *
     * @todo figure how proper error handling across the bid
     */
    handleError(error) {
        console.log(error);
        alert('An error was encountered, please contact support');
    };

    handleNewSection() {
        let section_number = this.state.sections.size + 1;
        this.state.modals.section_add.open(section_number);
    };

    /**
     * Determines if bid is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot bid creator
     *
     * @param {string} id - Bid item uuid
     */
    boot(id) {
        this.state.layout.setModeWindow();
        this.state.layout.setSidebarMode();
        // render is handled by boot since we know the root element is already available and isn't loaded via render
        this.render();

        // find elements
        this.elem.loader = this.elem.root.fxFind('loading-overlay');
        this.elem.flash_messages = this.elem.root.fxFind('flash-messages');
        this.elem.column_wrapper = this.elem.root.fxFind('column-wrapper');
        this.elem.content = this.elem.root.fxFind('content');
        this.elem.actions = this.elem.root.fxFind('actions');
        this.elem.action_groups = {
            builder: this.elem.actions.fxFind('action-group', {mode: 'builder'}),
            preview: this.elem.actions.fxFind('action-group', {mode: 'preview'}).hide()
        };
        this.elem.buttons = {};
        for (let action of ['preview', 'reload', 'submit-builder', 'finalize-builder', 'submit-preview', 'finalize-preview']) {
            this.elem.buttons[action] = this.elem.actions.fxFind('action', {action: action});
        }

        // set buttons which will be disabled during syncs
        this.setSaveDependantButtons(['preview', 'reload']);

        // hide specific buttons based on bid permissions
        this.updateButtons(
            window.bid_info.permissions.finalize ? ['submit-builder', 'submit-preview'] : ['finalize-builder', 'finalize-preview'],
            (button) => {
                button.hide();
            }
        );

        this.elem.sections = this.elem.content.fxFind('sections');
        this.elem.add_section = this.elem.content.fxFind('add-section');
        this.elem.preview = this.elem.content.fxFind('preview');
        this.elem.preview_loading = this.elem.preview.fxChildren('preview-loading');

        this.state.save_handler.on('request-received', () => {
            this.setChanged(true);
        })
            .on('pending', () => {
                this.setActionButtonState(false);
            })
            .on('saved', () => {
                this.setActionButtonState(true);
            });
        this.state.save_handler.boot();

        this.state.messages.boot(this.elem.flash_messages);
        this.state.sidebar.boot();

        // event handlers
        const that = this;
        this.elem.actions.fxClickWatcher('action', function (e) {
            e.preventDefault();
            that.handleAction($(this).data('action'));
            return false;
        });
        this.elem.sections.fxClickWatcher('section-edit-trigger', function (e) {
            e.preventDefault();
            let section_id = $(this).fxParents('section').data('id');
            that.state.modals.section_edit.open(that.getSection(section_id));
            return false;
        });

        let win = $(window);
        win.fxEvent(['resize'], Utils.debounce(() => {
            this.resize({
                width: win.width()
            });
        }, 500), {namespace: 'bc'});
        win.trigger('resize'); // trigger resize so the layout adjusts to current screen size properly

        window.addEventListener('beforeunload', (e) => {
            if (!this.state.before_unload_check || (!this.save_handler.hasPendingRequests() && !this.hasIncompleteUploads())) {
                return undefined;
            }
            let text = 'Not all changes have been saved';
            e.returnValue = text;
            return text;
        });

        // set function on window for our embedded pdf viewer iframe to call when it is done loading
        window.bidPreviewLoaded = () => {
            this.hidePreviewLoading();
        };

        this.state.id = id;

        // load bid by checking it's version, handling any upgrades, and then loading
        this.getVersion(id).then((entity) => {
            // if version is the same as current, we continue like normal
            if (entity.get('version') === window.bid_info.current_version) {
                this.retrieveBid(id);
                return;
            }
            this.startUpgrade(id, entity.get('project_id')).then(() => {
                this.retrieveBid(id);
            }, (error) => {
                this.handleError(error);
            });
        }, (error) => {
            this.handleError(error);
        });

        this.elem.add_section.fxClick((e) => {
            e.preventDefault();
            this.handleNewSection();
        });

        this.state.booted = true;
    };

    /**
     * Render layout template into root element passed in constructor
     *
     * @protected
     */
    render() {
        this.elem.root = $(layout_tpl({
            messages: this.state.messages.render()
        }));
        this.state.layout.elem.content.append(this.elem.root);
    };
}

module.exports = BidCreator;
