<div class="m-training">
    <div class="c-t-inner" data-js="inner">
        <div class="c-ti-training">
            <div class="c-tit-title">
                <h3 class="c-titt-text" data-js="title"></h3>
                {{#if show_finalize}}
                    <a class="c-titt-button" data-navigate="complete">
                        <h5 class="c-ticwb-title">Finalize Training</h5>
                    </a>
                {{/if}}
            </div>
            <div class="c-tit-video" data-js="video">
                <div data-js="player"></div>
            </div>
            <div class="c-tit-sections" data-js="sections"></div>
        </div>
        <div class="c-ti-complete">
            <h3>Next Steps</h3>
            {{#if is_primary}}
            <p>Click the buttons below to schedule a demo with one of our customer success managers, start your setup
                wizard, or finalize the training hub.</p>
            <div class="c-tic-wrapper">
                {{#if is_setup_wizard}}
                <a class="c-ticw-button" target="_blank" href="{{setup_wizard_link}}">
                    <h5 class="c-ticwb-circle">1</h5>
                    <h5 class="c-ticwb-title">Add Your Company Info</h5>
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-line"></use></svg>
                </a>
                {{else}}
                <div class="c-ticw-button t-disabled">
                    <h5 class="c-ticwb-circle">1</h5>
                    <h5 class="c-ticwb-title">Setup Wizard Complete</h5>
                </div>
                {{/if}}
                <a class="c-ticw-button" target="_blank" href="{{demo_link}}">
                    <h5 class="c-ticwb-circle">2</h5>
                    <h5 class="c-ticwb-title">Schedule Demo</h5>
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-line"></use></svg>
                </a>
                <a class="c-ticw-button" data-navigate="complete">
                    <h5 class="c-ticwb-circle">3</h5>
                    <h5 class="c-ticwb-title">Finalize Training</h5>
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-line"></use></svg>
                </a>
            </div>
            {{else}}
            <p>Click the button below to finalize the training hub.</p>
            <div class="c-tic-wrapper t-1-column">
                <a class="c-ticw-button" data-navigate="complete">
                    <div class="c-ticwb-circle">
                        <svg><use xlink:href="#remix-icon--system--check-line"></use></svg>
                    </div>
                    <h5 class="c-ticwb-title">Finalize Training</h5>
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-line"></use></svg>
                </a>
            </div>
            {{/if}}
        </div>
    </div>
    <div class="c-t-footer">
        <div class="c-tf-inner">
            <div class="c-tfi-info">
                <div class="c-tfii-title">
                    <svg class="c-tfiit-icon"><use xlink:href="#remix-icon--document--book-marked-line"></use></svg>
                    <h5 class="c-tfiit-text">Visit the Help Center</h5>
                </div>
                <div class="c-tfii-content">
                    <ol>
                        <li>Click the profile icon in the top right corner</li>
                        <li>Click Help</li>
                    </ol>
                </div>
            </div>
            <div class="c-tfi-info">
                <div class="c-tfii-title">
                    <svg class="c-tfiit-icon"><use xlink:href="#remix-icon--business--customer-service-line"></use></svg>
                    <h5 class="c-tfiit-text">Contact Support</h5>
                </div>
                <div class="c-tfii-content">
                    <ol>
                        <li>Click the support button in Help Center to submit a support ticket</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>