'use strict';

import 'remixicon/icons/System/close-line.svg';
import 'remixicon/icons/Document/book-marked-line.svg';

import $ from 'jquery';

import {find, findChild, insertTemplate, jsSelector, onClick} from '@ca-package/dom';

import Modal from '@ca-package/router/src/modal';
import {getPlayer} from '@cac-js/utils/youtube_iframe';

import module_tpl from '@cam-training-tpl/main-pages/module.hbs';
import content_tpl from '@cam-training-tpl/main-pages/module-components/content.hbs';

/**
 * @memberof module:Training/Pages/MainPages
 */
export class ModulePage extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        this.elem = {};
    };

    /**
     * Get layout instance
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.parent.layout;
    };

    /**
     * Fetch module content to display
     *
     * @param {number} module_id
     * @returns {object}
     */
    fetchContent(module_id) {
        return $.getJSON(`${window.fx_url.API}training/module/${module_id}`).promise();
    };

    /**
     * Populate modal with content
     *
     * @param {object} module
     * @returns {Promise<void>}
     */
    async populate({module}) {
        this.elem.title.text(module.title);
        this.elem.help_center.attr('href', module.help_center_url);
        // if try url includes project id, we need to replace the project id with the first project for the company
        if (module.try_url.includes('{{project_id}}')) {
            module.try_url = module.try_url.replace('{{project_id}}', window.training_data.first_project_id);
        }
        this.elem.try_now.attr('href', module.try_url);

        let has_video = module.youtube_video_id !== null,
            content = insertTemplate(this.elem.content, content_tpl({
                intro: module.intro,
                has_video,
                image: module.image_url,
                content: module.content
            }));
        content.insertAfter(this.elem.header);
        this.elem.content = content;

        if (has_video) {
            let player_elem = findChild(content, jsSelector('player'));
            await getPlayer(player_elem[0], {
                width: '100%',
                height: '100%',
                videoId: module.youtube_video_id,
                playerVars: {
                    modestbranding: 1,
                    playsinline: 1,
                    rel: 0,
                    start: module.youtube_video_start_time ?? 0
                }
            });
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        this.layout.showLoader();
        await super.load(request, next);
        this.fetchContent(request.params.id).then(data => this.populate(data)).then(() => {
            this.layout.hideLoader();
            this.elem.body.addClass('t-module-open');
            this.elem.overlay.addClass('t-show');
        }).catch(e => console.log(e));
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.elem.overlay.removeClass('t-show');
        this.elem.body.removeClass('t-module-open');
        await super.unload(request, next);

        this.elem.content.remove();
        this.elem.title.text('');
    };

    /**
     * Boot page
     */
    boot() {
        super.boot();

        this.elem.body = find('body');
        this.elem.overlay = insertTemplate(this.elem.body, module_tpl());
        this.elem.header = findChild(this.elem.overlay, jsSelector('header'));
        this.elem.title = findChild(this.elem.header, jsSelector('title'));
        this.elem.close = findChild(this.elem.header, jsSelector('close'));
        this.elem.help_center = findChild(this.elem.overlay, jsSelector('help-center'));
        this.elem.try_now = findChild(this.elem.overlay, jsSelector('try-now'));

        onClick(this.elem.close, () => this.router.navigate('main'), true);
    };
}
