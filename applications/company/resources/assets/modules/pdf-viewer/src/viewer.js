/* Copyright 2016 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

'use strict';

const PdfWorker = require('pdf.js/src/core/worker?worker');

function getViewerConfiguration() {
    return {
        appContainer: document.body,
        mainContainer: document.getElementById('viewerContainer'),
        viewerContainer: document.getElementById('viewer'),
        eventBus: null, // Using global event bus with (optional) DOM events.
        toolbar: {
            container: document.getElementById('toolbarViewer'),
            numPages: document.getElementById('numPages'),
            pageNumber: document.getElementById('pageNumber'),
            scaleSelectContainer: document.getElementById('scaleSelectContainer'),
            scaleSelect: document.getElementById('scaleSelect'),
            customScaleOption: document.getElementById('customScaleOption'),
            previous: document.getElementById('previous'),
            next: document.getElementById('next'),
            zoomIn: document.getElementById('zoomIn'),
            zoomOut: document.getElementById('zoomOut'),
            viewFind: document.getElementById('viewFind'),
            openFile: document.getElementById('openFile'),
            print: document.getElementById('print'),
            presentationModeButton: document.getElementById('presentationMode'),
            download: document.getElementById('download'),
            viewBookmark: document.getElementById('viewBookmark'),
        },
        secondaryToolbar: {
            toolbar: document.getElementById('secondaryToolbar'),
            toggleButton: document.getElementById('secondaryToolbarToggle'),
            toolbarButtonContainer:
                document.getElementById('secondaryToolbarButtonContainer'),
            presentationModeButton:
                document.getElementById('secondaryPresentationMode'),
            openFileButton: document.getElementById('secondaryOpenFile'),
            printButton: document.getElementById('secondaryPrint'),
            downloadButton: document.getElementById('secondaryDownload'),
            viewBookmarkButton: document.getElementById('secondaryViewBookmark'),
            firstPageButton: document.getElementById('firstPage'),
            lastPageButton: document.getElementById('lastPage'),
            pageRotateCwButton: document.getElementById('pageRotateCw'),
            pageRotateCcwButton: document.getElementById('pageRotateCcw'),
            cursorSelectToolButton: document.getElementById('cursorSelectTool'),
            cursorHandToolButton: document.getElementById('cursorHandTool'),
            scrollVerticalButton: document.getElementById('scrollVertical'),
            scrollHorizontalButton: document.getElementById('scrollHorizontal'),
            scrollWrappedButton: document.getElementById('scrollWrapped'),
            spreadNoneButton: document.getElementById('spreadNone'),
            spreadOddButton: document.getElementById('spreadOdd'),
            spreadEvenButton: document.getElementById('spreadEven'),
            documentPropertiesButton: document.getElementById('documentProperties'),
        },
        fullscreen: {
            contextFirstPage: document.getElementById('contextFirstPage'),
            contextLastPage: document.getElementById('contextLastPage'),
            contextPageRotateCw: document.getElementById('contextPageRotateCw'),
            contextPageRotateCcw: document.getElementById('contextPageRotateCcw'),
        },
        sidebar: {
            // Divs (and sidebar button)
            outerContainer: document.getElementById('outerContainer'),
            viewerContainer: document.getElementById('viewerContainer'),
            toggleButton: document.getElementById('sidebarToggle'),
            // Buttons
            thumbnailButton: document.getElementById('viewThumbnail'),
            outlineButton: document.getElementById('viewOutline'),
            attachmentsButton: document.getElementById('viewAttachments'),
            // Views
            thumbnailView: document.getElementById('thumbnailView'),
            outlineView: document.getElementById('outlineView'),
            attachmentsView: document.getElementById('attachmentsView'),
        },
        sidebarResizer: {
            outerContainer: document.getElementById('outerContainer'),
            resizer: document.getElementById('sidebarResizer'),
        },
        findBar: {
            bar: document.getElementById('findbar'),
            toggleButton: document.getElementById('viewFind'),
            findField: document.getElementById('findInput'),
            highlightAllCheckbox: document.getElementById('findHighlightAll'),
            caseSensitiveCheckbox: document.getElementById('findMatchCase'),
            entireWordCheckbox: document.getElementById('findEntireWord'),
            findMsg: document.getElementById('findMsg'),
            findResultsCount: document.getElementById('findResultsCount'),
            findPreviousButton: document.getElementById('findPrevious'),
            findNextButton: document.getElementById('findNext'),
        },
        passwordOverlay: {
            overlayName: 'passwordOverlay',
            container: document.getElementById('passwordOverlay'),
            label: document.getElementById('passwordText'),
            input: document.getElementById('password'),
            submitButton: document.getElementById('passwordSubmit'),
            cancelButton: document.getElementById('passwordCancel'),
        },
        documentProperties: {
            overlayName: 'documentPropertiesOverlay',
            container: document.getElementById('documentPropertiesOverlay'),
            closeButton: document.getElementById('documentPropertiesClose'),
            fields: {
                'fileName': document.getElementById('fileNameField'),
                'fileSize': document.getElementById('fileSizeField'),
                'title': document.getElementById('titleField'),
                'author': document.getElementById('authorField'),
                'subject': document.getElementById('subjectField'),
                'keywords': document.getElementById('keywordsField'),
                'creationDate': document.getElementById('creationDateField'),
                'modificationDate': document.getElementById('modificationDateField'),
                'creator': document.getElementById('creatorField'),
                'producer': document.getElementById('producerField'),
                'version': document.getElementById('versionField'),
                'pageCount': document.getElementById('pageCountField'),
                'pageSize': document.getElementById('pageSizeField'),
                'linearized': document.getElementById('linearizedField'),
            },
        },
        errorWrapper: {
            container: document.getElementById('errorWrapper'),
            errorMessage: document.getElementById('errorMessage'),
            closeButton: document.getElementById('errorClose'),
            errorMoreInfo: document.getElementById('errorMoreInfo'),
            moreInfoButton: document.getElementById('errorShowMore'),
            lessInfoButton: document.getElementById('errorShowLess'),
        },
        printContainer: document.getElementById('printContainer'),
        openFileInputName: 'fileInput',
        debuggerScriptPath: './debugger.js',
        imageResourcesPath: window.CA_VENDOR_PATH + 'images/',
        cMapUrl: window.CA_VENDOR_PATH + 'cmaps/'
    };
}

function setPDFOptions(app_options) {

    // PDF preferences are stored in local storage.
    // We clean it first so we can start fresh.
    const keysToRemove = Object.keys(localStorage).filter(key => key.startsWith('pdfjs.history'));
    keysToRemove.forEach(key => localStorage.removeItem(key));

    app_options.set('workerPort', new PdfWorker());
    app_options.set('defaultUrl', window.DEFAULT_URL);
    app_options.set('disablePreferences', true);
    app_options.set('eventBusDispatchToDOM', true);

    app_options.set('sidebarViewOnLoad', 1);  // 1 is the value for Thumbnails view
    app_options.set('defaultZoomValue', 90);

    return app_options;
}

function webViewerLoad() {
    let config = getViewerConfiguration(),
        app = require('pdfjs-web/app'),
        app_options = require('pdfjs-web/app_options').AppOptions;
    require('pdfjs-web/genericcom');
    require('pdfjs-web/pdf_print_service');

    app_options = setPDFOptions(app_options);

    window.PDFViewerApplication = app.PDFViewerApplication;
    window.PDFViewerApplicationOptions = app_options;

    app.PDFViewerApplication.run(config);
}

if (document.readyState === 'interactive' ||
    document.readyState === 'complete') {
    webViewerLoad();
} else {
    document.addEventListener('DOMContentLoaded', webViewerLoad, true);
}

document.addEventListener('pagesloaded', function () {
    if (typeof window.LOADED_CALLBACK !== 'string' || window.LOADED_CALLBACK.length === 0 || window.parent[window.LOADED_CALLBACK] === undefined) {
        return;
    }
    window.parent[window.LOADED_CALLBACK]();
}, true);
