'use strict';

const Api = require('@ca-package/api');

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
const Modal = require('@ca-submodule/modal').Base;

const FormValidator = require('@ca-submodule/validator');

const modal_tpl = require('@cam-lead-tpl/modals/main/manager/kill.hbs');

class Kill extends Modal {
    /**
     * Constructor
     *
     * @param {module:Lead} module
     */
    constructor(module) {
        super(modal_tpl({}), {
            size: Modal.Size.TINY,
            classes: ['t-kill-lead']
        });
        Object.assign(this.state, {
            module,
            lead: null,
            external_close: false,
            editor: null
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        this.elem.lead = this.elem.content.fxFind('lead');
        this.elem.form = this.elem.content.fxFind('form');

        this.elem.input = {};
        for (let name of [
            'dead_notes'
        ]) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.validator = FormValidator.create(this.elem.form, {
            dead_notes: {}
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });
    };

    /**
     * Populate data into form
     *
     * @param data
     * @returns {Promise<void>}
     */
    async populate(data) {
        this.elem.lead.text(`${data.first_name} ${data.last_name}`);

        if (data.dead_notes !== null) {
            this.elem.input.dead_notes.val(data.dead_notes).trigger('change');
        }
    };

    /**
     * Load dead notes wysiwyg
     *
     * @returns {Promise<*>}
     */
    async loadWysiwyg() {
        if (this.state.editor === null) {
            let textarea_config = {
                preset: 'simple',
                height: 200,
                remove_empty_paragraphs: true,
                no_image: true
            };
            this.state.dead_notes = FormInput.init(this.state.validator.getInputElem('dead_notes'), textarea_config);
            this.state.editor = await this.state.dead_notes.promise;
        }
        return this.state.editor;
    };

    /**
     * Fetch id for lead
     *
     * @param {string} lead_uuid
     * @returns {Promise<*>}
     */
    async fetchID(lead_uuid) {
        let {entities: leads} = await await Api.Resources.Leads().fields(['id', 'lead_uuid'])
            .filter('lead_uuid', lead_uuid)
            .all();
        let data = leads.map(lead => lead.data);

        return data[0];
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.lead_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    async open({lead_id, promise}) {
        this.state.promise = promise;
        await this.loadWysiwyg();
        this.startWorking();
        let data = await this.fetchID(lead_id);

        Api.Resources.Leads()
            .retrieve(data.id)
            .then((entity) => {
                let data = entity.get();
                this.state.lead = data;
                this.populate(data);
                this.resetWorking();
            }, error => {
                this.showErrorMessage('Unable to fetch lead info');
                console.log(error);
            });
        this.setTitle('Kill Lead');
        return super.open();
    };

    /**
     * Save changes
     */
    save() {
        this.startWorking();
        let dead_notes = this.state.validator.getInputElem('dead_notes').val();
        let data = {
            status: Api.Constants.Leads.Status.DEAD,
            priority: Api.Constants.Leads.Priority.DEAD
        };
        data['dead_notes'] = dead_notes !== '' ? dead_notes : null;

        Api.Resources.Leads().partialUpdate(this.state.lead.id, data).then(() => {
            this.resetWorking();
            this.state.promise.resolve(true);
            // this.close();
        }, (error) => {
            if (error.code === 1014) {
                error.message = 'Unable to kill lead';
            }
            this.showErrorMessage(error.message);
            this.resetWorking();
        });
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.state.promise.resolve(null);
        this.elem.content.scrollTop(0);
        this.resetWorking();
        this.state.lead = null;
        this.state.validator.reset();

        this.elem.form[0].reset();
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = Kill;
