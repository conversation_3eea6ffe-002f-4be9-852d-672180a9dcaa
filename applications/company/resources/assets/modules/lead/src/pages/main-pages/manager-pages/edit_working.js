'use strict';

import Modal from '@ca-package/router/src/modal';

import EditWorkingLeadModal from '../../../modals/main/manager/edit_working';

import {createSuccessMessage} from '@cas-notification-toast-js/message/success';

export class EditWorking extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get and cache edit working lead modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = EditWorkingLeadModal;
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open edit working lead modal with promise
     *
     * @param {string} lead_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal(lead_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                lead_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        this.openModal(request.params.lead_id).then((result) => {
            if (result === null) { // no action was taken
                this.router.navigate('manager');
                return;
            }
            this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Working notes successfully updated'));
            // redirect instead of navigate to remove the modal from the nav history so user can't hit back
            // and see the same modal which will no longer work
            this.router.navigate('manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}
