'use strict';

import moment from 'moment-timezone';

import Api from '@ca-package/api';
import Page from '@ca-package/router/src/page';
import {Base as Table} from '@ca-submodule/table';

const Cookie = require('@cac-js/utils/cookie');

import {Delete} from './manager-pages/delete';
import {Create} from './manager-pages/create';
import {Edit} from './manager-pages/edit';
import {EditWorking} from './manager-pages/edit_working';
import {Working} from './manager-pages/working';
import {Kill} from './manager-pages/kill';
import {Details} from './manager-pages/details';

import manager_tpl from '@cam-lead-tpl/pages/main-pages/manager.hbs';

export class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            page_name: 'lead',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                },
                filters: {
                    status: [Table.Operators.IN, [
                        Api.Constants.Leads.Status.NEW, Api.Constants.Leads.Status.WORKING
                    ]]
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            create: {
                path: '/create',
                modal: Create
            },
            edit: {
                path: '/edit/{lead_id}',
                modal: Edit,
                bindings: {
                    lead_id: 'uuid'
                }
            },
            kill: {
                path: '/kill/{lead_id}',
                modal: Kill,
                bindings: {
                    lead_id: 'uuid'
                }
            },
            working: {
                path: '/working/{lead_id}',
                modal: Working,
                bindings: {
                    lead_id: 'uuid'
                }
            },
            edit_working: {
                path: '/edit-working/{lead_id}',
                modal: EditWorking,
                bindings: {
                    lead_id: 'uuid'
                }
            },
            delete: {
                path: '/delete/{lead_id}',
                modal: Delete,
                bindings: {
                    lead_id: 'uuid'
                }
            },
            details: {
                path: '/details/{lead_id}',
                modal: Details,
                bindings: {
                    lead_id: 'uuid'
                }
            }
        };
    };

    /**
     * Define the default settings for customer DataTable
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            load_tooltip: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Create the Leads DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                this.router.navigate('manager.details', {
                    lead_id: data.lead_uuid,
                });
            })
            .on('scope_change', (scope) => {
                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_table_visibility`, config.visibility.toString());
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Leads'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            project_type_id: {
                label: 'Project Type',
                type: Table.FilterValueTypes.SELECT,
                options: lead_data.filter_options.project_types
            },
            priority: {
                label: 'Priority',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'Hot',
                        value: Api.Constants.Leads.Priority.HOT
                    },
                    2: {
                        label: 'Warm',
                        value: Api.Constants.Leads.Priority.WARM
                    },
                    3: {
                        label: 'Cold',
                        value: Api.Constants.Leads.Priority.COLD
                    },
                    4: {
                        label: 'Dead',
                        value: Api.Constants.Leads.Priority.DEAD
                    }
                }
            },
            assigned_to_user_id: {
                label: 'Assigned To User',
                type: Table.FilterValueTypes.SELECT,
                options: lead_data.filter_options.all_users
            },
            marketing_type_id: {
                label: 'Marketing Source',
                type: Table.FilterValueTypes.SELECT,
                options: lead_data.filter_options.marketing_sources
            },
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'New',
                        value: Api.Constants.Leads.Status.NEW
                    },
                    2: {
                        label: 'Working',
                        value: Api.Constants.Leads.Status.WORKING
                    },
                    3: {
                        label: 'Converted',
                        value: Api.Constants.Leads.Status.CONVERTED
                    },
                    4: {
                        label: 'Dead',
                        value: Api.Constants.Leads.Status.DEAD
                    }
                }
            },
            origin: {
                label: 'Origin',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Standard',
                        value: Api.Constants.Leads.Origin.STANDARD
                    },
                    2: {
                        label: 'Website Leads Form',
                        value: Api.Constants.Leads.Origin.WEBSITE_LEADS_FORM
                    }
                }
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: lead_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: lead_data.filter_options.all_users
            },
            working_at: {
                label: 'Working Date',
                type: Table.FilterValueTypes.DATE,
            },
            working_by_user_id: {
                label: 'Working By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: lead_data.filter_options.all_users
            },
            converted_at: {
                label: 'Converted Date',
                type: Table.FilterValueTypes.DATE,
            },
            converted_by_user_id: {
                label: 'Converted By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: lead_data.filter_options.all_users
            },
            dead_at: {
                label: 'Dead Date',
                type: Table.FilterValueTypes.DATE,
            },
            dead_by_user_id: {
                label: 'Dead By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: lead_data.filter_options.all_users
            }
        });

        let status_map = new Map([
            [Api.Constants.Leads.Status.NEW, '<span class="h-text t-blue">New</span>'],
            [Api.Constants.Leads.Status.WORKING, '<span class="h-text t-yellow">Working</span>'],
            [Api.Constants.Leads.Status.CONVERTED, '<span class="h-text t-green">Converted</span>'],
            [Api.Constants.Leads.Status.DEAD, '<span class="h-text t-grey">Dead</span>']
        ]);

        let priority_map = new Map([
            [Api.Constants.Leads.Priority.HOT, '<span class="h-text t-red">Hot</span>'],
            [Api.Constants.Leads.Priority.WARM, '<span class="h-text t-yellow">Warm</span>'],
            [Api.Constants.Leads.Priority.COLD, '<span class="h-text t-blue">Cold</span>'],
            [Api.Constants.Leads.Priority.DEAD, '<span class="h-text t-grey">Dead</span>']
        ]);

        let origin_map = new Map([
            [Api.Constants.Leads.Origin.STANDARD, 'Standard'],
            [Api.Constants.Leads.Origin.WEBSITE_LEADS_FORM, 'Website Leads Form'],
        ]);

        // set columns config
        this.state.table.setColumns({
            priority: {
                label: 'Priority',
                value: data => priority_map.get(data.priority)
            },
            project_type_name: {
                label: 'Project Type'
            },
            first_name: {
                label: 'Lead Name',
                value: (data) => {
                    let name = '';
                    if (data.first_name !== null) {
                        name = data.first_name;
                    }
                    if (data.last_name !== null) {
                        if (name !== '') {
                            name = `${name} `;
                        }
                        name = name + data.last_name;
                    }
                    if (data.business_name !== null) {
                        if (name !== '') {
                            name = `${name} (${data.business_name})`;
                        } else {
                            name = data.business_name;
                        }
                    }
                    return name;
                }
            },
            phone_number: {
                label: 'Phone',
            },
            email: {
                label: 'Email'
            },
            address: {
                label: 'Address',
                value: (data) => {
                    // address, address2, city, state, zip
                    let address = null;
                    if (data.address !== null) {
                        address = data.address;
                    }
                    if (data.city !== null) {
                        if (address !== '') {
                            address = `${address}, `;
                        }
                        address = address + data.city;
                    }
                    if (data.state !== null) {
                        if (address !== '') {
                            address = `${address}, `;
                        }
                        address = address + data.state;
                    }
                    if (data.zip !== null) {
                        if (address !== '') {
                            address = `${address}, `;
                        }
                        address = address + data.zip;
                    }
                    return address;
                }
            },
            status: {
                label: 'Status',
                value: data => status_map.get(data.status)
            },
            origin: {
                label: 'Origin',
                value: data => origin_map.get(data.origin)
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            created_by_user_name: {
                label: 'Created By',
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            },
            assigned_to_user_name: {
                label: 'Assigned To'
            },
            marketing_source: {
                label: 'Marketing Source'
            },
            working_at: {
                label: 'Working',
                value: (data) => {
                    let date = data.working_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            working_by_user_name: {
                label: 'Working By'
            },
            customer_name: {
                label: 'Customer Name',
                orderable: false,
                value: (data) => {
                    if (data.customer !== null) {
                        return `<a href="${fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.customer.id)}">${data.customer.first_name} ${data.customer.last_name}</a>`;
                    }
                    return;
                }
            },
            converted_at: {
                label: 'Converted',
                value: (data) => {
                    let date = data.converted_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            converted_by_user_name: {
                label: 'Converted By',
            },
            dead_at: {
                label: 'Dead',
                value: (data) => {
                    let date = data.dead_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            dead_by_user_name: {
                label: 'Dead By',
            }
        });

        // set row action config
        this.state.table.setRowActions({
            details: {
                label: 'Details',
                action: (data) => {
                    this.router.navigate('manager.details', {
                        lead_id: data.lead_uuid
                    });
                }
            },
            customer: {
                label: 'View Customer',
                visible: data => data.status === Api.Constants.Leads.Status.CONVERTED,
                link: {
                    href: (data) => {
                        return window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.customer.id);
                    },
                    target: '_blank'
                }
            },
            working: {
                label: 'Work Lead',
                visible: (data) => {
                    let allowed_statuses = [Api.Constants.Leads.Status.WORKING, Api.Constants.Leads.Status.CONVERTED];
                    if (allowed_statuses.indexOf(data.status) >= 0) {
                        return false;
                    }
                    return true;
                },
                action: (data) => {
                    this.router.navigate('manager.working', {
                        lead_id: data.lead_uuid
                    });
                }
            },
            edit_working: {
                label: 'Edit Working Notes',
                visible: data => data.status === Api.Constants.Leads.Status.WORKING,
                action: (data) => {
                    this.router.navigate('manager.edit_working', {
                        lead_id: data.lead_uuid
                    });
                }
            },
            convert: {
                label: 'Convert Lead',
                visible: data => data.status !== Api.Constants.Leads.Status.CONVERTED,
                confirm: true,
                link: {
                    href: (data) => {
                        return window.fx_pages.NEW_CUSTOMER.replace('{lead_id}', data.lead_uuid);
                    }
                }
            },
            kill: {
                label: 'Kill Lead',
                visible: (data) => {
                    let not_allowed_statuses = [Api.Constants.Leads.Status.DEAD, Api.Constants.Leads.Status.CONVERTED];
                    if (not_allowed_statuses.indexOf(data.status) >= 0) {
                        return false;
                    }
                    return true;
                },
                action: (data) => {
                    this.router.navigate('manager.kill', {
                        lead_id: data.lead_uuid
                    });
                }
            },
            edit: {
                label: 'Edit',
                visible: (data) => {
                    let not_allowed_statuses = [Api.Constants.Leads.Status.DEAD, Api.Constants.Leads.Status.CONVERTED];
                    if (not_allowed_statuses.indexOf(data.status) >= 0) {
                        return false;
                    }
                    return true;
                },
                action: (data) => {
                    this.router.navigate('manager.edit', {
                        lead_id: data.lead_uuid
                    });
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                visible: (data) => {
                    if (lead_data.is_primary) {
                        return true;
                    }
                    let allowed_statuses = [Api.Constants.Leads.Status.CONVERTED];
                    if (allowed_statuses.indexOf(data.status) >= 0) {
                        return false;
                    }
                    if (data.created_by_user_id === lead_data.user_id) {
                        return true;
                    }
                },
                action: (data) => {
                    this.router.navigate('manager.delete', {
                        lead_id: data.lead_uuid
                    });
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/leads' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                }
            },
            add: {
                label: 'New Lead',
                action: () => {
                    this.router.navigate('manager.create');
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.Leads, (request) => {
            request.accept('application/vnd.adg.fx.collection-v2+json');
        });
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        // false means we don't want to reset paging
        this.state.table.draw(false);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.table_loaded) {
            // false means we don't want to reset paging
            this.state.table.draw(false);
        } else {
            // if request query contains scope we use it
            if (Object.keys(request.query).length > 0) {
                this.state.table_scope = Table.buildScopeFromQuery(request.query);
                // otherwise we get it from the cookie
            } else if (this.state.saved_scope !== null) {
                this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
            }
            // otherwise we pull from the default scope stored in the state
            this.state.table.setState(this.state.table_scope);
            this.state.table.build();
            this.state.table_loaded = true;
        }
        await super.load(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }
        this.createTable();
    };

    /**
     * Render page
     */
    render() {
        return manager_tpl();
    };
}
