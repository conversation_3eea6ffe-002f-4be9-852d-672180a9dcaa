'use strict';

import Page from '@ca-package/router/src/page';

import main_tpl from '@cam-lead-tpl/pages/main.hbs';

import {Manager} from './main-pages/manager';

export class MainPage extends Page {
    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: Manager
            }
        };
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.pages = root.fxFind('pages');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl();
    };
}
