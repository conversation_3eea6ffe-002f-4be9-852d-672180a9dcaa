<div class="m-subscription" data-id="subscription">
    <form class="m-signup-form" data-js="form">
        <div class="c-sf-error" data-js="error"></div>
        <h4 class="c-sf-header">Start Trial</h4>
        <p class="c-sf-paragraph">
            Add your credit card information. Don't worry, we won't charge you today!<br>
            After your free trial expires, you will be charged <span data-js="instruction-total"></span>.</p>
        <div class="c-sf-sections">
            <div class="c-sfs-section t-form">
                <div class="c-sf-row">
                    <div class="c-sfr-field f-field">
                        <label for="credit-card" class="f-f-label">Credit Card</label>
                        <input id="credit-card" class="f-f-input" type="text"
                               data-parsley-credit="true"
                               data-js="credit_card">
                    </div>
                    <div class="c-sfr-field f-field t-expiration">
                        <label for="expiration" class="f-f-label">
                            Expiration
                        </label>
                        <input id="expiration" class="f-f-input" type="text" data-js="expiration_date"
                               data-parsley-pattern="^(1[0-2]|0[1-9]|\d)\/([2-9]\d[1-9]\d)$"
                               data-parsley-pattern-message="Expiration must be formatted as MM/YYYY.">
                    </div>
                    <div class="c-sfr-field f-field t-card-code">
                        <label for="card-code" class="f-f-label">
                            Card Code
                        </label>
                        <input id="card-code" class="f-f-input" type="text" data-js="card_code"
                               data-parsley-pattern="^[0-9]{3,4}$"
                               data-inputmask-regex="^[0-9]{3,4}$"
                               data-parsley-pattern-message="Card code must be numeric.">
                    </div>
                </div>
                <p style="font-size: 14px; font-weight: bold; margin-bottom: 0; margin-top: 24px;">Billing Address</p>
                <div class="c-sf-row">
                    <div class="c-sfr-field f-field">
                        <label for="address-line-1" class="f-f-label">Address</label>
                        <input id="address-line-1" class="f-f-input" type="text" data-js="address">
                    </div>
                    <div class="c-sfr-field f-field">
                        <label for="address-line-2" class="f-f-label">
                            Suite, Apt, Other
                            <span class="f-fl-optional">(Optional)</span>
                        </label>
                        <input id="address-line-2" class="f-f-input" type="text" data-js="address_2">
                    </div>
                </div>
                <div class="c-sf-row">
                    <div class="c-sfr-field f-field">
                        <label for="address-city" class="f-f-label">City</label>
                        <input id="address-city" class="f-f-input" type="text" data-js="city" />
                    </div>
                    <div class="c-sfr-field f-field">
                        <label for="address-state" class="f-f-label">State</label>
                        <select id="address-state" class="f-f-input" placeholder="-- Select One --" data-js="state">
                            <optgroup label="States">
                                {{#each states}}
                                    <option value="{{@key}}">{{this}}</option>
                                {{/each}}
                            </optgroup>
                            <optgroup label="US Territories">
                                {{#each us_territories}}
                                    <option value="{{@key}}">{{this}}</option>
                                {{/each}}
                            </optgroup>
                            <optgroup label="Provinces">
                                {{#each provinces}}
                                    <option value="{{@key}}">{{this}}</option>
                                {{/each}}
                            </optgroup>
                        </select>
                    </div>
                    <div class="c-sfr-field f-field">
                        <label for="address-postal-code" class="f-f-label">Postal Code</label>
                        <input id="address-postal-code" class="f-f-input" type="text" data-js="postal_code">
                    </div>
                </div>
            </div>
            <div class="c-sfs-section t-subscription">
                <p style="font-size: 16px; margin-bottom: 0;"><strong>Total: <span data-js="body-total"></span></strong></p>
                <p style="font-style: italic;">Automatically Charged on <span data-js="body-date"></span><br>
                    Includes 4 Users<br>
                    Change or Cancel Any Time</p>
            </div>
        </div>
    </form>
</div>
