/**
 * @module Signup
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for signup module
 *
 * @memberof module:Signup
 */
export class Controller {
    /**
     * Signup constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        this.state = {layout};
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.router = new Router(MainPage, {
            base_path: '/signup',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}
