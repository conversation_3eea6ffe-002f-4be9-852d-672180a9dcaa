'use strict';

import {find<PERSON><PERSON><PERSON>, js<PERSON><PERSON><PERSON>, on<PERSON><PERSON><PERSON><PERSON><PERSON>, on<PERSON>lick<PERSON>atcher} from "@ca-package/dom";
const Page = require('@ca-package/router/src/page');

import FormValidator from "@cas-validator-js";
import FormInput from '@ca-submodule/form-input';
import StaticDropdownInput from '@ca-submodule/form-input/src/static_dropdown';

import {initSelectPlaceholder} from '@cac-js/utils/select_placeholder'

import log from '../../log';
import $ from "jquery";
import Api from "@ca-package/api";

const intake_tpl = require('@cam-signup-tpl/pages/main-pages/intake.hbs');

FormInput.use(StaticDropdownInput);

/**
 * @memberof module:Signup/Pages/MainPages
 */
export class IntakePage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            options_loaded: false
        });
    };

    /**
     * Fetch existing address for company
     *
     * @returns {Promise<object>}
     */
    async fetchIntakeData() {
        try {
            let {data: entity} = await Api.Resources.Companies().accept('application/vnd.adg.fx.setup-v1+json')
                .retrieve('current');
            return entity;
        } catch (error) {
            log.error('Unable to fetch company intake info', {error});
        }
    };

    /**
     * Populate fields with existing values
     *
     * @param {object} data
     */
    populate(data) {
        this.state.validator.getInputElem('user_count').val(data.intake.user_count_id).trigger('change');
        this.state.validator.getInputElem('industry').val(data.intake.industry_id).trigger('change');
        this.state.validator.getInputElem('marketing_source').val(data.intake.marketing_source_id).trigger('change');

        let feature_ids = [];
        for (let feature of data.intake.features) {
            feature_ids.push(feature.intake_feature_id);
        }
        this.state.validator.getInputElem('features').val(feature_ids).trigger('change');
    };

    /**
     * Fetch option values from server
     *
     * @returns {Promise<object>}
     */
    async fetchOptions() {
        return await $.ajax({
            url: window.fx_url.API + 'intake/options',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded"
        });
    };

    /**
     * Create options from data from server
     *
     * @param {object} data
     * @param {object[]} data.features
     * @param {object[]} data.user_counts
     * @param {object[]} data.industries
     * @param {object[]} data.marketing_sources
     */
    setOptions(data) {
        this.state.features = FormInput.init(this.state.validator.getInputElem('features'), {
            placeholder: '-- Select Any That Apply --',
            data_provider: () => {
                let features = [];
                for (let feature of data.features) {
                    features.push({
                        id: feature.id,
                        text: feature.name
                    });
                }
                return features;
            }
        });
        // set user count options
        let user_count_elem = this.state.validator.getInputElem('user_count');
        for (let user_count of data.user_counts) {
            let user_count_option = $('<option/>')
                .attr('value', user_count.id)
                .text(user_count.name);
            user_count_elem.append(user_count_option);
        }

        // set industry options
        let industry_elem = this.state.validator.getInputElem('industry');
        for (let industry of data.industries) {
            let industry_option = $('<option/>')
                .attr('value', industry.id)
                .text(industry.name);
            industry_elem.append(industry_option);
        }

        // set marketing source options
        let marketing_source_elem = this.state.validator.getInputElem('marketing_source');
        for (let marketing_source of data.marketing_sources) {
            var marketing_source_option = $('<option/>')
                .attr('value', marketing_source.id)
                .text(marketing_source.name);
            marketing_source_elem.append(marketing_source_option);
        }
        this.state.options_loaded = true;
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Reset form and any custom inputs
     */
    clearForm() {
        this.state.validator.reset();
        this.state.features.setValue(null);
    };

    /**
     * Save intake data
     */
    async save() {
        this.parent.startWorking();
        this.clearError();
        let intake_features = this.state.validator.getInputElem('features').val(),
            feature_ids = intake_features.map(id => parseInt(id)),
            data = {
                user_count_id: parseInt(this.state.validator.getInputElem('user_count').val()),
                industry_id: parseInt(this.state.validator.getInputElem('industry').val()),
                marketing_source_id: parseInt(this.state.validator.getInputElem('marketing_source').val()),
                feature_ids
            };
        try {
            await $.ajax({
                url: window.fx_url.API + 'signup/intake',
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(data)
            });
            this.parent.resetWorking();
            this.router.navigate('subscription');
        } catch(error) {
            this.setError('Unable to save company intake information, please contact support');
            this.parent.resetWorking();
            log.error('Unable to save intake info', {error});
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.parent.startWorking();
        this.fetchIntakeData().then(data => {
            this.state.data = data;
            if (data.intake !== null) {
                this.populate(data);
            }
            this.parent.resetWorking();
        });
        this.parent.setIntakeNavState();
        this.state.parent.setScrollHeight();

        let that = this;
        onClickWatcher(this.parent.elem.root, jsSelector('button'), function () {
            if ($(this).data('value') === 'back') {
                that.router.navigate('address');
            }
            if ($(this).data('value') === 'continue') {
                that.elem.form.trigger('submit');
            }
        }, true);

        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // this.clearForm();
        this.clearError();
        onClickDestroy(this.parent.elem.root);
        await super.unload(request, next);
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            user_count: {
                required: true
            },
            industry: {
                required: true
            },
            marketing_source: {
                required: true
            },
            features: {}
        })
            .on('submit', () => this.save());
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.form = findChild(root, jsSelector('form'));
        this.initForm();
        initSelectPlaceholder(this.state.validator.getInputElem('user_count'));
        initSelectPlaceholder(this.state.validator.getInputElem('industry'));
        initSelectPlaceholder(this.state.validator.getInputElem('marketing_source'));

        if (this.state.options_loaded === false) {
            this.fetchOptions().then(options => {
                this.state.options = options;
                this.setOptions(options);
            });
        }
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return intake_tpl();
    };
}
