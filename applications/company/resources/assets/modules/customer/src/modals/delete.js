/**
 * @module Customer/Modals
 */

'use strict';

import Api from '@ca-package/api';
import {Confirm} from '@ca-submodule/modal';

import modal_delete_tpl from '@cam-customer-tpl/modals/delete.hbs';

/**
 * @memberof module:Customer/Modals
 */
export class Delete extends Confirm {
    /**
     * Constructor
     */
    constructor(controller) {
        super();
        this.state.controller = controller;
        this.setTitle('Delete Customer');
    };

    /**
     * Open modal
     *
     * @param {Object} data
     */
    open(data) {
        this.state.data = data;
        this.setContent(modal_delete_tpl({
            first_name: data.first_name,
            last_name: data.last_name
        }));
        super.open();
    };

    /**
     * Handle yes which means the user wants to delete
     */
    handleYes() {
        this.startWorking();
        Api.Resources.Customers().delete(this.state.data.id)
            .then(() => {
                this.state.controller.table.deleteRow(this.state.data.id);
                this.resetWorking();
                this.close();
            }, (error) => {
                if (error.code === 1014) {
                    error.message = 'Unable to delete customer';
                }
                this.resetWorking();
                this.showErrorMessage(error.message);
            });
    };

    /**
     * Handle no which means the user wants to abort
     */
    handleNo() {
        this.close();
    };

    /**
     * Close modal
     */
    close() {
        this.state.data = null;
        super.close();
    };
}
