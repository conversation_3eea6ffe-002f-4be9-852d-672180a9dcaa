/**
 * @module Marketplace
 */

'use strict';

const Router = require('@ca-package/router');

/**
 * Main controller for marketplace module
 *
 * @memberof module:Marketplace
 */
class Controller {
    /**
     * Marketplace constructor
     *
     * @param {module:Layout.Controller} layout
     * @param {jQuery} root - jQuery element that contains the marketplace module
     */
    constructor(layout, root) {
        this.state = {
            layout
        };
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('Marketplace');
        this.state.router = new Router(require('./pages/main'), {
            base_path: '/marketplace',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}

module.exports = Controller;
