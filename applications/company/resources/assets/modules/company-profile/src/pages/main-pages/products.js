'use strict';

const Page = require('@ca-package/router/src/page');

const products_tpl = require('@cam-company-profile-tpl/pages/main-pages/products.hbs');

class Products extends Page {
    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            items: {
                default: true,
                page: require('./products-pages/items')
            },
            categories: {
                path: '/categories',
                page: require('./products-pages/categories')
            },
            units: {
                path: '/units',
                page: require('./products-pages/units')
            },
            import: {
                path: '/import',
                page: require('./products-pages/import')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.loader = root.fxFind('loader');
        this.elem.page_container = root.fxFind('page-container');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return products_tpl();
    };
}

module.exports = Products;
