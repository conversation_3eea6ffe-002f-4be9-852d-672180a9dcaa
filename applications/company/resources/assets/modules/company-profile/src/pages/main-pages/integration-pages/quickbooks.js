'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const quickbooks_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/quickbooks.hbs');

class Quickbooks extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                default: true,
                page: require('./quickbooks-pages/details')
            },
            settings: {
                path: '/quickbooks/settings',
                page: require('./quickbooks-pages/settings')
            }
        };
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.page_container = findChild(root, jsSelector('quickbooks-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return quickbooks_tpl();
    };
}

module.exports = Quickbooks;