'use strict';

const moment = require('moment-timezone');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClickWatcher, onEvent, onClick} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
FormInput.use(require('@ca-submodule/form-input/src/hidden_textarea'));
FormInput.use(require('@ca-submodule/form-input/src/dynamic_dropdown'));
const Tooltip = require('@ca-submodule/tooltip');
const Inputmask = require("inputmask");

const FormValidator = require("@cas-validator-js");
const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const create_update_tpl = require('@cam-company-profile-tpl/pages/main-pages/terms-conditions-pages/items-pages/create_update.hbs');

class CreateUpdate extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            terms_conditions: parent.getParentByName('terms_conditions'),
            is_update: name === 'update',
            terms_conditions_data: null,
        });
    };


    /**
     * Pull data from form inputs
     *
     * @returns {object}
     */
    buildEntity() {

        let entity = {
            name: this.state.validator.getInputElem('name').val(),
            type: parseInt(this.state.validator.getInputElem('type').val()),
            content: this.state.validator.getInputElem('content').val(),
            is_default: this.elem.is_default.is(':checked'),
            is_locked: this.elem.is_locked.is(':checked'),
            is_required: this.elem.is_required.is(':checked'),
            is_answer_required: this.elem.is_answer_required.is(':checked'),
            product_items: this.state.validator.getInputElem('product_items').val(),
        };

        return entity;
    };

    /**
     * Save terms & conditions
     */
    save() {
        this.state.terms_conditions.showLoader();

        let data = this.buildEntity();
        let resource = Api.Resources.BidContent(),
            request = this.state.is_update ? resource.partialUpdate(this.state.terms_conditions_data.id, data) : resource.store(data);
        request.then(({data}) => {
            this.state.terms_conditions.hideLoader();
            let message = createSuccessMessage(`Terms & Conditions ${this.state.is_update ? 'edited' : 'added'} successfully`);
            this.router.main_route.layout.toasts.addMessage(message);
            this.router.navigate('terms_conditions.items.manager');
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    for (let item in item_errors) {
                        this.setError(item_errors[item]);
                    }
                    this.state.terms_conditions.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save terms & conditions, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Populate data from Api
     *
     * @param {object} data
     * @param {boolean} [duplicate=false]
     */
    async populate(data) {
        if (data.type !== Api.Constants.BidContent.Type.ACKNOWLEDGEMENT) {
            this.elem.is_answer_required.prop('disabled', true);
            this.state.is_answer_required_switch.toggleDisabled(true);
            this.elem.is_answer_required_label.addClass('t-disabled');
        }
        this.state.validator.getInputElem('name').val(data.name);
        this.state.validator.getInputElem('type').val(data.type);
        this.state.validator.getInputElem('content').val(data.content).trigger('change');
        this.elem.is_default.prop('checked', data.is_default).trigger('change');
        this.elem.is_answer_required.prop('checked', data.is_answer_required).trigger('change');
        this.elem.is_locked.prop('checked', data.is_locked).trigger('change');

        this.elem.type.prop('disabled', true);

        for (let item in data['product_items']) {
            let option = new Option(data['product_items'][item].name, data['product_items'][item].id, true, true);
            this.elem.products.append(option).trigger('change');
        }
    };

    /**
     * Fetch terms & conditions info by id
     *
     * @param {string} id - UUID
     * @returns {Promise<void>}
     */
    async fetchData(id) {
        try {
            let content_relations = {
                'product_items': {}
            };
            let {data: entity} = await Api.Resources.BidContent()
                .relations(content_relations)
                .retrieve(id);
            return entity;
        } catch (e) {
            let message = createErrorMessage('Unable to fetch terms & conditions info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    /**
     * Create dropdown of products
     *
     * @param {Object} input
     * @returns {Promise<boolean>}
     */
    async createProductDropdown(input) {
        input.prop('disabled', true);
        let product_input = FormInput.init(input, {
            placeholder: '-- Select One --',
            request: data => {
                let request = Api.Resources.ProductItems()
                    .fields(['id', 'name'])
                    .filter('status', Api.Constants.ProductItems.Status.ACTIVE)
                    .page(data.page).perPage(15);
                if (data.term) {
                    request.search(data.term);
                }
                return request.all();
            },
            response: (collection, formatter) => {
                let results = [];
                collection.entities.forEach((product) => {
                    results.push({
                        id: product.get('id'),
                        text: product.get('name'),
                        name: product.get('name'),
                    });
                });
                return formatter(results, collection.response.meta('pagination.next_page') !== null);
            }
        });

        await product_input.promise;
        input.prop('disabled', false);
        return product_input;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        // show parent loader
        this.state.terms_conditions.showLoader();
        this.state.product_select = await this.createProductDropdown(this.elem.products);

        if (this.state.is_update) {
            let data = await this.fetchData(request.params.terms_conditions_id);
            this.state.terms_conditions_data = data;
            this.populate(data);
        }

        this.state.terms_conditions.hideLoader();

        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.clearError();

        this.state.validator.reset();
        this.elem.root.scrollTop(0);
        this.elem.type.prop('disabled', false);

        this.elem.is_locked.prop('checked', false);
        this.elem.is_locked.prop('disabled', false);

        this.elem.is_default.prop('checked', false);
        this.elem.is_default.prop('disabled', false);

        this.elem.is_required.prop('checked', false);
        this.elem.is_required.prop('disabled', false);

        this.elem.is_answer_required.prop('checked', false);
        this.elem.is_answer_required.prop('disabled', false);
        this.state.is_answer_required_switch.toggleDisabled(false);
        this.elem.is_answer_required_label.removeClass('t-disabled');

        this.elem.products.prop('disabled', false);

        this.state.product_select.destroy();
        this.state.product_select = null;

        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            type: {
                required: true
            },
            content: {
                required: true
            },
            product_items: {
                valueEmpty: false,
                valueEmptyMessage: 'Products must be empty if content is marked required'
            },
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review form errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));

        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));

        this.elem.error = findChild(root, jsSelector('error'));

        this.elem.name = findChild(root, jsSelector('name'));
        this.elem.type = findChild(root, jsSelector('type'));
        this.elem.content = findChild(root, jsSelector('content'));
        this.elem.is_default = findChild(root, jsSelector('is-default'));
        this.elem.is_locked = findChild(root, jsSelector('is-locked'));
        this.elem.is_required = findChild(root, jsSelector('is-required'));
        this.elem.is_answer_required = findChild(root, jsSelector('is-answer-required'));
        this.elem.is_answer_required_label = findChild(root, jsSelector('is-answer-required-label'));
        this.elem.products = findChild(root, jsSelector('product_items'));

        initSelectPlaceholder(this.elem.type);
        this.elem.form = this.elem.root.find('form');

        FormInput.init(this.elem.is_default);
        FormInput.init(this.elem.is_locked);
        FormInput.init(this.elem.is_required);
        this.state.is_answer_required_switch = FormInput.init(this.elem.is_answer_required);

        this.state.textarea = FormInput.init(this.elem.content, {
            preset: 'simple',
            blocks: true,
            preview: true,
            text_color: true,
            preview_config: {
                title: 'Contract Preview',
                tag_replacements: [
                    {
                        content: 'date',
                        replacement: moment().format("MMMM D, YYYY"),
                    },
                    {
                        content: 'firstName',
                        replacement: 'John',
                    },
                    {
                        content: 'lastName',
                        replacement: 'Smith',
                    },
                    {
                        content: 'businessName',
                        replacement: 'ABC Contracting',
                    },
                    {
                        content: 'address',
                        replacement: '1234 Street Address',
                    },
                    {
                        content: 'city',
                        replacement: 'Cityville',
                    },
                    {
                        content: 'state',
                        replacement: 'MO',
                    },
                    {
                        content: 'zip',
                        replacement: '12345',
                    },
                    {
                        content: 'phone',
                        replacement: '(*************',
                    },
                    {
                        content: 'email',
                        replacement: '<EMAIL>',
                    },
                    {
                        content: 'bidNumber',
                        replacement: '#1234',
                    },
                    {
                        content: 'description',
                        replacement: 'Sample Bid',
                    }
                ]
            },
            remove_empty_paragraphs: true,
            tags: [
                {
                    label: 'Current Date',
                    content: 'date'
                },
                {
                    label: 'First Name',
                    content: 'firstName'
                },
                {
                    label: 'Last Name',
                    content: 'lastName'
                },
                {
                    label: 'Business Name',
                    content: 'businessName'
                },
                {
                    label: 'Address',
                    content: 'address'
                },
                {
                    label: 'City',
                    content: 'city'
                },
                {
                    label: 'State',
                    content: 'state'
                },
                {
                    label: 'Zip',
                    content: 'zip'
                },
                {
                    label: 'Phone',
                    content: 'phone'
                },
                {
                    label: 'Email',
                    content: 'email'
                },
                {
                    label: 'Bid Number',
                    content: 'bidNumber'
                },
                {
                    label: 'Project Description',
                    content: 'description'
                }
            ]
        });

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });


        onEvent(this.elem.is_required, 'change',(e) => {
            e.preventDefault();
            if ($(e.target).is(':checked')) {
                this.elem.is_locked.prop('checked', true);
                this.elem.is_default.prop('checked', true);
                this.elem.is_default.prop('disabled', true);
                this.elem.products.prop('disabled', true);
            } else {
                this.elem.is_default.prop('disabled', false);
                this.elem.products.prop('disabled', false);
            }
            return false;
        });

        // if the type dropdown changes we need to either disable or enable is_answer_required toggle and label
        // is_answer_required is only used for acknowledgements so we don't want to confuse them if they aren't adding
        // an acknowledgement type
        onEvent(this.elem.type, 'change',(e) => {
            e.preventDefault();
            if (parseInt($(e.target).val()) !== Api.Constants.BidContent.Type.ACKNOWLEDGEMENT) {
                this.elem.is_answer_required.prop('checked', false);
                this.elem.is_answer_required.prop('disabled', true);
                this.state.is_answer_required_switch.toggleDisabled(true);
                this.elem.is_answer_required_label.addClass('t-disabled');
            } else {
                this.elem.is_answer_required.prop('checked', false);
                this.elem.is_answer_required.prop('disabled', false);
                this.state.is_answer_required_switch.toggleDisabled(false);
                this.elem.is_answer_required_label.removeClass('t-disabled');
            }
            return false;
        });

        this.initForm();
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return create_update_tpl({
            ns: this.state.is_update ? 'e' : 'a',
            cancel_route: 'terms_conditions.items.manager',
            title: `${this.state.is_update ? 'Edit' : 'Add'} Terms & Conditions`
        });
    };
}

module.exports = CreateUpdate;
