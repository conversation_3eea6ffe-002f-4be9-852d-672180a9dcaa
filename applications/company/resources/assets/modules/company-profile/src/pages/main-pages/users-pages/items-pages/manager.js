'use strict';

const moment = require('moment-timezone');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const StatusModal = require('../../../../modals/user/status');
const ErrorModal = require('../../../../modals/error');
const NoUserSeatsAvailable = require('../../../../modals/user/no_seats');

const users_tpl = require('@cam-company-profile-tpl/pages/main-pages/users-pages/items-pages/manager.hbs');

class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            modals: {},
            table: null,
            table_scope: {
                sorts: {
                    first_name: Table.Sort.ASC,
                    last_name: Table.Sort.ASC
                },
                filters: {
                    status: [Table.Operators.IN, [
                        // when we set a default filter that contains IN operator we need to ensure these are a string
                        // to select the correct one at first
                        Api.Constants.Users.Status.ACTIVE.toString(), Api.Constants.Users.Status.INVITED.toString()
                    ]]
                }
            },
            add_users: false
        });

        this.state.modals = {
            user_status: new StatusModal(),
            error: new ErrorModal(),
            no_users: new NoUserSeatsAvailable()
        };
        [
            this.state.modals.user_status
        ].forEach((element) => {
            element.on('loaded', () => {
                this.state.table.toggleProcessing(false);
            }).on('loading', () => {
                this.state.table.toggleProcessing(true);
            }).on('redraw', () => {
                this.drawTable();
            }).on('error', (error) => {
                this.state.modals.error.setError(error);
                this.state.modals.error.open();
            });
        });
        this.state.modals.user_status.on('status_changed', (user_data) => {
            this.loadUserCount();
            this.state.table.updateRow(user_data.data, this.state.table.getRowById(user_data.data.id));
        });
    };

    /**
     * Send access link for user
     *
     * @param {number} user_id
     * @returns {Promise<void>}
     */
    async sendAccessLink(user_id) {
        await Api.Resources.Users()
            .method(Api.Request.Method.PUT)
            .custom(`${user_id}/send-access-link`);
        this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Access link sent to user successfully'));
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    createTable() {
        this.loadUserCount();
        let status_map = new Map([
            ['invited', '<span class="h-text t-yellow">Invited</span>'],
            [true, '<span class="h-text t-green">Active</span>'],
            [false, '<span class="h-text t-grey">Inactive</span>']
        ]);

        let status_map_new = new Map([
            [Api.Constants.Users.Status.INACTIVE, '<span class="h-text t-grey">Inactive</span>'],
            [Api.Constants.Users.Status.ACTIVE, '<span class="h-text t-green">Active</span>'],
            [Api.Constants.Users.Status.INVITED, '<span class="h-text t-yellow">Invited</span>']
        ]);

        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('users.items.update', {
                    user_id: data.id
                });
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Users'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Active',
                        value: Api.Constants.Users.Status.ACTIVE
                    },
                    2: {
                        label: 'Inactive',
                        value: Api.Constants.Users.Status.INACTIVE
                    },
                    3: {
                        label: 'Invited',
                        value: Api.Constants.Users.Status.INVITED
                    }
                }
            },
            role_primary: {
                label: 'Role - Primary',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_sales_management: {
                label: 'Role - Sales Management',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_sales: {
                label: 'Role - Sales',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_installation: {
                label: 'Role - Installation',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_marketing: {
                label: 'Role - Marketing',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_bid_creation: {
                label: 'Role - Bid Creation',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_bid_verification: {
                label: 'Role - Bid Verification',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
            role_metrics: {
                label: 'Role - Metrics',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'On',
                        value: 1
                    },
                    2: {
                        label: 'Off',
                        value: 0
                    }
                }
            },
        });

        // set columns config
        this.state.table.setColumns({
            calendar_bg_color: {
                width: '1%',
                label: '<div class="t-color-swatch-header"></div>',
                value: (data) => {
                    return `<div class="t-color-swatch-row" style="background: #${data.calendar_bg_color}""></div>`;
                }
            },
            first_name: {
                label: 'First Name',
            },
            last_name: {
                label: 'Last Name',
            },
            email: {
                label: 'Email',
                value: (data) => {
                    let string_length = 20;
                    if (data.email.length <= string_length) {
                        return data.email;
                    }
                    return `${data.email.substring(0, string_length)}...`;
                }
            },
            phone: {
                label: 'Phone',
                key: 'phones.0.number',
                orderable: false
            },
            status : {
                label: 'Status',
                value: (data) => {
                    return status_map_new.get(data.status);
                }
            },
            roles: {
                label: 'Roles',
                orderable: false,
                value: (data) => {
                    let roles = '';
                    if (data.role_primary) {
                        roles = 'Primary';
                    }
                    if (data.role_sales_management) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Sales Management`;
                    }
                    if (data.role_sales) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Sales`;
                    }
                    if (data.role_installation) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Installation`;
                    }
                    if (data.role_marketing) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Marketing`;
                    }
                    if (data.role_bid_creation) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Bid Creation`;
                    }
                    if (data.role_bid_verification) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Bid Verification`;
                    }
                    if (data.role_metrics) {
                        roles = roles !== '' ? `${roles}, ` : ''
                        roles = `${roles} Metrics`;
                    }
                    return roles;
                }
            },
        });

        // set row action config
        this.state.table.setRowActions({
            edit_user: {
                label: 'Edit',
                action: (data) => {
                    this.router.navigate('users.items.update', {
                        user_id: data.id
                    });
                }
            },
            activate: {
                label: 'Activate',
                confirm: true,
                visible: data => !data.is_active,
                action: (data) => {
                    this.state.modals.user_status.open(StatusModal.Status.ACTIVE, data.id);
                }
            },
            resend_access_link: {
                label: 'Resend Access Link',
                visible: data => data.is_active && !data.is_password_valid,
                action: data => {
                    this.sendAccessLink(data.id).catch(e => console.error(e));
                }
            },
            send_access_link: {
                label: 'Send Access Link',
                visible: data => data.is_active && data.is_password_valid,
                action: data => {
                    this.sendAccessLink(data.id).catch(e => console.error(e));
                }
            },
            inactivate: {
                label: 'Deactivate',
                negate: true,
                visible: data => data.is_active,
                action: (data) => {
                    this.state.modals.user_status.open(StatusModal.Status.INACTIVE, data.id);
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            add_user: {
                label: 'New User',
                action: () => {
                    if (this.state.add_users) {
                        this.router.navigate('users.items.create');
                    } else {
                        this.state.modals.no_users.open({
                            used: this.state.total_users,
                            total_users: this.state.available_users
                        });
                    }
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.Users, (request) => {
            request.fields([
                'id', 'first_name', 'last_name', 'calendar_bg_color', 'email', 'phone', 'is_user_invited', 'is_active',
                'status', 'is_password_valid', 'role_primary', 'role_sales_management', 'role_sales', 'role_installation',
                'role_marketing', 'role_bid_creation', 'role_bid_verification', 'role_metrics'
            ]);
            request.relation('phones', Api.Relation.make().filter('is_primary', true));
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Get active user count from the server
     */
    async getUserCount() {
        return await $.ajax({
            url: window.fx_url.API + 'company/profile/total-users',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded"
        });
    };

    /**
     * Load and store active user count
     */
    async loadUserCount() {
        this.state.add_users = false;
        let user_count_result = await this.getUserCount();

        if (user_count_result.status !== 1){
            this.state.modals.error.setError(error);
            this.state.modals.error.open();
            return;
        }
        this.state.total_users = user_count_result.result;
        this.state.available_users = profile_data.available_users;
        this.state.add_users = this.state.available_users === null || this.state.total_users < this.state.available_users;
    }

    /**
     * Redraw table
     */
    drawTable() {
        this.loadUserCount();
        this.state.table.draw();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // We have to force the filter to hide if it's still open when they unload
        this.state.table.hideFilterMenu();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return users_tpl();
    };
}

module.exports = Manager;