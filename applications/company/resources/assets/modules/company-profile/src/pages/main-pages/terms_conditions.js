'use strict';

const Page = require('@ca-package/router/src/page');

const terms_conditions_tpl = require('@cam-company-profile-tpl/pages/main-pages/terms_conditions.hbs');

class TermsConditions extends Page {
    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            items: {
                default: true,
                page: require('./terms-conditions-pages/items')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.loader = root.fxFind('loader');
        this.elem.page_container = root.fxFind('page-container');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return terms_conditions_tpl();
    };
}

module.exports = TermsConditions;