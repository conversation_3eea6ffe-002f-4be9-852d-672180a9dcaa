'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

class CreateUpdate extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            is_update: name === 'update'
        });
    };

    /**
     * Get and cache modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../../modals/form/category/create_update');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open create/update category modal with promise
     *
     * @param {object} config
     * @returns {Promise<undefined>}
     */
    openModal(config) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                config,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        let config = {};
        if (request.query.parent_id !== undefined) {
            config.parent_id = request.query.parent_id;
        }
        if (this.state.is_update) {
            config.category_id = request.params.category_id;
        }
        this.openModal(config).then(result => {
            let query = null;
            if (result !== null) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage(`Form Filter ${this.state.is_update ? 'updated' : 'added'} successfully`));
                query = {update: 'true'};
            }
            this.router.navigate('forms.categories', {}, query);
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.externalClose();
        await super.unload(request, next);
    };
}

module.exports = CreateUpdate;
