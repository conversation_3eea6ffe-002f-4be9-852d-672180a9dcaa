'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClick} = require("@ca-package/dom");

const wisetack_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/wisetack.hbs');

class Wisetack extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
        });
    };


    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                path: '/details',
                page: require('./wisetack-pages/details'),
            },
            settings: {
                path: '/settings',
                page: require('./wisetack-pages/settings'),
            }
        };
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.page_container = findChild(root, jsSelector('wisetack-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return wisetack_tpl()
    };
}

module.exports = Wisetack;