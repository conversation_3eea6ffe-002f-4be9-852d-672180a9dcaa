'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const Tooltip = require('@ca-submodule/tooltip');

const FormValidator = require("@cas-validator-js");

const email_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/email-pages/edit.hbs');

class Edit extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings')
        });
    };

    /**
     * Prepare entity to save
     *
     * @returns {object}
     */
    buildEntity() {
        let data = {
            email_from: this.state.validator.getInputElem('email_from').val(),
            email_reply: this.state.validator.getInputElem('email_reply').val(),
            settings: {
                appointment_reminder_24_hours: this.elem.reminder_24_hours_before_appointment.is(':checked')
            }
        };
        let additional_email_recipients = this.state.validator.getInputElem('additional_email_recipients').val();
        if (additional_email_recipients === '') {
            data.settings.additional_email_recipients = [];
            return data;
        }

        additional_email_recipients = additional_email_recipients.replace(/\s+/g, '');
        if (additional_email_recipients[additional_email_recipients.length - 1] === ',') {
            additional_email_recipients = additional_email_recipients.slice(0, -1);
        }
        data.settings.additional_email_recipients = additional_email_recipients.split(',').filter(function (value, index, self) {
            return self.indexOf(value) === index;
        });
        return data;
    };

    /**
     * Save settings data to server
     */
    save() {
        this.state.settings.showLoader();
        let data = this.buildEntity();

        Api.Resources.Companies().partialUpdate('current', data).then(({data}) => {
            setTimeout(() => {
                this.state.settings.hideLoader();
                let message = createSuccessMessage(`Company email settings saved successfully`);
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('settings.email.details');
            }, 2000);
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    // for (let item in item_errors) {
                    //     if (this.state.field[item] === undefined) {
                    //         continue;
                    //     }
                    //     this.state.field[item].addError('fx-' + item, {message: item_errors[item]});
                    // }
                    this.state.settings.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save company email settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        this.state.validator.getInputElem('email_from').val(data.email_from);
        this.state.validator.getInputElem('email_reply').val(data.email_reply);

        if (data.settings.additional_email_recipients.length > 0) {
            this.state.validator.getInputElem('additional_email_recipients').val(data.settings.additional_email_recipients.join(', '));
        }
        this.elem.reminder_24_hours_before_appointment.prop('checked', data.settings.appointment_reminder_24_hours);
    };

    /**
     * Fetch data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['email_from', 'email_reply']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {

        }
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.settings.setEditMode(true);
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.validator.getInputElem('email_from').val('');
        this.state.validator.getInputElem('email_reply').val('');
        this.state.validator.getInputElem('additional_email_recipients').val('');

        this.state.validator.reset();
        this.elem.root.scrollTop(0);
        this.clearError();
        this.state.settings.setEditMode(false);
        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    };

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            email_from: {
                required: true,
                type: 'email',
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            email_reply: {
                required: true,
                type: 'email',
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            additional_email_recipients: {
                multipleEmails: true
            }
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.save = findChild(root, jsSelector('save'));

        this.initForm();

        this.elem.reminder_24_hours_before_appointment = findChild(root, jsSelector('reminder-24-hours-before-appointment'));
        FormInput.init(this.elem.reminder_24_hours_before_appointment);

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return email_tpl({
            title: 'Edit Email Settings',
            cancel_route: 'settings.email.details'
        });
    };
}

module.exports = Edit;