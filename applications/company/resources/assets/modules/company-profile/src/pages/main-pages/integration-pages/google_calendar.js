'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const Table = require('@ca-submodule/table').Base;
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

const google_calendar_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/google_calendar.hbs');

const GoogleApiPath = `${window.fx_url.API}integration/google/calendar`;
const GoogleStatuses = {
    REQUESTED: 1,
    CONNECTED: 2,
    DISCONNECTING: 3,
    DISCONNECTED: 4,
    DISCONNECT_FAILED: 5
};
const CalendarStatuses = {
    ADDED: 1,
    REMOVING: 2,
    REMOVED: 3,
    REMOVE_FAILED: 4
};

class GoogleCalendar extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent,
            modals: {},
            table: null,
            google_calendar: false,
            table_buttons: false
        });
    };

    /**
     * Get Google connection status
     *
     * @readonly
     *
     * @returns {object}
     */
    static get GoogleStatus() {
        return GoogleStatuses;
    };

    /**
     * Get calendar status types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get CalendarStatus() {
        return CalendarStatuses;
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            google_calendar_add: {
                path: '/add',
                modal: require('../integration-pages/google-calendar-pages/calendar_add')
            },
            google_calendar_remove: {
                path: '/remove/{calendar_id}',
                modal: require('../integration-pages/google-calendar-pages/calendar_remove'),
                bindings: {
                    calendar_id: 'uuid'
                }
            }
        };
    };

    /**
     * Request a pull of all events for a calendar
     *
     * @param {string} id - calendar uuid
     * @returns {Promise}
     */
    async pullCalendarEvents(id) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: `${GoogleApiPath}/${id}/sync`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    full_sync: false
                }),
                success: () => {
                    this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Calendar events are being synced'));
                    resolve();
                }, error: function(jqXHR, textStatus, errorThrown) {
                    reject(errorThrown);
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        });
    };

    /**
     * Fetch all Calendars connected for the company
     *
     * Response will include user information to display in the calendar
     *
     * @returns {Promise}
     */
    async fetchGoogleCalendars() {
        if (this.state.table_buttons) {
            this.elem.button_add_calendar.show();
        }
        let {calendars} = await $.ajax({
            url: GoogleApiPath,
            dataType: 'json',
            type: 'GET',
            contentType: 'application/json',
            data: {
                owner_type: Api.Constants.GoogleCalendar.OwnerType.COMPANY,
                include_user: true
            }
        });
        return calendars;
    };

    /**
     * Create and cache the DataTable and apply settings and defaults
     */
    async getTable() {
        if (this.state.table === null) {
            let status_map = new Map([
                [CalendarStatuses.ADDED, '<span class="h-text t-green">Added</span>'],
                [CalendarStatuses.REMOVING, '<span class="h-text t-red">Removing</span>'],
                [CalendarStatuses.REMOVED, '<span class="h-text t-grey">Removed</span>'],
                [CalendarStatuses.REMOVE_FAILED, '<span class="h-text t-red">Remove Failed</span>']
            ]);

            let table = new Table(this.elem.google_table, {
                paging_enabled: false
            });

            // set toolbar config
            table.setToolbar({
                filter: false,
                settings: false
            });

            // set columns config
            table.setColumns({
                name: {
                    label: 'Name',
                    responsive: 1,
                    width: '50%'
                },
                owner: {
                    label: 'Owner',
                    responsive: 2,
                    width: '25%',
                    value: (data) => `${data.oauth_user.first_name} ${data.oauth_user.last_name}`
                },
                status: {
                    label: 'Status',
                    width: '15%',
                    value: (data, type) => type === 'display' ? status_map.get(data.status) : data.status,
                    orderable: false
                },
            });

            // set row action config
            table.setRowActions({
                sync: {
                    label: 'Pull Events',
                    visible: data => data.status === CalendarStatuses.ADDED,
                    action: data => this.pullCalendarEvents(data.id)
                },
                remove: {
                    label: 'Remove',
                    negate: true,
                    visible: data => {
                        return data.oauth_user.id === profile_data.user.id && (
                            data.status === CalendarStatuses.ADDED || data.status === CalendarStatuses.REMOVE_FAILED
                        );
                    },
                    action: data => this.router.navigate('integrations.google_calendar.google_calendar_remove', {calendar_id: data.id})
                }
            });
            table.setState({
                sorts: {
                    name: Table.Sort.ASC
                }
            });
            table.setData([]);
            this.state.table = table.setup();
        }
        return this.state.table;
    };

    /**
     * Remove table if exists
     */
    removeTable() {
        if (this.state.table === null) {
            return;
        }
        this.getTable().then(table => {
            table.destroy();
            this.state.table = null;
        });
    };

    /**
     * Build or draw table
     */
    async loadGoogleCalendarTable() {
        let table = await this.getTable();
        table.setTableData(await this.fetchGoogleCalendars());
    };

    /**
     * Populate the templates based on Google connection status
     */
    populateGoogleCalendar() {
        switch(window.profile_data.google.status) {
            case GoogleStatuses.REQUESTED:
                break;
            case GoogleStatuses.CONNECTED:
                this.elem.status.addClass('t-show');
                this.elem.add_calendar.removeClass('t-hidden');
                this.elem.disconnect_warning.addClass('t-show');
                this.elem.disconnect_link.addClass('t-show');
                this.elem.google_table.show();
                this.elem.status_tag.html('<svg data-icon><use xlink:href="#remix-icon--system--checkbox-circle-fill"></use></svg>');
                break;
            case GoogleStatuses.DISCONNECTING:
                this.elem.disconnecting_text.text('Your Google Calendar account is being disconnected.');
                this.elem.status.addClass('t-show');
                this.elem.status_bar.addClass('t-full');
                this.elem.disconnecting_text.addClass('t-show');
                this.elem.status_tag.html('<span class="h-text t-yellow">Disconnecting</span>');
                break;
            case GoogleStatuses.DISCONNECT_FAILED:
                this.elem.disconnecting_text.html('Your Google Calendar failed to disconnect, please contact <a target="_blank" href="mailto:<EMAIL>">support</a>.');
                this.elem.status.addClass('t-show');
                this.elem.status_bar.addClass('t-full');
                this.elem.disconnecting_text.addClass('t-show');
                this.elem.status_tag.html('<span class="h-text t-red">Disconnect Failed</span>');
                break;
            case GoogleStatuses.DISCONNECTED:
                this.elem.connect.removeClass('t-hidden');
                this.elem.disconnected_text.addClass('t-show');
                break;
        }
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        if (request.query.update === 'true') {
            this.loadGoogleCalendarTable().catch(e => console.log(e));
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.integrations.showLoader();
        await super.load(request, next);

        let promises = [];
        if (window.profile_data.google.is_enabled) {
            this.state.google_calendar = true;
            this.populateGoogleCalendar();
            promises.push(this.loadGoogleCalendarTable());
        }
        Promise.all(promises).then(() => {
            this.state.integrations.hideLoader();
        });
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.elem.disconnected_text.removeClass('t-show');
        this.elem.disconnecting_text.removeClass('t-show');
        this.elem.disconnect_warning.removeClass('t-show');
        this.elem.disconnect_link.removeClass('t-show');
        this.elem.status.removeClass('t-show');
        this.elem.add_calendar.addClass('t-hidden');
        this.elem.connect.addClass('t-hidden');
        this.elem.status_tag.text('');
        this.elem.status_bar.removeClass('t-full');
        this.elem.google_table.hide();

        if (this.state.google_calendar) {
            this.removeTable();
        }
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.connect = findChild(root, jsSelector('connect'));
        this.elem.add_calendar = findChild(root, jsSelector('add-calendar'));
        this.elem.disconnect_link = findChild(root, jsSelector('disconnect-link'));
        this.elem.disconnected_text = findChild(root, jsSelector('disconnected-text'));
        this.elem.disconnecting_text = findChild(root, jsSelector('disconnecting-text'));
        this.elem.status = findChild(root, jsSelector('status'));
        this.elem.status_bar = findChild(root, jsSelector('status-bar'));
        this.elem.status_tag = findChild(root, jsSelector('status-tag'));
        this.elem.google_table = findChild(root, jsSelector('table-container'));
        this.elem.disconnect_warning = findChild(root, jsSelector('disconnect-warning'));

        onEvent(this.elem.add_calendar, 'click', (e) => {
            e.preventDefault();
            this.router.navigate('integrations.google_calendar.google_calendar_add');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return google_calendar_tpl({
            connect_link: `${GoogleApiPath}/connect&destination=company-profile-add-calendar`,
            disconnect_link: `${GoogleApiPath}/disconnect&destination=company-profile`,
            brand_name: profile_data.brand_name,
            user_profile: fx_pages.USER_PROFILE
        });
    };
}

module.exports = GoogleCalendar;