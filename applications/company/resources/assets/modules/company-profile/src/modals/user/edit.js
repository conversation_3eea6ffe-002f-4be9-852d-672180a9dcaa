const UserModal = require('../user');
const Api = require('@ca-package/api');

const lang = require('lodash/lang');

const modal_user_tpl = require('@cam-company-profile-tpl/modals/user/user.hbs');

module.exports = class extends UserModal {
    constructor() {
        super(modal_user_tpl({
            ns: 'edit',
            phoneAdd: window.fx_url.assets.IMAGE+'icons/phone_add.png',
            info: window.fx_url.assets.IMAGE+'icons/info.png'
        }));

        this.elem.close_user.on('click.fx', (e) => {
            e.preventDefault();
            this.clearForm();
            this.close();
            return false;
        });
    };

    open(id) {
        if (lang.isNull(id)) {
            throw new Error('User id is not defined.');
        }
        let user_relations = {
            'phones': {},
            'image_media_urls': {}
        };
        this.getApiRequest().relations(user_relations).retrieve(id).then((entity, response) => {
            let data = entity.get();

            for (let item of this.elem.form_elements) {
                let this_item = $(item);

                switch(this_item.attr('type')) {
                    case 'checkbox':
                        this_item.prop('checked', data[this_item.attr('name')]);
                        break;
                    case 'file':
                        break;
                    case 'submit':
                        break;
                    default:
                        this_item.val(data[this_item.attr('name')]);
                }
            }

            if (profile_data.user.id === data['id']) {
                this.elem.same_user.hide();
            }

            this.elem.color.spectrum('set', data['calendar_bg_color']);

            for (let phone of data.phones) {
                let descriptions = Object.assign({}, this.phoneDescriptions());
                descriptions[phone.description].selected = true;

                this.addPhoneRow({
                    id: phone.id,
                    descriptions: descriptions,
                    number: phone.number,
                    primary: phone.is_primary
                });
            }

            if (!lang.isNull(data.image_file_id)) {
                this.elem.new_photo.show();
                this.elem.upload_photo.hide();
                this.elem.user_photo.append(`<img src="${data.image_media_urls['profile_thumbnail']+'?'+Math.floor(Math.random() * (20 - 1)) + 1}" />`);
            }

            this.fire('loaded');
            this.elem.title.html('Edit User');
            super.open();
            this.elem.root.scrollTop(0);
        }, (error) => {
            this.fire('error', error);
        });
    }

    save() {
        if (this.saving) {
            return;
        }
        this.saving = true;
        this.formReset();
        this.fire('loading');
        let data = this.getData();
        let image = this.getImage();

        this.getApiRequest().partialUpdate(data["id"], data).then((entity, response) => {
            if (image['upload_image']) {
                this.getApiRequest().file(image['input'][0].files[0]).method(Api.Request.Method.PUT).custom(`${data["id"]}/image`).fail((error) => {
                    this.fire('loaded');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                });
            }
            this.fire('redraw');
            this.fire('loaded');
            super.close();
            this.clearForm();
            this.saving = false;
        }, (error, response) => {
            this.saving = false;
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    for (let item in item_errors) {
                        if (!lang.isUndefined(this.field[item])) {
                            this.field[item].addError('fx-'+item, {message: item_errors[item]});
                        }
                    }
                    this.fire('loaded');
                    break;
                default:
                    this.fire('loaded');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                    break;
            }
        });
    };
};
