'use strict';

const {onEvent} = require("@ca-package/dom");

const Api = require('@ca-package/api');
const Modal = require('@ca-submodule/modal').Base;
const FormValidator = require('@ca-submodule/validator');
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(NumberInput);
const Tooltip = require('@ca-submodule/tooltip');

const Debounce = require('@cac-js/utils/debounce');

const modal_tpl = require('@cam-company-profile-tpl/modals/materials/add.hbs');
const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

class Add extends Modal {
    /**
     * Constructor
     *
     * @param {module:CompanyProfile.Materials} module
     */
    constructor(module) {
        super(modal_tpl(), {
            size: Modal.Size.TINY,
            classes: ['t-add-material']
        });
        Object.assign(this.state, {
            module,
            external_close: false,
            units_loaded: false
        });
        Tooltip.initAll(this.elem.content);
        this.setTitle('New Material');
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of ['name', 'price', 'unit']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 100
            },
            unit: {
                required: true
            },
            cost: {
                required: true,
                pattern: '([0-9]+,?)+(.[0-9]+)?'
            },
            markup: {
            },
            unit_price: {
                pattern: '([0-9]+,?)+(.[0-9]+)?'
            }
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        FormInput.init(this.state.validator.getInputElem('cost'), {
            type: NumberInput.Type.CURRENCY,
            right_align: true,
            allow_minus: false
        });

        FormInput.init(this.state.validator.getInputElem('markup'), {
            type: NumberInput.Type.PERCENTAGE,
            right_align: true,
            allow_minus: false
        });

        FormInput.init(this.state.validator.getInputElem('unit_price'), {
            type: NumberInput.Type.CURRENCY,
            right_align: true,
            allow_minus: false
        });

        initSelectPlaceholder(this.state.validator.getInputElem('unit'));

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            };
            this.reset();
        });

        let that = this;
        this.state.validator.getInputElem('cost').fxEvent('keyup', Debounce(() => {
            that.calculateUnitPrice();
        }, 750));

        this.state.validator.getInputElem('markup').fxEvent('keyup', Debounce(() => {
            that.calculateUnitPrice();
        }, 750));
    };

    /**
     * Calculate the unit price based on cost and markup
     */
    calculateUnitPrice() {
        let cost = parseFloat(this.state.validator.getInputElem('cost').val()),
            markup = this.state.validator.getInputElem('markup').val(),
            unit_price_field = this.state.validator.getInputElem('unit_price'),
            value = 0;
        if (markup == 0 || markup === '') {
            markup = null;
        }

        if (markup !== null) {
            value = cost * (parseFloat(markup) / 100) + cost;
        } else {
            value = cost;
        }
        unit_price_field.val(value);
    };

    /**
     * Load units into select input
     *
     * @returns {Promise<void>}
     */
    async loadUnits() {
        if (this.state.units_loaded === false) {
            let unit_elem = this.state.validator.getInputElem('unit');
            unit_elem.prop('disabled', true);

            let {entities: units} = await Api.Resources.Units()
                .filter('status', Api.Constants.Units.Status.ACTIVE)
                .sort('name', 'asc')
                .all();
            for (let unit of units) {
                let unit_option = $('<option/>')
                    .attr('value', unit.get('id', ''))
                    .text(unit.get('name', ''));
                unit_elem.append(unit_option);
            }
            unit_elem.prop('disabled', false);
            this.state.units_loaded = true;
        }
    };

    /**
     * Open modal
     *
     * @param {Promise} $0.promise
     */
    open(promise) {
        this.state.promise = promise;
        this.loadUnits();
        super.open();
    };

    /**
     * Save changes
     */
    save() {
        this.clearMessages();
        this.startWorking();

        let markup = null,
            markup_val = this.state.validator.getInputElem('markup').val();
        if (markup_val !== '' &&  markup_val !== '0') {
            markup = markup_val;
        }

        let data = {
            'name': this.state.validator.getInputElem('name').val(),
            'unit_id': this.state.validator.getInputElem('unit').val(),
            'cost': this.state.validator.getInputElem('cost').val(),
            'markup': markup,
            'unit_price': this.state.validator.getInputElem('unit_price').val()
        };

        Api.Resources.Materials().store(data).then(({data}) => {
            this.state.promise.resolve(data);
        }, (error, response) => {
            this.resetWorking();
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    console.log(item_errors);
                    break;
                default:
                    this.showErrorMessage(
                        'Unable to save material, please contact support'
                    );
                    break;
            }
        });
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.state.validator.reset();
        this.elem.form[0].reset();
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = Add;
