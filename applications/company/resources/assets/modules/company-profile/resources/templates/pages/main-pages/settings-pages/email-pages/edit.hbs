<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-page-wrapper">
        <form class="m-company-settings-form" data-js="form">
            <div class="c-csf-error" data-js="error"></div>
            <div class="c-csf-row t-email">
                <div class="f-field">
                    <label class="f-f-label">
                        From Email Address
                        <span data-tooltip data-type="info">
                        You can send appointment reminders, bids, or other emails via {{brand_name}}. While we facilitate the
                        sending, the emails come from your company. This is the email address emails will come from. (example: <EMAIL>)
                    </span>
                    </label>
                    <input class="f-f-input" type="text" required data-js="email_from" />
                </div>
                <div class="f-field">
                    <label class="f-f-label">
                        Reply-To Email Address
                        <span data-tooltip data-type="info">
                        The email address for customer email responses. (example: <EMAIL>)
                    </span>
                    </label>
                    <input class="f-f-input" type="text" required data-js="email_reply" />
                </div>
                <div class="f-field">
                    <label class="f-f-label">
                        Additional Email Recipients
                        <span data-tooltip data-type="info">
                        Email address(es) that will be CC'd on system generated emails for the project salesperson (bid has been viewed, bid accepted, bid rejected).
                    </span>
                        <span class="f-fl-optional">(separate multiple email addresses with a comma)</span>
                    </label>
                    <textarea class="f-f-input" data-js="additional_email_recipients"></textarea>
                </div>
            </div>
            <div class="c-csf-row t-switch">
                <div class="f-field">
                    <input class="f-f-input" type="checkbox" id="reminder_24_hours_before_appointment"
                           data-fx-form-input="switch" data-js="reminder-24-hours-before-appointment">
                    <label class="f-f-label t-label" for="reminder_24_hours_before_appointment">
                        Reminder 24 Hours Before Appointment
                        <span data-tooltip data-type="info">
                        Reminder email will be sent 24 hours before appointment
                    </span>
                    </label>
                </div>
            </div>
        </form>
    </div>
</div>