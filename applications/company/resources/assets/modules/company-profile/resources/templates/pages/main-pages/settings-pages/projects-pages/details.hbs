<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="edit" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-company-settings">
        <div class="c-cs-wrapper">
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Recently Closed Projects
                    <span data-tooltip data-type="info">
                    Number of days a closed project will show in the pipeline
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="recently-completed-status"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Primary Marketing Source Required
                    <span data-tooltip data-type="info">
                    Require primary marketing source when creating or editing a project
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="marketing-source"></div>
                <h4 class="c-cswc-content t-link" style="margin-top: 16px">
                    To edit Marketing Sources, visit <a href="{{marketing_link}}">Marketing</a> via the top navigation bar
                </h4>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Project Sales Collaboration
                    <span data-tooltip data-type="info">
                    Allow salespeople to have access to all projects; including projects that are not assigned to them
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="project-sales-collaboration"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Project Install Collaboration
                    <span data-tooltip data-type="info">
                    Allow installers to have access to all projects; including projects where they are not assigned to an installation appointment
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="project-install-collaboration"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Result Required
                    <span data-tooltip data-type="info">
                        Require a result when closing or cancelling a project
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="result-required"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Show Project Costing<br>To Primary Users Only
                    <span data-tooltip data-type="info">
                    Only allow primary users to view and manage project costing on the Project Overview Page
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="project-costing-for-primary-only"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Default Project Summary
                    <span data-tooltip data-type="info">
                        Add default content or custom questions (e.g., Budget, Start Date) that will appear in the Summary field during Customer Intake
                    </span>
                </h4>
                <div class="c-cswc-content t-project-summary" data-js="default-project-summary"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Project Type Required
                    <span data-tooltip data-type="info">
                        Require a project type when creating or editing a project
                    </span>
                </h4>
                <div class="c-cswc-content" data-js="project-type-required"></div>
            </div>

        </div>

        <div class="m-project-types">
            <div class="c-lt-title t-info">
                Project Types
                <span data-tooltip data-type="info">
                    Project type is a way to organize leads and projects based on the service being provided.
                </span>
            </div>
            <div class="c-lt-table">
                <div class="c-ltt-header">
                    <div class="f-field t-name">
                        <label class="f-f-label">Name</label>
                    </div>
                    <div class="f-field t-status">
                        <label class="f-f-label">Status</label>
                    </div>
                </div>
                <div class="c-ltt-body" data-js="project-types-table"></div>
            </div>
        </div>

       <div class="m-result-types">
            <div class="c-lt-title t-info">
                Result Types
                <span data-tooltip data-type="info">
                    Result types are a way to note the outcome of projects for quick visibility and aggregated reporting
                </span>
            </div>
            <div class="c-lt-table">
                <div class="c-ltt-header">
                    <div class="f-field t-name">
                        <label class="f-f-label">Name</label>
                    </div>
                    <div class="f-field t-status">
                        <label class="f-f-label">Status</label>
                    </div>
                </div>
                <div class="c-ltt-body" data-js="result-types-table"></div>
            </div>
        </div>

    </div>
</div>