<form class="m-form" data-js="form">
    <div class="c-f-grid">
        <div class="f-field t-name">
            <label class="f-f-label">Name</label>
            <input class="f-f-input" type="text" data-js="name" />
        </div>
        <div class="f-field t-unit">
            <label class="f-f-label">Unit</label>
            <select class="f-f-input" data-js="unit" placeholder="-- Select One --"></select>
        </div>
        <div class="f-field t-cost">
            <label class="f-f-label">Cost</label>
            <input class="f-f-input" type="text" data-fx-form-input="number" data-js="cost" />
        </div>
        <div class="f-field t-markup">
            <label class="f-f-label">
                Markup
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" data-fx-form-input="number" value="0" data-js="markup" />
        </div>
        <div class="f-field t-unit-price">
            <label class="f-f-label">Unit Price</label>
            <input class="f-f-input" disabled type="text" data-fx-form-input="number" data-js="unit_price" />
        </div>
    </div>
</form>
