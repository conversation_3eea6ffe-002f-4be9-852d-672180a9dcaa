<div class="c-cacb-row" data-index="{{index}}">
    <div class="f-field t-item">
        <label class="f-f-label">
            Item
        </label>
        <select class="f-f-input" data-js="item" data-index="{{index}}"
                required
                data-parsley-required-message="Required"
                data-fx-form-input="dynamic-dropdown"></select>
    </div>
    <div class="f-field t-cost">
        <label class="f-f-label">
            Cost
            <span class="f-fl-optional">(Before Markup)</span>
        </label>
        <input class="f-f-input" type="text" value="{{cost}}" data-js="cost" data-content="" disabled />
    </div>
    <div class="f-field t-quantity">
        <label class="f-f-label">
            Quantity
        </label>
        <input class="f-f-input" type="text" value="{{quantity}}" data-fx-form-input="number" data-js="quantity" data-index="{{index}}"
               required
               data-parsley-min=".0001"
               data-parsley-min-message=""
               data-parsley-required-message="" />
    </div>
    <div class="f-field t-markup">
        <label class="f-f-label">
            Markup
            <span class="f-fl-optional">(%)</span>
        </label>
        <input class="f-f-input" type="text" value="{{markup}}" data-fx-form-input="number" data-js="markup" disabled />
    </div>
    <div class="f-field t-total">
        <label class="f-f-label">
            Total
        </label>
        <input class="f-f-input" type="text" value="{{total}}" data-fx-form-input="number" data-js="total" disabled />
        <div class="t-button-remove">
            <button class="f-f-button-remove" data-js="remove" data-index="{{index}}">
                <div data-text>Delete Row</div>
                <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
            </button>
        </div>
    </div>
</div>
