<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="edit" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-company-settings">
        <div class="c-cs-wrapper">
            <div class="c-csw-content">
                <div class="c-cswc-title">
                    Text Messaging Status
                    <span data-tooltip data-type="info">
                Determines if system will send any text messages to customers
            </span>
                </div>
                <div class="c-cswc-content" data-js="text-messaging-enabled"></div>
            </div>
            <div class="c-csw-content">
                <div class="c-cswc-title">
                    Delivery Failure Email Recipients
                    <span data-tooltip data-type="info">
                Email address which will be notified if text message fails to be delivered
            </span>
                </div>
                <div class="c-cswc-content" data-js="delivery-failure-email-recipients"></div>
            </div>
        </div>
    </div>
</div>