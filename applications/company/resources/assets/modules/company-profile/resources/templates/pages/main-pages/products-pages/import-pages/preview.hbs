<div class="c-p--p-page m-products-import" data-js="step-container">
    <div class="m-page-header">
        <div class="c-ph-actions" data-button-left>
            <button class="c-pha-tertiary t-icon-text" data-navigate="{{back_route}}">
                <div data-text>Back</div>
                <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-left-line"></use></svg>
            </button>
        </div>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-text-icon" data-navigate="{{next_route}}">
                <div data-text>Next</div>
                <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="c-import-step upload-step" data-js="upload-step">
        <div class="c-import-step-content">
            <div class="c-pi-mapping">
                <div class="c-pi-components" data-js="components">
                    <div class="c-pfc-header">
                        <h4>CSV Columns</h4>
                    </div>
                    <div class="c-pfc-input-container">
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">ID</span>
                            <select class="f-f-input" aria-placeholder="ID" data-js="dropdown-id_user_defined"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Name</span>
                            <select class="f-f-input" aria-placeholder="Name" data-js="dropdown-name"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Description</span>
                            <select class="f-f-input" aria-placeholder="Description" data-js="dropdown-description"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Price</span>
                            <select class="f-f-input" aria-placeholder="Price" data-js="dropdown-price"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Unit</span>
                            <select class="f-f-input" aria-placeholder="Unit" data-js="dropdown-unit"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Pricing Disclaimer</span>
                            <select class="f-f-input" aria-placeholder="Pricing Disclaimer" data-js="dropdown-pricing_disclaimer"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Categories</span>
                            <select class="f-f-input" aria-placeholder="Categories" data-js="dropdown-category"></select>
                        </div>
                    </div>
                </div>
            </div>
            <h4>Preview</h4>
            <div class="c-pi-table t-pi-table-columns t-pi-preview-table" data-js="preview-table-container"></div>
        </div>
    </div>
</div>
