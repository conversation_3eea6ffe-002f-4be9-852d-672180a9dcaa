<div class="c-i--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>Wisetack Settings</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="cancel" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-integrations">
        <div class="c-i-error" data-js="error"></div>
        <form class="c-i-form" data-js="form">
            <div class="c-f-row">
                <div data-js="current-pricing-plan-wrapper" class="t-hidden">
                    <div class="c-fr-field c-if-half f-field">
                        <label class="f-f-label">0% APR Add-Ons
                        <span data-tooltip data-type="info">
                            Add longer 0% terms to your standard plan
                        </span></label>
                    </div>
                    <p>
                        Expand the 0% APR options you offer. You only pay a higher fee when your customer qualifies for and selects a 0% APR option for 6, 12, or 24 months.
                        For all other options, the fee you pay is 3.9%.<a href="https://cxlratr.to/hc-wisetack" target="_blank">Click here to learn more in our Help Center</a>
                    </p>

                    <div class="f-field flex no-margin c-if-inline-pointer" data-js="pricing-plan-radios">
                    </div>
                </div>
            </div>

            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="financing_required_for_all_projects"
                               data-fx-form-input="switch" data-js="financing-required-for-all-projects">
                        <label class="f-f-label t-label" for="financing_required_for_all_projects">
                            Financing Required For All Projects
                            <span data-tooltip data-type="info">
                                Financing will be required for all projects, users will not have the option to turn it on/off per project
                            </span>
                        </label>
                    </div>
                </div>

                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="project_financing_enabled_by_default"
                               data-fx-form-input="switch" data-js="project-financing-enabled-by-default">
                        <label class="f-f-label t-label" for="project_financing_enabled_by_default">
                            Enable Project Financing By Default
                            <span data-tooltip data-type="info">
                                New projects will automatically have project financing on by default
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


