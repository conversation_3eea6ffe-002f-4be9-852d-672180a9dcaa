<div class="c-vdb-row" data-js="volume-discount-row" data-index="{{index}}">
    <div class="f-field t-min-count">
        <label class="f-f-label">
            Min Qty
            <span data-tooltip data-type="info">
                Minimum quantity for the volume discount range.
            </span>
            <span class="f-fl-optional">(From...)</span>
        </label>
        <input class="f-f-input" type="text" value="{{min_count}}" required data-fx-form-input="number"
            data-js="min-count" data-index="{{index}}"
            {{#if first_row}} disabled{{else}} data-parsley-min="1"
            data-parsley-min-message=""
            data-parsley-required-message=""{{/if}}/>
        <span>-</span>
    </div>
    <div class="f-field t-max-count">
        <label class="f-f-label">
            Max Qty
            <span data-tooltip data-type="info">
                Maximum quantity is less than this number for the volume discount range.
            </span>
            <span class="f-fl-optional">(...to less than)</span>
        </label>
        <input class="f-f-input" type="text" value="{{max_count}}" data-fx-form-input="number" data-js="max-count"
               disabled {{#if last_row}} placeholder="Infinity"{{/if}} />
    </div>
    <div class="f-field t-adjustment{{#unless allow_adjustment_type}} t-components{{/unless}}">
        <label class="f-f-label">
            Adjustment
            <span data-tooltip data-type="info">
                Amount the unit price will be discounted if the quantity is within the given range.
            </span>
        </label>
        <select class="f-f-input" data-fx-form-input="button-group" data-js="adjustment-type"
            {{#if first_row}} disabled{{/if}}{{#unless allow_adjustment_type}} disabled{{/unless}} data-index="{{index}}">
            {{#eachInMap adjustment_types}}
                <option value="{{key}}"{{#ifeq key ../adjustment_type}} selected{{/ifeq}}>{{value.label}}</option>
            {{/eachInMap}}
        </select>
        <input class="f-f-input" type="text" value="{{adjustment}}"
            data-fx-form-input="number" data-js="adjustment" data-index="{{index}}"
            {{#if first_row}} disabled{{else}}
            required data-parsley-min="0.01"
            data-parsley-pattern="^([0-9]+,?)+(.[0-9]+)?$"
            data-parsley-min-message=""
            data-parsley-required-message=""
            data-parsley-pattern-message=""{{/if}} />
    </div>
    <div class="f-field t-price">
        <label class="f-f-label">
            Unit Price
            <span data-tooltip data-type="info">
                The price to charge per unit of this product when the quantity is within the given range.
            </span>
        </label>
        <input class="f-f-input" type="text" value="{{price}}" data-fx-form-input="number" data-js="price"
            {{#if enable_total}} required
            data-parsley-required-message=""
            data-parsley-pattern="^([0-9]+,?)+(.[0-9]+)?$"
            data-parsley-pattern-message=""{{else}} disabled{{/if}} />
        <div class="t-button-remove">
            <button class="f-f-button-remove" {{#if first_row}} disabled{{/if}} data-js="remove" data-index="{{index}}">
                <div data-text>Delete Row</div>
                <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
            </button>
        </div>
    </div>
</div>
