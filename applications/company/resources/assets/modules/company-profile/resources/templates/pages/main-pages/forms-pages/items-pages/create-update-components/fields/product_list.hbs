<div class="c-fcus-item t-product-list" data-js="override" data-id="{{id}}">
    <h3 class="c-fcusi-label">{{label}}<span class="c-fcusil-required">*</span></h3>
{{#if description}}
    <p>{{nl2br description}}</p>
{{/if}}
    <div class="c-fcusi-field t-with-button f-field">
        <div class="c-fcusif-input">
            <select data-fx-form-input="nested-dropdown" data-js="input"></select>
        </div>
        <div class="c-fcusif-button">
            <a class="b-text t-primary" data-js="create-category">New Category</a>
        </div>
    </div>
    <div class="c-fcusi-category" data-js="category-info">Category contains <span data-js="product-count"></span>. <a data-js="add-product">Add product.</a></div>
</div>