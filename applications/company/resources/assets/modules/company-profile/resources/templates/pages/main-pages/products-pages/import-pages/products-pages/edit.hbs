<form class="m-form" data-js="form">
    <div class="c-f-row t-name-price-unit">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Name</label>
            <input class="f-f-input" required type="text" value="{{name}}" data-js="name"/>
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Price</label>
            <input class="f-f-input" required type="text" value="{{price}}" data-fx-form-input="number" data-parsley-trigger="keyup" data-js="price"/>
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Unit</label>
            <input class="f-f-input" required type="text" value="{{unit}}" data-js="unit"/>
        </div>
    </div>
    <div class="c-f-row">
        <div class="c-pf-description">
            <textarea class="f-f-input" data-js="description" data-fx-form-input="hidden-textarea"></textarea>
        </div>
    </div>
    <div class="c-f-row">
        <div class="c-pf-pricing-disclaimer">
            <textarea class="f-f-input" data-js="pricing_disclaimer" data-fx-form-input="hidden-textarea"></textarea>
        </div>
    </div>
    <div class="c-f-row">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label t-optional">
                Categories
                <span data-tooltip data-type="info">
                     Use comma separated items to assign multiple categories
                </span>
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="{{category}}" data-js="category"/>
        </div>
    </div>
</form>
