@use '~@cac-sass/base';
@use '~@cas-form-input-sass/switch';

.m-emails {
    .c-e--pages {
        height: 100%;
    }
    .c-e--p-page {
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }
}

.m-email-form {
    overflow: auto;
    height: calc(100% - 57px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(32px);
    justify-content: flex-start;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    .c-ef-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-ef-row {
        display: grid;
        row-gap: base.unit-rem-calc(8px);
        column-gap: base.unit-rem-calc(16px);
        .f-field, .c-efr-instructions {
            flex: 1;
            min-width: base.unit-rem-calc(256px);
        }
        &.t-switch {
            display: flex;
            justify-content: flex-start;
            padding: base.unit-rem-calc(12px);
            margin-bottom: base.unit-rem-calc(16px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(8px);
            background: base.$color-background-form;
            min-height: base.unit-rem-calc(47px);
            overflow: auto;
            .f-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                > .t-label {
                    @include base.typo-paragraph-medium;
                    padding: 0 0 base.unit-rem-calc(1px);
                    text-wrap: nowrap;
                }
                .m-tooltip-info {
                    padding-left: base.unit-rem-calc(4px);
                }
            }
        }
    }
    .c-ef-subject-row {
        display: flex;
        gap: base.unit-rem-calc(16px);
        align-items: flex-end;
        justify-content: flex-end;
        margin-bottom: base.unit-rem-calc(-16px);
        @include base.respond-to('<small') {
            flex-wrap: wrap;
            gap: base.unit-rem-calc(16px);
        }
        > div {
            flex: 1;
            @include base.respond-to('<small') {
                width: 100%;
                flex: auto;
            }
        }
        > a {
            &.t-tertiary {
                @include base.button-text-icon-tertiary;
            }
        }
    }
        .c-efr-instructions {
            @include base.callout-info;
            margin-bottom: 0;
        }
}

.m-email-subject-tag-menu {
    display: flex;
    flex-direction: column;
    background-color: base.$color-white-default;
    border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    border-radius: base.unit-rem-calc(8px);
    box-shadow: base.$elevation-level-3;
    z-index: 299;
    padding: base.unit-rem-calc(4px);
    gap: base.unit-rem-calc(4px);
    margin-top: base.unit-rem-calc(4px);
    margin-right: base.unit-rem-calc(-4px);
    .c-estm-action {
        display: flex;
        align-items: center;
        color: base.$color-grey-dark-4;
        font-size: base.unit-rem-calc(14px);
        font-weight: 400;
        cursor: pointer;
        height: base.unit-rem-calc(32px);
        padding: 0 base.unit-rem-calc(8px);
        border-radius: base.unit-rem-calc(4px);
        min-width: base.unit-rem-calc(128px);
        transition: all 0.2s ease-out;
        &:last-child {
            border-bottom: none;
        }
        &:hover {
            color: base.$color-primary-default;
            background-color: base.$color-primary-light-4;
        }
    }
}

.m-emails-list {
    padding: base.unit-rem-calc(12px);
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
    }
}
.c-el-table {
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        .c-t-table-wrapper {
            border-radius: 0;
            border-width: base.unit-rem-calc(1px) 0;
        }
        .c-t-header {
            padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(12px);
        }
    }
}
