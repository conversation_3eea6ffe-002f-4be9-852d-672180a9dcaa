@use '~@cac-sass/base';

.m-integrations-section {
    .c-i--pages {
        height: 100%;
    }
    .c-i--p-page {
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }
}


/* Integrations Section Page Content */
.m-integrations {
    width: 100%;
    height: calc(100% - 58px - 56px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(24px);
    overflow: auto;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }

    .t-hidden {
        display: none;
    }

    .c-i-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        margin-bottom: base.unit-rem-calc(16px);
        &.t-show {
            display: inline-flex;
        }
    }
    .c-i-form {
        display: flex;
        gap: base.unit-rem-calc(16px);
        flex-direction: column;
    }
        .c-if-half {
            width: 50%;
            @include base.respond-to('<xlarge') {
                width: 100%;
            }
        }
        .c-if-switch-wrapper {
            display: flex;
            padding: base.unit-rem-calc(12px);
            background: base.$color-background-form;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(8px);
            flex-wrap: wrap;
        }
        .c-ifsw-group {
            display: flex;
            justify-content: flex-start;
            width: 50%;
            @include base.respond-to('<small') {
                width: 100%;
            }
            .switch {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
            }
        }

        .c-if-inline-pointer {
            display: inline-block;
            label {
                cursor: pointer;
            }
        }
    .c-i-table {
        margin-bottom: base.unit-rem-calc(32px);
        display: none;
    }
    .c-i-wrapper {
        grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(224px), 1fr));
        row-gap: base.unit-rem-calc(32px);
        column-gap: base.unit-rem-calc(16px);
        background: base.$color-background-form;
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(16px);
        margin-bottom: base.unit-rem-calc(32px);
        min-height: base.unit-rem-calc(48px);
        overflow: auto;
        display: none;
        &.t-quickbooks-settings {
            margin-bottom: base.unit-rem-calc(16px);
        }
        &.t-status {
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
            margin-bottom: base.unit-rem-calc(16px);
        }
        &.t-instructions {
            display: none;
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            @include base.typo-paragraph-small;
            &.t-show {
                display: block;
            }
        }
        &.t-show {
            display: grid;
        }
    }
    .c-iw-content {
        display: flex;
        flex-direction: column;
        &.t-flex-row {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }
    .c-iwc-title {
        @include base.typo-paragraph-medium;
        width: max-content;
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(4px);
        margin-bottom: base.unit-rem-calc(4px);
    }
    .c-iwc-subtitle {
        @include base.typo-paragraph;
        color: base.$color-grey-dark-2;
        font-style: italic;
        margin-bottom: base.unit-rem-calc(8px);
    }
    .c-iwc-content {
        @include base.typo-paragraph;

    }
    .c-iwc-content {
        @include base.typo-paragraph;
        color: base.$color-grey-dark-1;
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(8px);
        text-wrap: nowrap;
        .h-text {
            padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
        }
        &.t-bold {
            @include base.typo-paragraph-medium;
            color: base.$color-grey-dark-4;
        }
        &.t-full {
            width: 100%;
            justify-content: space-between;
        }
        .c-iwcc-status {
            display: flex;
            > [data-icon] {
                @include base.svg-icon('default-24');
                color: base.$color-green-light-1;
            }
        }
    }

    .c-iwc-disconnect {
        display: none;
        @include base.button-text-icon-tertiary('negate');
        &.t-show {
            display: inline-flex;
        }
    }

}

.m-quickbooks-sync {
    #quickbooks-data-table {
        table {
            &.dataTable {
                width:100%;
                td {
                    &:last-child {
                        position: relative;
                    }
                    padding: .5rem 0.625rem;
                    .button-loading {
                        &.table {
                            right: 0px;
                            left: unset;
                            width: 1.2rem;
                            height: 1.2rem;
                            top: 12px;
                        }
                    }
                    .remove-sync {
                        display: none;
                        position: absolute;
                        color: #8a8a8a;
                        right: .3rem;
                        top: .7rem;
                        font-size: 1.2rem;
                        line-height: 1;
                        cursor: pointer;
                        &.show {
                            display: inline;
                        }
                        &:hover {
                            color: #0a0a0a;
                        }
                        &.disabled {
                            color: lightgray;
                        }
                    }
                }
            }
        }
        .dataTables_length {
            label {
                float: left;
                text-align: left;
                margin-bottom: .5rem;
            }
            select {
                width: 75px;
                margin-bottom: 0;
            }
        }
        .paginate_button {
            padding: 0 10px;
            &.current {
                background: base.$color-primary-default;
                border-radius: 3px;
                color: #ffffff;
                padding: .25rem 10px;
            }
            &.disabled {
                color: #cacaca;
                cursor: not-allowed;
            }
            &.loading {
                cursor: wait;
            }
        }
        .dataTables_info {
            clear: both;
            float: left;
            font-size: .875rem;
            color: #636465;
        }
        div.dataTables_wrapper div.dataTables_paginate {
            float: right;
            margin: 0;
        }
    }
}

.m-wt-wrapper {
    padding:1rem 1.5rem;
}

.m-wt-button-section .t-hidden {
    display: none;
}
.m-wt-integrations-section {
    &.t-hidden {
        display: none!important;
    }

    .t-flex-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .t-bottom-gap {
        margin-bottom: 2rem;

        span {
            width: 80%;
        }
    }

    .m-wt-redirect-wrapper {
        display: flex;
        flex-direction: column;

        .m-wt-title {
            width: max-content;
            display: block;
        }
    }

    .wt-primary-button {
        @include base.button-text-primary;
    }

    .wt-tertiary-button {
        @include base.button-text-icon-tertiary('negate');
        &.t-show {
            display: inline-flex;
        }
    }

    .wt-button-wrapper {
        padding: 1em 0;
    }

    .t-wisetack-settings {
        .c-iwc-content {
            text-wrap: auto;
            width: 90%;
        }
    }

    .c-sync {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
    }
    .c-s-content {
        .t-description {
            span {
                color: base.$color-grey-light-1;
                @include base.typo-paragraph-small;
                font-style: italic;
            }
        }
        >h6 {
            line-height: base.unit-rem-calc(20px);
        }
    }
    .c-sc-group {
        display: flex;
        gap: base.unit-rem-calc(16px);
        margin-top: base.unit-rem-calc(16px);
    }
    .c-scg-input {
        flex: 1 1 0;
        input {
            border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid !important;
            border-radius: base.unit-rem-calc(3px) !important;
            color: base.$color-grey-light-2;
            cursor: not-allowed;
        }
    }
    .c-scg-button {
        flex: 0 0 auto;
        button {
            border-radius: base.unit-rem-calc(3px)
        }
    }

    .c-cs-wrapper {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(224px), 1fr));
        row-gap: base.unit-rem-calc(32px);
        column-gap: base.unit-rem-calc(16px);
        background: base.$color-background-form;
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(9px) base.unit-rem-calc(16px);
        margin-bottom: base.unit-rem-calc(32px);
        overflow: auto;

        &.t-flex-row {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }

    .c-csw-content {
        display: flex;
        flex-direction: column;
    }
    .c-csw-content-row {
        gap: 1em;
        display: flex;
    }

    .c-cswc-title {
        @include base.typo-paragraph-medium;
        width: max-content;
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(4px);
        margin-bottom: 0;
    }
    .c-cswc-subtitle {
        @include base.typo-paragraph;
        color: base.$color-grey-dark-2;
        font-style: italic;
        margin-bottom: base.unit-rem-calc(8px);
    }
    .c-cswc-content {
        @include base.typo-paragraph;
        color: base.$color-grey-dark-1;
        .h-text {
            padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
            margin-top: base.unit-rem-calc(4px);
        }
    }
}

/******* Modal Overrides ********/
.c-settings {
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(8px);
    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
    background: base.$color-background-form;
    border-radius: base.unit-rem-calc(8px);
    padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
    .i-g-row {
        margin-bottom: 0;
        &:last-child {
            margin-bottom: 0 !important;
        }
        .switch {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
        }
    }
}
