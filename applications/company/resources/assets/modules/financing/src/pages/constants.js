import moment from "moment-timezone";

const report_period_types = {
    ALL_DATES: 1,
    TODAY: 2,
    THIS_WEEK: 3,
    THIS_WEEK_TO_DATE: 4,
    THIS_MONTH: 5,
    THIS_MONTH_TO_DATE: 6,
    THIS_QUARTER: 7,
    THIS_QUARTER_TO_DATE: 8,
    THIS_YEAR: 9,
    THIS_YEAR_TO_DATE: 10,
    THIS_YEAR_TO_LAST_MONTH: 11,
    YESTERDAY: 12,
    RECENT: 13,
    LAST_WEEK: 14,
    LAST_WEEK_TO_DATE: 15,
    LAST_MONTH: 16,
    LAST_MONTH_TO_DATE: 17,
    LAST_QUARTER: 18,
    LAST_QUARTER_TO_DATE: 19,
    LAST_YEAR: 20,
    LAST_YEAR_TO_DATE: 21,
    LAST_30_DAYS: 22,
    LAST_60_DAYS: 23,
    LAST_90_DAYS: 24,
    LAST_365_DAYS: 25,
    NEXT_WEEK: 26,
    NEXT_4_WEEKS: 27,
    NEXT_MONTH: 28,
    NEXT_QUARTER: 29,
    NEXT_YEAR: 30,
    CUSTOM: 31
};

const report_period_options = {
    [report_period_types.ALL_DATES]: {
        label: 'All Dates',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(10, 'years').startOf('month').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.TODAY]: {
        label: 'Today',
        is_option: false,
        range: () => {
            let start_date = moment().format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_WEEK]: {
        label: 'This Week',
        is_option: true,
        range: () => {
            let start_date = moment().startOf('week').format('MM/DD/YYYY'),
                end_date = moment().endOf('week').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_WEEK_TO_DATE]: {
        label: 'This Week-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().startOf('week').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_MONTH]: {
        label: 'This Month',
        is_option: true,
        range: () => {
            let start_date = moment().startOf('month').format('MM/DD/YYYY'),
                end_date = moment().endOf('month').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_MONTH_TO_DATE]: {
        label: 'This Month-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().startOf('month').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_QUARTER]: {
        label: 'This Quarter',
        is_option: true,
        range: () => {
            let start_date = moment().startOf('quarter').format('MM/DD/YYYY'),
                end_date = moment().endOf('quarter').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_QUARTER_TO_DATE]: {
        label: 'This Quarter-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().startOf('quarter').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_YEAR]: {
        label: 'This Year',
        is_option: true,
        range: () => {
            let start_date = moment().startOf('year').format('MM/DD/YYYY'),
                end_date = moment().endOf('year').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_YEAR_TO_DATE]: {
        label: 'This Year-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().startOf('year').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.THIS_YEAR_TO_LAST_MONTH]: {
        label: 'This Year-To-Last-Month',
        is_option: false,
        range: () => {
            let start_date = moment().startOf('year').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'months').endOf('month').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.YESTERDAY]: {
        label: 'Yesterday',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(1, 'days').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'days').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.RECENT]: {
        label: 'Recent',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(7, 'days').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_WEEK]: {
        label: 'Last Week',
        is_option: true,
        range: () => {
            let start_date = moment().subtract(1, 'weeks').startOf('week').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'weeks').endOf('week').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_WEEK_TO_DATE]: {
        label: 'Last Week-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(1, 'weeks').startOf('week').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_MONTH]: {
        label: 'Last Month',
        is_option: true,
        range: () => {
            let start_date = moment().subtract(1, 'months').startOf('month').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'months').endOf('month').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_MONTH_TO_DATE]: {
        label: 'Last Month-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(1, 'months').startOf('month').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_QUARTER]: {
        label: 'Last Quarter',
        is_option: true,
        range: () => {
            let start_date = moment().subtract(1, 'quarters').startOf('quarter').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'quarters').endOf('quarter').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_QUARTER_TO_DATE]: {
        label: 'Last Quarter-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(1, 'quarters').startOf('quarter').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_YEAR]: {
        label: 'Last Year',
        is_option: true,
        range: () => {
            let start_date = moment().subtract(1, 'years').startOf('year').format('MM/DD/YYYY'),
                end_date = moment().subtract(1, 'years').endOf('year').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_YEAR_TO_DATE]: {
        label: 'Last Year-To-Date',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(1, 'years').startOf('year').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_30_DAYS]: {
        label: 'Last 30 Days',
        is_option: true,
        is_default: true,
        range: () => {
            let start_date = moment().subtract(30, 'days').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_60_DAYS]: {
        label: 'Last 60 Days',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(60, 'days').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_90_DAYS]: {
        label: 'Last 90 Days',
        is_option: false,
        range: () => {
            let start_date = moment().subtract(90, 'days').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.LAST_365_DAYS]: {
        label: 'Last 365 Days',
        is_option: true,
        range: () => {
            let start_date = moment().subtract(1, 'year').format('MM/DD/YYYY'),
                end_date = moment().format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.NEXT_WEEK]: {
        label: 'Next Week',
        is_option: false,
        range: () => {
            let start_date = moment().add(1, 'weeks').startOf('week').format('MM/DD/YYYY'),
                end_date = moment().add(1, 'weeks').endOf('week').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.NEXT_4_WEEKS]: {
        label: 'Next 4 Weeks',
        is_option: false,
        range: () => {
            let start_date = moment().add(1, 'weeks').startOf('week').format('MM/DD/YYYY'),
                end_date = moment().add(4, 'weeks').endOf('week').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.NEXT_MONTH]: {
        label: 'Next Month',
        is_option: false,
        range: () => {
            let start_date = moment().add(1, 'months').startOf('month').format('MM/DD/YYYY'),
                end_date = moment().add(1, 'months').endOf('month').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.NEXT_QUARTER]: {
        label: 'Next Quarter',
        is_option: false,
        range: () => {
            let start_date = moment().add(1, 'quarters').startOf('quarter').format('MM/DD/YYYY'),
                end_date = moment().add(1, 'quarters').endOf('quarter').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.NEXT_YEAR]: {
        label: 'Next Year',
        is_option: false,
        range: () => {
            let start_date = moment().add(1, 'years').startOf('year').format('MM/DD/YYYY'),
                end_date = moment().add(1, 'years').endOf('year').format('MM/DD/YYYY');
            return [start_date, end_date];
        }
    },
    [report_period_types.CUSTOM]: {
        label: 'Custom',
        is_option: true,
        range: () => {},
    }
};

export { report_period_types, report_period_options };