'use strict';

import moment from 'moment-timezone';

import Api from '@ca-package/api';

import {Base as Table} from '@ca-submodule/table';

const Number = require('@cac-js/utils/number');
const {createSuccessMessage} = require("@cas-notification-toast-js/message/success");
const { STATUS_DETAILS, PRICING_PLANS } = require("@cac-js/wisetack.js");

export default class TransactionsTable {
    constructor(elem, state) {
        if (!elem || !state) {
            throw new Error('Element and state are required to initialize FinancingTable');
        }

        this.elem = elem;
        this.state = Object.assign(state, {
            table_component: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                },
                filters: {
                    created_at: [
                        Table.Operators.BETWEEN, [
                            moment.tz(state.layout.user.timezone)
                                .subtract(1, 'months')
                                .startOf('month')
                                .startOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
                            moment.tz(state.layout.user.timezone)
                                .endOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
                        ]
                    ]
                }
            }
        });
    };

    /**
     * Initialize and create the table
     */
    create() {
        try {
            if (this.state.table_loaded) {
                this.refresh();
                return
            }

            this.state.table_component = new Table(this.elem)
                .on('row_click', this.handleRowClick.bind(this))
                .on('scope_change', this.handleScopeChange.bind(this));

            this.setColumns();
            this.setRowActions();
            this.setButtons();
            this.setAjaxRequest();

            if (this.state.table_scope) {
                this.state.table_component.setState(this.state.table_scope);
            }

            this.state.table_component.build();
            this.state.table_loaded = true;
        } catch (error) {
            console.error('Error creating the table:', error);
        }
    };

    /**
     * Refresh the table
     */
    refresh() {
        this.state.table_component.draw();
    };

    /**
     * Update the scope of the table
     * @param {Object} data - The new scope data
     * @param {boolean} setState - Whether to set the table state
     */
    updateScope(data, setState = false) {
        Object.assign(this.state.table_scope, data)

        if (setState) {
            this.state.table_component.setState(data);
        }
    };

    /**
     * Handle row click event
     * @param {Object} data - The data of the clicked row
     */
    handleRowClick(data) {
        if (!data || !data.bid || !data.bid.id || !data.bid.project || !data.bid.project.id) return ;

        let bid_id = data.bid.id.replace(/-/g, '').toUpperCase(),
            project_id = data.bid.project.id,
            url = window.financing_data.project_management_url.replace('{project_id}', project_id) + '/bids/' + bid_id;
        window.open(url, '_blank');
    };

    /**
     * Handle scope change event
     * @param {Object} scope - The new scope of the table
     */
    handleScopeChange(scope) {
        this.state.table_scope = scope;
        window.history.replaceState(null, '', window.location.href.split('?')[0] + Table.buildUrlFromScope(this.state.table_scope));
    };

    /**
     * Build span tooltip for column headers
     *
     * @param {string} text
     * @returns {string}
     */
    buildTooltip(text) {
        return `<span data-tooltip data-type="info">${text}</span>`
    };


    /**
     * Set the columns configuration
     */
    setColumns() {
        this.state.table_component.setColumns({
            customer_name: {
                label: 'Customer Name',
                value: data => {
                    let first_name = data && data.bid && data.bid.project && data.bid.project.property && data.bid.project.property.customer && data.bid.project.property.customer.first_name,
                        last_name = data && data.bid && data.bid.project && data.bid.project.property && data.bid.project.property.customer && data.bid.project.property.customer.last_name;
                    return first_name && last_name ? first_name + ' ' + last_name : null;
                },
                orderable: false,
            },
            project_name: {
                label: 'Project Name',
                value: data => {
                    let description = data && data.bid && data.bid.project && data.bid.project.description;
                    return description || null;
                },
                orderable: false,
            },
            bid_description: {
                label: 'Bid Description',
                value: data => {
                    let description = data && data.bid && data.bid.description;
                    return description || null;
                },
                orderable: false,
            },
            bid_total: {
                label: 'Bid Total',
                value: data => {
                    let total = data && data.bid && data.bid.total;
                    return total ? Number.toCurrency(data.bid.total) : null;
                },
                orderable: false,
            },
            bid_accepted_at: {
                label: 'Bid Accepted',
                value: data => {
                    let accepted_at = data && data.bid && data.bid.accepted_at;
                    return accepted_at ? this.formatDate(accepted_at) : null;
                },
                orderable: false,
            },
            requested_loan_amount: {
                label: 'Requested Amount',
                value: data => data.requested_loan_amount ? Number.toCurrency(data.requested_loan_amount) : null,
            },
            approved_loan_amount: {
                label: 'Approved Amount',
                value: data => data.approved_loan_amount ? Number.toCurrency(data.approved_loan_amount) : null,
            },
            settled_loan_amount: {
                label: 'Settled Amount',
                value: data => data.settled_loan_amount ? Number.toCurrency(data.settled_loan_amount) : null,
            },
            status: {
                label: 'Status',
                value: data => this.formatStatus(data.status),
            },
            pricing_plan: {
                label: `0% APR Terms Offered ${this.buildTooltip('At loan creation date, the current 0% APR Terms that were offered')}`,
                value: data => PRICING_PLANS[data.pricing_plan]["short_name"] || null,
                orderable: false,
            },
            created_at: {
                label: 'Sent',
                value: data => this.formatDate(data.created_at),
            },
            updated_at: {
                label: 'Date of Last Update',
                value: data => this.formatDate(data.updated_at),
            },
            initiated_at: {
                label: `Initiated At ${this.buildTooltip('Customer has input information but has not completed loan application')}`,
                value: data => this.formatDate(data.initiated_at),
            },
            actions_required_at: {
                label: `Actions Required At ${this.buildTooltip('Customer needs to provide additional documentation')}`,
                value: data => this.formatDate(data.actions_required_at),
            },
            authorized_at: {
                label: `Approved At ${this.buildTooltip('Customer is approved for the loan')}`,
                value: data => this.formatDate(data.authorized_at),
            },
            loan_terms_accepted_at: {
                label: `Offer Locked At ${this.buildTooltip('Customer has selected a loan offer and accepted terms/locked in their offer')}`,
                value: data => this.formatDate(data.loan_terms_accepted_at),
            },
            loan_confirmed_at: {
                label: `Confirmed At ${this.buildTooltip('Customer released payment on the loan')}`,
                value: data => this.formatDate(data.loan_confirmed_at),
            },
            settled_at: {
                label: `Paid At ${this.buildTooltip('The payout to the merchant was sent and will be received in their bank account within the next few business days')}`,
                value: data => this.formatDate(data.settled_at),
            },
            declined_at: {
                label: `Declined At ${this.buildTooltip('Customer completed the loan application and was declined')}`,
                value: data => this.formatDate(data.declined_at),
            },
            expiration_date: {
                label: 'Expiration',
                value: data => this.formatDate(data.expiration_date),
            }
        });
    };


    /**
     * Format the status with corresponding HTML
     * @param {string} status - The status to format
     */
    formatStatus(status) {
        if (!status || status === 'PENDING') return null;
        let details = STATUS_DETAILS[status];
        return `<span class="h-text ${details.class}">${details.label}</span>`;
    };

    /**
     * Format the date
     * @param {string} date - The date to format
     * @returns {string} - The formatted date string
     */
    formatDate(date) {
        return date ? moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY') : null;
    };

    /**
     * Set the row actions configuration
     */
    setRowActions() {
        this.state.table_component.setRowActions({
            bid: {
                label: 'View Bid',
                link: {
                    href: data => {
                        if (!data || !data.bid || !data.bid.id || !data.bid.project || !data.bid.project.id) return '#';

                        let bid_id = data.bid.id.replace(/-/g, '').toUpperCase(),
                            project_id = data.bid.project.id;
                        return window.financing_data.project_management_url.replace('{project_id}', project_id) + '/bids/' + bid_id;
                    },
                    target: '_blank',
                },
            },
            link: {
                label: 'Copy Payment Link',
                action: (data) => {
                    navigator.clipboard.writeText(data.link).then(() => {
                        this.state.layout.toasts.addMessage(createSuccessMessage('Copied Payment Link.'));
                    })
                },
            },
        });
    };

    setButtons() {
        this.state.table_component.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table_component.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/financing-transactions' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                }
            },
        });
    }

    /**
     * Set the AJAX request for fetching table data
     */
    setAjaxRequest() {
        this.state.table_component.setAjax(Api.Resources.WisetackTransactions, (request) => {
            request.accept('application/vnd.adg.fx.report-financing-transactions-v1+json')
        });
    };
}
