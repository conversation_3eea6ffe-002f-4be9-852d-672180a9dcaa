'use strict';

import moment from 'moment-timezone';

import Api from '@ca-package/api';

import {Base as Table} from '@ca-submodule/table';

const Number = require('@cac-js/utils/number');
const { PRICING_PLANS } = require("@cac-js/wisetack.js");


const STATUS_DETAILS = {
    'PENDING': { class: 't-yellow', label: null },
    'INITIATED': { class : 't-blue', label: 'Initiated' },
    'PREQUALIFIED': { class: 't-green', label: 'Prequalified' },
    'DECLINED': { class: 't-red', label: 'Declined' },
    'EXPIRED': { class: 't-grey', label: 'Expired' }
};

export default class PrequalificationsTable {
    constructor(elem, state) {
        if (!elem || !state) {
            throw new Error('Element and state are required to initialize FinancingTable');
        }

        this.elem = elem;
        this.state = Object.assign(state, {
            table_component: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                },
                filters: {
                    created_at: [
                        Table.Operators.BETWEEN, [
                            moment.tz(state.layout.user.timezone)
                                .subtract(1, 'months')
                                .startOf('month')
                                .startOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
                            moment.tz(state.layout.user.timezone)
                                .endOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
                        ]
                    ]
                }
            }
        });
    };

    /**
     * Initialize and create the table
     */
    create() {
        try {
            if (this.state.table_loaded) {
                this.refresh();
                return
            }

            this.state.table_component = new Table(this.elem)
                .on('row_click', this.handleRowClick.bind(this))
                .on('scope_change', this.handleScopeChange.bind(this));

            this.setColumns();
            this.setRowActions();
            this.setButtons();
            this.setAjaxRequest();

            if (this.state.table_scope) {
                this.state.table_component.setState(this.state.table_scope);
            }

            this.state.table_component.build();
            this.state.table_loaded = true;
        } catch (error) {
            console.error('Error creating the table:', error);
        }
    };

    /**
     * Refresh the table
     */
    refresh() {
        this.state.table_component.draw();
    };

    /**
     * Update the scope of the table
     * @param {Object} data - The new scope data
     * @param {boolean} setState - Whether to set the table state
     */
    updateScope(data, setState = false) {
        Object.assign(this.state.table_scope, data)

        if (setState) {
            this.state.table_component.setState(data);
        }
    };

    /**
     * Handle row click event
     * @param {Object} data - The data of the clicked row
     */
    handleRowClick(data) {
        if (!data || !data.project_id) return;

        let id = data && data.project_id,
            project_url = window.financing_data.project_management_url.replace('{project_id}', id)
        window.open(project_url, '_blank');
    };

    /**
     * Handle scope change event
     * @param {Object} scope - The new scope of the table
     */
    handleScopeChange(scope) {
        this.state.table_scope = scope;
        window.history.replaceState(null, '', window.location.href.split('?')[0] + Table.buildUrlFromScope(this.state.table_scope));
    };

    /**
     * Build span tooltip for column headers
     *
     * @param {string} text
     * @returns {string}
     */
    buildTooltip(text) {
        return `<span data-tooltip data-type="info">${text}</span>`
    };

    /**
     * Set the columns configuration
     */
    setColumns() {
        this.state.table_component.setColumns({
            customer_name: {
                label: 'Customer Name',
                value: data => data.customer_name || null,
                orderable: false
            },
            project_name: {
                label: 'Project Name',
                value: data => {
                    let project = data && data.project && data.project;
                    return project ? project.description : null;
                },
                orderable: false
            },
            pre_qualified_amount: {
                label: 'Pre-qualified Amount',
                value: data => data.pre_qualified_amount ? Number.toCurrency(data.pre_qualified_amount) : null,
            },
            status: {
                label: 'Status',
                value: data => this.formatStatus(data.status),
            },
            pricing_plan: {
                label: `0% APR Terms Offered ${this.buildTooltip('At prequal creation date, the current 0% APR Terms that were offered')}`,
                value: data => PRICING_PLANS[data.pricing_plan]["short_name"] || null,
                orderable: false,
            },
            created_at: {
                label: 'Sent',
                value: data => this.formatDate(data.created_at),
            },
            initiated_at: {
                label: `Initiated At ${this.buildTooltip('Customer has input information but has not completed the pre-qualification application')}`,
                value: data => this.formatDate(data.initiated_at),
            },
            prequalified_at: {
                label: `Prequalified At ${this.buildTooltip('Customer is pre-qualified for financing')}`,
                value: data => this.formatDate(data.prequalified_at),
            },
            declined_at: {
                label: `Declined At ${this.buildTooltip('Customer completed the pre-qualification application and was declined')}`,
                value: data => this.formatDate(data.declined_at),
            },
            expiration_date: {
                label: 'Expiration',
                value: data => this.formatDate(data.expiration_date),
            }
        });
    };

    /**
     * Format the status with corresponding HTML
     * @param {string} status - The status to format
     */
    formatStatus(status) {
        if (!status || status === 'PENDING') return null;
        let details = STATUS_DETAILS[status];
        return `<span class="h-text ${details.class}">${details.label}</span>`;
    };

    /**
     * Format the date
     * @param {string} date - The date to format
     * @returns {string} - The formatted date string
     */
    formatDate(date) {
        return date ? moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY') : null;
    };

    /**
     * Set the row actions configuration
     */
    setRowActions() {
        this.state.table_component.setRowActions({
            details: {
                label: 'View Project',
                link: {
                    href: data => window.financing_data.project_management_url.replace('{project_id}', data.project_id),
                    target: '_blank',
                },
            },
        });
    };

    setButtons() {
        this.state.table_component.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table_component.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/financing-prequalifications' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                }
            },
        });
    }

    /**
     * Set the AJAX request for fetching table data
     */
    setAjaxRequest() {
        this.state.table_component.setAjax(Api.Resources.WisetackPrequals, (request) => {
            request.accept('application/vnd.adg.fx.collection-v1+json');
            request.accept('application/vnd.adg.fx.report-financing-prequals-v1+json')
        });
    };
}
