'use strict';

const EventEmitter = require('events');

const {jsSelector, onClickWatcher} = require("@ca-package/dom");
import Page from '@ca-package/router/src/page';
const Tooltip = require('@ca-submodule/tooltip');

import {PrequalificationsPage} from "./manager-pages/prequalifications";
import {OpportunitiesPage} from "./manager-pages/opportunities";
import {TransactionPage} from "./manager-pages/transactions";

import manager_tpl from '@cam-financing-tpl/pages/main-pages/manager.hbs';

export class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            main: parent,
            layout: parent.state.layout,
            events: new EventEmitter,
            filter_period: null,
            tab_items: {
                prequalifications: {
                    title: 'Prequalifications',
                    icon: 'editor--list-check-3',
                    is_enabled: true,
                    route: 'manager.prequalifications',
                    tooltip: 'Includes any customer who has engaged with a pre-qualification link or has been sent a unique link via the software'
                },
                transactions: {
                    title: 'Loan Applications',
                    icon: 'document--file-check-line',
                    is_enabled: true,
                    route: 'manager.transactions',
                    tooltip: 'Includes any customer who has engaged with a loan application link or has been sent a unique link via the software'
                },
                opportunities: {
                    title: 'Opportunities',
                    icon: 'finance--hand-coin-line',
                    is_enabled: true,
                    route: 'manager.opportunities',
                    tooltip: 'Includes any customer who has has an open bid or accepted bid that has not been sent a financing application link or engaged with financing via the bid or costs page'
                }
            }
        });
    };

    /**
    * Get child routes
    *
    * @returns {object}
    */
    static get routes() {
        return {
            prequalifications: {
                default: true,
                path: '/prequals',
                page: PrequalificationsPage,
            },
            transactions: {
                path: '/transactions',
                page: TransactionPage,
            },
            opportunities: {
                path: '/opportunities',
                page: OpportunitiesPage,
            },
        };
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Set active tab menu
     *
     * @param {string} id
     * @param {string} full_route
     */
    setActiveTabMenu(id, full_route = null) {
        if (this.state.active_menu_tab === id) {
            return;
        }

        for (let key in this.state.tab_items) {
            this.state.tab_items[key].elem.removeClass('t-active');
        }

        this.state.tab_items[id].elem.addClass('t-active');
        this.state.active_menu_tab = id;

        if (full_route !== null) {
            this.router.navigate(full_route);
            return;
        }

        this.router.navigate(this.state.tab_items[id].route);
    };

    setActiveItemFromRoute(route_id, full_route) {
        for (let id of Object.keys(Manager.routes)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }

            this.setActiveTabMenu(id, full_route);
            break;
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        for (let item of this.elem.tab_item) {
            let $item = $(item);

            this.state.tab_items[$item.data('id')]['elem'] = $item;
        }

        let that = this;
        onClickWatcher(this.elem.tab_menu, jsSelector('tab-item'), function(a) {
            let id = $(this).data('id');
            that.setActiveTabMenu(id);
            return false;
        }, true);

        let route_id = this.router.current_route.name;
        this.setActiveItemFromRoute(route_id.replace('manager.', ''), route_id);

        this.state.main.on('period_filter_change', (data) => {
            this.state.filter_period = data;
            this.state.events.emit(this.state.active_menu_tab + '_period_filter_change', {
                ...data,
                type: this.state.active_menu_tab
            });
        });

        await super.load(request, next);
    };


    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        this.elem.tab_item = root.fxFind('tab-item');
        this.elem.tab_menu = root.fxFind('tab-menu');
        this.elem.container = root.fxFind('manager-table-content');
        super.boot(this.elem.container);

        Tooltip.initAll(root);
    };

    /**
     * Render page
     */
    render() {
        return manager_tpl({
            tab_items: this.state.tab_items,
        });
    };
}
