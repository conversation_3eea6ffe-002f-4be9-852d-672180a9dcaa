'use strict';

const EventEmitter = require('events');

import Page from '@ca-package/router/src/page';

import { Manager } from './main-pages/manager';
import main_tpl from '@cam-notification-tpl/pages/main.hbs';

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            events: new EventEmitter,
        });
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: Manager
            },
        };
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.pages = root.fxFind('pages');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl();
    };
}
