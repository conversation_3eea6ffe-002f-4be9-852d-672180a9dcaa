'use strict';

import Modal from '@ca-package/router/src/modal';

import DeleteTaskModal from '../../../modals/main/manager/delete';

import {createSuccessMessage} from '@cas-notification-toast-js/message/success';

export class Delete extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get and cache delete task modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = DeleteTaskModal;
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open delete task modal with promise
     *
     * @param {string} task_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal(task_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                task_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        this.openModal(request.params.task_id).then((result) => {
            if (result === null) { // no action was taken
                this.router.navigate('manager');
                return;
            }
            this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Task deleted successfully'));
            // redirect instead of navigate to remove the delete from the nav history so user can't hit back
            // and see the same modal which will no longer work
            this.router.navigate('manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}
