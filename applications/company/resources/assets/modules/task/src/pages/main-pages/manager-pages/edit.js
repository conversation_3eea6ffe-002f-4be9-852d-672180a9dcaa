'use strict';

import Modal from '@ca-package/router/src/modal';

import ManageTaskModal from '../../../modals/main/manager/manage_task';

export class Edit extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get and cache manage task modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = ManageTaskModal;
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open manage task modal with promise
     *
     * @param {string} task_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal(task_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                config: {
                    action: ManageTaskModal.Action.EDIT,
                    task_id
                },
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        this.openModal(request.params.task_id).then((result) => {
            this.router.navigate('manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}
