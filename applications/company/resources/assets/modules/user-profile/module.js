'use strict';

require('./resources/sass/main.scss');

const $ = require('jquery');
window.$ = window.jQuery = $;

require('@ca-package/dom/src/jquery_plugin');
require('@ca-submodule/validator');
require('@ca-submodule/validator/src/validators/password');
require('@ca-submodule/layout');

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

import 'remixicon/icons/Design/edit-2-line.svg'
import 'remixicon/icons/System/refresh-line.svg'
import 'remixicon/icons/System/checkbox-circle-line.svg'
import 'remixicon/icons/System/checkbox-circle-fill.svg'
import 'remixicon/icons/System/delete-bin-2-line.svg'
import 'remixicon/icons/System/upload-cloud-2-line.svg'
import 'remixicon/icons/System/information-line.svg'
import 'remixicon/icons/System/add-circle-line.svg'
import 'remixicon/icons/Arrows/arrow-down-s-line.svg'
import 'remixicon/icons/Arrows/arrow-right-s-line.svg'
import 'remixicon/icons/System/more-line.svg'
import 'remixicon/icons/User & Faces/account-circle-line.svg'
import 'remixicon/icons/System/lock-line.svg'
import 'remixicon/icons/Editor/link.svg'
import 'remixicon/icons/System/close-line.svg'
import 'remixicon/icons/User & Faces/user-line.svg'


const {layout} = require('@ca-submodule/layout');

const UserProfile = require('./src/index');
window.UserProfile = new UserProfile(layout);
