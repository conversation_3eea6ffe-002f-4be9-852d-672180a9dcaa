@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/button/text';
@use '~@cac-sass/app/form';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/grid';
@use '~@cas-form-input-sass/password';
@use '~@cas-form-input-sass/switch';
@use '~@cas-layout-sass/layout';
@use '~@cas-modal-sass/modal';
@use '~@cas-table-sass/table';
@use '~@cas-validator-sass/validator';
@use '~@cas-tooltip-sass/tooltip';

@use '~@uppy/core/dist/style' as uppy-core;
@use '~@uppy/dashboard/dist/style' as uppy-dashboard;

// User profile page styles
.m-user-profile {
    padding: base.unit-rem-calc(24px);
    width: 100%;
    height: 100%;
    @include base.respond-to('<small') {
        padding: 0;
    }
    .c-up-content {
        max-width: base.unit-rem-calc(1200px);
        @include base.full-width-height;
        margin: auto;
        border-radius: base.unit-rem-calc(12px);
        box-shadow: base.$elevation-level-2;
        background-color: base.$color-white-default;
        overflow: hidden;
        @include base.respond-to('<small') {
            border-radius: 0;
            box-shadow: none;
            padding: 0;
        }
    }
    .c-up-manager {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    // User profile headers
    .m-profile-header {
        .c-wrapper {}
            .c-w-info {
                height: 100%;
            }
                .c-wi-details {
                    display: flex;
                    align-items: center;
                    flex-direction: column;
                    padding: base.unit-rem-calc(24px) 0;
                    @include base.respond-to('<small') {
                        margin: 0;
                        padding: base.unit-rem-calc(16px) 0 0;
                    }
                    &.t-mobile {
                        .c-wid-wrapper {
                            display: none;
                        }
                    }
                    &.t-small {
                        padding: base.unit-rem-calc(16px) 0 base.unit-rem-calc(8px);
                    }
                }
                    .c-wid-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: base.unit-rem-calc(160px);
                        width: base.unit-rem-calc(160px);
                        border-radius: base.unit-rem-calc(160px);
                        margin-bottom: base.unit-rem-calc(16px);
                        overflow: hidden;
                        background-color: base.$color-white-default;
                        box-shadow: base.$elevation-level-3;
                        transition: all 0.3s ease-in-out;
                        .c-widw-edit {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            position: absolute;
                            border-radius: base.unit-rem-calc(160px);
                            background-color: rgba(0, 0, 0, 0.4);
                            backdrop-filter: blur(base.unit-rem-calc(2px));
                            opacity: 0;
                            color: base.$color-white-default;
                            @include base.typo-header($size: 16px);
                            width: base.unit-rem-calc(160px);
                            height: base.unit-rem-calc(160px);
                            transition: all 0.3s ease-out;
                        }
                        .c-widw-placeholder {
                            object-fit: cover;
                            padding: 20%;
                            fill: base.$color-grey-light-2;
                        }
                        @include base.respond-to('<small') {
                            width: base.unit-rem-calc(96px);
                            height: base.unit-rem-calc(96px);
                        }
                        > img {
                            object-fit: cover;
                            width: 100%;
                            height: 100%;
                            @include base.respond-to('<small') {
                                width: 100%;
                                height: 100%;
                            }
                        }
                        &:hover {
                            cursor: pointer;
                            .c-widw-edit {
                                display: flex;
                                opacity: 1;
                                &.t-hidden {
                                    opacity: 0;
                                    cursor: default;
                                }
                            }
                        }
                        &.t-has-image:hover {
                        }
                        @include base.respond-to('<small') {
                            float: right;
                        }
                        &.t-small {
                            transition: all 0.3s ease-in-out;
                            width: base.unit-rem-calc(96px);
                            height: base.unit-rem-calc(96px);
                            margin-bottom: base.unit-rem-calc(8px);
                            .c-widw-edit {
                                width: base.unit-rem-calc(96px);
                                height: base.unit-rem-calc(96px);
                            }
                        }
                        &.t-xsmall {
                            transition: all 0.3s ease-in-out;
                            width: base.unit-rem-calc(64px);
                            height: base.unit-rem-calc(64px);
                            .c-widw-edit {
                                width: base.unit-rem-calc(64px);
                                height: base.unit-rem-calc(64px);
                            }
                        }
                    }
                    .c-wid-content {
                        flex: 1;
                        @include base.respond-to('<small') {
                            padding-bottom: base.unit-rem-calc(8px);
                        }
                    }
                    .c-widc-name {
                        height: base.unit-rem-calc(32px);
                        text-align: center;
                        @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
                    }
        // User profile tab styles
        .c-w-menu-panel {
            display: flex;
            flex: 1;
            justify-content: center;
            @include base.respond-to('<small') {
                position: static;
                width: 100%;
            }
        }
        .c-menu {
            display: flex;
            flex: 1;
            justify-content: center;
            align-items: center;
            max-width: base.unit-rem-calc(448px);
            padding: base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(12px);
            background: base.$color-background-form;
            @include base.respond-to('<small') {
                border-width: base.unit-rem-calc(1px) 0;
                max-width: none;
                border-radius: 0;
                overflow: auto;
                justify-content: left;
            }
        }
            .c-menu-panel {
                display: flex;
                flex: 1;
                gap: base.unit-rem-calc(8px);
            }
                .c-m-tab {
                    cursor: pointer;
                    display: flex;
                    flex: 1;
                    align-items: center;
                    justify-content: center;
                    height: base.unit-rem-calc(40px);
                    gap: base.unit-rem-calc(8px);
                    border: base.unit-rem-calc(1px) solid transparent;
                    border-radius: base.unit-rem-calc(6px);
                    transition: all 0.2s ease-out;
                    padding: 0 base.unit-rem-calc(8px);
                    &:hover {
                        background: #EDF1F7;
                    }
                    &.active {
                        box-shadow: base.$elevation-level-2;
                        background: base.$color-white-default;
                        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                        @include base.respond-to('<small') {
                            padding: 0 base.unit-rem-calc(24px);
                        }
                        .c-mt-icon, .c-mt-title {
                            color: base.$color-grey-dark-2;
                        }
                    }
                    &.t-disabled {
                        cursor: not-allowed;
                        color: base.$color-grey-light-4;
                        .c-mt-icon, .c-mt-title {
                            cursor: not-allowed;
                            color: base.$color-grey-light-4;
                        }
                        &:hover {
                            border: base.unit-rem-calc(1px) transparent solid;
                            color: base.$color-grey-light-4;
                            background: none;
                            .c-mt-icon, .c-mt-title {
                                color: base.$color-grey-light-4;
                            }
                        }
                    }
                    .c-mt-title {
                        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
                        color: base.$color-grey-dark-1;
                    }
                    .c-mt-icon {
                        @include base.svg-icon('default-18');
                        color: base.$color-grey-dark-1;
                    }
                }
    }
    // Section page styles
    .m-sections {
        overflow: hidden;
        height: 100%;
    }
        .c-u-wrapper {
            height: 100%;
        }
        .c-up-section {
            height: 100%;
            &.t-hidden {
                display: none;
            }
        }
            .c-ups-loading {
                display: none;
                position: absolute;
                top: 0;
                left: 0;
                @include base.full-width-height;
                background: rgba(255, 255, 255, 0.5) url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
                z-index: 120;
                border-radius: base.unit-rem-calc(12px);
            }
    .c-title-wrap {
        display: flex;
        gap: base.unit-rem-calc(16px);
        justify-content: right;
        border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
        padding: base.unit-rem-calc(12px);
        .c-t-title {
            display: flex;
            gap: base.unit-rem-calc(8px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: base.unit-rem-calc(4px);
            .t-subtitle {
                flex: auto;
            }
            &.t-connect {
            }
        }
        .c-t-connect {
            flex: 0 0 auto;
        }
        .c-t-connection-status {
            display: flex;
            align-items: flex-end;
            gap: base.unit-rem-calc(8px);
            color: base.$color-green-default;
            font-size: base.unit-rem-calc(12px);
            font-style: italic;
            flex: 1;
            > svg {
                @include base.svg-icon('default-18');
            }
            > span {
                width: max-content;
            }
        }
        .c-t-buttons {
            display: flex;
            flex: 1;
            justify-content: right;
            gap: base.unit-rem-calc(16px);
        }
        .c-tb-sync {
            @include base.button-text-tertiary();
        }
        .c-tb-add {
            @include base.button-text-primary;
        }

    }
    .c-body-wrap {
        height: calc(100% - 57px);
        padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
        overflow: auto;
        @include base.respond-to('<small') {
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
        }
    }
}

// user profile details page
.m-details {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: base.unit-rem-calc(16px);
    @include base.respond-to('<small') {
        display: flex;
        flex-direction: column;
    }
    .f-f-label {
        @include base.form-input-label;
        .f-fl-optional {
            font-size: base.unit-rem-calc(12px);
            font-weight: 400;
            font-style: italic;
            line-height: base.unit-rem-calc(16px);
        }
    }
    .c-name {
        display: flex;
        flex: 1;
        gap: base.unit-rem-calc(16px);
        grid-area: 1 / 1 / 2 / 3;
        @include base.respond-to('<medium') {
            grid-area: 1 / 1 / 2 / 4;
        }
        .f-field {
            flex: 1;
        }
    }
    .c-email {
        grid-area: 1 / 3 / 2 / 5;
        @include base.respond-to('<medium') {
            grid-area: 1 / 4 / 2 / 7;
        }
    }
    .c-photo-button {
        position: relative;
        display: flex;
        gap: base.unit-rem-calc(16px);
        align-items: flex-end;
        grid-area: 1 / 5 / 2 / 7;
        @include base.respond-to('<medium') {
            grid-area: 2 / 1 / 3 / 7;
        }
    }
        .c-pb-button {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
            .c-pbb-file {
                width: 0px;
                height: 0px;
                overflow: hidden;
            }
            .c-pbb-upload {
                @include base.button-text-icon-primary;
            }
            .c-pbb-error {
                margin-bottom: base.unit-rem-calc(5px);
                color: base.$form-error-text-color;
                font-size: base.unit-rem-calc(14px);
                &:last-child {
                    margin-bottom: 0;
                }
            }
    .c-phone {
        display: flex;
        flex-direction: column;
        grid-area: 2 / 1 / 3 / 4;
        @include base.respond-to('<medium') {
            grid-area: 3 / 1 / 4 / 7;
        }
    }
        .c-p-container {
            border-radius: base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background: base.$color-background-edit;
        }
            .c-pc-header {
                display: grid;
                grid-template-columns: base.unit-rem-calc(32px) repeat(2, 1fr) base.unit-rem-calc(32px);
                grid-template-rows: base.unit-rem-calc(32px);
                gap: base.unit-rem-calc(8px);
                align-items: center;
                border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                margin-top: base.unit-rem-calc(8px);
                padding: 0 base.unit-rem-calc(8px);
                @include base.respond-to('<xsmall') {
                    display: none;
                }
            }
                .c-pch-icon {
                    display: flex;
                    justify-content: center;
                }
            .c-pc-body {
                display: flex;
                flex-direction: column;
                gap: base.unit-rem-calc(16px);
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                @include base.respond-to('<xsmall') {
                    gap: base.unit-rem-calc(8px);
                    padding: 0;
                }
            }
            .c-pc-footer {
                display: flex;
                justify-content: flex-end;
                padding: 0 base.unit-rem-calc(15px) base.unit-rem-calc(16px);
                @include base.respond-to('<xsmall') {
                    padding: base.unit-rem-calc(16px) base.unit-rem-calc(15px);
                    border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                }
            }
                .c-pcf-add {
                    @include base.button-text-icon-tertiary;
                }
    .c-bio {
        grid-area: 2 / 4 / 3 / 7;
        @include base.respond-to('<medium') {
            grid-area: 4 / 1 / 5 / 7;
        }
    }

    .c-line-item {
        display: grid;
        align-items: start;
        grid-template-columns: base.unit-rem-calc(32px) repeat(2, 1fr) base.unit-rem-calc(32px);
        grid-template-rows: auto;
        gap: base.unit-rem-calc(8px);
        @include base.respond-to('<xsmall') {
            grid-template-columns: repeat(1, 1fr);
            grid-template-rows: auto;
            column-gap: base.unit-rem-calc(16px);
            row-gap: base.unit-rem-calc(8px);
            padding-top: base.unit-rem-calc(8px);
            &:last-child {
                .c-li-delete {
                    border-bottom: none;
                }
            }
            .c-li-delete {
                display: flex;
                justify-content: center;
                order: 2;
                padding: base.unit-rem-calc(8px) 0;
                border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                .c-ld-button {
                    > [data-text] {
                        display: block;
                    }
                    @include base.respond-to('<xsmall'){
                        > [data-icon] {
                            display: none;
                        }
                    }
                }
            }
            .f-field {
                padding: 0 base.unit-rem-calc(8px);
                &:first-child {
                    justify-content: flex-start !important;
                    gap: base.unit-rem-calc(16px);
                    order: 1;
                }
                .f-f-label {
                    display: block !important;
                    .f-f-input {
                        width: base.unit-rem-calc(32px);
                    }
                }
            }
        }
        .f-field {
            &:first-child {
                gap: base.unit-rem-calc(8px);
                height: base.unit-rem-calc(32px);
                display: flex;
                justify-content: center;
            }
            &:nth-child(2) {
                padding-right: base.unit-rem-calc(8px);
            }
            .f-f-label {
                display: none;
            }
        }
    }
        .c-l-radio {
            display: flex;
            align-items: center;
            justify-content: center;
            width: base.unit-rem-calc(32px);
        }
        .c-ld-button {
            @include base.button-icon-text-tertiary($style: 'negate');
            > [data-text] {
                display: none;
            }
        }
}

// user profile edit page
.m-profile {
    display: flex;
    flex-direction: column;
    height: 100%;
    .t-primary-phone {
        font-size: base.unit-rem-calc(12px);
        font-style: italic;
    }
    .c-p-mobile-button {
        width: base.unit-rem-calc(48px);
        height: base.unit-rem-calc(32px);
        border-radius: base.unit-rem-calc(3px);
        display: none;
        align-items: center;
        justify-content: center;
        background-color: base.$color-primary-default;
        box-shadow: base.$elevation-primary-base;
        margin-right: base.unit-rem-calc(-16px);
        .c-pmb-arrow {
            @include base.svg-icon('default-24');
            color: base.$color-grey-light-2;
            display: none;
        }
        .c-pmb-more {
            @include base.svg-icon('default-18');
            color: base.$color-white-default;
            display: flex;
        }
        @include base.respond-to('<small') {
            &.t-edit-mode {
                display: flex;
            }
            &.t-hide {
                width: base.unit-rem-calc(32px);
                background-color: transparent;
                box-shadow: none;
                margin-right: 0;
                .c-pmb-arrow {
                    display: flex;
                }
                .c-pmb-more {
                    display: none;
                }
            }
        }
    }
    .c-p-edit {
        @include base.button-text-icon-tertiary;
        margin-right: base.unit-rem-calc(-8px);
        @include base.respond-to('<small') {
            margin-right: base.unit-rem-calc(-16px);
        }
    }
    .c-p-actions {
        display: flex;
        width: 0%;
        justify-content: end;
        overflow: hidden;
        gap: base.unit-rem-calc(16px);
        @include base.respond-to('<small') {
            transition: all 0.3s ease-out;
        }
        &.t-show {
            width: auto;
            padding: base.unit-rem-calc(16px) 0;
            margin: base.unit-rem-calc(-16px) 0;
            @include base.respond-to('<small') {
                flex: 1;
                width: 100%;
                opacity: 1;
                transition: all 0.3s ease-in-out;
            }
            .c-pa-save, .c-pa-cancel {
                opacity: 1;
            }
        }
        .c-pa-save {
            opacity: 0;
            @include base.button-text-primary;
        }
        .c-pa-cancel {
            opacity: 0;
            @include base.button-text-tertiary($style: 'negate-grey');
        }
    }
        .c-pb-page {
            &.t-hidden {
                display: none;
            }
        }
            .c-pbp-wrapper {
                display: flex;
                column-gap: base.unit-rem-calc(16px);
                row-gap: base.unit-rem-calc(32px);
                flex-wrap: wrap;
                background: base.$color-background-form;
                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                border-radius: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(16px);
                overflow: auto;
                .c-pbpw-details {
                    display: flex;
                    flex: 1;
                    flex-direction: column;
                    min-width: base.unit-rem-calc(256px);
                    &.t-bio {
                        min-width: 100%;
                    }
                    h6 {
                        line-height: base.unit-rem-calc(20px);
                    }
                    .c-pbpwd-text {
                        @include base.typo-paragraph;
                        color: base.$color-grey-dark-1;
                    }
                }
            }
    // current image
    .c-current-image {
        display: flex;
        gap: base.unit-rem-calc(16px);
    }
        .c-ci-image {
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            border-radius: base.unit-rem-calc(3px);
            border: base.unit-rem-calc(0.5px) base.$color-grey-light-4 solid;
            overflow: hidden;
            > img {
                object-fit: cover;
                width: 100%;
                height: 100%;
            }
        }
        .c-ci-delete {
            display: flex;
            align-items: center;
            justify-content: center;
            width: base.unit-rem-calc(24px);
            height: base.unit-rem-calc(24px);
            background-color: base.$color-red-default;
            border-radius: base.unit-rem-calc(24px);
            box-shadow: base.$elevation-level-3;
            position: absolute;
            top: base.unit-rem-calc(20px);
            left: base.unit-rem-calc(147px);
            > svg {
                color: base.$color-white-default;
                @include base.svg-icon('default-16');


            }
            &:hover {
                background-color: base.$color-red-dark-1;
                box-shadow: base.$elevation-level-4;
            }
        }

    .c-p-header {
        margin-top: base.unit-rem-calc(18px);
        margin-bottom: base.unit-rem-calc(16px)
    }

    .c-p-roles {
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px) base.unit-rem-calc(12px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        background: base.$color-background-form;
        > h4 {
            margin-bottom: base.unit-rem-calc(24px);
            .f-fl-optional {
                font-size: base.unit-rem-calc(12px);
                font-weight: 400;
                font-style: italic;
                line-height: base.unit-rem-calc(16px);
            }
        }
        > p {
            color: base.$color-grey-dark-1;
            @include base.typo-paragraph-small;
            font-style: italic;
        }
        .c-pr-settings {
            display: grid;

            @include base.respond-to('<small') {
                grid-template-columns: repeat(2, 1fr);
            }

            grid-template-columns: repeat(4, 1fr);
            gap: base.unit-rem-calc(32px);
            &.t-edit {
                grid-template-columns: repeat(4, 1fr);

                @include base.respond-to('<small') {
                    grid-template-columns: repeat(2, 1fr);
                }
            }
            .f-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                grid-column: span 2;
                @include base.respond-to('<800px') {
                    grid-column: span 3;
                }
                @include base.respond-to('<small') {
                    grid-column: span 6;
                }
                > .t-label {
                    @include base.typo-paragraph-medium;
                    padding: 0;
                    margin-right: base.unit-rem-calc(16px);
                }
                .m-tooltip-info {
                    padding-left: base.unit-rem-calc(4px);
                }
            }
        }
            .c-prs-content {
                display: flex;
                flex-direction: column;
                > *:not(:first-child) {
                    margin-top: base.unit-rem-calc(4px);
                }
            }
                .c-prsc-title {
                    @include base.typo-paragraph-medium;
                    width: max-content;
                    display: flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                    margin-bottom: 0;
                }
                .c-prsc-content {
                    @include base.typo-paragraph;
                    color: base.$color-grey-dark-1;

                    div {
                        display: flex;
                        gap: 0.5rem;
                        align-items: center;
                        margin-top: base.unit-rem-calc(4px);

                    }

                    .h-text {
                        padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                        margin-top: base.unit-rem-calc(4px);
                        margin-bottom: base.unit-rem-calc(4px);
                    }
                }
    }
}

// title and description styling
.t-subtitle {
    @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
    padding-left: base.unit-rem-calc(12px);
    margin: 0;
    flex: 1;
    @include base.respond-to('<small') {
        @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
        padding-left: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.t-description {
    font-size: base.unit-rem-calc(14px);
    margin-bottom: 0;
}
.t-section-title {
    font-weight: bold;
    font-size: base.unit-rem-calc(14px);
    margin-bottom: 0;
    line-height: base.unit-rem-calc(32px);
}

// user security details page
.m-security {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-pb-inputs {
        display: flex;
        flex-wrap: wrap;
        gap: base.unit-rem-calc(16px);
        .f-field {
            flex: 1;
            min-width: base.unit-rem-calc(192px);
        }
    }

    .c-s-mobile-button {
        width: base.unit-rem-calc(48px);
        height: base.unit-rem-calc(32px);
        border-radius: base.unit-rem-calc(3px);
        display: none;
        align-items: center;
        justify-content: center;
        background-color: base.$color-primary-default;
        box-shadow: base.$elevation-primary-base;
        .c-smb-arrow {
            @include base.svg-icon('default-24');
            color: base.$color-grey-light-2;
            display: none;
        }
        .c-smb-more {
            @include base.svg-icon('default-18');
            color: base.$color-white-default;
            display: flex;
        }
        @include base.respond-to('<small') {
            display: flex;
            margin-right: base.unit-rem-calc(-16px);
            &.t-hide {
                width: base.unit-rem-calc(32px);
                background-color: transparent;
                box-shadow: none;
                margin-right: 0;
                .c-smb-arrow {
                    display: flex;
                }
                .c-smb-more {
                    display: none;
                }
            }
        }
    }
    .c-s-actions {
        display: flex;
        justify-content: end;
        overflow: hidden;
        gap: base.unit-rem-calc(16px);
        @include base.respond-to('<small') {
            width: 0%;
            opacity: 0;
            transition: all 0.3s ease-out;
            &.t-show {
                flex: 1;
                width: 100%;
                opacity: 1;
            }
        }
    }
        .c-sa-save {
            @include base.button-text-primary;
        }
        .c-sa-reset {
            @include base.button-text-icon-tertiary;
        }
}

// user integrations page
.m-integrations {
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(32px);
    height: calc(100% - 57px);
    overflow: auto;
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    .c-google {}
        .c-g-header {
            display: flex;
            gap: base.unit-rem-calc(16px);
            justify-content: right;
            margin-bottom: base.unit-rem-calc(16px);
            .c-gh-title {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                flex: 1;
                overflow: hidden;
                > h4 {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                > svg {
                    color: base.$color-green-light-1;
                    padding: base.unit-rem-calc(1px);
                    min-width: base.unit-rem-calc(24px);
                    @include base.svg-icon('default-24');
                }
            }
            .c-gh-buttons {
                display: flex;
                flex: 1;
                justify-content: right;
                gap: base.unit-rem-calc(16px);
            }
            .c-ghb-sync {
                @include base.button-text-tertiary();
            }
            .c-ghb-add {
                @include base.button-text-primary;
            }
            .c-t-connect {
                flex: 0 0 auto;
            }



        }
        .c-g-description {
            display: flex;
        }
        .c-g-footer {
            margin-top: base.unit-rem-calc(16px);
            display: flex;
            flex-direction: column;
        }
        .c-gf-button {
            @include base.button-text-primary($style: 'negate');
            width: fit-content;
            > a {
                color: white;
            }
        }
        .c-gf-description {
            flex: 1;
            margin-top: base.unit-rem-calc(8px);
            span {
                color: base.$color-grey-light-1;
                @include base.typo-paragraph-small;
                font-style: italic;
            }
        }
    .c-sync {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
    }
        .c-s-content {
            .t-description {
                span {
                    color: base.$color-grey-light-1;
                    @include base.typo-paragraph-small;
                    font-style: italic;
                }
            }
            >h6 {
                line-height: base.unit-rem-calc(20px);
            }
        }
            .c-sc-group {
                display: flex;
                gap: base.unit-rem-calc(16px);
                margin-top: base.unit-rem-calc(16px);
            }
                .c-scg-input {
                    flex: 1 1 0;
                    input {
                        border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid !important;
                        border-radius: base.unit-rem-calc(3px) !important;
                        color: base.$color-grey-light-2;
                        cursor: not-allowed;
                    }
                }
                .c-scg-button {
                    flex: 0 0 auto;
                    button {
                        border-radius: base.unit-rem-calc(3px)
                    }
                }
}
