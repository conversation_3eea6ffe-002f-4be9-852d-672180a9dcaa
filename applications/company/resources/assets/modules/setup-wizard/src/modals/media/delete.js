/**
 * @module SetupWizard/Modals/Media/Delete
 */

'use strict';

import Api from '@ca-package/api';
import {Confirm} from '@ca-submodule/modal';

import modal_delete_tpl from '@cam-setup-wizard-tpl/modals/media/delete.hbs';

/**
 * @memberof module:SetupWizard/Modals/Media
 */
export class Delete extends Confirm {
    /**
     * Constructor
     */
    constructor(controller) {
        super();
        this.state.controller = controller;
        this.setTitle('Delete Media');
    };

    /**
     * Open modal
     *
     * @param {Object} data
     */
    open(data) {
        this.state.data = data;
        this.setContent(modal_delete_tpl());
        super.open();
    };

    /**
     * Handle yes which means the user wants to delete
     */
    handleYes() {
        this.state.controller.clearError();
        this.state.controller.startWorking();
        Api.Resources.Media().partialUpdate(this.state.data.id, {'status': 2})
            .then(() => {
                this.state.controller.table.deleteRow(this.state.data.id);
                this.state.controller.resetWorking();
                this.close();
            }, (error) => {
                if (error.code === 1014) {
                    error.message = 'Unable to delete customer';
                }
                this.state.controller.resetWorking();
                this.state.controller.setError(error.message);
            });
    };

    /**
     * Handle no which means the user wants to abort
     */
    handleNo() {
        this.close();
    };

    /**
     * Close modal
     */
    close() {
        this.state.data = null;
        super.close();
    };
}
