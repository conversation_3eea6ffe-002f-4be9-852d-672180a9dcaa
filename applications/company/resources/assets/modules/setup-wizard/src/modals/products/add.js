/**
 * @module SetupWizard/Modals/Products/Add
 */

'use strict';

const Api = require('@ca-package/api');
const Modal = require('@ca-submodule/modal').Base;
const FormValidator = require('@ca-submodule/validator');
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(NumberInput);
FormInput.use(require('@ca-submodule/form-input/src/nested_dropdown'));
const Tooltip = require('@ca-submodule/tooltip');

const modal_tpl = require('@cam-setup-wizard-tpl/modals/products/add.hbs');
const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

/**
 * @memberof module:SetupWizard/Modals/Products
 */
class Add extends Modal {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages.Main} module
     */
    constructor(module) {
        super(modal_tpl(), {
            size: Modal.Size.TINY,
            classes: ['t-add-product']
        });
        Object.assign(this.state, {
            module,
            external_close: false,
            units_loaded: false,
            product_categories: null
        });
        Tooltip.initAll(this.elem.content);
        this.setTitle('Quick Add: Product');
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of ['name', 'price', 'unit', 'categories']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 100
            },
            price: {
                required: true,
                pattern: '([0-9]+,?)+(.[0-9]+)?'
            },
            unit: {
                required: true
            },
            categories: {}
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        FormInput.init(this.state.validator.getInputElem('price'), {
            type: NumberInput.Type.CURRENCY,
            right_align: true,
            allow_minus: false
        });

        initSelectPlaceholder(this.state.validator.getInputElem('unit'));

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            };
            this.reset();
        });
    };

    /**
     * Load units into select input
     *
     * @returns {Promise<void>}
     */
    async loadUnits() {
        if (this.state.units_loaded === false) {
            let unit_elem = this.state.validator.getInputElem('unit');
            unit_elem.prop('disabled', true);

            let {entities: units} = await Api.Resources.Units()
                .filter('status', Api.Constants.Units.Status.ACTIVE)
                .sort('name', 'asc')
                .all();
            for (let unit of units) {
                let unit_option = $('<option/>')
                    .attr('value', unit.get('id', ''))
                    .text(unit.get('name', ''));
                unit_elem.append(unit_option);
            }
            unit_elem.prop('disabled', false);
            this.state.units_loaded = true;
        }
    };

    /**
     * Get categories from API in nested format
     *
     * @returns {Promise<object[]>}
     */
    async getCategories() {
        if (this.state.product_categories === null) {
            let {entities: categories} = await Api.Resources.ProductCategories()
                .sort('name', 'asc')
                .accept('application/vnd.adg.fx.list-v1+json')
                .all();
            this.state.product_categories = categories.map(category => category.data);
        }
        return this.state.product_categories;
    };

    /**
     * Load categories into select field
     *
     * @returns {Promise<void>}
     */
    async loadCategories() {
        let category_elem = this.state.validator.getInputElem('categories');
        category_elem.prop('disabled', true);

        this.state.category_select = FormInput.init(category_elem, {
            placeholder: '-- Select --',
            data_provider: this.getCategories.bind(this),
            option_handler: (data, option) => {
                option({
                    id: data.id,
                    text: data.name
                }, data.categories);
            },
            closeOnSelect: true
        });
        await this.state.category_select.promise;

        category_elem.prop('disabled', false);
    };

    /**
     * Open modal
     *
     * @param {Promise} $0.promise
     */
    open(promise) {
        this.state.promise = promise;
        this.loadUnits();
        this.loadCategories();
        super.open();
    };

    /**
     * Save changes
     */
    save() {
        this.clearMessages();
        this.startWorking();

        let category_ids = this.state.validator.getInputElem('categories').val();

        let data = {
            'name': this.state.validator.getInputElem('name').val(),
            'unit_id': this.state.validator.getInputElem('unit').val(),
            'is_intangible': false,
            'pricing_type': Api.Constants.ProductItems.PricingTypes.BASIC,
            'prices': [
                {
                    'min_count': '0',
                    'max_count': null,
                    'status': Api.Constants.ProductItemPrices.Status.ACTIVE,
                    'price': this.state.validator.getInputElem('price').val().replace(/,/g, '')
                }
            ]
        };
        if (category_ids.length > 0) {
            data['categories'] = category_ids;
        }
        Api.Resources.ProductItems().store(data).then(({data}) => {
            this.state.promise.resolve(data);
        }, (error, response) => {
            this.resetWorking();
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    console.log(item_errors);
                    break;
                default:
                    this.showErrorMessage(
                        'Unable to save product, please contact support'
                    );
                    break;
            }
        });
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.state.validator.reset();
        this.elem.form[0].reset();
        this.state.category_select.destroy();
        this.state.category_select = null;
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = Add;
