'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-setup-wizard-tpl/modals/google-calendar/calendar_remove.hbs');

const GoogleApiPath = `${window.fx_url.API}integration/google/calendar`;

class Remove extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Remove Calendar');
        this.setContent(content_tpl());
        this.elem.name = this.elem.content.fxFind('name');
    };

    /**
     * Get calendar info to fill into confirmation message
     *
     * @param {string} id - calendar uuid
     * @returns {Promise<*>}
     */
    async fetchCalendarInfo(id) {
        return await $.ajax({
            url: `${GoogleApiPath}/${id}`,
            dataType: "json",
            type: "GET",
            contentType: "application/json"
        });
    };

    /**
     * Open modal
     *
     * @param {string} calendar_id
     * @param promise
     * @returns {module:Modal.Base}
     */
    open({calendar_id, promise}) {
        this.elem.name.text('');
        this.state.calendar_id = calendar_id;
        this.startWorking();
        this.fetchCalendarInfo(this.state.calendar_id).then((info) => {
            this.elem.name.text(info.name);
            this.resetWorking();
        }, () => {
            this.showErrorMessage('Unable to fetch calendar info');
        });
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Remove calendar
     *
     * @param {string} id - calendar id
     * @returns {Promise<*>}
     */
    async removeCalendar(id) {
        return await $.ajax({
            url: `${GoogleApiPath}/${id}`,
            dataType: "json",
            type: "DELETE",
            contentType: "application/json"
        });
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        this.removeCalendar(this.state.calendar_id).then(() => {
            this.resetWorking();
            this.state.promise.resolve(true);
        }, () => {
            this.showErrorMessage('Unable to remove calendar');
            this.resetWorking();
        });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
        this.close();
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = Remove;
