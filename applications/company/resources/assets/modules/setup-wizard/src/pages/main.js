/**
 * @module SetupWizard/Pages/MainPages
 */

'use strict';

import {findChild, jsSelector} from '@ca-package/dom';
import Page from '@ca-package/router/src/page';

import {Instruction} from './main-pages/instruction';
import {Step} from './main-pages/step';
import {Review} from './main-pages/review';
import {Complete} from './main-pages/complete';

import main_tpl from '@cam-setup-wizard-tpl/pages/main.hbs';

export const SetupWizardSteps = {
    INSTRUCTION: 0,
    GENERAL: 1,
    USERS: 2,
    BID_CUSTOMIZATION: 3,
    EMAILS: 4,
    TERMS_CONDITIONS: 5,
    PRODUCTS: 6,
    MEDIA: 7,
    WARRANTY_PACKET: 8,
    QUICKBOOKS_ONLINE: 9,
    GOOGLE_CALENDAR: 10,
    ADDITIONAL_SERVICES: 11,
    REVIEW: 12,
    COMPLETE: 13
};

/**
 * @memberof module:SetupWizard/Pages
 */
export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            buttons: {},
            title: {},
            setup_wizard_data: null
        });
    };

    /**
     * Get steps in setup wizard
     *
     * @returns {object}
     */
    get steps() {
        return SetupWizardSteps;
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            instruction: {
                path: '/introduction',
                page: Instruction
            },
            step: {
                path: '/step',
                page: Step
            },
            review: {
                path: '/review',
                page: Review
            },
            complete: {
                path: '/complete',
                page: Complete
            }
        };
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Start doing work, show loader image
     */
    startWorking() {
        this.elem.loader.show();
    };

    /**
     * Stop doing work, hide loader image
     */
    resetWorking() {
        this.elem.loader.hide();
    };

    /**
     * Adds borders for instruction and review pages
     *
     * @param {boolean} boolean
     */
    toggleBorders(boolean) {
        if (boolean) {
            this.elem.pages.addClass('t-borders');
            return;
        }
        this.elem.pages.removeClass('t-borders');
    };

    /**
     * Reset buttons state between page load
     */
    resetButtons() {
        for (let button in this.state.buttons) {
            let this_button = this.state.buttons[button].elem;
            this_button.addClass('t-hidden');
            this_button.attr('data-navigate', '');
        }
    }

    /**
     * Set navigation state for instruction page
     *
     * @param {object} config
     */
    setInstructionNavState(config) {
        this.resetButtons();

        let continue_btn = this.state.buttons.continue.elem;
        continue_btn.removeClass('t-hidden');
        continue_btn.attr('data-navigate', config.continue);
    };

    /**
     * Set navigation state for step pages
     *
     * @param {object} config
     */
    setStepNavState(config) {
        this.resetButtons();

        let back_btn = this.state.buttons.back.elem,
            next_btn = this.state.buttons.next.elem,
            skip_btn = this.state.buttons.skip.elem;
        back_btn.removeClass('t-hidden');
        back_btn.attr('data-navigate', config.back);
        next_btn.removeClass('t-hidden');
        next_btn.attr('data-navigate', config.next);

        if (config.skip !== null) {
            skip_btn.removeClass('t-hidden');
            skip_btn.attr('data-navigate', config.skip);
            next_btn.addClass('t-hidden');
            next_btn.attr('data-navigate', '');
        }
    };

    /**
     * Change skip to next in step pages once action has been taken
     *
     * @param {object} config
     */
    setSkipToNext(config) {
        let next_btn = this.state.buttons.next.elem,
            skip_btn = this.state.buttons.skip.elem;

        skip_btn.addClass('t-hidden');
        skip_btn.attr('data-navigate', '');
        next_btn.removeClass('t-hidden');
        next_btn.attr('data-navigate', config.next);
    };

    /**
     * Reset skip back to next in step pages
     *
     * @param {object} config
     */
    setNextToSkip(config) {
        let next_btn = this.state.buttons.next.elem,
            skip_btn = this.state.buttons.skip.elem;

        next_btn.addClass('t-hidden');
        next_btn.attr('data-navigate', '');
        skip_btn.removeClass('t-hidden');
        skip_btn.attr('data-navigate', config.skip);
    };

    /**
     * Set navigation state for review page
     *
     * @param {object} config
     */
    setReviewNavState(config) {
        this.resetButtons();

        let back_btn = this.state.buttons.back.elem,
            complete_btn = this.state.buttons.complete.elem;
        back_btn.removeClass('t-hidden');
        back_btn.attr('data-navigate', config.back);
        complete_btn.removeClass('t-hidden');
        complete_btn.attr('data-navigate', config.complete);
    };

    /**
     * Set navigation state for complete page
     */
    setCompleteNavState() {
        this.elem.header.hide();
        this.resetButtons();
        this.elem.pages.addClass('t-no-border');
    };

    /**
     * Get necessary data for setup wizard
     *
     * @returns {Promise}
     */
    async fetchSetupWizardData() {
        if (this.state.setup_wizard_data === null) {
            try {
                let data = await $.ajax({
                    url: window.fx_url.API + 'setup-wizard/all',
                    dataType: 'json',
                    type: 'GET',
                    contentType: 'application/json'
                });
                this.state.setup_wizard_data = data['setup'];
                delete data['setup'];
                this.state.setup_wizard_data['company'] = data;
            } catch (error) {
                alert('Unable to get setup wizard data, please contact support');
            }
        }
        return this.state.setup_wizard_data;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        request.data = await this.fetchSetupWizardData();
        // if one of the step children routes is passed in we allow them to go directly there
        let override_routes = [
            'step.google_calendar.add', 'step.google_calendar.remove', 'step.products.add'
        ];
        if (override_routes.includes(this.router.current_route.name)) {
            this.router.redirect(this.router.current_route.name);
            return;
        }
        // otherwise the route is set based on their current saved step in the company record
        let status_map = new Map([
            [SetupWizardSteps.INSTRUCTION, 'instruction'],
            [SetupWizardSteps.GENERAL, 'step.general'],
            [SetupWizardSteps.USERS, 'step.users'],
            [SetupWizardSteps.BID_CUSTOMIZATION, 'step.bid_customization'],
            [SetupWizardSteps.EMAILS, 'step.emails'],
            [SetupWizardSteps.TERMS_CONDITIONS, 'step.terms_conditions'],
            [SetupWizardSteps.PRODUCTS, 'step.products'],
            [SetupWizardSteps.MEDIA, 'step.media'],
            [SetupWizardSteps.WARRANTY_PACKET, 'step.warranty_packet'],
            [SetupWizardSteps.QUICKBOOKS_ONLINE, 'step.quickbooks_online'],
            [SetupWizardSteps.GOOGLE_CALENDAR, 'step.google_calendar'],
            [SetupWizardSteps.ADDITIONAL_SERVICES, 'step.additional_services'],
            [SetupWizardSteps.REVIEW, 'review'],
            [SetupWizardSteps.COMPLETE, 'complete']
        ]);
        let route = status_map.get(window.setup_wizard_data.current_step);
        if (route === undefined) {
            window.location.href = window.fx_pages.HOME;
            return;
        }
        this.router.redirect(route);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    async boot(root) {
        super.boot(root);
        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.header = findChild(root, jsSelector('header'));
        this.elem.pages_wrapper = findChild(root, jsSelector('pages-wrapper'));
        this.elem.pages = findChild(root, jsSelector('pages'));
        let buttons = findChild(root, jsSelector('button'));
        for (let button of buttons) {
            let $button = $(button);
            this.state.buttons[$button.data('value')] = {
                elem: $button
            };
        }
        this.fetchSetupWizardData();
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl();
    };
}
