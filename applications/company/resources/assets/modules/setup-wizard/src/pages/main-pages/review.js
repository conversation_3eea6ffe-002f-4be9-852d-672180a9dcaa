'use strict';

import Page from '@ca-package/router/src/page';
import {find<PERSON>hil<PERSON>, jsSelector, onClick<PERSON><PERSON><PERSON>, onClickWatcher, onEvent} from "@ca-package/dom";

import completed_all_tpl from '@cam-setup-wizard-tpl/pages/main-pages/review/completed_all.hbs';
import item_tpl from '@cam-setup-wizard-tpl/pages/main-pages/review/item.hbs';
import review_tpl from '@cam-setup-wizard-tpl/pages/main-pages/review.hbs';
import skipped_steps_tpl from '@cam-setup-wizard-tpl/pages/main-pages/review/skipped_steps.hbs';

/**
 * @memberof module:SetupWizard/Pages/MainPages
 */
export class Review extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            previous: {
                route: 'step.additional_services',
                step: parent.steps.ADDITIONAL_SERVICES
            },
            next: {
                route: 'complete',
                step: parent.steps.COMPLETE
            },
            steps: {
                general: {
                    label: 'General Info',
                    step: 1
                },
                users: {
                    label: 'Users',
                    step: 2
                },
                bid_customization: {
                    label: 'Bid Intro',
                    step: 3
                },
                emails: {
                    label: 'Emails',
                    step: 4
                },
                terms_conditions: {
                    label: 'Terms & Conditions',
                    step: 5
                },
                products: {
                    label: 'Products',
                    step: 6
                },
                media: {
                    label: 'Media',
                    step: 7
                },
                warranty_packet: {
                    label: 'Warranties',
                    step: 8
                },
                quickbooks_online: {
                    label: 'QuickBooks Online',
                    step: 9
                },
                google_calendar: {
                    label: 'Google Calendar',
                    step: 10
                },
                additional_services: {
                    label: 'Additional Services',
                    step: 11
                }
            }
        });
    };

    /**
     * Loop through data to show user what they skipped
     *
     * @param {object} data
     */
    setReviewData(data) {
        this.state.parent.startWorking();

        // find skipped steps from request data
        let skipped_steps = [];
        for (let step in this.state.steps) {
            if (!data[step]) {
                skipped_steps.push(step);
            }
        }

        // loop through skipped steps to add to template
        if (skipped_steps.length > 0) {
            this.elem.description.text('It looks like you\'re almost done with Setup Wizard. Once complete, any changes to your company\'s Settings, Products, Users, etc., can be made in the Company Profile.');
            this.elem.skipped_items = $(skipped_steps_tpl({
                skipped_multiple: skipped_steps.length > 1 ?? true
            }));
            let skipped_items_wrapper = findChild(this.elem.skipped_items, jsSelector('items'));
            for (let item of skipped_steps) {
                let this_state = this.state.steps[item];
                skipped_items_wrapper.append(item_tpl({
                    link: `step.${item}`,
                    step: this_state.step,
                    label: this_state.label,
                }));
            }
            this.elem.content.append(this.elem.skipped_items);
        } else {
            this.elem.description.append(completed_all_tpl());

            this.elem.complete_link = findChild(this.elem.root, jsSelector('complete-link'));
            onEvent(this.elem.complete_link, 'click', (e) => {
                e.preventDefault();
                this.complete();
                return false;
            });
        }
        this.state.parent.resetWorking();
    };

    /**
     * Save back step with company record
     *
     * @returns {Promise}
     */
    async saveCompanyStep() {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: this.state.previous.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(this.state.previous.route);
        } catch (error) {
            this.state.parent.resetWorking();
            alert('Unable to go back to previous step, please try again');
        }
    };

    /**
     * Complete setup wizard for company
     *
     * @returns {Promise}
     */
    async complete() {
        this.state.parent.startWorking();
        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/complete`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json'
            });
            this.state.parent.resetWorking();
            this.router.navigate(this.state.next.route);
        } catch (error) {
            this.state.parent.resetWorking();
            alert('Unable to complete setup wizard, please try again');
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.parent.setReviewNavState({
            back: this.state.previous.route,
            complete: this.state.next.route,
        });
        this.state.parent.toggleBorders(true);
        this.setReviewData(request.data);

        let that = this;
        onClickWatcher(this.state.parent.elem.header, jsSelector('button'), function () {
            let $this = $(this);
            if ($this.data('value') === 'complete') {
                that.complete();
                return false;
            }
            if ($this.data('value') === 'back') {
                that.saveCompanyStep();
                return false;
            }
        }, false);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.parent.toggleBorders(false);
        onClickDestroy(this.state.parent.elem.header);
        if (this.elem.skipped_items !== undefined) {
            this.elem.skipped_items.empty();
        }
        this.elem.description.empty();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.description = findChild(root, jsSelector('description'));
        this.elem.content = findChild(root, jsSelector('content'));
    };

    /**
     * Render page
     */
    render() {
        return review_tpl();
    };
}
