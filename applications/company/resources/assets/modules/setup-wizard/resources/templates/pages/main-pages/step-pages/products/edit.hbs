<form class="m-form" data-js="form">
    {{#if callout}}
        <div class="c-f-instructions">{{callout}}</div>
    {{/if}}
    <div class="c-f-row">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Name</label>
            <input class="f-f-input" {{#if disable_save}} disabled{{/if}} required type="text" value="{{name}}" data-js="name"
                   maxlength="100"/>
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Price</label>
            <input class="f-f-input" {{#if disable_save}} disabled{{/if}} required type="text" value="{{price}}" data-js="price"
                   data-fx-form-input="number"
                   data-parsley-trigger="keyup"
                   data-parsley-pattern="^([0-9]+,?)+(.[0-9]+)?$"
                   data-parsley-pattern-message="Number must be formatted as a price." />
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Unit</label>
            <select class="f-f-input" {{#if disable_save}} disabled{{/if}} required data-js="unit" placeholder="-- Select One --">
                {{#each units}}
                    <option value="{{id}}" {{#if selected}}selected{{/if}}>{{name}}</option>
                {{/each}}
            </select>
        </div>
    </div>
    <div class="c-f-row">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Categories</label>
            {{#each categories}}
                <span class="c-frf-tag">{{this}}</span>
            {{else}}
                <p class="c-frf-none">No categories assigned</p>
            {{/each}}
        </div>
    </div>
</form>
