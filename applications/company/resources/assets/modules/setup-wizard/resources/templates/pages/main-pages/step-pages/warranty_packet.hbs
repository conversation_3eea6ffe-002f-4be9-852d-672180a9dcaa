<div class="c-sbcs-step">
    <div class="c-sbcss-header" data-js="header">
        <div class="c-sbcssh-error" data-js="error"></div>
        <h4 class="c-sbcssh-title">Edit Warranty Cover Letter</h4>
        <button class="c-sbcssh-button t-save" data-js="save-changes">Save Changes</button>
        <button class="c-sbcssh-button t-saving" disabled data-js="saving">Saving...</button>
        <button class="c-sbcssh-button t-saved" data-js="saved">Saved</button>
        <p class="c-sbcssh-instruction">
            Warranty packets can be sent via email from the software. The packet includes a cover letter and any
            appropriate warranty certificates. <strong>Please review and edit the cover letter below</strong> and use
            the tools to add personalization or update formatting. Click the eyeball icon to preview your changes using
            sample tag data.
            <a href="https://cxlratr.to/hc-warranty" target="_blank">Learn More</a>
        </p>
    </div>
    <form class="m-form t-warranty" data-js="form">
        <div class="c-f-row t-section">
            <div class="c-fr-field t-full f-field">
                <textarea class="f-f-input" style="display:none;" data-fx-form-input="wysiwyg" required data-js="cover_letter"></textarea>
            </div>
        </div>
    </form>
</div>
