'use strict';

import './resources/sass/main.scss';

const $ = require('jquery');
window.$ = window.jQuery = $;

require('@ca-package/dom/src/jquery_plugin');

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

import 'remixicon/icons/Business/customer-service-line.svg';
import 'remixicon/icons/Business/send-plane-2-line.svg';
import 'remixicon/icons/Business/mail-send-line.svg';
import 'remixicon/icons/Design/edit-2-line.svg';
import 'remixicon/icons/Document/file-list-line.svg';
import 'remixicon/icons/System/add-line.svg';
import 'remixicon/icons/Arrows/arrow-down-s-line.svg';
import 'remixicon/icons/Arrows/arrow-go-back-line.svg';
import 'remixicon/icons/Arrows/arrow-down-s-line.svg';
import 'remixicon/icons/Arrows/arrow-left-line.svg';
import 'remixicon/icons/Arrows/arrow-right-line.svg';
import 'remixicon/icons/System/check-line.svg';
import 'remixicon/icons/System/checkbox-circle-line.svg';
import 'remixicon/icons/System/checkbox-circle-fill.svg';
import 'remixicon/icons/System/download-cloud-2-line.svg';
import 'remixicon/icons/System/eye-line.svg';
import 'remixicon/icons/System/information-line.svg';
import 'remixicon/icons/System/lock-password-line.svg';
import 'remixicon/icons/System/upload-cloud-2-line.svg';
import 'remixicon/icons/System/search-line.svg';
import 'remixicon/icons/User & Faces/team-line.svg';

import {layout} from '@ca-submodule/layout';
import {Controller} from './src/index';

window.SetupWizard = new Controller(layout);
