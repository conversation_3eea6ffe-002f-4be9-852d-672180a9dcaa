'use strict';

const Page = require('@ca-package/router/src/page');

const Api = require('../../../api');
const LegacyHistory = require('./legacy/history');

const history_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/history.hbs');

class History extends Page {
    /**
     * Fetch data for page
     *
     * @returns {object}
     */
    async fetchData() {
        let {data: entity} = await Api.Resources.Customers().fields(['email', 'is_unsubscribed'])
            .retrieve(this.state.customer_id);
        return entity.data;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();

        await super.load(request, next);
        this.state.customer_id = request.data.customer_id;
        let data = await this.fetchData();

        LegacyHistory.projectHistory({
            id: request.params.project_id,
            customer: data
        });
        this.elem.loader.hide();
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        LegacyHistory.setup({
            loader: this.parent.elem.loader
        });
        e$(this.elem.root[0]).find('[data-reveal]').foundation();
    }

    /**
     * Render page
     */
    render() {
        return history_tpl();
    };
}

module.exports = History;
