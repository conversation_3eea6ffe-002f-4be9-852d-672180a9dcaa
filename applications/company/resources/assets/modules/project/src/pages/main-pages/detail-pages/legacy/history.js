(function($){
    var state = {};
    var elem = {};
    module.exports = {};

    function setup(config) {
        Object.assign(elem, config);
    }
    module.exports.setup = setup;

    function projectHistory(config) {
        Object.assign(state, config);

        $('#projectHistory').empty();

        $.ajax({
            url: window.fx_url.BASE + 'getProjectHistory.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectID: state.id
            },
            success: (response) => {
                if (!response == '') {
                    $.each(response, function (i, item) {
                        var nameDisplay = '';
                        if (this.firstName != null && this.lastName != null){
                            nameDisplay = ' by ' + this.firstName + ' ' + this.lastName;
                        }

                        if (this.historyType == 'project') {
                            $('#projectHistory').append('<div class="callout primary history"><p>' + this.projectType + '<br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p></div>');
                        } else if (this.historyType == 'evaluation') {
                            if (this.evalType == 'Bid First Sent') {
                                var resendBidEmailButton = '';
                                if (this.importedDate == null) {
                                    resendBidEmailButton = '<button name="resendBidEmail" class="button resendBidEmail" style="float:right;" href="111">Resend Email</button>';
                                }
                                $('#projectHistory').append('<div class="callout primary history" evaluationId="' + this.id + '"><p>' + this.description + ' - ' + this.evalType + '<br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p><p style="height: 2.3rem;">'+resendBidEmailButton+'</p></div>');
                                //$('.history#History' + this.id + ' .resendBidEmail').click(resendBidEmail);

                            } else if (this.evalType == 'Bid Accepted') {
                                var bidAcceptEmailButton = '';
                                if (this.importedDate == null) {
                                    bidAcceptEmailButton = '<button name="resendBidAcceptedEmail" class="button resendBidAcceptedEmail" style="float:right;" href="111">Resend Email</button>';
                                }
                                $('#projectHistory').append('<div class="callout primary history" bidId="' + this.bidID + '"><p>' + this.description + ' - ' + this.evalType + '<br /><span style="font-size:.7rem;">' + this.date + ' by Customer</span></p><p style="height: 2.3rem;">'+bidAcceptEmailButton+'</p></div>');
                                //$('.history#History' + this.bidID + ' .resendBidAcceptedEmail').click(resendBidAcceptedEmail);

                            } else if (this.evalType == 'Bid Rejected') {
                                var bidRejectEmailButton = '';
                                if (this.importedDate == null) {
                                    bidRejectEmailButton = '<button name="resendBidRejectedEmail" class="button resendBidRejectedEmail" style="float:right;" href="111">Resend Email</button>';
                                }
                                $('#projectHistory').append('<div class="callout primary history" bidId="' + this.bidID + '"><p>' + this.description + ' - ' + this.evalType + '<br /><span style="font-size:.7rem;">' + this.date + ' by Customer</span></p><p style="height: 2.3rem;">'+bidRejectEmailButton+'</p></div>');
                                //$('.history#History' + this.bidID + ' .resendBidRejectedEmail').click(resendBidRejectedEmail);

                            } else if (this.evalType == 'Warranties Sent') {
                                var finalReportEmailButton = '';
                                if (this.importedDate == null) {
                                    finalReportEmailButton = '<button name="resendFinalReportEmail" class="button resendFinalReportEmail" style="float:right;" href="111">Resend Email</button>';
                                }
                                $('#projectHistory').append('<div class="callout primary history" evaluationId="' + this.id + '"><p>' + this.description + ' - ' + this.evalType + '<br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p><p style="height: 2.3rem;">'+finalReportEmailButton+'</p></div>');
                                //$('.history#History' + this.id + ' .resendFinalReportEmail').click(resendFinalReportEmail);

                            } else if (this.evalType == 'Invoice Last Sent') {
                                $('#projectHistory').append('<div class="callout primary history" evaluationId="' + this.id + '"><p>' + this.description + ' - ' + this.invoiceName + ' Invoice<br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p><p style="height: 2.3rem;"><a target="_blank" class="button" style="float:right;" href="invoice.php?custom=' + this.custom + '&eid=' + this.id + '&invoice=' + this.invoiceType + '&email=send">Resend Invoice</a></p></div>');

                            } else {
                                $('#projectHistory').append('<div class="callout primary history"><p>' + this.description + ' - ' + this.evalType + '<br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p></div>');
                            }

                        } else if (this.historyType == 'schedule') {
                            if (this.type == 'Evaluation') {
                                $('#projectHistory').append('<div class="callout primary history" evaluationId="' + this.id + '"><p>' + this.type + ' Scheduled<br /><span>Start: ' + this.startDate + '<br/>End: ' + this.endDate + '</span><br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p><p style="height: 2.3rem;"><button name="resendEvaluationEmail" class="button resendEvaluationEmail" style="float:right;" href="111">Resend Notifications</button></p></div>');
                                //$('.history#History' + this.id + ' .button.resendEvaluationEmail').click(resendEvaluationEmail);

                            } else if (this.type == 'Installation') {
                                $('#projectHistory').append('<div class="callout primary history" evaluationId="' + this.id + '"><p>' + this.type + ' Scheduled<br /><span>Start: ' + this.startDate + '<br/>End: ' + this.endDate + '</span><br /><span style="font-size:.7rem;">' + this.date + nameDisplay + '</span></p><p style="height: 2.3rem;"><button name="resendInstallationEmail" class="button resendInstallationEmail" style="float:right;" href="111">Resend Notifications</button></p></div>');
                                //$('.history#History' + this.id + ' .button.resendInstallationEmail').click(resendInstallationEmail);

                            }
                        }
                    });
                    $('.history .resendBidEmail').click(resendBidEmail);
                    $('.history .resendBidAcceptedEmail').click(resendBidAcceptedEmail);
                    $('.history .resendBidRejectedEmail').click(resendBidRejectedEmail);
                    $('.history .resendFinalReportEmail').click(resendFinalReportEmail);
                    $('.history .button.resendEvaluationEmail').click(resendEvaluationEmail);
                    $('.history .button.resendInstallationEmail').click(resendInstallationEmail);

                    //Disable Resend Buttons
                    if (state.customer.email === null || state.customer.is_unsubscribed === true) {
                        $('.history .resendBidEmail, .history .resendBidAcceptedEmail, .history .resendBidRejectedEmail, .history .resendFinalReportEmail, .history .resendEvaluationEmail, .history .resendInstallationEmail').addClass('disabled');
                    }
                }
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    }
    module.exports.projectHistory = projectHistory;

    //Resend Customer Bid Email from History Tab
    var resendBidEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'resend-bid.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    idToResend: idToResend
                },
                success: function (response) {
                    elem.loader.hide();
                    if (response.success) {
                        $('#emailSentModal h3').text('Bid Email Resent');
                        $('#emailSentModal p.modalContent').html('Your bid has been resent.');
                        $('#emailSentModal').foundation('open');
                    }
                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to resend bid, please contact support');
                    elem.loader.hide();
                }
            });
        }
    };

    //Resend Bid Accept Email from History Tab
    var resendBidAcceptedEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('bidId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'bid-accept-email.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    id: idToResend,
                    email: 'send',
                    resend: 'resend'
                },
                success: function (response) {
                    if (response == 'true') {
                        elem.loader.hide();
                        $('#emailSentModal h3').text('Bid Accept Email Resent');
                        $('#emailSentModal p.modalContent').html('The bid accept email has been resent.');
                        $('#emailSentModal').foundation('open');
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    //Resend Bid Reject Email from History Tab
    var resendBidRejectedEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('bidId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'bid-reject-email.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    id: idToResend,
                    resend: 'resend'
                },
                success: function (response) {
                    if (response == 'true') {
                        elem.loader.hide();
                        $('#emailSentModal h3').text('Bid Reject Email Resent');
                        $('#emailSentModal p.modalContent').html('The bid reject email has been resent.');
                        $('#emailSentModal').foundation('open');
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    //Resend Evaluation Email from History Tab
    var resendEvaluationEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.API + 'project/event/resend-notifications',
                dataType: "json",
                type: "PUT",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    idToResend: idToResend
                },
                success: function (response) {
                    if (response.success) {
                        elem.loader.hide();
                        $('#emailSentModal h3').text('Evaluation Email Resent');
                        $('#emailSentModal p.modalContent').html('The evaluation email has been resent.');
                        $('#emailSentModal').foundation('open');

                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    //Resend Installation Email from History Tab
    var resendInstallationEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.API + 'project/event/resend-notifications',
                dataType: "json",
                type: "PUT",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    idToResend: idToResend
                },
                success: function (response) {
                    if (response.success) {
                        elem.loader.hide();
                        $('#emailSentModal h3').text('Installation Email Resent');
                        $('#emailSentModal p.modalContent').html('The installation email has been resent.');
                        $('#emailSentModal').foundation('open');
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    //Resend Final Report
    var resendFinalReportEmail = (e) => {
        var idToResend = $(e.target).parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'final-email.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    projectID: state.id,
                    evaluationID: idToResend,
                    email: 'resend'
                },
                success: function (response) {
                    if (response == 'true') {

                        elem.loader.hide();
                        $('#emailSentModal h3').text('Warranties Resent');
                        $('#emailSentModal p.modalContent').html('Your warranties have been resent.');
                        $('#emailSentModal').foundation('open');
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

})(e$);
