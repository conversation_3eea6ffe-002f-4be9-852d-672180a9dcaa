(function($){
    var state = {
        bid_acceptance: (project_data.invoice_split.bid_acceptance * 100),
        project_complete: (project_data.invoice_split.project_complete * 100)
    };
    var elem = {};
    module.exports = {};

    function setup(config) {
        Object.assign(elem, config);
        //Remove Evaluation Names from Copy Modal Dropdown
        $('#currentEvaluationNames option').not(':first').remove();

        //Open Custom Evaluation Modal
        $('#openCustomEvaluationModal').click(function () {
            var modal = $('#uploadEvaluation');
            modal.find('[name="evaluationDescription"]').val(state.project_name);
            modal.foundation('open');
        });

        //Cancel Add On Items
        $('#cancelScopeChange').click(function () {
            $('#scopeChangeModal .scopeChangeItemTable tbody tr').not(':first').remove();

            $('#scopeChangeModal').foundation('close');
        });

        //Save Add On Items
        $('#saveScopeChange').on('click', function(e) {
            $(e.currentTarget).prop('disabled', true);
            saveScopeChange();
        });

        //Save Edit Evaluation Name Modal
        $('#saveEvaluationName').click(saveEvaluationName);

        //Close Edit Evaluation Name Modal
        $('#closeEditEvaluationModal').click(closeEditEvaluationModal);

        $('#sendReport').click(sendFinalReport);

        //Remove Errors On Change for Evaluation Description
        $('input[name="evaluationDescription"]').change(function () {
            $('input[name="evaluationDescription"]').removeClass('is-invalid-input');
            $('input[name="evaluationDescription"]').parent().removeClass('is-invalid-label');
            $('input[name="evaluationDescription"]').parent().find('.form-error').removeClass('is-visible');
        });

        //Remove Errors On Change for Custom Evaluation Upload
        $('input[name="customEvaluation"]').change(function () {
            $('input[name="customEvaluation"]').removeClass('is-invalid-input');
            $('input[name="customEvaluation"]').parent().removeClass('is-invalid-label');
            $('input[name="customEvaluation"]').parent().find('.form-error').removeClass('is-visible');
        });

        //Remove Errors On Change for Document Description
        $('input[name="documentDescription"]').change(function () {
            $('input[name="documentDescription"]').removeClass('is-invalid-input');
            $('input[name="documentDescription"]').parent().removeClass('is-invalid-label');
            $('input[name="documentDescription"]').parent().find('.form-error').removeClass('is-visible');
        });

        //Remove Errors On Change for Document Description
        $('input[name="documentDescription"]').change(function () {
            $('input[name="documentDescription"]').removeClass('is-invalid-input');
            $('input[name="documentDescription"]').parent().removeClass('is-invalid-label');
            $('input[name="documentDescription"]').parent().find('.form-error').removeClass('is-visible');
        });

        //Remove Errors On Change for Document Upload
        $('input[name="document"]').change(function () {
            $('input[name="document"]').removeClass('is-invalid-input');
            $('input[name="document"]').parent().removeClass('is-invalid-label');
            $('input[name="document"]').parent().find('.form-error').removeClass('is-visible');
        });


        //Cancel Adding A Custom Evaluation Modal
        $('#cancelCustomEvaluation').click(function () {
            $('#uploadEvaluation input[name="evaluationDescription"]').removeClass('is-invalid-input');
            $('#uploadEvaluation input[name="evaluationDescription"]').parent().removeClass('is-invalid-label');
            $('#uploadEvaluation input[name="evaluationDescription"]').parent().find('.form-error').removeClass('is-visible');
            $('#uploadEvaluation input[name="customEvaluation"]').removeClass('is-invalid-input');
            $('#uploadEvaluation input[name="customEvaluation"]').parent().removeClass('is-invalid-label');
            $('#uploadEvaluation input[name="customEvaluation"]').parent().find('.form-error').removeClass('is-visible');

            $('#uploadEvaluation input[name="evaluationDescription"]').val('');
            $('#uploadEvaluation input[name="customEvaluation"]').val('');

            $('#uploadEvaluation').foundation('close');
        });

        //
        // Handle New Bid
        //
        var new_bid = $('#newBid');
        var bid_description = new_bid.find('input[name="bidDescription"]');

        //Open New Bid Modal
        $('#openNewBidModal').click(function () {
            bid_description.val(state.project_name);
            new_bid.foundation('open');
        });

        $('#cancelBid').click(function () {
            bid_description.removeClass('is-invalid-input');
            bid_description.parent().removeClass('is-invalid-label');
            bid_description.parent().find('.form-error').removeClass('is-visible');

            bid_description.val('');

            new_bid.foundation('close');
        });

        //Remove Errors On Change for Bid Description
        bid_description.change(function () {
            bid_description.removeClass('is-invalid-input');
            bid_description.parent().removeClass('is-invalid-label');
            bid_description.parent().find('.form-error').removeClass('is-visible');
        });

        // copied from addCustomEvaluation and cleaned up a tiny bit
        var loading_image = elem.loader;
        $('#addBid').click(function () {
            var projectID = state.id;
            var description = bid_description.val();
            if (description === '') {
                bid_description.addClass('is-invalid-input');
                bid_description.parent().addClass('is-invalid-label');
                bid_description.parent().find('.form-error').addClass('is-visible');
                return;
            }
            loading_image.show();
            $.ajax({
                url: window.fx_url.BASE + 'custom-evaluation-add.php',
                type: 'POST',
                data: {
                    projectID: projectID,
                    description: description,
                    _guided_bid: '1'
                },
                dataType: 'json',
                success: function (response) {
                    if (response.bid_item_id) {
                        window.location.href = fx_pages.BIDS_CREATE.replace('{id}', response.bid_item_id);
                    }
                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('An error occured while creating bid, please contact support.');
                    loading_image.hide();
                }
            });
        });

        //Click Cancel on Custom Eval Invoice Modal
        $("#cancelInvoice").click(function(){
            $('#invoiceModal .invoiceTable tbody tr').not(':first').remove();

            $('#invoiceModal #invoiceEvaluationId').text('');

            $('input[name="invoiceSplitBidAcceptance"]').val(state.bid_acceptance);
            $('input[name="bidAcceptanceTotal"]').val('');

            $('#invoiceSplitProjectComplete').text(state.project_complete);
            $('#projectCompleteTotal').text('');

            //Close Invoice Modal
            $('#invoiceModal').foundation('close');
        });

        //Click Cancel on Custom Eval Invoice Modal
        $("#cancelContinueWithoutEmail").click(function(){
            $('#invoiceModal .invoiceTable tbody tr').not(':first').remove();

            $('#invoiceModal #invoiceEvaluationId').text('');

            $('input[name="invoiceSplitBidAcceptance"]').val(state.bid_acceptance);
            $('input[name="bidAcceptanceTotal"]').val('');

            $('#invoiceSplitProjectComplete').text(state.project_complete);
            $('#projectCompleteTotal').text('');

            //Close Invoice Modal
            $('#emailWontSendModal').foundation('close');
        });

        //Add A Custom Evaluation
        $('#addCustomEvaluation').click(function () {

            elem.loader.show();
            var projectID = state.id;
            var description = $('#uploadEvaluation input[name="evaluationDescription"]').val();

            var customFile = $('#uploadEvaluation input[name="customEvaluation"]').val();

            var formdata = new FormData();
            formdata.append('projectID', projectID);
            formdata.append('description', description);
            formdata.append('file', $('#uploadEvaluation input[name="customEvaluation"]')[0].files[0]);

            if (description == '' || customFile == '') {
                $('#uploadEvaluation input[name="evaluationDescription"]').addClass('is-invalid-input');
                $('#uploadEvaluation input[name="evaluationDescription"]').parent().addClass('is-invalid-label');
                $('#uploadEvaluation input[name="evaluationDescription"]').parent().find('.form-error').addClass('is-visible');

                $('#uploadEvaluation input[name="customEvaluation"]').addClass('is-invalid-input');
                $('#uploadEvaluation input[name="customEvaluation"]').parent().addClass('is-invalid-label');
                $('#uploadEvaluation input[name="customEvaluation"]').parent().find('.form-error').addClass('is-visible');
            } else {

                $.ajax({
                    url: window.fx_url.BASE + 'custom-evaluation-add.php',
                    type: "POST",
                    data: formdata,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,  // tell jQuery not to set contentType
                    success: function (response) {
                        response = JSON.parse(response);

                        $('#invoiceModal #invoiceEvaluationId').text(response.evaluation_id);

                        $('#evaluationList').empty();

                        //Get Project Evaluations
                        projectEvaluations();

                        $('#uploadEvaluation input[name="evaluationDescription"]').val('');
                        $('#uploadEvaluation input[name="customEvaluation"]').val('');

                        $('#uploadEvaluation').foundation('close');

                        //Open Invoice Modal
                        $('#bidTotalModal').foundation('open');

                        elem.loader.hide();


                    }, error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);

                    }

                });
            }

        });

        $('#closeFinalWarrantiesModal').click(closeFinalWarrantiesModal);

        $('#finalWarrantiesModal [name="warrantyID"]').change(function (e) {
            addWarranty(e);
        });

        $('#finalWarrantiesModal .warrantyList').on('click.fx', 'a', function (e) {
            e.preventDefault();
            removeWarranty(e);
            return false;
        });

        //Add Custom Copied Evaluation
        $('#startCustomCopiedEvaluation').click(function () {
            var projectID = state.id;
            var description = $('#copyCustomEvaluationModal input[name="evaluationDescription"]').val();
            var idToCopy = $('#copyCustomEvaluationModal #idToCopy').val();

            if (description == '') {
                $('#copyCustomEvaluationModal input[name="evaluationDescription"]').addClass('is-invalid-input');
                $('#copyCustomEvaluationModal input[name="evaluationDescription"]').parent().addClass('is-invalid-label');
                $('#copyCustomEvaluationModal input[name="evaluationDescription"]').parent().find('.form-error').addClass('is-visible');

            } else {
                loading_image.show();
                $.ajax({
                    url: window.fx_url.BASE + 'copied-custom-evaluation-add.php',
                    dataType: "json",
                    type: "POST",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        projectID: projectID,
                        evaluationID: idToCopy,
                        description: description
                    },
                    success: function (response) {
                        if (response.bid_item_id) {
                            window.location.href = fx_pages.BIDS_CREATE.replace('{id}', response.bid_item_id);
                            return;
                        }
                        loading_image.hide();

                        $('#invoiceModal #invoiceEvaluationId').text(response.evaluation_id);

                        $('#evaluationList').empty();

                        //Get Project Evaluations
                        projectEvaluations();

                        $('#copyCustomEvaluationModal input[name="evaluationDescription"]').val('');

                        $('#copyCustomEvaluationModal').foundation('close');

                        //Open Invoice Modal
                        $('#bidTotalModal').foundation('open');


                    }, error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                        alert('Unable to copy bid, please contact support');
                        loading_image.hide();
                    }

                });
            }

        });


        //Close Adding A Custom Copied Evaluation Modal
        $('#closeCopiedCustomEvaluationModal').click(function () {
            $('#copyCustomEvaluationModal input[name="evaluationDescription"]').removeClass('is-invalid-input');
            $('#copyCustomEvaluationModal input[name="evaluationDescription"]').parent().removeClass('is-invalid-label');
            $('#copyCustomEvaluationModal input[name="evaluationDescription"]').parent().find('.form-error').removeClass('is-visible');
            $('#copyCustomEvaluationModal #idToCopy').val('');
            $('#copyCustomEvaluationModal input[name="evaluationDescription"]').val('');
            $('#copyCustomEvaluationModal').foundation('close');
        });


        //Close Cancel Evaluation Modal
        $('#closeCancelEvaluationModal').click(function () {
            $('#cancelEvaluationModal #idToCancel').val('');
            $('#cancelEvaluationModal').foundation('close');
        });


        //Cancel Evaluation
        $('#cancelEvaluation').click(function () {
            var projectID = state.id;
            var idToCancel = $('#cancelEvaluationModal #idToCancel').val();

            $.ajax({
                url: window.fx_url.BASE + 'evaluation-cancel.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    projectID: projectID,
                    evaluationID: idToCancel
                },
                success: function (response) {
                    $('#evaluationList').empty();

                    //Get Project Evaluations
                    projectEvaluations();

                    $('#cancelEvaluationModal').foundation('close');

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        });

        $("#bidTotalNext").click(function () {
            finalizeCustomBid();
        });

        //Send Custom Bid
        $("#invoiceApprove").click(function() {

            invoiceApproveCustomBid();

        });

        //Click Cancel on Bid Total Modal
        $("#cancelBidTotal").click(function(){

            $('#bidTotalModal input[name="customBidTotal"]').val('');

            //Close Invoice Modal
            $('#bidTotalModal').foundation('close');
        });

        //Calculate Invoice Percentage And Mount
        $(".changeInvoiceAmount").change(addInvoiceTotalAmount);
        $(".changeInvoicePercent").change(addInvoiceTotalPercent);


        //Add Row for Invoice on Click +/-
        $(".button.numberInvoices").on("click", function () {

            var $button = $(this);
            var oldValue = $('#invoiceModal .invoiceTable tbody > tr:last-child td input').attr("sort");

            var oldInputCount = $('#invoiceModal .invoiceTable tbody tr:not(:first-child)').length;


            if ($button.text() == "+") {
                $('.invoiceTable input').removeClass('is-invalid-input');
                $('.invoiceTable input').parent().find('.form-error').removeClass('is-visible');

                var newVal = parseFloat(oldValue) + 1;
                $('#invoiceModal .invoiceTable tbody').append($('#invoiceModal .invoiceTable tbody tr:first').clone().css('display', ''));
                $('#invoiceModal .invoiceTable tbody > tr:last-child td input').attr("sort", "" + (newVal) + "");
                $('#invoiceModal .invoiceTable tbody > tr:last-child td input.changeInvoiceAmount').change(addInvoiceTotalAmount);
                $('#invoiceModal .invoiceTable tbody > tr:last-child td input.changeInvoicePercent').change(addInvoiceTotalPercent);

            }
            else if ($button.text() == "-") {
                $('.invoiceTable input').removeClass('is-invalid-input');
                $('.invoiceTable input').parent().find('.form-error').removeClass('is-visible');

                // Don't allow decrementing below default
                if (oldInputCount > 0) {
                    var newVal = parseFloat(oldValue) - 1;
                    if ($('#invoiceModal .invoiceTable tbody tr:last-child td input.changeInvoicePercent').val() == '' && $('#invoiceModal .invoiceTable tbody tr:last-child td input.changeInvoiceAmount').val() == '') {
                        $('#invoiceModal .invoiceTable tbody tr:last-child').remove();
                    } else {
                        $('#invoiceModal .invoiceTable tbody tr:last-child').remove();
                        addInvoiceTotalAmount();
                    }

                } else {
                    newVal = 2;
                }
            }

            var newInputCount = $('#invoiceModal .invoiceTable tbody tr:not(:first-child)').length;
            newInputCount = newInputCount + 2;

            $button.parent().siblings(".invoiceCountInput").children(".invoiceCount").val(newInputCount);
        });
        //End Add Row for Invoice on Click

        $('#continueWithoutEmail').click(function() {
            storeInvoiceData();
            $('#emailWontSendModal').foundation('close');
            $('#invoiceModal .invoiceTable tbody tr').not(':first').remove();

            $('#invoiceModal #invoiceEvaluationId').text('');

            $('input[name="invoiceSplitBidAcceptance"]').val(state.bid_acceptance);
            $('input[name="bidAcceptanceTotal"]').val('');

            $('#invoiceSplitProjectComplete').text(state.project_complete);
            $('#projectCompleteTotal').text('');

            $('#evaluationList').empty();

            //Get Project Evaluations
            projectEvaluations();

            // //Get Billing info
            // populateBillingInfo();
        });
    }
    module.exports.setup = setup;

    function projectEvaluations(config) {
        Object.assign(state, config);

        $('#evaluationList').empty();

        $.ajax({
            url: window.fx_url.BASE + 'getProjectEvaluations.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectID: state.id
            },
            success: (response) => {
                var finalize_evaluation_id = null;
                if (!response == '') {

                    for (let bid of response) {
                        //Add Evaluation Names to Copy Dropdown
                        if (bid.customEvaluation == null && bid.bidItemID == null) {
                            $('#currentEvaluationNames').append('<option value="' + bid.evaluationID + '">' + bid.evaluationDescription + '</option>');
                        }

                        var referenceID = '';

                        if (bid.referenceID !== null) {
                            referenceID = ' <span><small class="reference-id">#' + bid.referenceID + '</small></span>';
                        }

                        var evaluationCreatedDisplay = '';
                        var evaluationLastUpdatedDisplay = '';
                        var bidFirstSentDisplay = '';
                        var bidLastSentDisplay = '';
                        var bidLastViewedDisplay = '';
                        var evaluationCancelledDisplay = '';
                        var evaluationFinalizedDisplay = '';
                        var evaluationSubmittedDisplay = '';
                        var bidAcceptedDisplay = '';
                        var bidRejectedDisplay = '';
                        var editEvaluationDisplay = '';
                        var viewEvaluationDisplay = '';
                        var bidSummaryDisplay = '';
                        var evaluationCancelDisplay = '';
                        var copyEvaluationDisplay = '';
                        var status = '';
                        var resendBidEmailDisplay = '';
                        var evaluationReportDisplay = '';
                        var evaluationReportPhotos = '';
                        var viewBidDisplay = '';
                        var scopeChangeDisplay = '';
                        var finalizeCustomDisplay = '';
                        var sendFinalReportDisplay = '';
                        var finalReportDisplay = '';
                        var finalReportSentDisplay = '';
                        var contractDisplay = '';
                        var customEvaluationDisplay = '';
                        var importedDateDisplay = '';
                        var appDrawingDisplay = '';

                        if (bid.bidAccepted != null) {
                            // bidAcceptedDisplay = 'Bid Accepted By Customer <span>' + bid.bidAccepted + '</span><br/>';
                            // status = 'Bid Accepted';
                            // console.log(" bidaccepted 2694: " + bid.bidAccepted);

                            $('#bidAccepted').text('1');

                        } //FXLRTR-366

                        if (bid.appDrawingExists && bid.evaluationCancelled == null) {
                            appDrawingDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'getAppDrawing.php?evaluationID=' + bid.evaluationID + '">Repair Plan Drawings </a></li>';
                        }

                        if (bid.customEvaluation == null && bid.bidItemID == null) {
                            customEvaluationDisplay = 'custom="false"';

                            if (bid.evaluationCreated != null) {
                                var evaluationCreatedDisplayName = '';
                                if (bid.evalCreatedByFirstName != null && bid.evalCreatedByLastName != null) {
                                    evaluationCreatedDisplayName = ' by ' + bid.evalCreatedByFirstName + ' ' + bid.evalCreatedByLastName;
                                }
                                evaluationCreatedDisplay = ' Started <span>' + bid.evaluationCreated + evaluationCreatedDisplayName + '</span><br/>';
                                status = 'Bid In Progress';
                            }

                            if (bid.bidFirstSent == null) {
                                contractDisplay = '';
                                if (bid.evaluationCancelled == null) {
                                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                        editEvaluationDisplay = '';
                                    }

                                    if (bid.evaluationFinalized != null) {
                                        if (window.project_data.user.primary == 1 || window.project_data.user.bid_creation == 1 || window.project_data.user.bid_verification == 1) {
                                            bidSummaryDisplay = '';
                                        }
                                    }
                                }
                            } else {
                                if (bid.evaluationCancelled == null) {
                                    resendBidEmailDisplay = '<a name="resendBidEmailCustomer" class="button expanded resendBidEmailCustomer">Resend Bid Email</a>';
                                    //resendBidEmailDisplay = '<a href="bid-accept-email.php?id=' + bid.bidID + '&email=send&resend=yes" class="button expanded">Resend Bid Email</a>';
                                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                        viewBidDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'media/bids/original/' + bid.bidFileID + '">View Bid</a></li>';
                                    }
                                }

                            }

                            //viewEvaluationDisplay = '<a class="button expanded" href="evaluation-view.php?eid=' + bid.evaluationID + '">View Evaluation</a>';

                            if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                copyEvaluationDisplay = '';

                                if (bid.bidAccepted != null && bid.evaluationCancelled == null) {

                                    finalReportDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'final-email.php?projectID=' + state.id + '&evaluationID=' + bid.evaluationID + '">Warranties</a></li>';

                                    if (bid.finalReportSent == null) {
                                        sendFinalReportDisplay = '<a name="sendFinalReport" class="button expanded sendFinalReport">Send Warranties</a>';

                                    } else {
                                        var finalReportSentDisplayName = '';
                                        if (bid.finalReportSentFirstName != null && bid.finalReportSentLastName != null) {
                                            finalReportSentDisplayName = ' by ' + bid.finalReportSentFirstName + ' ' + bid.finalReportSentLastName;
                                        }
                                        finalReportSentDisplay = 'Warranties Sent <span>' + bid.finalReportSent + finalReportSentDisplayName + '</span><br/>';
                                    }

                                }

                            }

                            //ELSE CUSTOM EVALUATION//
                        } else {
                            // if (finalize_bid_id !== false && bid.bidItemID !== null && finalize_bid_id === bid.bidItemID && bid.bidFirstSent == null) {
                            //     finalize_evaluation_id = bid.evaluationID;
                            // }
                            customEvaluationDisplay = 'custom="true"';
                            if (bid.importedDate == null) {
                                if (bid.contractID) {
                                    contractDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'download-contract.php?eid=' + bid.evaluationID + '&custom=1">Download Contract</a></li>';
                                }
                            } else {
                                importedDateDisplay = 'Imported <span>' + bid.importedDate + '</span><br/>';
                            }
                            if (bid.evaluationCreated != null) {
                                if (bid.bidItemID !== null && bid.evaluationCancelled === null && bid.bidFirstSent === null) {
                                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                        editEvaluationDisplay = '<a class="button expanded" href="' + fx_pages.BIDS_CREATE.replace('{id}', bid.bidItemID) + '">Edit Bid</a>';
                                    }
                                }
                                evaluationCreatedDisplayName = '';
                                if (bid.evalCreatedByFirstName != null && bid.evalCreatedByLastName != null) {
                                    evaluationCreatedDisplayName = ' by ' + bid.evalCreatedByFirstName + ' ' + bid.evalCreatedByLastName;
                                }
                                evaluationCreatedDisplay = (bid.bidItemID === null ? 'Uploaded' : 'Started') + ' <span>' + bid.evaluationCreated + evaluationCreatedDisplayName + '</span><br/>';
                                status = bid.bidItemID !== null ? 'Bid In Progress' : 'Bid In Progress';
                            }

                            if (bid.bidFirstSent == null) {
                                contractDisplay = '';
                                if (bid.evaluationCancelled == null) {
                                    if (bid.bidItemID === null && (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1)) {
                                        //editEvaluationDisplay = '<a class="button expanded" href="">Edit Evaluation</a>';

                                        finalizeCustomDisplay = '<a class="button expanded finalizeCustom">Finalize Bid</a>';
                                    }
                                }

                            } else {
                                if (bid.evaluationCancelled == null && bid.importedDate == null) {
                                    resendBidEmailDisplay = '<a name="resendBidEmailCustomer" class="button expanded resendBidEmailCustomer">Resend Bid Email</a>';
                                    //resendBidEmailDisplay = '<a href="bid-accept-email.php?id=' + bid.bidID + '&email=send&resend=yes" class="button expanded">Resend Bid Email</a>';
                                }

                                if (bid.importedDate == null) {
                                    if (bid.bidItemID !== null) {
                                        evaluationReportDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'media/scope-of-work/original/' + bid.bidItemID + '">Job Document</a></li>';
                                    }
                                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                        var viewBidLink = '';
                                        if (bid.bidItemID !== null) {
                                            viewBidLink = window.fx_url.BASE + 'media/bids/original/' + bid.bidItemID;
                                        } else {
                                            viewBidLink = window.fx_url.BASE + 'image.php?type=evaldoc&pid=' + state.id + '&eid=' + bid.evaluationID + '&name=' + encodeURIComponent(bid.customEvaluation);
                                        }
                                        viewBidDisplay = '<li><a target="_blank" href="' + viewBidLink + '">View Bid</a></li>';
                                        viewEvaluationDisplay = '<a class="button expanded" data-navigate="detail.bids.detail.line_items" data-bid_id="' + bid.bidItemID + '">View Details</a>';
                                    }
                                }

                            }

                            if (bid.importedDate == null && bid.bidItemID === null) {
                                var viewEvaluationLink = window.fx_url.BASE + 'image.php?type=evaldoc&pid=' + state.id + '&eid=' + bid.evaluationID + '&name=' + encodeURIComponent(bid.customEvaluation);
                                viewEvaluationDisplay = '<a class="button expanded" target="_blank" href="' + viewEvaluationLink + '">View Bid</a>';
                            }

                            if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                if (bid.importedDate == null) {
                                    copyEvaluationDisplay = '<a class="button expanded copyCustomEvaluationOpen">Copy ' + (bid.bidItemID === null ? 'Evaluation' : 'Bid') + '</a>';
                                }

                                if (bid.bidAccepted != null) {

                                    if (bid.importedDate == null) {
                                        finalReportDisplay = '<li><a target="_blank" href="' + window.fx_url.BASE + 'final-email.php?projectID=' + state.id + '&evaluationID=' + bid.evaluationID + '&custom=true">Warranties</a></li>';

                                        if (bid.finalReportSent == null) {
                                            sendFinalReportDisplay = '<a name="sendFinalReport" class="button expanded sendFinalReport">Send Warranties</a>';

                                        } else {
                                            finalReportSentDisplayName = '';
                                            if (bid.finalReportSentFirstName != null && bid.finalReportSentLastName != null) {
                                                finalReportSentDisplayName = ' by ' + bid.finalReportSentFirstName + ' ' + bid.finalReportSentLastName;
                                            }
                                            finalReportSentDisplay = 'Warranties Sent <span>' + bid.finalReportSent + finalReportSentDisplayName + '</span><br/>';
                                        }
                                    }

                                }
                            }

                        }

                        if (bid.evaluationFinalized != null) {
                            var evaluationFinalizedDisplayName = '';
                            if (bid.evaluationFinalizedFirstName != null && bid.evaluationFinalizedLastName != null) {
                                evaluationFinalizedDisplayName = ' by ' + bid.evaluationFinalizedFirstName + ' ' + bid.evaluationFinalizedLastName;
                            }
                            evaluationFinalizedDisplay = 'Bid Finalized <span>' + bid.evaluationFinalized + evaluationFinalizedDisplayName + '</span><br/>';
                            status = 'Bid Finalized';
                        }

                        if (bid.submittedAt != null) {
                            evaluationSubmittedDisplay = 'Bid Submitted <span>' + bid.submittedAt + '</span><br/>';
                            status = 'Bid Submitted For Approval';
                        }

                        if (bid.bidFirstSent != null) {
                            var bidFirstSentDisplayName = '';
                            if (bid.bidFirstSentFirstName != null && bid.bidFirstSentLastName != null) {
                                bidFirstSentDisplayName = ' by ' + bid.bidFirstSentFirstName + ' ' + bid.bidFirstSentLastName;
                            }
                            bidFirstSentDisplay = 'Bid Sent <span>' + bid.bidFirstSent + bidFirstSentDisplayName + '</span><br/>';
                            status = 'Bid Sent to Customer';
                        }

                        if (bid.evaluationLastUpdated != null) {
                            var evaluationLastUpdatedDisplayName = '';
                            if (bid.evalLastUpdatedFirstName != null && bid.evalLastUpdatedLastName != null) {
                                evaluationLastUpdatedDisplayName = ' by ' + bid.evalLastUpdatedFirstName + ' ' + bid.evalLastUpdatedLastName;
                            }
                            evaluationLastUpdatedDisplay = ' Last Edited <span>' + bid.evaluationLastUpdated + evaluationLastUpdatedDisplayName + '</span><br/>';
                        }

                        if (bid.bidLastSent != null) {
                            bidLastSentDisplay = 'Bid Reminder Sent <span>' + bid.bidLastSent + '</span><br/>';
                        }

                        if (bid.bidLastViewed != null) {
                            bidLastViewedDisplay = 'Bid Last Viewed By Customer <span>' + bid.bidLastViewed + '</span><br/>';
                        }

                        if (bid.evaluationCancelled != null) {
                            var evaluationCancelledDisplayName = '';
                            if (bid.evalCancelledFirstName != null && bid.evalCancelledLastName != null) {
                                evaluationCancelledDisplayName = ' by ' + bid.evalCancelledFirstName + ' ' + bid.evalCancelledLastName;
                            }
                            evaluationCancelledDisplay = 'Bid Cancelled <span>' + bid.evaluationCancelled + evaluationCancelledDisplayName + '</span><br/>';
                            contractDisplay = '';
                            status = 'Bid Cancelled';
                        } else {

                            if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                evaluationCancelDisplay = '<a class="button expanded cancelEvaluationOpen">Cancel Bid</a>';
                            }

                            if (bid.bidAccepted != null) {
                                bidAcceptedDisplay = 'Bid Accepted By Customer <span>' + bid.bidAccepted + '</span><br/>';
                                status = 'Bid Accepted';
                                $('#bidAccepted').text('1');

                                if (bid.importedDate == null) {
                                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                        scopeChangeDisplay = '<a class="button expanded scopeChange">Scope of Work Change</a>';
                                    }
                                }

                            } else if (bid.bidRejected != null) {
                                bidRejectedDisplay = 'Bid Rejected By Customer <span>' + bid.bidRejected + '</span><br/>';
                                status = 'Bid Rejected';
                            } else {
                                if (bid.bidFirstSent != null && bid.importedDate == null && (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1)) {
                                    bidAcceptedDisplay = '<a target="_blank" href="' + window.fx_url.BASE + 'view-bid.php?id=' + bid.bidID + '">Accept Bid</a><br/>';
                                }
                            }
                        }


                        $('#evaluationList').append('<div class="callout primary" id="Evaluation' + bid.evaluationID + '" evaluationId="' + bid.evaluationID + '" bidID="' + bid.bidID + '" ' + customEvaluationDisplay + '><div class="row expanded"><div class="medium-6 columns" style="margin-bottom:.5rem;"><h5 style="margin:0;"><span data-js="evaluation-description">' + bid.evaluationDescription + '</span>' + referenceID + ' <span style="font-size: .9rem;font-style: italic;"><a class="editEvaluationName">Edit</a></span></h5></div><div class="medium-6 columns text-right" style="margin-bottom:.5rem;"><p><span>' + status + '</span></p></div></div><div class="row expanded"><div class="medium-5 large-5 columns" style="border-right:1px dotted #cbcacf;"><p>' + importedDateDisplay + '' + evaluationCreatedDisplay + ' ' + evaluationLastUpdatedDisplay + ' ' + evaluationFinalizedDisplay + ' ' + evaluationSubmittedDisplay + ' ' + bidFirstSentDisplay + ' ' + bidLastSentDisplay + ' ' + bidLastViewedDisplay + ' ' + evaluationCancelledDisplay + ' ' + bidAcceptedDisplay + ' ' + bidRejectedDisplay + '' + finalReportSentDisplay + '</p></div><div class="medium-4 large-5 columns" style="border-right:1px dotted #cbcacf"><p evaldocs class="no-margin">Documents</p><ul class="evalList">' + evaluationReportDisplay + ' ' + viewBidDisplay + '' + finalReportDisplay + '' + contractDisplay + appDrawingDisplay + '</ul></div><div class="medium-3 large-2 columns text-center">' + editEvaluationDisplay + ' ' + viewEvaluationDisplay + ' ' + bidSummaryDisplay + ' ' + copyEvaluationDisplay + ' ' + resendBidEmailDisplay + ' ' + scopeChangeDisplay + ' ' + finalizeCustomDisplay + ' ' + evaluationCancelDisplay + '' + sendFinalReportDisplay + '</div></div></div>');

                        // only do photos and drawing lookup with legacy (non custom/uploaded) bids
                        if (bid.customEvaluation == null && bid.bidItemID == null && bid.evaluationCancelled == null) {
                            $.ajax({
                                url: window.fx_url.BASE + 'getAllPhotos.php',
                                dataType: "json",
                                type: "POST",
                                contentType: "application/x-www-form-urlencoded",
                                data: {
                                    evaluationID: bid.evaluationID
                                },
                                success: function (response) {
                                    if (!response) {
                                    } else {
                                        evaluationReportPhotos = '<li><a target="_blank" href="' + window.fx_url.BASE + 'evaluation-report-photos.php?eid=' + bid.evaluationID + '">Evaluation Photos</a></li>';
                                        $('#evaluationList #Evaluation' + bid.evaluationID + ' .evalList').append(evaluationReportPhotos);
                                    }
                                }
                            });
                            $.ajax({
                                url: window.fx_url.BASE + 'getDrawing.php',
                                dataType: "json",
                                type: "POST",
                                contentType: "application/x-www-form-urlencoded",
                                data: {
                                    evaluationID: bid.evaluationID
                                },
                                success: function (response) {
                                    if (!response) {
                                    } else {
                                        evaluationDrawing = '<li><a target="_blank" href="' + window.fx_url.BASE + 'image.php?type=evaldrawing&pid=' + state.id + '&name=' + encodeURIComponent(response.evaluationDrawing) + '&eid=' + bid.evaluationID + '">Bid Drawing</a></li>';
                                        $('#evaluationList #Evaluation' + bid.evaluationID + ' .evalList').append(evaluationDrawing);
                                    }
                                }
                            });
                        }

                        // //With Documents
                        // $('#evaluationList').append('<div class="callout primary" id="Evaluation' + bid.evaluationID + '"><div class="row expanded"><div class="medium-6 columns" style="margin-bottom:.5rem;"><h5 style="margin:0;">' + bid.evaluationDescription + ' Evaluation</h5></div><div class="medium-6 columns text-right" style="margin-bottom:.5rem;"><p><span>' + status + '</span></p></div></div><div class="row expanded"><div class="medium-5 columns" style="border-right:1px dotted #cbcacf;"><p>' + evaluationCreatedDisplay + ' ' + evaluationLastUpdatedDisplay + ' ' + evaluationFinalizedDisplay + ' ' + bidFirstSentDisplay + ' ' + bidLastSentDisplay + ' ' + bidLastViewedDisplay + ' ' + evaluationCancelledDisplay + ' ' + bidAcceptedDisplay + ' ' + bidRejectedDisplay + '</p></div><div class="medium-5 columns" style="border-right:1px dotted #cbcacf"><p class="no-margin">Documents</p><ul><li><a href="#button">Document 1</a></li><li><a href="#button">Bid</a></li><li><a href="#button">Contract</a> <span>Executed 3/21/16</span><br/></li><li><a href="#button">Invoice</a></li></ul></div><div class="medium-2 columns text-center">' + editEvaluationDisplay + ' ' + viewEvaluationDisplay + ' ' + bidSummaryDisplay + ' ' + copyEvaluationDisplay + ' ' + evaluationCancelDisplay + '</div></div></div>');

                        $('#Evaluation' + bid.evaluationID + ' .cancelEvaluationOpen').click(openCancelModal);

                        $('#Evaluation' + bid.evaluationID + ' .copyCustomEvaluationOpen').click(openCustomCopyModal);

                        $('#Evaluation' + bid.evaluationID + ' .resendBidEmailCustomer').click(resendBidEmailCustomer);

                        $('#Evaluation' + bid.evaluationID + ' .scopeChange').click(scopeChange);

                        $('#Evaluation' + bid.evaluationID + ' .finalizeCustom').click(finalizeCustomEvaluation);

                        $('#Evaluation' + bid.evaluationID + ' .sendFinalReport').click(prepareFinalReport);

                        $('#Evaluation' + bid.evaluationID + ' .editEvaluationName').click(editEvaluationName);

                        //Disable Resend Button
                        if (state.customer.email === null || state.customer.is_unsubscribed === true) {
                            $('#Evaluation' + bid.evaluationID + ' .resendBidEmailCustomer, #Evaluation' + bid.evaluationID + ' .sendFinalReport').addClass('disabled');
                        }
                    }

                }

                if (onload == 'true') {
                    projectSchedule();
                    onload = 'false';
                }
                if (finalize_evaluation_id !== null) {
                    finalizeGuidedBid(finalize_evaluation_id);
                }
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    //Open Cancel Evaluation Modal
    var openCancelModal = function () {
        var idToCancel = $(this).parent().parent().parent().attr('evaluationId');
        $('#cancelEvaluationModal #idToCancel').val(idToCancel);
        $('#cancelEvaluationModal').foundation('open');
    };

    //Open Custom Copy Evaluation Modal
    var openCustomCopyModal = function () {
        var bid_parent = $(this).parent().parent().parent();
        var idToCopy = bid_parent.attr('evaluationId');
        var modal = $('#copyCustomEvaluationModal');
        var parent_bid_name = bid_parent.find('[data-js="evaluation-description"]').text();

        modal.find('#idToCopy').val(idToCopy);
        modal.find('[name="evaluationDescription"]').val(parent_bid_name + ' (Copy)');
        modal.foundation('open');
    };

    //Resend Customer Bid Email from Evaluation/Bid Tab
    var resendBidEmailCustomer = (e) => {
        var idToResend = $(e.target).parent().parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'resend-bid.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    idToResend: idToResend
                },
                success: function (response) {
                    elem.loader.hide();
                    if (response.success) {
                        $('#emailSentModal h3').text('Bid Email Resent');
                        $('#emailSentModal p.modalContent').html('Your bid has been resent.');
                        $('#emailSentModal').foundation('open');
                    }
                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to resend bid, please contact support');
                    elem.loader.hide();
                }
            });
        }
    };

    //Add On Items
    var scopeChange = function() {
        // console.log('clicked');
        var evaluationID = $(this).parent().parent().parent().attr('evaluationId');
        var bidID = $(this).parent().parent().parent().attr('bidid');

        $('#scopeChangeModal #scopeChangeEvaluationId').text(evaluationID);
        $('#scopeChangeModal #scopeChangeBidId').text(bidID);

        $.ajax({
            url: window.fx_url.BASE + 'getScopeChanges.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                evaluationID: evaluationID
            },
            success: function (response) {

                if (response != null) {

                    $.each(response, function (i, item) {

                        $cloneRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');

                        $cloneRow.addClass('dbRow');

                        $cloneRow.find('input[name="changeID"]').val(item.scopeChangeItemID);
                        $cloneRow.find('input.ChangeDate').val(item.date);
                        $cloneRow.find('textarea.ChangeItem').val(item.item);
                        $cloneRow.find('input.ChangePrice').val(item.price);

                        $cloneRow.find('.button-group .button').removeClass('active');

                        if (item.type == 0) {
                            $cloneRow.find('button#charge').addClass('active');
                        } else if (item.type == 1) {
                            $cloneRow.find('button#credit').addClass('active');
                        }

                        $cloneRow.find('input.ChangeDate').attr('sort', item.sort);
                        $cloneRow.find('textarea.ChangeItem').attr('sort', item.sort);
                        $cloneRow.find('input.ChangePrice').attr('sort', item.sort);

                        $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneRow);
                        $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
                        $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
                        $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
                        $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
                        $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
                        autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));

                    });

                    changeScopeTotal();
                    $('#scopeChangeModal').foundation('open');

                    $(document).find('#scopeChangeModal .scopeChangeItemTable textarea').each(function () {
                        var $this = $(this);
                        autosize.update($this);
                    });

                    var oldValue = $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input').attr('sort');
                    var newVal = parseFloat(oldValue) + 1;

                    $cloneBlankRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');
                    $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneBlankRow);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td textarea').attr('sort',newVal);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
                    autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));

                } else {

                    $('#scopeChangeModal').foundation('open');

                    var oldValue = $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input').attr('sort');
                    var newVal = parseFloat(oldValue) + 1;

                    $cloneBlankRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');
                    $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneBlankRow);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td textarea').attr('sort',newVal);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
                    $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
                    autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));
                }

            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);

                var oldValue = $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input').attr('sort');
                var newVal = parseFloat(oldValue) + 1;

                $cloneBlankRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');
                $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneBlankRow);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td textarea').attr('sort',newVal);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
                autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));
            }

        });
    };
    module.exports.projectEvaluations = projectEvaluations;

    var addRowScopeChange = function () {

        if ($('#scopeChangeModal .scopeChangeItemTable tbody').children().length == 1) {
            var oldValue = $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child:visible input').attr('sort');
            if (oldValue >= 1) {
                var newVal = parseFloat(oldValue) + 1;

            } else {
                var newVal = parseFloat(0) + 1;
            }

            $cloneBlankRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');
            $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneBlankRow);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td textarea').attr('sort',newVal);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
            $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
            autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));

        } else {
            if ($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangeDate').val() == '' &&
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea.ChangeItem').val() == '' &&
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').val() == '') {

            } else {

                var oldValue = $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child:visible input').attr('sort');
                if (oldValue >= 1) {
                    var newVal = parseFloat(oldValue) + 1;

                } else {
                    var newVal = parseFloat(0) + 1;
                }

                $cloneBlankRow = $('#scopeChangeModal .scopeChangeItemTable tbody tr:first').clone().css('display', '');
                $('#scopeChangeModal .scopeChangeItemTable tbody').append($cloneBlankRow);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child td textarea').attr('sort',newVal);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.datepicker').mouseenter(pickDate);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input, #scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea').change(addRowScopeChange);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child .delete a').click(deleteRowScopeChange);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child button').click(changeScopeType);
                $('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child input.ChangePrice').change(changeScopeTotal);
                autosize($('#scopeChangeModal .scopeChangeItemTable tbody > tr:last-child textarea'));
            }
        }

    };

    var deleteRowScopeChange = function () {

        if ($(this).parent().parent().hasClass('dbRow')) {
            $(this).parent().parent().find('input').attr('delete','delete');
            $(this).parent().parent().css('display','none');

            if ($('#scopeChangeModal .scopeChangeItemTable tbody').children(':visible').length == 0)
                addRowScopeChange();
        } else {
            $(this).parent().parent().remove();

            if ($('#scopeChangeModal .scopeChangeItemTable tbody').children().length == 1) {
                addRowScopeChange();
            }

            // if ($('#scopeChangeModal .scopeChangeItemTable tbody').children(':visible').length == 0)
            //     addRowScopeChange();
        }
    };

    var changeScopeType = function() {
        $(this).parent().find('button').removeClass('active');
        $(this).addClass('active');

        if ($(this).attr('type') == 'credit') {
            var value = $(this).parent().parent().parent().find('input.ChangePrice').val();
            var firstValue = value.slice(0,1);

            if (firstValue != '-') {
                value = Number(value);
                var newValue = 0 - value;
                newValue = parseFloat(newValue).toFixed(2);

                $(this).parent().parent().parent().find('input.ChangePrice').val(newValue);
                $(this).parent().parent().parent().find('input.ChangePrice').trigger('change');
            }

        } else if ($(this).attr('type') == 'charge') {
            var value = $(this).parent().parent().parent().find('input.ChangePrice').val();
            var firstValue = value.slice(0,1);

            if (firstValue == '-') {
                value = Number(value);
                var newValue = value * -1;
                newValue = parseFloat(newValue).toFixed(2);

                $(this).parent().parent().parent().find('input.ChangePrice').val(newValue);
                $(this).parent().parent().parent().find('input.ChangePrice').trigger('change');
            }
        }
    };

    var changeScopeTotal = function() {
        var changeTotal = 0;
        $('#scopeChangeModal .scopeChangeItemTable tbody > tr input.ChangePrice').each(function() {
            if ($(this).val() != '0') {
                if ($(this).parent().parent().find('.button.active').attr('type') == 'credit') {
                    //console.log('Credit '+$(this).val());
                    var value = $(this).val();
                    var firstValue = value.slice(0,1);

                    if (firstValue != '-') {
                        //console.log('Credit - First Value IS NOT -');
                        value = Number(value);
                        var newValue = 0 - value;
                        newValue = parseFloat(newValue).toFixed(2);

                        $(this).val(newValue);
                    }

                } else if ($(this).parent().parent().find('.button.active').attr('type') == 'charge') {
                    //console.log('Charge '+$(this).val());
                    var value = $(this).val();
                    var firstValue = value.slice(0,1);
                    //console.log(firstValue);

                    if (firstValue == '-') {
                        //console.log('Charge - First Value IS -');
                        value = Number(value);
                        var newValue = value * -1;
                        newValue = parseFloat(newValue).toFixed(2);

                        $(this).val(newValue);
                    }
                }
            }
            //console.log($(this).val());
            changeTotal += Number($(this).val());
        });

        function ReplaceNumberWithCommas(Total) {
            //Seperates the components of the number
            var n= Total.toString().split(".");
            //Comma-fies the first part
            n[0] = n[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            //Combines the two sections
            return n.join(".");
        }

        changeTotal = parseFloat(changeTotal).toFixed(2);

        var changeTotalFirst = changeTotal.slice(0,1);

        if (changeTotalFirst == '-') {
            var changeTypeDisplay = '(Credit Memo)';
            var changeTypeHidden = 'creditMemo';
        } else {
            var changeTypeDisplay = '(Invoice)';
            var changeTypeHidden = 'invoice';
        }

        $('#scopeChangeModal #hiddenChangeTotal').val(changeTotal);
        $('#scopeChangeModal #hiddenChangeType').val(changeTypeHidden);

        $('#scopeChangeModal .changeTotal').html('<strong>Total: $'+ReplaceNumberWithCommas(changeTotal)+'</strong> <span>'+changeTypeDisplay+'</span>');
    };

    var pickDate = function() {
        $(this).datepicker({ });
    };

    //Save Add On Items
    var saveScopeChange = function() {
        elem.loader.show();
        var evaluationID = $('#scopeChangeModal #scopeChangeEvaluationId').text();
        var bidID = $('#scopeChangeModal #scopeChangeBidId').text();
        var custom = $('#evaluationList #Evaluation' + evaluationID + '.callout').attr('custom');

        if (custom == 'false') {
            custom = '';
        } else {
            custom = 1;
        }

        storeScopeChangeTableData();

        function storeScopeChangeTableData() {
            var scopeChangeTableData = new Array();
            $('#scopeChangeModal .scopeChangeItemTable tbody tr').each(function(row,tr){
                scopeChangeTableData[row]={
                    "changeDelete":$(tr).find('td:eq(0)').find('input').attr('delete'),
                    "changeSort":$(tr).find('td:eq(0)').find('input').attr('sort'),
                    "changeID":$(tr).find('td:eq(0)').find('input[name="changeID"]').val(),
                    "changeDate":$(tr).find('td:eq(0)').find('input.ChangeDate').val(),
                    "changeItem":$(tr).find('td:eq(1)').find('textarea.ChangeItem').val(),
                    "changePrice":$(tr).find('td:eq(2)').find('input.ChangePrice').val(),
                    "changeType":$(tr).find('td:eq(3)').find('button.active').attr('type')
                };
            });
            scopeChangeTableData.shift();
            var scopeChangeTableArray = scopeChangeTableData;

            var changeTotal = $('#scopeChangeModal #hiddenChangeTotal').val();
            var changeType = $('#scopeChangeModal #hiddenChangeType').val();
            // console.log(scopeChangeTableArray);

            $.ajax({
                url: window.fx_url.BASE + 'evaluation-scope-change-edit.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    evaluationID: evaluationID,
                    bidID: bidID,
                    custom: custom,
                    quickbooksID: state.customer.quickbooks_id,
                    scopeChange: scopeChangeTableArray,
                    changeTotal: changeTotal,
                    changeType: changeType
                },
                success: function(response) {
                    console.log(response);
                    if(response == 'true'){
                        elem.loader.hide();
                        $('#saveScopeChange').prop('disabled', false);
                        $('#scopeChangeModal').foundation('close');
                        $('#scopeChangeModal .scopeChangeItemTable tbody tr').not(':first').remove();
                    }
                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });


        }

    };

    var addInvoiceTotalAmount = function() {
        //var value = parseFloat($(this).val());
        //var updatedName = $(this).attr('name');
        var invoiceTotal = $('#bidTotal').text();
        var invoiceTotalFormat = invoiceTotal.replace(/,/g, '');
        invoiceTotalFormat = parseFloat(invoiceTotalFormat);

        var totalAmount = 0;
        $('#invoiceModal').find(".changeInvoiceAmount").each(function() {
            var amount = $(this).val();

            if (amount != '') {
                var amountPercent = amount / invoiceTotalFormat * 100;
                amountPercent = parseFloat(amountPercent).toFixed(2);

                if ($(this).hasClass('default')) {
                    $(this).parent().parent().parent().parent().find('.changeInvoicePercent').val(amountPercent);
                } else {
                    $(this).parent().parent().find('.changeInvoicePercent').val(amountPercent);
                }

                totalAmount += Number(amount);
            }

        });

        totalAmount = parseFloat(totalAmount).toFixed(2);
        var newinvoiceTotal = invoiceTotalFormat - totalAmount;
        newinvoiceTotal = parseFloat(newinvoiceTotal).toFixed(2);

        $('#projectCompleteTotal').text(newinvoiceTotal);

        var totalPercent = 0;
        $('#invoiceModal').find(".changeInvoicePercent").each(function() {
            var percent = $(this).val();

            if (percent != '') {

                totalPercent += Number(percent);
            }

        });

        totalPercent = parseFloat(totalPercent).toFixed(2);
        var newinvoicePercent = 100 - totalPercent;
        newinvoicePercent = parseFloat(newinvoicePercent).toFixed(2);

        $('#invoiceSplitProjectComplete').text(newinvoicePercent);

    };

    var addInvoiceTotalPercent = function() {
        var invoiceTotal = $('#bidTotal').text();
        var invoiceTotalFormat = invoiceTotal.replace(/,/g, '');
        invoiceTotalFormat = parseFloat(invoiceTotalFormat);

        var totalPercent = 0;
        $('#invoiceModal').find(".changeInvoicePercent").each(function() {
            var percent = $(this).val();

            if (percent != '') {

                var percentDecimal = percent / 100;

                var amountPercent = invoiceTotalFormat * percentDecimal;
                amountPercent = parseFloat(amountPercent).toFixed(2);

                if ($(this).hasClass('default')) {
                    $(this).parent().parent().parent().parent().find('.changeInvoiceAmount').val(amountPercent);
                } else {
                    $(this).parent().parent().find('.changeInvoiceAmount').val(amountPercent);
                }
                totalPercent += Number(percent);
            }

        });

        totalPercent = parseFloat(totalPercent).toFixed(2);
        var newinvoicePercent = 100 - totalPercent;
        newinvoicePercent = parseFloat(newinvoicePercent).toFixed(2);

        $('#invoiceSplitProjectComplete').text(newinvoicePercent);

        var totalAmount = 0;
        $('#invoiceModal').find(".changeInvoiceAmount").each(function() {
            var amount = $(this).val();

            if (amount != '') {
                totalAmount += Number(amount);
            }

        });

        totalAmount = parseFloat(totalAmount).toFixed(2);
        var newinvoiceTotal = invoiceTotalFormat - totalAmount;
        newinvoiceTotal = parseFloat(newinvoiceTotal).toFixed(2);

        $('#projectCompleteTotal').text(newinvoiceTotal);

    };

    var finalizeCustomEvaluation = function() {

        var idToEdit = $(this).parent().parent().parent().attr('evaluationId');

        $('#invoiceModal #invoiceEvaluationId').text(idToEdit);

        $('#bidTotalModal').foundation('open');
    };

    var finalizeGuidedBid = function(evaluation_id) {
        var total = getQueryField('bid-total');
        $('#invoiceModal #invoiceEvaluationId').text(evaluation_id);
        finalizeCustomBid(total);
    };

    var finalizeCustomBid = function(total) {
        elem.loader.show();

        function ReplaceNumberWithCommas(Total) {
            //Seperates the components of the number
            var n= Total.toString().split(".");
            //Comma-fies the first part
            n[0] = n[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            //Combines the two sections
            return n.join(".");
        }

        // var discountTotal = $('.discountTotal').text();
        // var bidTotal = response.toString();
        // bidTotal = Number(bidTotal - discountTotal);

        //bidTotalModal
        var customBidTotal = typeof total !== 'undefined' ? total : $('input[name="customBidTotal"]').val();

        customBidTotal = customBidTotal.replace(/\,/g,"");

        $('input[name="customBidTotal"]').val('');

        var customBidTotalFormat = parseFloat(customBidTotal).toFixed(2);
        $('#bidTotal').html(ReplaceNumberWithCommas(customBidTotalFormat));


        var bidAcceptPercent = parseFloat($('[name="invoiceSplitBidAcceptance"]').val());
        var bidAcceptanceTotal = bidAcceptPercent / 100 * customBidTotal;
        bidAcceptanceTotal = parseFloat(bidAcceptanceTotal).toFixed(2);
        $('[name="bidAcceptanceTotal"]').val(bidAcceptanceTotal);

        var projectCompletePercent = parseFloat($('#invoiceSplitProjectComplete').text());
        var projectCompleteTotal = projectCompletePercent / 100 * customBidTotal;
        projectCompleteTotal = parseFloat(projectCompleteTotal).toFixed(2);
        $('#projectCompleteTotal').text(projectCompleteTotal);

        if (window.project_data.company.default_invoices > 2) {
            var defaultTotalInvoices = window.project_data.company.default_invoices - 2;

            for (i = 0; i < defaultTotalInvoices; i++) {
                var oldValue = $('.invoiceTable tbody > tr:last-child input').attr("sort");
                var newVal = parseFloat(oldValue) + 1;

                $cloneBlankRow = $('.invoiceTable tbody tr:first').clone().css('display', '');
                $('.invoiceTable tbody').append($cloneBlankRow);
                $('.invoiceTable tbody > tr:last-child td input').attr("sort", "" + (newVal) + "");
                $('.invoiceTable tbody > tr:last-child td input.changeInvoiceAmount').change(addInvoiceTotalAmount);
                $('.invoiceTable tbody > tr:last-child td input.changeInvoicePercent').change(addInvoiceTotalPercent);

            }
        }

        $(".invoiceCountInput .invoiceCount").val(window.project_data.company.default_invoices);


        //populate the bid number in the invoice modal with the next available bid number
        $.ajax({
            url: window.fx_url.BASE + 'getLastBidNumber.php',
            dataType: "json",
            data: {
            },
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            success: function (response) {
                if (!response){
                    $('[name="bidNumber"]').val('1');
                }
                else{
                    if (response.reference_id != null) {
                        $('[name="bidNumber"]').val(response.reference_id);
                    } else {
                        $('[name="bidNumber"]').val('1');
                    }

                }

                elem.loader.hide();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });

        if (window.project_data.company.connection_status == 'qb-disconnected') {
            $('#disconnectedError').addClass('is-visible');
            $('#invoiceApprove').prop('disabled', 'disabled');
        }

        //Open Invoice Modal
        $('#invoiceModal').foundation('open');

    };

    var removeInvoiceRowError = function(){
        $(this).parent().removeClass('is-invalid-label');
        $(this).removeClass('is-invalid-input');
        $(this).parent().find('.form-error').removeClass('is-visible');
    };

    var invoiceApproveCustomBid = function(){
        var bidNumberValidate;
        var percentageValidate = 1;
        var projectCompletePercentInteger = Number($('#invoiceSplitProjectComplete').text());
        var percentageTotal = 0;

        $('.changeInvoicePercent').not('[sort="0"]').each(function () {
            if ($(this).val() != ''){
                percentageTotal += Number($(this).val());
            }
        });

        percentageTotal += projectCompletePercentInteger;

        //console.log(percentageTotal);

        if (percentageTotal < 100 || percentageTotal > 100 ){
            percentageValidate = 0;
            $('.changeInvoicePercent').addClass('is-invalid-input');
        }
        else{
            $('.changeInvoicePercent').removeClass('is-invalid-input');
        }

        if (parseInt($('#invoiceSplitProjectComplete').text()) < 1){
            $('#invoiceSplitProjectComplete').addClass('is-invalid-label');
            percentageValidate = 0;
        }
        else{
            $('#invoiceSplitProjectComplete').removeClass('is-invalid-label');
        }

        var bidAcceptanceName = $('[name="bidAcceptanceName"]').val().trim();
        var projectCompleteName = $('[name="projectCompleteName"]').val().trim();

        if (bidAcceptanceName !='' && projectCompleteName !=''){
            var evaluationID = $('#invoiceModal #invoiceEvaluationId').text();
            var bidNumberInput = $('[name="bidNumber"]').val();

            if (bidNumberInput != ''){
                bidNumberValidate = 1;
                validateSendBid();
            }

        }

        function validateSendBid() {
            // console.log('bidNumberValidate '+bidNumberValidate);
            // console.log('percentageValidate '+percentageValidate);
            if (bidNumberValidate == 1 && percentageValidate == 1) {
                if (bidNumberInput != ''){
                    // console.log('Store Invoice Data');

                    if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
                        storeInvoiceData();
                    }
                    else{
                        if (state.customer.email == null){
                            var text = 'no email.';
                        }
                        if (state.customer.is_unsubscribed !== false){
                            var text = 'unsubscribed from emails.';
                        }

                        $('#emailWontSendModal').foundation('open');
                        $('#emailWontSendModal p').text('Bid will not be sent because the customer has ' + text + ' Continue?');
                    }
                }
                // else {
                //  //console.log('Store Invoice Data No Bid');
                //  storeInvoiceDataNoBidNumber();
                // }
            }
        }


    };

    function storeInvoiceData() {
        var invoiceTableData = new Array();
        $('#invoiceModal .invoiceTable tbody tr').each(function(row,tr){
            invoiceTableData[row]={
                "invoiceName":$(tr).find('td:eq(0)').find('input').val(),
                "invoiceSort":$(tr).find('td:eq(0)').find('input').attr('sort'),
                "invoiceSplit":$(tr).find('td:eq(1)').find('input').val(),
                "invoiceAmount":$(tr).find('td:eq(2)').find('input').val()
            };
        });
        invoiceTableData.shift();
        var invoiceTableArray = invoiceTableData;

        //console.log(invoiceTableArray);

        var evaluationID = $('#invoiceModal #invoiceEvaluationId').text();
        var bidNumberInput = $('[name="bidNumber"]').val();

        elem.loader.show();
        $.ajax({
            url: window.fx_url.BASE + 'send-bid.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                evaluationID: evaluationID,
                bidNumber: bidNumberInput,
                bidAcceptanceName: $('#invoiceModal [name="bidAcceptanceName"]').val(),
                bidAcceptanceAmount: $('#invoiceModal [name="bidAcceptanceTotal"]').val(),
                projectCompleteName: $('#invoiceModal [name="projectCompleteName"]').val(),
                projectCompleteAmount: $('#invoiceModal #projectCompleteTotal').text(),
                bidAcceptanceSplit: $('#invoiceModal [name="invoiceSplitBidAcceptance"]').val(),
                projectCompleteSplit: $('#invoiceModal #invoiceSplitProjectComplete').text(),
                bidSubTotal: '',
                bidTotal: $('#bidTotal').text(),
                invoiceArray: invoiceTableArray,
                customEvaluation: 'true',
                bidItemID: null

            },
            success: (response) => {

                //Open Bend Sent Modal
                if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
                    $('#bidSentModal').foundation('open');
                }

                $('#invoiceModal .invoiceTable tbody tr').not(':first').remove();

                $('#invoiceModal #invoiceEvaluationId').text('');

                $('input[name="invoiceSplitBidAcceptance"]').val(state.bid_acceptance);
                $('input[name="bidAcceptanceTotal"]').val('');

                $('#invoiceSplitProjectComplete').text(state.project_complete);
                $('#projectCompleteTotal').text('');

                $('#evaluationList').empty();

                //Get Project Evaluations
                projectEvaluations();

                // //Get Billing info
                // populateBillingInfo();
                elem.loader.hide();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    }

    //Send Customer Final Report
    var prepareFinalReport = function() {
        var parent = $(this).parent().parent().parent();
        var idToSend = parent.attr('evaluationId');
        var custom = parent.attr('custom');

        elem.loader.show();
        var warrantyModal = $('#finalWarrantiesModal');
        var warrantySelect = warrantyModal.find('select[name="warrantyID"]');
        var warrantyList = warrantyModal.find('.warrantyList');

        warrantyModal.find('.idToSend').val(idToSend);
        warrantyModal.find('.custom').val(custom);
        $.ajax({
            url: window.fx_url.BASE + 'getWarranty.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            success: function (response) {
                function openWarrantyModal() {
                    warrantyModal.foundation('open');
                    elem.loader.hide();
                }

                if (response !== null) {
                    $.each(response, function (i, item) {
                        warrantySelect.append(`<option value="${item.warrantyID}">${item.name}</option>`);
                    });

                    $.ajax({
                        url: window.fx_url.BASE + 'getEvaluationWarranty.php',
                        dataType: "json",
                        type: "POST",
                        contentType: "application/x-www-form-urlencoded",
                        data: {
                            evaluationID: idToSend
                        },
                        success: function (response) {
                            if (response != null) {
                                $.each(response, function (i, item) {
                                    warrantyList.append(`<span class="no-margin" data-id="${item.warrantyID}">${item.warrantyName} <a>&#x2715</a></span>`);
                                    warrantySelect.find(`option[value="${item.warrantyID}"]`).attr('disabled', true);
                                });
                            }
                            openWarrantyModal();
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                            console.log(jqXHR.responseText);
                        }
                    });
                } else {
                    openWarrantyModal();
                }
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    };

    var sendFinalReport = () => {
        var warrantyModal = $('#finalWarrantiesModal');
        var idToSend = warrantyModal.find('.idToSend').val();

        var custom = true;
        if (warrantyModal.find('.custom').val() === 'false') {
            custom = false;
        }

        warranties = [];
        warrantyModal.find('.warrantyList span').each(function() {
            warranties.push($(this).data('id'));
        });

        var emailSendModal = $('#emailSentModal');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'final-email.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    projectID: state.id,
                    evaluationID: idToSend,
                    email: 'send',
                    custom: custom,
                    warranties: warranties
                },
                success: function (response) {
                    if (response == 'true') {
                        //$('#loading-image').hide();
                        $.ajax({
                            url: window.fx_url.BASE + 'finalReportSent.php',
                            dataType: "json",
                            type: "POST",
                            contentType: "application/x-www-form-urlencoded",
                            data: {
                                evaluationID: idToSend,
                                finalReportSentByID: window.project_data.user.id
                            },
                            success: function (response) {
                                if (response == 'true') {
                                    $('#evaluationList').empty();

                                    //Get Project Evaluations
                                    projectEvaluations();

                                    elem.loader.hide();
                                    closeFinalWarrantiesModal();
                                    emailSendModal.find('h3').text('Warranties Sent');
                                    emailSendModal.find('p.modalContent').html('Your warranties have been sent.');
                                    emailSendModal.foundation('open');
                                }
                            }, error: function (jqXHR, textStatus, errorThrown) {
                                console.log(textStatus, errorThrown);
                                console.log(jqXHR.responseText);
                            }
                        });
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    var closeFinalWarrantiesModal = function() {
        var warrantyModal = $('#finalWarrantiesModal');
        var warrantySelect = warrantyModal.find('select[name="warrantyID"]');
        warrantySelect.find('option').not(':first').remove();
        warrantyModal.find('.idToSend').val('');
        warrantyModal.find('.custom').val('');
        warrantyModal.find('.warrantyList').empty();
        warrantyModal.foundation('close');
    };

    var addWarranty = function(e) {
        var warrantyModal = $('#finalWarrantiesModal');
        var select = $(e.currentTarget);
        var option = select.find('option:selected');

        warrantyModal.find('.warrantyList').append(`<span class="no-margin" data-id="${select.val()}">${$(option).text()} <a>&#x2715</a></span>`);
        option.attr('disabled', true);
        select.val('');
    };

    var removeWarranty = function(e) {
        var warrantyModal = $('#finalWarrantiesModal');
        var warranty = $(e.currentTarget).parent();
        var warrantyID = warranty.data('id');

        warrantyModal.find(`select option[value="${warrantyID}"]`).prop('disabled', false);
        warranty.remove();
    };

    //Resend Final Report
    var resendFinalReportEmail = () => {
        var idToResend = $(this).parent().parent().attr('evaluationId');
        if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'final-email.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    projectID: state.id,
                    evaluationID: idToResend,
                    email: 'resend'
                },
                success: function (response) {
                    if (response == 'true') {

                        elem.loader.hide();
                        $('#emailSentModal h3').text('Warranties Resent');
                        $('#emailSentModal p.modalContent').html('Your warranties have been resent to ' + project_data.customer.first_name + ' ' + project_data.customer.last_name + ' at ' + project_data.customer.email + '.');
                        $('#emailSentModal').foundation('open');
                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

//Edit Evaluation Name
    var editEvaluationName = function(){
        var idToEdit = $(this).parent().parent().parent().parent().parent().attr('evaluationId');
        $('#editEvaluationNameModal .idToEdit').val(idToEdit);

        $.ajax({
            url: window.fx_url.BASE + 'getEvaluation.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                evaluationID: idToEdit
            },
            success: function (response) {
                if (response != '') {
                    //console.log(response);
                    $('#editEvaluationNameModal input[name="evaluationDescription"]').val(response.evaluationDescription);
                }

                $('#editEvaluationNameModal').foundation('open');

            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });

    };

//Save Evaluation Name
    var saveEvaluationName = function(){
        var idToEdit = $('#editEvaluationNameModal .idToEdit').val();
        var evaluationDescription = $('#editEvaluationNameModal input[name="evaluationDescription"]').val();
        //console.log(idToEdit);

        if (evaluationDescription == '') {
            $('#editEvaluationNameModal input[name="evaluationDescription"]').parent().addClass('is-invalid-label');
            $('#editEvaluationNameModal input[name="evaluationDescription"]').addClass('is-invalid-input');
            $('#editEvaluationNameModal input[name="evaluationDescription"]').parent().find('.form-error').addClass('is-visible');

        } else {
            $('#editEvaluationNameModal input[name="evaluationDescription"]').parent().removeClass('is-invalid-label');
            $('#editEvaluationNameModal input[name="evaluationDescription"]').removeClass('is-invalid-input');
            $('#editEvaluationNameModal input[name="evaluationDescription"]').parent().find('.form-error').removeClass('is-visible');

            elem.loader.show();
            $.ajax({
                url: window.fx_url.BASE + 'evaluation-edit-name.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    evaluationID: idToEdit,
                    evaluationDescription: evaluationDescription
                },
                success: function (response) {
                    if (response == 'true') {

                        $('#Evaluation'+idToEdit+'.callout').find('h5 span').eq(0).text(evaluationDescription);

                        // $('#evaluationList').empty();

                        // //Get Project Evaluations
                        // projectEvaluations();

                        elem.loader.hide();
                        $('#editEvaluationNameModal').foundation('close');

                    }

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });
        }
    };

    var closeEditEvaluationModal = function(){
        $('#editEvaluationNameModal .idToEdit').val('');
        $('#editEvaluationNameModal input[name="evaluationDescription"]').val('');

        $('#editEvaluationNameModal').foundation('close');
    };
})(e$);
