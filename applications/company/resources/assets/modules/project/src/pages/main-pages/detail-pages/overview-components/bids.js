'use strict';

const Component = require('@ca-package/router/src/component');
const Number = require('@cac-js/utils/number');

const bids_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/bids.hbs');
const table_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/bids/table.hbs');
const item_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/bids/item.hbs');

const Statuses = {
    IN_PROGRESS: 1,
    FINALIZED: 2,
    SUBMITTED: 3,
    CANCELLED: 4,
    ACCEPTED: 5,
    REJECTED: 6
};

class Bids extends Component {
    /**
     * Set status of legacy bid based on dates
     *
     * @param {object} data
     * @returns {number}
     */
    status(data) {
        if (data.evaluationCancelled != null) {
            return Statuses.CANCELLED;
        }
        if (data.bidAccepted != null) {
            return Statuses.ACCEPTED;
        }
        if (data.bidRejected != null) {
            return Statuses.REJECTED;
        }
        if (data.bidFirstSent != null) {
            return Statuses.FINALIZED;
        }
        if (data.submittedAt != null) {
            return Statuses.SUBMITTED;
        }
        return Statuses.IN_PROGRESS;
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        await super.load(request);
        this.elem.bids.empty();
        let status_map = new Map([
            [Statuses.IN_PROGRESS, '<span class="h-text t-yellow">In Progress</span>'],
            [Statuses.FINALIZED, '<span class="h-text t-blue">Finalized</span>'],
            [Statuses.SUBMITTED, '<span class="h-text t-blue">Submitted</span>'],
            [Statuses.CANCELLED, '<span class="h-text t-grey">Cancelled</span>'],
            [Statuses.ACCEPTED, '<span class="h-text t-green">Accepted</span>'],
            [Statuses.REJECTED, '<span class="h-text t-red">Rejected</span>']
        ]);

        let that = this;
        $.ajax({
            url: window.fx_url.BASE + 'getProjectEvaluations.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectID: request.params.project_id
            },
            success: (response) => {
                if (response !== null) {
                    this.elem.table = $(table_tpl());
                    this.elem.table_body = this.elem.table.fxFind('table-body');
                    this.elem.no_bids.hide();
                    $.each(response, (i, item) => {
                        let total = item.bidTotal === null ? '--' : `${Number.toCurrency(item.bidTotal)}`;
                        that.elem.table_body.append(item_tpl({
                            name: item.evaluationDescription,
                            total,
                            status: status_map.get(this.status(item))
                        }));
                    });
                    this.elem.bids.append(this.elem.table);
                }
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request) {
        this.elem.no_bids.show();
        this.elem.bids.empty();
        await super.unload(request);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.root.show();
        this.elem.bids = this.elem.root.fxFind('bids-list');
        this.elem.no_bids = this.elem.root.fxFind('no-bids');
    };

    /**
     * Render component
     */
    render() {
        return bids_tpl();
    };
}

module.exports = Bids;
