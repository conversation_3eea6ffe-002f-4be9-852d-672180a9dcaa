'use strict';

const Api = require('@ca-package/api');

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-project-tpl/modals/main/project/bid/detail/disable_follow_ups.hbs');

class Disable_follow_ups extends Confirm {
    constructor() {
        super();
        this.setTitle('Disable Follow-Ups');
        this.setContent(content_tpl());
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.bid_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({bid_id, promise}) {
        this.state.bid_id = bid_id;
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        let data = {
            follow_up_notification_status: Api.Constants.BidItems.FollowUpNotificationStatus.DISABLED
        };

        Api.Resources.BidItems().partialUpdate(this.state.bid_id, data).then((data) => {
            this.resetWorking();
            this.state.promise.resolve(true);
            this.close();
        }, () => {
            this.showErrorMessage('Unable to disable follow up notifications for this bid');
            this.resetWorking();
        });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
        this.close();
    };
}

module.exports = Disable_follow_ups;
