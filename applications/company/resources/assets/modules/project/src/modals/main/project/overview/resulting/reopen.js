'use strict';

/**
 * @type {module:Modal.Legacy}
 */
const Modal = require('@ca-submodule/modal').Legacy;

const Api = require('../../../../../api');

const modal_reopen_tpl = require('@cam-project-tpl/modals/main/project/overview/resulting/reopen.hbs');

class Reopen extends Modal {
    constructor() {
        super(Modal.Size.TINY, false, modal_reopen_tpl());
        this.state = {};
        this.elem.close = this.elem.root.fxFind('close');
        this.elem.submit = this.elem.root.fxFind('save');

        this.elem.close.on('click.fx', (e) => {
            e.preventDefault();
            this.close();
            return false;
        });

        this.elem.submit.on('click.fx', (e) => {
            e.preventDefault();
            this.save();
            return false;
        });
    };

    /**
     * Open modal
     *
     * @param {number} id
     */
    open(id) {
        this.state.id = id;
        super.open();
    };

    /**
     * Save data
     */
    save() {
        this.elem.submit.attr('disabled', true);
        Api.Resources.Projects().partialUpdate(
            this.state.id, {
                'status': Api.Constants.Projects.Status.ACTIVE
            }).then((entity, response) => {
            this.close();
            this.fire('saved');
        }, (data) => {
            //let error = data.data;
        });
    };

    /**
     * Close modal
     */
    close() {
        super.close();
        this.elem.submit.attr('disabled', false);
    };
};

module.exports = Reopen;
