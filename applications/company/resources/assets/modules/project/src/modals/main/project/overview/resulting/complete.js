'use strict';

const lang = require('lodash/lang');

const FormValidator = require('@ca-submodule/validator');

/**
 * @type {module:Modal.Legacy}
 */
const Modal = require('@ca-submodule/modal').Legacy;

/**
 * @type {typeof module:FormInput.Base}
 */
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/date_time'));
const Tooltip = require('@ca-submodule/tooltip');


const Api = require('../../../../../api');

const modal_complete_tpl = require('@cam-project-tpl/modals/main/project/overview/resulting/complete.hbs');
const $ = require("jquery");

class Complete extends Modal {
    constructor() {
        super(Modal.Size.TINY, false, modal_complete_tpl());
        Tooltip.initAll(this.elem.root);
        this.state = {};
        this.elem.close = this.elem.root.fxFind('close');
        this.elem.submit = this.elem.root.fxFind('save');
        this.elem.completed = this.elem.root.fxFind('completed');
        this.elem.result = this.elem.root.fxFind('result');

        this.elem.form = this.elem.root.fxFind('form');
        this.elem.fields = this.elem.form.find('input, select');

        this.elem.close.on('click.fx', (e) => {
            e.preventDefault();
            this.close();
            return false;
        });
    };

    loadFormValidator() {
        this.form = FormValidator.init(this.elem.form).on('form:submit', () => {
            this.save();
            return false;
        });

        let fields = {
            completed_on: {
                required: true
            },
            result: {
                required: false
            }
        };

        if (this.state.settings?.result_required) {
            fields.result.required = true
            this.elem.result_optional = this.elem.root.fxFind('result_optional');
            this.elem.result_optional.hide();
        }

        this.state.fields = {};
        for (let item in fields) {
            if (lang.isUndefined(fields[item].requiredMessage)) {
                fields[item].requiredMessage = 'This value is required.';
            }
            this.state.fields[item] = this.elem.fields.filter(`[name="${item}"]`).parsley(fields[item]);
        }

        // Clear previous instance of FormInput if it exists
        if (this.elem.completed.data('fx_input')) {
            this.elem.completed.data('fx_input').destroy();
        }

        FormInput.init(this.elem.completed);
    }


    /**
     * Open modal
     *
     * @param {number} id
     */
    open(id) {
        this.state.id = id;
        this.loadResults();
    };

    /**
     * Load results
     */
    loadResults() {
        this.elem.result.empty();
        this.elem.result.append(`<option value="">-- Select One --</option>`);

        this.getProjectInfo().then((data) => {
            this.state.settings = data.settings ?? {};
            this.state.results = data.results ?? [];
            this.state.results.forEach((result) => {
                this.elem.result.append(`<option value="${result.id}">${result.name}</option>`);
            });

            this.loadFormValidator();
            super.open();
        });
    }


    /**
     * Get project info
     * @returns {Promise<*|null>}
     */
    async getProjectInfo() {
        try {
            return await $.ajax({
                url: `${window.fx_url.API}project/info` + `?project_id=${this.state.id}`,
                type: Api.Request.Method.GET,
                contentType: "application/x-www-form-urlencoded",
            });

        } catch (e) {
            console.error(e);
            return null;
        }
    }

    /**
     * Save data
     */
    save() {
        let data = {
            status: Api.Constants.Projects.Status.CLOSED,
            completed_on: this.elem.completed.data('fx_input').getIsoFormat(),
        };

        const result_value = this.elem.form.fxFind('result').val();
        if (result_value) {
            data.result_type_id = result_value;
        }

        this.elem.submit.attr('disabled', true);

        Api.Resources.Projects().partialUpdate(this.state.id, data).then((entity, response) => {
            this.close();
            this.fire('saved');
        }, (data) => {
            let error = data.data;
            console.error(error);
            this.elem.submit.attr('disabled', false);
        });
    }

    /**
     * Reset form
     */
    formReset() {
        this.elem.form[0].reset();
        this.form.reset();
    };

    /**
     * Clear form
     */
    clearForm() {
        this.state.fields = {};
        this.elem.completed.data('fx_input').clear();
        this.elem.submit.attr('disabled', false);
        this.formReset();
    };

    /**
     * Close modal
     */
    close() {
        this.clearForm();
        super.close();
    };
};

module.exports = Complete;
