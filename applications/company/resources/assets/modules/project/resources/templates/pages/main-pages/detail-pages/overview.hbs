<div class="c-pcc-component">
    <div class="c-pccc-loading" data-js="loader"></div>
    <div class="row expanded">
        <div class="small-12 medium-12 large-8 columns no-pad general-info-border">
            <div class="row expanded">
                <div class="small-12 medium-7 columns small-order-2 medium-order-1 large-order-1">
                    <div class="c-pccc-subcomponent" data-js="component" data-name="property_image"></div>
                </div>
                <div class="small-12 medium-5 columns small-order-1 medium-order-2 large-order-2">
                    <div class="c-pccc-subcomponent" data-js="component" data-name="info"></div>
                </div>
            </div>

            <div data-js="note-section" class="row expanded">
                <div class="c-pccc-subcomponent" data-js="component" data-name="notes"></div>
            </div>
        </div>
        <div class="small-12 medium-12 large-4 columns general-info-padding">
            <div class="medium-12 columns">
                <hr class="show-for-small hide-for-large">
                <strong>Project Overview</strong><br/>
                <a data-js="overview-link" target="_blank" href="">Download PDF</a>
                <hr>
                <strong>Tasks</strong><a class="subcomponent-action t-hidden" data-js="add-task" href="" target="_blank">Add Task</a><br/>
                <span data-js="task-link"></span>
                <hr>
                <div class="c-pccc-subcomponent" data-js="component" data-name="schedule"></div>
                <hr>
                <div class="c-pccc-subcomponent" data-js="component" data-name="files"></div>
                <div class="c-pccc-subcomponent" data-js="component" data-name="drawings"></div>
                <hr>
                <div class="c-pccc-subcomponent" data-js="component" data-name="wisetack"></div>
                <div class="c-pccc-subcomponent" data-js="component" data-name="bids"></div>
                <div class="c-pccc-subcomponent" data-js="component" data-name="billing"></div>
                {{#unless hide_project_costs}}
                <div class="c-pccc-subcomponent" data-js="component" data-name="costs"></div>
                <hr>
                {{/unless}}
                <div class="c-pccc-subcomponent" data-js="component" data-name="resulting"></div>
            </div>
        </div>
    </div>
</div>