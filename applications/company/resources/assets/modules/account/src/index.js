'use strict';

const SubscriptionSection = require('./sections/subscription');
const PaymentMethodsSection = require('./sections/payment_methods');

const account_tpl = require('@cam-account-tpl/account.hbs');
const sidemenu_item_tpl = require('@cam-account-tpl/sidemenu_item.hbs');

class Account {
    constructor(layout, root) {
        this.layout = layout;
        this.elem = {root};
        this.sections = {
            subscription: {
                label: 'Subscription',
                section: new SubscriptionSection(this)
            },
            payment_methods: {
                label: 'Payment Methods',
                section: new PaymentMethodsSection(this)
            }
        };
        this.section_slugs = {
            'payment-methods': 'payment_methods'
        };
        for (let slug in this.section_slugs) {
            this.sections[this.section_slugs[slug]].slug = slug;
        }

        this.active = null;
        this.boot();
    };

    showSection(section, update_url = true) {
        let $section = this.sections[section];

        //Start gif Loader
        this.elem.loader.show();

        //Add Active to New Side Menu Item
        $section.sidemenu.addClass('active');
        if (update_url) {
            this.updateUrlSection(section);
        }

        //Remove Active Class From Side Menu Item
        if (this.active !== null) {
            this.sections[this.active].sidemenu.removeClass('active');
        }

        //If Section Has Not Been Booted
        if (!$section.booted) {
            // Render Item on Content Wrapper
            $section.elem = $($section.section.render());
            this.elem.content.append($section.elem);

            $section.section.on('booted', () => {
                //Hide Items after Rendered and Booted
                $section.elem.hide();

                //Set Section is Booted
                $section.booted = true;

                $section.section.load();
            }).on('load', () => {
                if (this.active !== null) {
                    this.hideSection(this.active);
                }
                //Hide Loading gif
                this.elem.loader.hide();

                $section.elem.show();
                this.active = section;
            }).on('show-loader', () => {
                this.elem.loader.show();
            }).on('hide-loader', () => {
                this.elem.loader.hide();
            });

            // Boot and Hide Item Elements
            $section.section.boot();
            return;
        }

        $section.section.load();
    };

    hideSection(section) {
        let $section = this.sections[section];
        $section.elem.hide();
        $section.section.unload();
    };

    getUrlSection() {
        return window.location.pathname.split('/').pop();
    };

    updateUrlSection(section) {
        let slug = section;
        if (this.sections[section].slug !== undefined) {
            slug = this.sections[section].slug;
        }
        history.pushState(null, null, '/company/account/'+slug);
    };

    loadFromUrl() {
        let url_section = this.getUrlSection();
        let update_url = true;
        if (typeof this.section_slugs[url_section] !== 'undefined') {
            url_section = this.section_slugs[url_section];
            update_url = false;
        } else if (typeof this.sections[url_section] === 'undefined') {
            url_section = Object.keys(this.sections)[0];
        } else {
            update_url = false;
        }
        if (this.active !== url_section) {
            this.showSection(url_section, update_url);
        }
    };

    setMenuHeight() {
        let window_height = this.window.height();
        let side_menu_height = window_height -  this.elem.footer.height() -  this.elem.header.height();
        this.elem.sidemenu.height(side_menu_height);
        let side_menu_list_area = side_menu_height - this.elem.title.outerHeight(true);

        if (this.menu_list_height > side_menu_list_area) {
            this.elem.sidemenu_list.height(side_menu_list_area);
        } else {
            this.elem.sidemenu_list.height(this.menu_list_height);
        }
    };

    boot() {
        this.elem.root.append(account_tpl({
            loader: `${window.fx_url.assets.IMAGE}ajax-loader.gif`
        }));
        this.layout.setModeWindow();
        this.elem.wrapper = this.elem.root.find('.acct-module');

        this.elem.sidemenu = this.elem.wrapper.find('.side-menu');
        this.elem.sidemenu_list = this.elem.sidemenu.find('ul');
        this.elem.title = this.elem.sidemenu.find('.project-title');
        this.elem.content = this.elem.wrapper.find('.sections');
        this.elem.loader = this.elem.wrapper.find('.loading-image');
        this.window = $(window);
        this.elem.footer = $(document).find('.footer');
        this.elem.header = $(document).find('.top-bar');

        this.elem.content.on('click.fx', '[data-link-section]', (e) => {
            e.preventDefault();
            let section = $(e.currentTarget).data('link-section');
            if (section !== this.active) {
                this.showSection(section);
            }
            return false;
        });

        //Build Menu
        for (let section in this.sections) {
            let $section = this.sections[section];
            $section.sidemenu = $(sidemenu_item_tpl({
                name: $section.label,
                section: section
            }));
            this.elem.sidemenu_list.append($section.sidemenu);
        }

        this.menu_list_height = this.elem.sidemenu_list.height();

        // On window resize
        this.window.on('resize.fx', (e) => {
            this.setMenuHeight();
        });

        this.window.trigger('resize.fx');

        let that = this;

        //On menu click
        this.elem.sidemenu.on('click.fx', 'a', function (e) {
            e.preventDefault();
            let section = $(this).data('section');
            if (section !== that.active) {
                that.showSection(section);
            }
            return false;
        });

        //On Page Load
        this.loadFromUrl();

        //On URL change
        $(window).on('popstate', () => {
            this.loadFromUrl();
        });
    };
}

module.exports = Account;
