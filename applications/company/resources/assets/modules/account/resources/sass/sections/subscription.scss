@use '~@cac-sass/base';

.section[data-id="subscription"] {
    h5 {
        margin-top: 1rem;
        small {
            color: #000000;
        }
    }
    .adjustment-section {
        background-color: #F0F0F0;
        padding: .5rem 0 .5rem 1rem;
        margin-bottom: 1rem;
    }
    h6 {
        &.adjustment-type {
            font-weight: bold;
        }
    }
    .option-name {
        font-size: 1.5625rem;
    }
    .row {
        .columns {
            .custom-callout {
                &.t-yellow {
                    @include base.callout-warning;
                }
                &.t-red {
                    @include base.callout-error;
                }
                &.t-blue {
                    @include base.callout-info;
                }
                &.t-grey {
                    @include base.callout-info;
                    background-color: base.$color-background-form;
                    color: base.$color-grey-dark-1;
                    border-color: rgba(92, 111, 133, 15%);
                    a {
                        color: base.$color-grey-dark-1;
                    }
                }
                &.t-hidden {
                    display: none;
                }
            }
            .hidden {
                display: none;
            }
        }
        .subscription-option {
            padding: 0.5rem;
            border-radius: 5px;
            border: 1px solid black;
            height: 100%;
            cursor: pointer;
            position: relative;
            &.selected {
                background-color: #F0F0F0;
            }
            &[disabled] {
                border: 1px solid #eaeaea;
                cursor: unset;
            }
            .message {
                position: absolute;
                bottom: 0;
                text-align: center;
                width: 100%;
                margin-left: -.5rem;
                display:block;
                padding-bottom: .3rem;
                &.current {
                    color: #929598;
                    font-size: .9rem;
                    font-style: italic;
                }
                &.disabled {
                    color: #B94A48;
                    font-size: .8rem;
                    line-height: 1.2;
                }
            }
            .subscription-adjustment-wrapper {
                padding: 0rem 0 .5rem 1rem;
                &:last-of-type {
                    margin-bottom: 2rem;
                }
            }
        }
    }
    .users-included {
        font-size: 1rem;
    }
    .subscription-price {
        text-align: center;
        margin-top: .5rem;
        &.monthly {
            margin-bottom: 2.35rem;
        }
    }
    .subscription-price.interval-length {
        margin-bottom: 0;
    }
    .adjustment-title {
        font-style: normal;
        font-size: .9rem;
        color: #000000;
    }
    .capitalize {
        text-transform: capitalize;
    }
    .subscription-option {
        margin-bottom: 1.5rem;
        ul {
            list-style-type: circle;
            font-size: .95rem;
            margin-bottom: 0;
        }
    }
    .button {
        margin-top: 1rem;
        &.save {
            margin-top: .5rem;
        }
        &.hidden {
            display: none;
        }
        &.invoice {
            margin: .25rem;
        }
        &.statement {
            margin: .25rem;
        }
    }
    .error {
        color: #B94A48;
        display:none;
        &.show {
           display: block;
        }
    }
    .cancelled {
        color: base.$color-red-default;
        font-style: normal;
    }
    .credit {
        color: base.$color-green-default;
        font-style: normal;
        font-weight: bold;
    }
    .button-group {
        .button {
            font-size: .7rem;
            padding: 0.5em 1.5em;
        }
    }
    .cost-summary {
        display: none;
        .callout.due-callout {
            @include base.callout-success;
        }
        .price {
            font-weight: bold;
        }
    }
    .invoice-wrapper {
        width: 100%;
    }
    .payment-method-message {
        font-style: italic;
    }
}

.ach-authorization {
   span {
       font-size: 1rem;
       font-style: normal;
       color: #000000;
    }
}
