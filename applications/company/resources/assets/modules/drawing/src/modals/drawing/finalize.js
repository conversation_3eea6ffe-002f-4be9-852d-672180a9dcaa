'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const DrawingRepo = require('../../repositories/drawing');
const DrawingEntity = require('../../entities/drawing');

const log = require('../../log');

const modal_tpl = require('@cam-drawing-tpl/modals/drawing/finalize.hbs');

/**
 * @memberof module:Drawing/Modals/Drawing
 */
class Finalize extends Confirm {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} drawing
     */
    constructor(drawing) {
        super();
        Object.assign(this.state, {drawing});
        this.setTitle('Finalize Drawing');
        this.setContent(modal_tpl());

        this.on('close', () => {
            this.resetWorking();
        });
    };

    /**
     * Open modal
     *
     * @param {object} promise
     */
    open({promise}) {
        this.state.promise = promise;
        super.open();
    };

    /**
     * Handle 'yes' answer
     */
    handleYes() {
        this.startWorking();
        this.state.drawing.save_handler.promise.then(async () => {
            await DrawingRepo.partialUpdate(this.state.drawing.id, {
                status: DrawingEntity.Status.FINALIZED,
                sync_status: DrawingEntity.SyncStatus.PENDING
            });
            // set synced status to false (without updating DB since we just did that) so when the drawing panel
            // unloads a drawing push will occur if user is online
            this.state.drawing.setSynced(false, false);
            this.close();
            this.state.promise.resolve(true);
        }).catch((error) => {
            this.showErrorMessage('Unable to finalize drawing');
            log.error('Unable to finalize drawing', {error});
        });
    };

    /**
     * Handle 'no' answer
     */
    handleNo() {
        this.close();
        this.state.promise.resolve(null);
    };
}

module.exports = Finalize;
