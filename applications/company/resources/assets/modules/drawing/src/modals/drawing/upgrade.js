'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const Error = require('../../error');

const upgrade = require('../../entity-upgrades/drawing');
const log = require('../../log');

const modal_tpl = require('@cam-drawing-tpl/modals/drawing/upgrade.hbs');

/**
 * @memberof module:Drawing/Modals/Drawing
 */
class Upgrade extends Confirm {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} drawing
     */
    constructor(drawing) {
        super();
        Object.assign(this.state, {drawing});
        this.setTitle('Drawing Upgrade Required');
        this.setContent(modal_tpl());

        this.on('close', () => {
            this.resetWorking();
        });
    };

    /**
     * Open modal
     *
     * @param {module:Drawing/Entities.Drawing} entity
     * @param {{resolve: function, reject: function}} promise
     */
    open({entity, promise}) {
        Object.assign(this.state, {entity, promise});
        super.open();
    };

    /**
     * Handle 'yes' answer
     */
    handleYes() {
        this.startWorking();
        upgrade.run(this.state.entity).then((entity) => {
            this.state.promise.resolve(entity);
            this.close();
        }, (error) => {
            this.showErrorMessage('Unable to upgrade drawing');
            log.error('Unable to upgrade drawing', {error});
        });
    };

    /**
     * Handle 'no' answer
     */
    handleNo() {
        this.close();
        this.state.promise.reject(new Error.Entity.UpgradeAborted);
    };
}

module.exports = Upgrade;
