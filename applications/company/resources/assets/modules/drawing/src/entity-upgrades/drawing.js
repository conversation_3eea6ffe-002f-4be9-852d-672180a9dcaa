'use strict';

const DrawingRepo = require('../repositories/drawing');
const NodeEntity = require('../entities/drawing/node');
const LineNode = require('../pages/main-pages/drawing-components/nodes/line');
const Upgrade = require('../entity_upgrade');

let upgrade = new Upgrade;
upgrade.version(
    2,
    /**
     * Upgrade drawing to version 2
     *
     * @param {module:Drawing/Entities.Drawing} drawing
     * @returns {module:Drawing/Entities.Drawing}
     */
    async (drawing) => {
        let dimension_types = [NodeEntity.Type.LINE_DIMENSION_MAIN, NodeEntity.Type.LINE_DIMENSION_ATTACHMENT],
            stamp_types = [
                NodeEntity.Type.SOIL_ANCHOR, NodeEntity.Type.DEADMAN, NodeEntity.Type.INTERIOR_PIER,
                NodeEntity.Type.EXTERIOR_PIER, NodeEntity.Type.CARBON_FIBER, NodeEntity.Type.SUPPORT_POST,
                NodeEntity.Type.WALL_BRACE, NodeEntity.Type.WALL_CRACK
            ],
            line_types = [
                NodeEntity.Type.BEAM, NodeEntity.Type.EXTERIOR_DRAIN, NodeEntity.Type.INTERIOR_DRAIN_TILE,
                NodeEntity.Type.INTERIOR_WALL, NodeEntity.Type.FOUNDATION
            ],
            node_type_map = [];
        // build id to type mapping for use later on
        drawing.nodes.forEach((node) => {
            node_type_map[node.id] = node.type;
        });
        // loop through node list, remove dimension nodes from attachments, update line data structure, remove line
        // to line attachments which don't exist anymore
        let nodes = [];
        for (let node of drawing.nodes) {
            if (dimension_types.indexOf(node.type) !== -1) {
                continue;
            }
            // if node is line type we do some clean up work
            if (line_types.indexOf(node.type) !== -1) {
                let data = node.data;
                data.style = LineNode.Style.SOLID;
                data.dimension_enabled = {
                    main: !!data.show_dimension_main,
                    attachment: !!data.show_dimension_attachment
                };
                delete data.show_dimension_main;
                delete data.show_dimension_attachment;
                data.dimension_data = {};

                let attachments = node.attachments;
                if (attachments !== null) {
                    let attachment_nodes = [];
                    for (let attachment of attachments.nodes) {
                        let [node_id, attach_data] = attachment,
                            node_type = node_type_map[node_id];
                        if (dimension_types.indexOf(node_type) !== -1) {
                            continue;
                        }
                        if (line_types.indexOf(node_type) !== -1) {
                            continue;
                        }
                        // if attachment is stamp type, we update tagging since it was standardized and is now
                        // required for line splitting and joining
                        if (stamp_types.indexOf(node_type) !== -1) {
                            attach_data.tags = ['stamp'];
                        }
                        attachment_nodes.push(attachment);
                    }
                    attachments.nodes = attachment_nodes;
                }
            }
            nodes.push(node);
        }
        drawing.version = 2;
        drawing.nodes = nodes;
        await DrawingRepo.store(drawing, false);
        return drawing;
    }
);

module.exports = upgrade;
