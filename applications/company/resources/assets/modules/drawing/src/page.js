/**
 * @module Drawing/Pages
 */

'use strict';

const RouterPage = require('@ca-package/router/src/page');

/**
 * Base class for pages
 *
 * @memberof module:Drawing/Pages
 * @abstract
 */
class Page extends RouterPage {
    /**
     * Get main page
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages.Main}
     */
    get controller() {
        return this.router.main_route;
    };

    /**
     * Show page
     */
    show() {
        this.elem.root.addClass('t-top t-active');
        if (this.router.current_route.is_initial) {
            this.elem.root.addClass('t-no-animate');
        }
    };

    /**
     * Hide page
     */
    hide() {
        this.elem.root.addClass('t-bottom').removeClass('t-top t-active t-no-animate');
    };
}

module.exports = Page;
