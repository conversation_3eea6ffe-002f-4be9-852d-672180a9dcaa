'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class SprinklerLine extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, SprinklerLine.Type.SPRINKLER_LINE);
        Object.assign(this.state, {
            label: 'Sprinkler Line',
            icon: 'module--drawing--tools--sprinkler-line',
            node_type: Node.Entity.Type.SPRINKLER_LINE,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = SprinklerLine;
