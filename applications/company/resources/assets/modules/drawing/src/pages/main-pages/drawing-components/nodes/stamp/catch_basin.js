'use strict';

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class CatchBasin extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: CatchBasin.Entity.Type.CATCH_BASIN,
            tool_type: Tool.Type.CATCH_BASIN,
            handles: Object.assign(this.properties.handles, {
                scale: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 1})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let rect = new this.paper.ps.Path.Rectangle({
            size: this.properties.sizes.template,
            strokeWidth: 1,
            strokeColor: '#000',
            fillColor: '#fff',
            insert: false
        });
        // 'C' shape as path so it will scale properly
        let letter_c = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [[21.8,27.4],[-0.3,1.4],[0,0]],[[24,28],[0,0],[-0.5,1.8]],[[21.5,32.1],[1.2,-0.9],[-1.2,0.9]],
                [[17.1,33.5],[1.8,0],[-1.8,0]],[[12.8,32.4],[1.1,0.7],[-1.1,-0.7]],[[10.3,29.2],[0.5,1.4],[-0.6,-1.4]],
                [[9.4,24.8],[0,1.5],[0,-1.7]],[[10.4,20.3],[-0.7,1.3],[0.6,-1.3]],[[13.2,17.4],[-1.2,0.6],[1.2,-0.7]],
                [[17.2,16.4],[-1.4,0],[1.7,0]],[[21.4,17.7],[-1.2,-0.9],[1.1,0.8]],[[23.8,21.3],[-0.5,-1.6],[0,0]],
                [[21.6,21.8],[0,0],[-0.4,-1.2]],[[19.9,19.2],[0.8,0.5],[-0.7,-0.5]],[[17.1,18.4],[1.2,0],[-1.3,0]],
                [[13.9,19.3],[0.9,-0.6],[-0.9,0.6]],[[12.1,21.8],[0.3,-1.1],[-0.3,1]],[[11.6,25],[0,-1.1],[0,1.4]],
                [[12.2,28.7],[-0.4,-1.1],[0.4,1]],[[14.1,31.1],[-0.9,-0.6],[0.9,0.5]],[[16.9,31.9],[-1,0],[1.2,0]],
                [[20,30.8],[-0.8,0.7],[1,-1]]
            ],
            closed: true,
            fillRule: 'evenodd'
        });
        // 'B' shape as path so it will scale properly
        let letter_b = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [26.6,33.2],[26.6,16.7],[[32.8,16.7],[0,0],[1.3,0]],[[35.8,17.2],[-0.7,-0.3],[0.8,0.3]],
                [[37.6,18.7],[-0.4,-0.6],[0.4,0.7]],[[38.2,20.9],[0,-0.8],[0,0.7]],[[37.6,22.9],[0.4,-0.6],[-0.4,0.6]],
                [[35.9,24.4],[0.7,-0.4],[1,0.3]],[[38.2,25.9],[-0.5,-0.7],[0.5,0.7]],[[39,28.4],[0,-1],[0,0.8]],
                [[38.5,30.6],[0.3,-0.7],[-0.3,0.7]],[[37.3,32.1],[0.5,-0.3],[-0.5,0.4]],
                [[35.5,32.9],[0.7,-0.2],[-0.7,0.2]],[[32.8,33.2],[1.1,0],[0,0]]
            ],
            closed: true,
            fillRule: 'evenodd'
        });
        // 'B' fill as path so it will scale properly
        let letter_b_2 = new this.paper.ps.Path({
            fillColor: [255, 255, 255],
            insert: false,
            segments: [
                [28.8,23.6],[[32.4,23.6],[0,0],[1,0]],[[34.5,23.4],[-0.4,0.1],[0.6,-0.2]],
                [[35.8,22.6],[-0.3,0.4],[0.3,-0.4]],[[36.2,21.2],[0,0.5],[0,-0.5]],[[35.8,19.8],[0.3,0.4],[-0.3,-0.4]],
                [[34.7,18.9],[0.5,0.2],[-0.5,-0.2]],[[32.2,18.7],[1.2,0],[0,0]],[28.9,18.7],[28.9,23.6]
            ],
            closed: true,
            fillRule: 'evenodd'
        });
        // 'B' fill as path so it will scale properly
        let letter_b_3 = new this.paper.ps.Path({
            fillColor: [255, 255, 255],
            insert: false,
            segments: [
                [28.8,31.2],[[32.9,31.2],[0,0],[0.7,0]],[[34.4,31.1],[-0.3,0.1],[0.5,-0.1]],
                [[35.7,30.6],[-0.4,0.3],[0.3,-0.2]],[[36.5,29.7],[-0.2,0.4],[0.2,-0.4]],[[36.8,28.3],[0,0.5],[0,-0.6]],
                [[36.3,26.7],[0.3,0.4],[-0.3,-0.4]],[[35,25.7],[0.5,0.2],[-0.5,-0.2]],[[32.6,25.4],[1,0],[0,0]],
                [28.8,25.4]
            ],
            closed: true,
            fillRule: 'evenodd'
        });
        return new this.paper.ps.Group({
            children: [rect, letter_c, letter_b, letter_b_2, letter_b_3]
        });
    };
}

module.exports = CatchBasin;
