'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Furnace extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Furnace.Type.FURNACE);
        Object.assign(this.state, {
            label: 'Furnace',
            icon: 'module--drawing--tools--furnace',
            node_type: Node.Entity.Type.FURNACE
        });
    };
}

module.exports = Furnace;
