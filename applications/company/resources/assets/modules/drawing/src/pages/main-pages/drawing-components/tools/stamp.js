/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */

'use strict';

const Base = require('./base');
const Event = require('../event');
const Angle = require('../utils/angle');

const HandleMoveAngleMap = {
    top_left: {contract: [0, 90], expand: [180, 270]},
    top_center: {contract: [45, 135], expand: [225, 315]},
    top_right: {contract: [90, 180], expand: [270, 0]},
    left_center: {contract: [315, 45], expand: [135, 225]},
    right_center: {contract: [135, 225], expand: [315, 45]},
    bottom_left: {contract: [270, 0], expand: [90, 180]},
    bottom_center: {contract: [225, 315], expand: [45, 135]},
    bottom_right: {contract: [180, 270], expand: [0, 90]}
};

/**
 * Base stamp tool class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools
 * @abstract
 */
class Stamp extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     * @param {number} type
     */
    constructor(controller, type) {
        super(controller, type);
        Object.assign(this.state, {
            accept_events: [Event.Type.DOWN, Event.Type.MOVE, Event.Type.UP, Event.Type.TAP],
            pivot_point: null,
            last_vector: null,
            start_rotation: null,
            angle_change: 0,
            duplicate_offset: [20, 20]
        });
    };

    /**
     * Set tool mode
     *
     * @param {number} mode
     */
    setMode(mode) {
        switch (mode) {
            case Base.Mode.CREATE:
            case Base.Mode.UPDATE:
                this.controller.interaction.down_hit_test = false;
                break;
            case Base.Mode.IDLE:
                this.controller.interaction.down_hit_test = true;
                break;
        }
        super.setMode(mode);
    };

    /**
     * Handle tap event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractTap(event) {
        if (this.mode === Base.Mode.CREATE) {
            let node = this.controller.paper.createNode(this.state.node_type, {
                position: event.point.round()
            });
            node.commit();
            this.clearTool();
            this.setAsPrevTool();
        } else if (this.mode === Base.Mode.UPDATE) {
            this.clearTool();
        }
    };

    /**
     * Handle down event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractDown(event) {
        if (this.mode === Base.Mode.UPDATE) {
            let result = this.state.node.hitTest(event.point);
            if (result !== null && result.type === 'handle') {
                switch (result.handle) {
                    case 'rotate':
                        this.state.action = Base.Action.ROTATE;
                        this.state.pivot_point = this.node.position;
                        this.state.last_vector = event.point.subtract(this.state.pivot_point);
                        this.state.start_rotation = this.node.rotation;
                        break;
                    case 'top_left':
                    case 'top_right':
                    case 'bottom_left':
                    case 'bottom_right':
                        this.state.action = Base.Action.SCALE;
                        let {contract, expand} = HandleMoveAngleMap[result.handle],
                            rotation = Angle.to360(this.node.rotation);
                        this.state.angles = {
                            contract: {
                                min: Angle.add(contract[0], rotation),
                                max: Angle.add(contract[1], rotation)
                            },
                            expand: {
                                min: Angle.add(expand[0], rotation),
                                max: Angle.add(expand[1], rotation)
                            }
                        };
                        this.state.curr_size = this.node.size.clone();
                        break;
                }
                return;
            }
            this.state.action = Base.Action.MOVE;
            this.setMovePoints({
                position: this.node.position
            });
        }
    };

    /**
     * Handle move event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractMove(event) {
        if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                let delta = this.move(event);
                if (delta === false) {
                    return;
                }
                this.node.move(this.getMovePoints(), delta);
            } else if (this.action === Base.Action.ROTATE) {
                let vector = event.point.subtract(this.state.pivot_point),
                    delta = vector.angle - this.state.last_vector.angle;
                this.state.angle_change += delta;
                let angle = Angle.add(this.state.start_rotation, this.state.angle_change);
                let sign = Math.sign(angle);
                angle = Math.round(Math.abs(angle) / 15) * 15;
                if (sign !== 0) {
                    angle *= sign;
                }
                this.node.update({
                    rotation: angle
                });
                this.state.last_vector = vector;
            } else if (this.action === Base.Action.SCALE) {
                let delta = this.getMoveDelta(event);
                if (delta.length === 0) {
                    return;
                }
                let angle = Angle.to360(delta.angle),
                    sign = null;
                if (Angle.between(angle, this.state.angles.contract.min, this.state.angles.contract.max)) {
                    sign = -1;
                } else if (Angle.between(angle, this.state.angles.expand.min, this.state.angles.expand.max)) {
                    sign = 1;
                }
                if (sign === null) {
                    return;
                }
                let abs_delta = delta.abs(),
                    change = Math.max(abs_delta.x, abs_delta.y) * sign;
                this.state.curr_size.width += change;
                this.state.curr_size.height += change;
                this.node.update({
                    size: this.node.getSize(this.state.curr_size.width, this.state.curr_size.height)
                });
            }
        }
    };

    /**
     * Handle up event
     */
    onInteractUp() {
        if (this.mode === Base.Mode.UPDATE) {
            switch (this.action) {
                case Base.Action.MOVE:
                    this.clearMoveData();
                    this.node.commit();
                    break;
                case Base.Action.ROTATE:
                    this.state.pivot_point = null;
                    this.state.last_vector = null;
                    this.state.start_rotation = null;
                    this.state.angle_change = 0;
                    this.node.commit();
                    break;
                case Base.Action.SCALE:
                    this.clearMoveData();
                    this.state.angles = null;
                    this.state.curr_size = null;
                    break;
            }
            this.state.action = null;
        }
    };

    /**
     * Handle node duplication
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Stamp} node
     */
    onDuplicateNode(node) {
        node.update({
            position: node.position.add(this.state.duplicate_offset)
        });
        super.onDuplicateNode(node);
    };
}

module.exports = Stamp;
