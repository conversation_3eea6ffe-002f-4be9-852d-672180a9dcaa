'use strict';

const LineDimension = require('../line_dimension');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineDimension
 */
class Main extends LineDimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Main.Type.LINE_DIMENSION_MAIN);
        Object.assign(this.state, {
            allow_zero_distance: true
        });
    };
}

module.exports = Main;
