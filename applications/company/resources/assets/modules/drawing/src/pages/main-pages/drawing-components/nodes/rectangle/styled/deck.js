'use strict';

const Styled = require('../styled');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle/Styled
 */
class Deck extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Deck.Entity.Type.DECK,
            tool_type: Tool.Type.DECK,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                resize: true,
                rotate: true
            }),
            show_dimensions: true,
            // label: 'Deck'
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: 'rgba(138, 73, 21, 0.5)'
        });
        return state;
    };
}

module.exports = Deck;
