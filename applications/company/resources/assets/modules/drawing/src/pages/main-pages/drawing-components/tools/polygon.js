'use strict';

const Event = require('../event');
const Base = require('./base');
const Node = require('../nodes/base');
const ConfigPanel = require('../config-panels/base');
const Angle = require('../utils/angle');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Rectangle
 */
class Polygon extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Polygon.Type.POLYGON);
        Object.assign(this.state, {
            label: 'Polygon',
            icon: 'module--drawing--tools--polygon',
            node_type: Node.Entity.Type.POLYGON
        });
    };


    // onMouseDown(event) {
    //     // Add a segment to the path at the position of the mouse:
    //     this.state.path.add(event.point);
    // }

    /**
     * Handle down event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    // onInteractDown(event) {
    //     switch (this.mode) {
    //         case Base.Mode.CREATE:
    //             // this.node.addPoint(event.point);
    //             break;
    //         case Base.Mode.UPDATE:
    //             switch (this.action) {
    //                 case Base.Action.MOVE:
    //                     this.node.commit();
    //                     this.clearMoveData();
    //                     break;
    //                 case Base.Action.ROTATE:
    //                     this.state.pivot_point = null;
    //                     this.state.last_vector = null;
    //                     this.state.start_rotation = null;
    //                     this.state.angle_change = 0;
    //                     this.node.commit();
    //                     break;
    //             }
    //             this.state.action = null;
    //             break;
    //     }
    // };

    // /**
    //  * Create rectangle node
    //  *
    //  * Default stairs are designed to be 3ft wide and 16 10" treads.
    //  *
    //  * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
    //  * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
    //  */
    // createNode(point) {
    //     let paper = this.controller.paper,
    //         node = paper.createNode(this.state.node_type, {point}, false);
    //     node.update({
    //         size: Size.of([paper.getPixelsFromUnit({feet: 3}), 16 * node.tread_length])
    //     });
    //     return node;
    // };
}

module.exports = Polygon;
