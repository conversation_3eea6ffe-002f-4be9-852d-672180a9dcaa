'use strict';

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Ac extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Ac.Entity.Type.AC,
            tool_type: Tool.Type.AC,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                rotate: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 2})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let rect = new this.paper.ps.Path.Rectangle({
                size: this.properties.sizes.template,
                radius: 6,
                strokeWidth: 1,
                strokeColor: '#000',
                fillColor: '#fff',
                insert: false
            });
        // 'A' shape as path so it will scale properly
        let letter_a = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [9, 33.473],
                [15.598, 16.293],
                [18.047, 16.293],
                [25.078, 33.473],
                [22.488, 33.473],
                [20.484, 28.27],
                [13.301, 28.27],
                [11.414, 33.473]
            ],
            closed: true
        });
        // 'A' fill as path so it will scale properly
        let letter_a_2 = new this.paper.ps.Path({
            fillColor: [255, 255, 255],
            insert: false,
            segments: [
                [13.957, 26.418],
                [19.781, 26.418],
                [[17.988, 21.66], [0, 0], [-0.547, -1.445]],
                [[16.77, 18.098], [0.265, 0.929], [-0.22299, 1.11573]],
                [[15.844, 21.378], [0.3934, -1.06762], [0, 0]]
            ],
            closed: true
        });
        // 'C' shape as path so it will scale properly
        let letter_c = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [[39.152, 27.449], [-0.313, 1.446], [0, 0]],
                [[41.426, 28.023], [0, 0], [-0.477, 1.868]],
                [[38.854, 32.295], [1.238, -0.981], [-1.239, 0.98]],
                [[34.313, 33.765], [1.789, 0], [-1.852, 0]],
                [[29.795, 32.635], [1.16, 0.754], [-1.16, -0.754]],
                [[27.146, 29.359], [0.606, 1.43], [-0.605, -1.43]],
                [[26.238, 24.754], [0, 1.641], [0, -1.79]],
                [[27.264, 20.072], [-0.684, 1.332], [0.683, -1.332]],
                [[30.182, 17.037], [-1.262, 0.692], [1.261, -0.691]],
                [[34.348, 16], [-1.516, 0], [1.718, 0]],
                [[38.684, 17.313], [-1.172, -0.876], [1.171, 0.875]],
                [[41.133, 21.003], [-0.461, -1.585], [0, 0]],
                [[38.895, 21.531], [0, 0], [-0.399, -1.25]],
                [[37.16, 18.801], [0.758, 0.57], [-0.758, -0.57]],
                [[34.3, 17.945], [1.15, 0], [-1.32, 0]],
                [[30.99, 18.895], [0.887, -0.633], [-0.886, 0.632]],
                [[29.121, 21.443], [0.359, -1.066], [-0.35889, 1.06313]],
                [[28.582, 24.743], [-0.00197, -1.12207], [0, 1.46]],
                [[29.221, 28.568], [-0.426, -1.089], [0.425, 1.09]],
                [[31.207, 31.012], [-0.898, -0.539], [0.8794, 0.5336]],
                [[34.125, 31.82], [-1.02862, 0.00527], [1.273, 0]],
                [[37.359, 30.719], [-0.882, 0.734], [0.883, -0.735]]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [rect, letter_a, letter_a_2, letter_c]
        });
    };
}

module.exports = Ac;
