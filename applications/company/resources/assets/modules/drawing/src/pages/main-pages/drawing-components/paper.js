/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Paper
 */

'use strict';

const paper = require('paper');
const cloneDeep = require('lodash/cloneDeep');

const Number = require('@cac-js/utils/number');

const Node = require('./nodes/base');
const DrawingEntity = require('../../../entities/drawing');
const Length = require('./utils/length');
const PubSub = require('../../../pubsub');

const Layers = {
    BASE: 1,
    BACKGROUND: 2,
    PROJECT: 3,
    BACKGROUND_STAMP: 4,
    AREA: 5,
    LINE: 6,
    LINE_SEGMENT: 7,
    LINE_DIMENSION: 8,
    LINE_OPENING: 9,
    LINE_CONNECTOR: 10,
    STAMP: 11,
    FREEFORM_LINE: 12,
    TEXT: 13,
    SELECTED: 14,
    GUIDE: 15,
    OVERLAY: 16
};
const PdfInfo = {
    image_container: {
        width: 806,
        height: 604
    }
};

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents
 * @mixes Event
 * @mixes Observable
 */
class Paper {
    /**
     * Constructor
     */
    constructor() {
        this.state = {
            ps: null,
            entity: null,
            nodes: new Map,
            node_cache: [],
            storage: new Map,
            layer_config: [
                {
                    name: 'project',
                    id: Layers.PROJECT,
                    layers: [
                        {name: 'background_stamp', id: Layers.BACKGROUND_STAMP},
                        {name: 'area', id: Layers.AREA},
                        {name: 'line_dimension', id: Layers.LINE_DIMENSION},
                        {
                            name: 'line',
                            id: Layers.LINE,
                            layers: [
                                {name: 'line_dimension', id: Layers.LINE_DIMENSION},
                                {name: 'line_segment', id: Layers.LINE_SEGMENT},
                                {name: 'line_opening', id: Layers.LINE_OPENING},
                                {name: 'line_connector', id: Layers.LINE_CONNECTOR}
                            ]
                        },
                        {name: 'stamp', id: Layers.STAMP},
                        {name: 'freeform_line', id: Layers.FREEFORM_LINE},
                        {name: 'text', id: Layers.TEXT},
                        {name: 'selected', id: Layers.SELECTED}
                    ]
                }
            ],
            layers: new Map,
            settings: null,
            transfer_last_node_data: true,
            length: null,
            bounds: null,
            pdf_scaling_enabled: true,
            pdf_scale: 1
        };
    };

    /**
     * Get available layers
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Layer() {
        return Layers;
    };

    /**
     * Get paper.js paper scope
     *
     * @readonly
     *
     * @returns {paper}
     */
    get ps() {
        return this.state.ps;
    };

    /**
     * Get paper.js active project
     *
     * @readonly
     *
     * @returns {paper.project}
     */
    get project() {
        return this.state.project;
    };

    /**
     * Get entity
     *
     * @readonly
     *
     * @returns {module:Drawing/Entities.Drawing}
     */
    get entity() {
        return this.state.entity;
    };

    /**
     * Get settings
     *
     * @readonly
     *
     * @returns {{unit: number, pixels_per_unit: number}}
     */
    get settings() {
        if (this.state.settings === null) {
            this.state.settings = {
                unit: this.entity.unit,
                pixels_per_unit: this.entity.pixels_per_unit
            };
        }
        return this.state.settings;
    };

    /**
     * Get storage map
     *
     * @returns {Map<any, any>}
     */
    get storage() {
        return this.state.storage;
    };

    /**
     * Get length helper
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Utils.Length}
     */
    get length() {
        return this.state.length;
    };

    /**
     * Get scale factor based on PDF image container size and project bounds
     *
     * Used to scale text and other elements so they are visible when the SVG is scaled to fit on the PDF.
     *
     * @returns {number}
     */
    get pdf_scale() {
        return this.state.pdf_scale;
    };

    /**
     * Get layer by id
     *
     * @param {number} id
     * @returns {*}
     */
    getLayer(id) {
        return this.state.layers.get(id);
    };

    /**
     * Get pixel count based on unit amount
     *
     * @param {(number|object)} amount
     * @param {boolean} [round=false]
     * @returns {number}
     */
    getPixelsFromUnit(amount, round = false) {
        if (this.settings.unit === DrawingEntity.Unit.FEET) {
            return this.state.length.fromUnits(amount, round);
        }
        throw new Error('Unsupported unit');
    };

    /**
     * Get paper.js size from unit values
     *
     * Converts units to pixels for use in size instance
     *
     * @param {(number|object)} width
     * @param {(number|object|null)} [height=null] - if height is null, width will be used to make square
     * @param {boolean} [round=false]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size}
     */
    getSizeFromUnits(width, height = null, round = false) {
        if (height === null) {
            height = width;
        }
        return new this.ps.Size(this.getPixelsFromUnit(width, round), this.getPixelsFromUnit(height, round));
    };

    /**
     * Get unit count from pixel amount
     *
     * @param {number} amount
     * @param {boolean} [as_string=true]
     * @returns {string|object}
     */
    getUnitsFromPixels(amount, as_string = true) {
        if (this.settings.unit === DrawingEntity.Unit.FEET) {
            return this.state.length[as_string ? 'format' : 'get'](amount);
        }
        throw new Error('Unsupported unit');
    };

    /**
     * Get area in units from width and height
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} size
     */
    getAreaUnitsFromPixels(size) {
        if (this.settings.unit === DrawingEntity.Unit.FEET) {
            return this.state.length.formatArea(size.width, size.height);
        }
        throw new Error('Unsupported unit');
    };

    /**
     * Round pixel amount
     *
     * @param {number} amount
     * @returns {number}
     */
    roundPixelsToUnit(amount) {
        return this.state.length.roundToNearestDivision(amount);
    };

    /**
     * Save node and add to internal cache for later commit
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     */
    saveNode(node) {
        // if persistent node, we trigger a project bounds calculation to update PDF scaling for text, etc.
        this.calculateBounds();
        // skip saving node if instance is already pending save
        if (this.state.node_cache.indexOf(node) !== -1) {
            return;
        }
        this.state.node_cache.push(node);
    };

    /**
     * Get node by id
     *
     * @param {string} node_id - node uuid
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base|null}
     */
    getNode(node_id) {
        let node = this.state.nodes.get(node_id);
        return node !== undefined ? node : null;
    };

    /**
     * Add node to paper cache and optionally render
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {boolean} [render=true] - determines if node is rendered
     */
    addNode(node, render = true) {
        this.state.nodes.set(node.id, node);
        if (render) {
            node.render();
        }
    };

    /**
     * Create node by type and configuration data
     *
     * @param {number} type
     * @param {(object|module:Drawing/Entities/Drawing.Node)} [data={}]
     * @param {boolean} [render=true]
     * @param {boolean} [transfer_data=true]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    createNode(type, data = {}, render = true, transfer_data = true) {
        if (this.state.transfer_last_node_data && transfer_data) {
            let last_node = this.state.storage.get('last_created_node');
            if (last_node !== undefined && last_node.type === type) {
                data = Object.assign(cloneDeep(last_node.entity.data), data);
            }
        }
        let class_name = Node.Classes.get(type),
            node = new class_name(this, data);
        this.addNode(node, render);
        if (this.state.transfer_last_node_data && transfer_data && node.transfer_data) {
            this.state.storage.set('last_created_node', node);
        }
        return node;
    };

    /**
     * Create node from existing entity
     *
     * @param {module:Drawing/Entities/Drawing.Node} entity
     * @param {boolean} [render=true]
     * @param {boolean} [transfer_data=true]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    createNodeFromEntity(entity, render = true, transfer_data = true) {
        return this.createNode(entity.type, entity, render, transfer_data);
    };

    /**
     * Load drawing entity into paper
     *
     * Creates node instances from entities, injects them, initializes, and renders
     *
     * @param {module:Drawing/Entities.Drawing} entity
     * @returns {Promise<void>}
     */
    async load(entity) {
        this.ps.activate();
        this.setup();
        this.state.entity = entity;
        this.state.transfer_last_node_data = false;
        this.state.length = new Length(entity.pixels_per_unit, 12);

        let nodes = entity.nodes;
        if (nodes !== null && nodes.length > 0) {
            for (let node of nodes) {
                this.createNodeFromEntity(node, false);
            }
            this.initNodes();
            this.renderNodes();
            this.calculateBounds(true);
        }

        this.state.transfer_last_node_data = true;
    };

    /**
     * Initialize all loaded nodes
     *
     * This is done after they are loaded in so nodes can look each up to handle relations
     * and event bindings between each other
     */
    initNodes() {
        for (let node of this.state.nodes.values()) {
            node.init();
        }
        this.commit(true);
    };

    /**
     * Render nodes
     */
    renderNodes() {
        for (let node of this.state.nodes.values()) {
            node.preRender();
        }
        for (let node of this.state.nodes.values()) {
            node.render();
        }
    };

    /**
     * Pull bounds of project
     *
     * @param {boolean} [init=false]
     */
    calculateBounds(init = false) {
        let prev_bounds = this.state.bounds,
            bounds = this.getLayer(Layers.PROJECT).bounds;
        if (prev_bounds !== null && prev_bounds.equals(bounds)) {
            return;
        }
        this.state.bounds = bounds;
        this.calculatePdfScale(bounds, init);
    };

    /**
     * Calculate PDF scaling based on defined image container size and current project bounds
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} bounds
     * @param {boolean} [init=false]
     */
    calculatePdfScale(bounds, init = false) {
        if (!this.state.pdf_scaling_enabled) {
            return;
        }
        let step = 0.25,
            width_scale = Number.roundToNearest(bounds.width / PdfInfo.image_container.width, step),
            height_scale = Number.roundToNearest(bounds.height / PdfInfo.image_container.height, step),
            prev_scale = this.state.pdf_scale,
            scale = Math.max(1, width_scale, height_scale).toFixed(4);
        if (prev_scale === scale) {
            return;
        }
        this.state.pdf_scale = scale;
        this.notify('pdf-scale-changed', {
            prev_scale,
            scale: this.state.pdf_scale
        }, init);
    };

    /**
     * Temporary disable PDF scaling while running code which would cause a loop
     *
     * @param {function} callback
     */
    disablePdfScaling(callback) {
        this.state.pdf_scaling_enabled = false;
        callback();
        this.state.pdf_scaling_enabled = true;
    };

    /**
     * Commit all pending node changes
     *
     * @param {boolean} [now=false] - determines if nodes are committed synchronously without timeout
     * @param {boolean} [notify=true]
     */
    commit(now = false, notify = true) {
        if (this.state.node_cache.length === 0) {
            return;
        }
        let nodes = this.state.node_cache;
        this.state.node_cache = [];

        let entities = [];
        for (let node of nodes) {
            if (node.deleted) {
                this.state.nodes.delete(node.id);
            }
            let entity = node.entity;
            if (entity.drawing_id === null) {
                entity.drawing_id = this.entity.id;
            }
            // if entity doesn't current exist in persistent storage, we mark it for future node updates
            if (!entity.existing) {
                // if entity was committed and subsequently deleted before the paper commit happened, we just need
                // to skip the entity entirely. we have some code to prevent this scenario in the node base class, but
                // some use cases require this check as well
                if (entity.is_deleted) {
                    continue;
                }
                entity.existing = true;
            }
            entities.push(entity);
        }

        PubSub.Handler[now ? 'publishSync' : 'publish'](PubSub.Topics.Node.SAVE, {
            entities,
            now
        });
        if (notify) {
            this.emit('commit', {
                nodes
            });
        }
    };

    /**
     * Clear all nodes and commit
     */
    clear() {
        if (this.state.nodes.size === 0) {
            return;
        }
        for (let node of this.state.nodes.values()) {
            node.delete(false);
        }
        this.commit();
    };

    /**
     * Create layers from config and assign to defined parent
     *
     * Designed to be used recursively to handle nested layers
     *
     * @param {{name: string, id: number, layers: (array|undefined)}[]} layers
     * @param {object} parent - paper.js layer instance
     */
    createLayers(layers, parent) {
        for (let {name, id, layers: sublayers = []} of layers) {
            let layer = new this.ps.Layer({name, parent});
            layer.bringToFront();
            this.state.layers.set(id, layer);
            if (sublayers.length > 0) {
                this.createLayers(sublayers, layer);
            }
        }
    };

    /**
     * Setup paper using paper.js project
     *
     * Create all necessary layers
     */
    setup() {
        this.state.layers.set(Layers.BASE, this.state.project.activeLayer);
        this.createLayers(this.state.layer_config, this.state.project.activeLayer);
    };

    /**
     * Reset paper completely
     *
     * Required to be called if switching drawings
     */
    reset() {
        Object.assign(this.state, {
            entity: null,
            settings: null,
            length: null,
            bounds: null,
            pdf_scaling_enabled: true,
            pdf_scale: 1
        });
        this.state.nodes.clear();
        this.state.storage.clear();
        this.state.layers.clear();
        this.state.project.clear();
        this.clearEvents();
        this.unsubscribeAll();
    };

    /**
     * Boot paper
     *
     * @param {HTMLCanvasElement} canvas
     */
    boot(canvas) {
        let ps = new paper.PaperScope();
        ps.setup(canvas);

        this.state.ps = ps;
        this.state.project = ps.project;
    };
}

require('@cac-js/mixins/event')(Paper);
require('@cac-js/mixins/observable')(Paper);

module.exports = Paper;
