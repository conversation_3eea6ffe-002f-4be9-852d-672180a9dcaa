'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class DryWell extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, DryWell.Type.DRY_WELL);
        Object.assign(this.state, {
            label: 'Dry Well',
            icon: 'module--drawing--tools--dry-well',
            node_type: Node.Entity.Type.DRY_WELL
        });
    };
}

module.exports = DryWell;
