/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Tools/LineDimension
 */

'use strict';

const Base = require('./base');
const Event = require('../event');
const Angle = require('../utils/angle');
const LineDimensionNode = require('../nodes/line_dimension');

/**
 * Base line dimension tool class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools
 * @abstract
 */
class LineDimension extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     * @param {number} type
     */
    constructor(controller, type) {
        super(controller, type);
        Object.assign(this.state, {
            config_panel_type: null,
            accept_events: [Event.Type.DOWN, Event.Type.MOVE, Event.Type.UP, Event.Type.TAP],
            line_angle: null,
            right_range: null,
            left_range: null,
            min_distance: null,
            allow_zero_distance: false
        });
    };

    /**
     * Set tool mode
     *
     * @param {number} mode
     */
    setMode(mode) {
        switch (mode) {
            case Base.Mode.UPDATE:
                this.controller.interaction.down_hit_test = false;
                break;
            case Base.Mode.IDLE:
                this.controller.interaction.down_hit_test = true;
                break;
        }
        super.setMode(mode);
    };

    /**
     * Handle tap event
     */
    onInteractTap() {
        if (this.mode === Base.Mode.UPDATE) {
            this.clearTool();
        }
    };

    /**
     * Handle down event
     */
    onInteractDown() {
        if (this.mode === Base.Mode.UPDATE) {
            this.state.action = Base.Action.MOVE;
            let line = this.node.line;
            this.state.line_angle = line.to.subtract(line.from).angle;
            this.state.right_range = [Angle.to360(this.state.line_angle + 45), Angle.to360(this.state.line_angle + 135)];
            this.state.left_range = [Angle.to360(this.state.line_angle - 135), Angle.to360(this.state.line_angle - 45)];
            this.state.min_distance = this.node.getMinDistance();
            this.state.distance = this.node.distance;
        }
    };

    /**
     * Handle move event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractMove(event) {
        if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                let delta = this.getMoveDelta(event);
                if (delta.length === 0) {
                    return;
                }
                let angle = Angle.to360(delta.angle),
                    side,
                    distance = null;
                if (Angle.between(angle, ...this.state.right_range)) {
                    distance = delta.length;
                    side = LineDimensionNode.Side.RIGHT;
                } else if (Angle.between(angle, ...this.state.left_range)) {
                    distance = delta.length;
                    side = LineDimensionNode.Side.LEFT;
                }
                if (distance !== null) {
                    let multiplier = 1;
                    // if we are going the opposite direction, we want to subtract
                    if (this.node.side !== side) {
                        multiplier = -1;
                    }
                    this.state.distance = this.state.distance + (distance * multiplier);
                    distance = Math.abs(this.state.distance);
                    let min_distance = this.state.min_distance,
                        distance_valid = distance >= min_distance,
                        side_update = false;
                    if (
                        multiplier === -1 &&
                        (
                            (this.state.allow_zero_distance && this.state.distance <= 0) ||
                            (!this.state.allow_zero_distance && this.state.distance < this.state.min_distance)
                        )
                    ) {
                        this.node.update({
                            side: this.node.side === LineDimensionNode.Side.RIGHT ? LineDimensionNode.Side.LEFT : LineDimensionNode.Side.RIGHT
                        }, distance_valid, false);
                        this.state.min_distance = this.node.getMinDistance(true);
                        this.state.distance = this.state.allow_zero_distance ? 0 : this.state.min_distance;
                        side_update = true;
                    }
                    if (!distance_valid) {
                        distance = this.state.allow_zero_distance ? 0 : this.state.min_distance;
                    }
                    if (side_update || distance !== this.node.distance) {
                        this.node.update({
                            distance,
                            user_interaction: distance !== min_distance
                        });
                    }
                }
            }
        }
    };

    /**
     * Handle up event
     */
    onInteractUp() {
        if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                this.state.line_angle = null;
                this.state.right_range = null;
                this.state.left_range = null;
                this.state.min_distance = null;
                this.state.distance = null;
                this.clearMoveData();
                this.node.commit();
            }
            this.state.action = null;
        }
    };
}

module.exports = LineDimension;
