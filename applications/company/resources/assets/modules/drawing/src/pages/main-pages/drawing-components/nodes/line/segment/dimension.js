/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment/Dimension
 */

'use strict';

const Segment = require('../segment');

const EntityTypes = Segment.Entity.Type;

const DimensionNameMap = {
    main: EntityTypes.LINE_DIMENSION_MAIN,
    attachment: EntityTypes.LINE_DIMENSION_ATTACHMENT
};
const TrackNodeTypes = [
    EntityTypes.INTERIOR_PIER, EntityTypes.EXTERIOR_PIER, EntityTypes.DEADMAN, EntityTypes.SOIL_ANCHOR,
    EntityTypes.CARBON_FIBER, EntityTypes.WALL_BRACE, EntityTypes.WALL_CRACK, EntityTypes.SUPPORT_POST,
    EntityTypes.DOOR, EntityTypes.WINDOW, EntityTypes.GATE, EntityTypes.GARAGE_DOOR, EntityTypes.DOUBLE_DOOR,
    EntityTypes.SLIDING_DOOR, EntityTypes.DOUBLE_GATE, EntityTypes.BASEMENT_WINDOW_WELL,
    EntityTypes.EGRESS_WINDOW_WELL, EntityTypes.FENCE_POST, EntityTypes.SPRINKLER_HEAD, EntityTypes.SINGLE_CANTILEVER,
    EntityTypes.DOUBLE_CANTILEVER, EntityTypes.OPENING
];

/**
 * Base class for all lines with dimensions
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment
 * @abstract
 */
class Dimension extends Segment {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            force_show_dimensions: {
                main: null,
                attachment: null
            }
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            dimension_enabled: {
                main: true,
                attachment: true
            },
            dimension_data: {},
            dimension_nodes: {},
            tracked_nodes: []
        });
        return state;
    };

    /**
     * Load data into state
     *
     * Fixes issue with empty dimension data coming from API as array instead of object.
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        if (Array.isArray(state.dimension_data)) {
            state.dimension_data = {};
        }
        return state;
    };

    /**
     * Get dimension enabled settings
     *
     * @returns {{main: boolean, attachment: boolean}}
     */
    get dimension_enabled() {
        return this.state.dimension_enabled;
    };

    /**
     * Get dimension data
     *
     * @returns {object}
     */
    get dimension_data() {
        return this.state.dimension_data;
    };

    /**
     * Get line of side
     *
     * @param {number} side
     * @returns {{from: module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point, to: module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}}
     */
    getSideLine(side) {
        let angle = 90 * side,
            vector = this.vector.normalize(this.half_width).rotate(angle);
        return {
            from: this.from.add(vector),
            to: this.to.add(vector)
        };
    };

    /**
     * Get tracked node cache
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base[]}
     */
    get tracked_nodes() {
        return this.state.tracked_nodes;
    };

    /**
     * Create dimension node
     *
     * @param {string} name
     * @param {boolean} [render=true]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.LineDimension}
     */
    createDimensionNode(name, render = true) {
        let node = this.paper.createNode(DimensionNameMap[name], this.getDimensionData(name) || {}, false);
        node.line = this;
        this.state.dimension_nodes[name] = node;
        // @todo render here if needed
        return node;
    };

    /**
     * Get dimension node by name
     *
     * @param {string} name
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.LineDimension}
     */
    getDimensionNode(name) {
        return this.state.dimension_nodes[name];
    };

    /**
     * Remove dimension node by name
     *
     * @param {string} name
     */
    removeDimensionNode(name) {
        let node = this.getDimensionNode(name);
        if (node === undefined) {
            throw new Error(`Unable to find dimension node ${name} to remove`);
        }
        node.delete();
        this.state.dimension_nodes[name] = undefined;
    };

    /**
     * Determine if dimension is enabled
     *
     * @param {string} name
     * @returns {boolean}
     */
    isDimensionEnabled(name) {
        return this.state.dimension_enabled[name];
    };

    /**
     * Set if dimension is enabled
     *
     * @param {string} name
     * @param {boolean} enabled
     */
    setDimensionEnabled(name, enabled) {
        if (this.state.dimension_enabled[name] === enabled) {
            return;
        }
        this.state.dimension_enabled[name] = enabled;
        this.update({
            dimension_enabled: this.state.dimension_enabled
        });
    };

    /**
     * Determine if dimension is visible
     *
     * Checks force settings before checking if dimension is enabled. If attachment dimension, we check if we have
     * any tracked nodes.
     *
     * @param {string} name
     * @returns {boolean}
     */
    isDimensionVisible(name) {
        if (this.properties.force_show_dimensions[name] !== null) {
            return this.properties.force_show_dimensions[name];
        }
        if (name === 'attachment' && this.state.tracked_nodes.length === 0) {
            return false;
        }
        return this.state.dimension_enabled[name];
    };

    /**
     * Get dimension data by name
     *
     * @param {string} name
     * @returns {object|undefined}
     */
    getDimensionData(name) {
        return this.state.dimension_data[name];
    };

    /**
     * Set dimension data by name
     *
     * @param {string} name
     * @param {object} data
     */
    setDimensionData(name, data) {
        this.state.dimension_data[name] = data;
    };

    /**
     * Render specified dimension node
     *
     * @param {string} name
     */
    renderDimensionNode(name) {
        let node = this.getDimensionNode(name);
        if (node === undefined) {
            return;
        }
        node.reload();
    };

    /**
     * Initialize node
     *
     * Loop through attachments, set if attachment dimension is rendered, and build initial cache of dimension nodes
     */
    init() {
        super.init();
        // build initial dimension nodes list
        for (let attachment of this.properties.attached_nodes.values()) {
            if (!this.canTrackAttachment(attachment)) {
                continue;
            }
            this.state.tracked_nodes.push(attachment.node);
        }
        this.createDimensionNode('main', false);
        if (this.state.tracked_nodes.length > 0) {
            this.createDimensionNode('attachment', false);
        }
    };

    /**
     * Force show defined dimension
     *
     * @param {string} name
     * @param {(null|boolean)} visible
     * @param {boolean} [render=false]
     */
    forceShowDimension(name, visible, render = false) {
        this.properties.force_show_dimensions[name] = visible;
        if (render) {
            this.renderDimensionNode(name);
        }
    };

    /**
     * Duplicate node
     *
     * Create main dimension node after duplication is done.
     *
     * @param {boolean} [clear_attachments=true] - determines if attachments are removed before node is made
     * @param {boolean} [render=true]
     * @param {boolean} [notify=true]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    duplicate(clear_attachments = true, render = true, notify = true) {
        let node = super.duplicate(clear_attachments, render, notify);
        node.createDimensionNode('main', render);
        return node;
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        let data = super.entityData();
        return Object.assign(data, {
            dimension_enabled: this.dimension_enabled,
            dimension_data: this.dimension_data
        });
    };

    /**
     * Render attachment dimension node
     *
     * Clears attachment dimension node dimension points, updates distance, and renders.
     */
    renderAttachmentDimensionNode() {
        let dimension_node = this.getDimensionNode('attachment');
        dimension_node.clearDimensionPoints();
        dimension_node.updateDistance(false, false);
        dimension_node.render(true);
    };

    /**
     * Determines if node type can be tracked
     *
     * @param {number} type
     * @returns {boolean}
     */
    canTrackNodeType(type) {
        return TrackNodeTypes.indexOf(type) !== -1;
    };

    /**
     * Determine if attachment can be tracked for use with dimensioning
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @returns {boolean}
     */
    canTrackAttachment({node}) {
        return this.canTrackNodeType(node.type);
    };

    /**
     * Add node to tracked nodes cache
     *
     * If no tracked nodes are present, a new attachment dimension node is created and attached
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {boolean} [reload=true] - render attachment dimension node
     */
    registerNode(node, reload = true) {
        let node_count = this.state.tracked_nodes.length;
        this.state.tracked_nodes.push(node);
        if (node_count === 0) {
            let dimension_node = this.createDimensionNode('attachment', false);
            let main_data = this.getDimensionData('main'),
                update = {
                    distance: dimension_node.getMinDistance()
                };
            if (main_data !== undefined) {
                update.side = main_data.side;
            }
            dimension_node.update(update, true, false);
        } else {
            this.renderAttachmentDimensionNode();
        }
    };

    /**
     * Remove node from tracked node cache
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {boolean} [reload=true] - render attachment dimension node
     */
    unregisterNode(node, reload = true) {
        this.state.tracked_nodes = this.state.tracked_nodes.filter((tracked_node) => tracked_node !== node);
        let node_count = this.state.tracked_nodes.length;
        if (node_count === 0) {
            this.removeDimensionNode('attachment');
        } else if (node_count > 0 && reload) {
            this.renderDimensionNode('attachment');
        }
    };

    /**
     * Attach node
     *
     * Used to track dimension nodes as they are added to keep in cached list for dimensioning nodes to use
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {(null|object)} config
     * @param {object} [config.data={}]
     * @param {array} [config.tags=[]]
     * @param {boolean} [notify=true]
     * @returns {{tags: Array, node: module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base, data: Object}}
     */
    attachNode(node, config = null, notify = true) {
        let attachment = super.attachNode(node, config, notify);
        if (this.canTrackAttachment(attachment)) {
            this.registerNode(attachment.node, notify);
        }
        return attachment;
    };

    /**
     * Update attached node
     *
     * Overloaded to update attachment dimension node
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {object} config
     * @param {(undefined|object)} config.data
     * @param {(undefined|array)} config.tags
     * @param {boolean} [notify=true]
     * @returns {{tags: Array, node: module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base, data: Object}}
     */
    updateNodeAttachment(node, config, notify = true) {
        let attachment = super.updateNodeAttachment(node, config, notify);
        if (notify) {
            this.renderDimensionNode('attachment');
        }
        return attachment;
    };

    /**
     * Detach node
     *
     * Used to remove node from dimension node cache array
     *
     * @param {(module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base|string)} node - node instance or uuid
     * @param {boolean} [notify=true]
     * @returns {{tags: Array, node: module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base, data: Object}}
     */
    detachNode(node, notify = true) {
        let attachment = super.detachNode(node, notify);
        if (this.canTrackAttachment(attachment)) {
            this.unregisterNode(attachment.node, notify);
        }
        return attachment;
    };

    /**
     * Handle event from attached node
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {string} event - event name
     * @param {object} data - event data
     */
    onAttachedNodeEvent(node, event, data) {
        super.onAttachedNodeEvent(node, event, data);
        switch (event) {
            case 'update':
            case 'move':
                // if update is from a dimensioned node, then we render again to update everything
                if (this.canTrackNodeType(node.type)) {
                    this.renderAttachmentDimensionNode();
                }
                break;
        }
    };
}

module.exports = Dimension;
