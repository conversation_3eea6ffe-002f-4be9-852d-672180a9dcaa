'use strict';

const Modal = require('../../../modal');

const ManageDrawingModal = require('../../../modals/manage_drawing');

/**
 * @memberof module:Drawing/Pages/MainPages/ManagerPages
 */
class Create extends Modal {
    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.controller.openManageDrawingModal({
            action: ManageDrawingModal.Action.ADD,
            project_id: request.query.project_id
        }).then((drawing) => {
            if (drawing === null) { // closed or cancelled
                this.router.navigate('manager');
                return;
            }
            this.router.navigate('drawing', {
                drawing_id: drawing.id
            });
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.controller.manage_drawing_modal.externalClose();
        await super.unload(request, next);
    };
}

module.exports = Create;
