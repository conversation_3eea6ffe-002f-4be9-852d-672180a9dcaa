'use strict';

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Electrical extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Electrical.Entity.Type.ELECTRICAL,
            tool_type: Tool.Type.ELECTRICAL,
            handles: Object.assign(this.properties.handles, {
                scale: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 2})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let rect = new this.paper.ps.Path.Rectangle({
                size: this.properties.sizes.template,
                strokeWidth: 1,
                strokeColor: '#000',
                fillColor: '#fff',
                insert: false
            });
        // 'E' shape as path so it will scale properly
        let letter_e = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [
                    17.4,
                    35.2
                ],
                [
                    17.4,
                    15
                ],
                [
                    32,
                    15
                ],
                [
                    32,
                    17.4
                ],
                [
                    20.1,
                    17.4
                ],
                [
                    20.1,
                    23.6
                ],
                [
                    31.2,
                    23.6
                ],
                [
                    31.2,
                    26
                ],
                [
                    20.1,
                    26
                ],
                [
                    20.1,
                    32.9
                ],
                [
                    32.5,
                    32.9
                ],
                [
                    32.5,
                    35.3
                ],
                [
                    17.4,
                    35.3
                ]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [rect, letter_e]
        });
    };
}

module.exports = Electrical;
