'use strict';

const LineOpening = require('../line_opening');
const Node = require('../../nodes/base');
const ConfigPanel = require('../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening
 */
class Window extends LineOpening {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Window.Type.WINDOW);
        Object.assign(this.state, {
            label: 'Window',
            icon: 'module--drawing--tools--window',
            node_type: Node.Entity.Type.WINDOW,
            config_panel_type: ConfigPanel.Type.LINE_OPENING_WIDTH_ONLY,
            side_snap: false
        });
    };
}

module.exports = Window;
