'use strict';

const Dimension = require('../dimension');
const Tool = require('../../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment/Dimension
 */
class ExteriorDrain extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: ExteriorDrain.Entity.Type.EXTERIOR_DRAIN,
            tool_type: Tool.Type.EXTERIOR_DRAIN
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({inches: 6}),
            color: 'rgba(0, 208, 255, 0.5)'
        });
        return state;
    };
}

module.exports = ExteriorDrain;
