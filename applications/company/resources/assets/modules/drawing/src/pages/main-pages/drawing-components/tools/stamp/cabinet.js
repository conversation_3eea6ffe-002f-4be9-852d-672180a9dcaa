'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Cabinet extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Cabinet.Type.CABINET);
        Object.assign(this.state, {
            label: 'Cabinet',
            icon: 'module--drawing--tools--cabinet',
            node_type: Node.Entity.Type.CABINET
        });
    };
}

module.exports = Cabinet;
