'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Shower extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Shower.Type.SHOWER);
        Object.assign(this.state, {
            label: 'Shower',
            icon: 'module--drawing--tools--shower',
            node_type: Node.Entity.Type.SHOWER
        });
    };
}

module.exports = Shower;
