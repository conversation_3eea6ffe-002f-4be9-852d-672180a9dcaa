'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Toilet extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Toilet.Type.TOILET);
        Object.assign(this.state, {
            label: 'Toilet',
            icon: 'module--drawing--tools--toilet',
            node_type: Node.Entity.Type.TOILET
        });
    };
}

module.exports = Toilet;
