'use strict';

const Dimension = require('../dimension');
const Tool = require('../../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment/Dimension
 */
class DimensionLine extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: DimensionLine.Entity.Type.LINE_DIMENSION,
            tool_type: Tool.Type.LINE_DIMENSION
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({inches: 1}),
            color: '#000'
        });
        return state;
    };
}

module.exports = DimensionLine;
