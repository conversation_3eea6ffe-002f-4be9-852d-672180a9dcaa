'use strict';

const Anchor = require('../anchor');
const Node = require('../../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp/Anchor
 */
class Soil extends Anchor {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Soil.Type.SOIL_ANCHOR);
        Object.assign(this.state, {
            label: 'Wall Anchor',
            icon: 'module--drawing--tools--soil-anchor',
            node_type: Node.Entity.Type.SOIL_ANCHOR
        });
    };
}

module.exports = Soil;
