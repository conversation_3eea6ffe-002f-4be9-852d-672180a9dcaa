'use strict';

const Dimension = require('../dimension');
const Tool = require('../../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment/Dimension
 */
class Beam extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Beam.Entity.Type.BEAM,
            tool_type: Tool.Type.BEAM
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: Math.ceil(this.paper.getPixelsFromUnit({inches: 4})),
            color: '#bf0300'
        });
        return state;
    };
}

module.exports = Beam;
