'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class FloorVent extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, FloorVent.Type.FLOOR_VENT);
        Object.assign(this.state, {
            label: 'Floor Vent',
            icon: 'module--drawing--tools--floor-vent',
            node_type: Node.Entity.Type.FLOOR_VENT
        });
    };
}

module.exports = FloorVent;
