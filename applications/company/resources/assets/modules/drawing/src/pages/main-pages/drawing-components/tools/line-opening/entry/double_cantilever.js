'use strict';

const Entry = require('../entry');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening/Entry
 */
class DoubleCantilever extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, DoubleCantilever.Type.DOUBLE_CANTILEVER);
        Object.assign(this.state, {
            label: 'Dou. Cantilever',
            icon: 'module--drawing--tools--double-cantilever',
            node_type: Node.Entity.Type.DOUBLE_CANTILEVER,
            config_panel_type: ConfigPanel.Type.LINE_OPENING_WIDTH_SIDE_ONLY
        });
    };
}

module.exports = DoubleCantilever;
