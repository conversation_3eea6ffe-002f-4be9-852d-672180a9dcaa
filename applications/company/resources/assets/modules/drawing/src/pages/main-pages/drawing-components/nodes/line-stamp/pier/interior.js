'use strict';

const Pier = require('../pier');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineStamp/Pier
 */
class Interior extends Pier {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Interior.Entity.Type.INTERIOR_PIER,
            tool_type: Tool.Type.INTERIOR_PIER,
            fill_color: '#696969',
            text_color: '#fff'
        });
    };
}

module.exports = Interior;
