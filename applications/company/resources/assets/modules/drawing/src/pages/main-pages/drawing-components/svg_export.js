'use strict';

const Paper = require('./paper');

/**
 * SVG exporting library to generate SVG from node data in offscreen canvas
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents
 */
class SvgExport {
    constructor() {
        let paper = new Paper();
        paper.boot([1000, 1000]);

        this.state = {paper};
    };

    /**
     * Get paper instance
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents.Paper}
     */
    get paper() {
        return this.state.paper;
    };

    /**
     * Get Blob of SVG data for drawing
     *
     * Finds bounds of just project related items (not background, etc.) and creates SVG with viewbox at those dimensions.
     * This means all whitespace the user leaves in the drawing is ignored. Allows user to draw anywhere on the canvas and
     * still get a good result.
     *
     * @param {module:Drawing/Entities.Drawing} drawing
     * @returns {Promise<ArrayBuffer>}
     */
    async run(drawing) {
        // load in everything
        await this.paper.load(drawing);

        let layer = this.paper.getLayer(Paper.Layer.PROJECT);
        let bounds = layer.strokeBounds.expand(10);
        bounds.point = [1, 1];
        layer.position = [(bounds.width / 2) + 1, (bounds.height / 2) + 1];
        let svg = this.paper.project.exportSVG({
            bounds: bounds,
            onExport: (item, node) => {
                item.data = {};
                if (item._class === 'PointText') {
                    // fix issue with multiline text not working svg output
                    // @see https://github.com/paperjs/paper.js/issues/988
                    node.textContent = null;
                    for (let i = 0; i < item._lines.length; i++) {
                        let tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');
                        tspan.textContent = item._lines[i];
                        let dy = item.leading;
                        if (i === 0) {
                            dy = 0;
                        }
                        tspan.setAttributeNS(null, 'x', node.getAttribute('x'));
                        tspan.setAttributeNS(null, 'dy', dy);
                        node.appendChild(tspan);
                    }
                }
                return node;
            }
        });
        svg.removeAttribute('width');
        svg.removeAttribute('height');

        this.paper.reset();

        let serializer = new XMLSerializer();
        svg = serializer.serializeToString(svg);
        let encoder = new TextEncoder();
        return encoder.encode(svg).buffer;
    };
}

module.exports = new SvgExport;
