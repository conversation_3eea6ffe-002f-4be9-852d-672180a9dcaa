'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class JointSealant extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, JointSealant.Type.JOINT_SEALANT);
        Object.assign(this.state, {
            label: 'Joint Sealant',
            icon: 'module--drawing--tools--joint-sealant',
            node_type: Node.Entity.Type.JOINT_SEALANT,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = JointSealant;
