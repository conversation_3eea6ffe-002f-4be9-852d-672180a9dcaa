'use strict';

const Number = require('@cac-js/utils/number');

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Scale extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Scale.Entity.Type.SCALE,
            tool_type: Tool.Type.SCALE,
            min_font_size: 12,
            text_offset_factor: 3
        });
        this.updateFontSize(paper.pdf_scale);
        this.paperSubscribe('pdf-scale-changed', 'onPdfScaleChange');
    };

    /**
     * Update font size for text using PDF scale factor
     *
     * @param {number} pdf_scale
     */
    updateFontSize(pdf_scale) {
        this.properties.font_size = Math.max(Math.floor(this.properties.min_font_size * pdf_scale), this.properties.min_font_size);
        this.updateTextOffset(this.properties.font_size);
    };

    /**
     * Update text offset based on offset factor and current font size
     *
     * @param {number} font_size
     */
    updateTextOffset(font_size) {
        this.properties.text_offset = Math.floor(font_size / this.properties.text_offset_factor);
    };

    /**
     * Handle PDF scaling change
     *
     * @param {object} data
     * @param {number} data.scale
     */
    onPdfScaleChange({scale}) {
        this.updateFontSize(scale);
        this.render(true);
    };

    /**
     * Create label for specified distance
     *
     * @param {*} parent - paper.js item
     * @param {number} distance
     */
    createLabel(parent, distance) {
        new this.paper.ps.PointText({
            position: [distance, -this.properties.text_offset],
            content: this.paper.getUnitsFromPixels(distance),
            justification: 'center',
            fillColor: '#000',
            fontFamily: this.properties.font_family,
            fontSize: this.properties.font_size,
            parent
        });
    };

    /**
     * Create box using provided info
     *
     * @param {*} parent - paper.js item
     * @param {[number, number]} point
     * @param {[number, number]} size
     * @param {string} fillColor
     */
    createBox(parent, point, size, fillColor) {
        new this.paper.ps.Path.Rectangle({
            point,
            size,
            fillColor,
            parent
        });
    };

    /**
     * Create paper.js path
     *
     * @returns {*}
     */
    getPath() {
        let pdf_scale = this.paper.pdf_scale / 2,
            units = Math.max(10, Number.roundToNearest(10 * pdf_scale, 10.0)),
            width = this.paper.getPixelsFromUnit(units),
            height = Math.floor(10 * pdf_scale),
            padding = Math.floor(pdf_scale),
            group = new this.paper.ps.Group({
                applyMatrix: false
            });
        // create main box
        this.createBox(group, [0, 0], [width + (padding * 2), height + (padding * 2)], '#000');
        this.createLabel(group, 0);
        this.createLabel(group, width);

        let half = width / 2;
        this.createLabel(group, half);
        this.createBox(group, [half + padding, padding], [half, height], '#fff');

        let per_unit = this.paper.settings.pixels_per_unit,
            unit_count = half / per_unit;
        for (let i = 1; i <= unit_count; i++) {
            if (i === unit_count) {
                break;
            }
            this.createBox(group, [(per_unit * i) + padding, padding], [per_unit, height], i % 2 === 0 ? '#000' : '#fff');
        }
        return group;
    };
}

module.exports = Scale;
