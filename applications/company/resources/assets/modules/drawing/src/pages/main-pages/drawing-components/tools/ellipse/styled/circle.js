'use strict';

const Styled = require('../styled');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Ellipse/Styled
 */
class Circle extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Circle.Type.CIRCLE);
        Object.assign(this.state, {
            label: 'Circle',
            icon: 'module--drawing--tools--circle',
            node_type: Node.Entity.Type.CIRCLE,
            config_panel_type: ConfigPanel.Type.RECTANGLE_COLORS_ONLY
        });
    };
}

module.exports = Circle;
