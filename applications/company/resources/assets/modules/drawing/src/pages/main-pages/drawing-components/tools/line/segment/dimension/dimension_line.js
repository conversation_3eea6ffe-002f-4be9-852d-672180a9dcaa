'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class DimensionLine extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, DimensionLine.Type.LINE_DIMENSION);
        Object.assign(this.state, {
            label: 'Measuring Line',
            icon: 'module--drawing--tools--line',
            node_type: Node.Entity.Type.LINE_DIMENSION,
            config_panel_type: ConfigPanel.Type.FULL_LINE_DIMENSION_ONLY
        });
    };
}

module.exports = DimensionLine;
