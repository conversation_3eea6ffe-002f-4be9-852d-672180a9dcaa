'use strict';

const Paper = require('../../paper');
const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Fireplace extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Fireplace.Entity.Type.FIREPLACE,
            tool_type: Tool.Type.FIREPLACE,
            layer: Paper.Layer.BACKGROUND_STAMP,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                rotate: true
            }),
            sizes: {
                template: new paper.ps.Size([48, 36]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 4})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let segment_1 = new this.paper.ps.Path({
            fillColor: [0.42745, 0.43137, 0.44314],
            strokeWidth: 1,
            strokeColor: '#000',
            insert: false,
            segments: [
                [
                    0.6,
                    14.4
                ],
                [
                    49.5,
                    14.4
                ],
                [
                    49.5,
                    29.9
                ],
                [
                    37.6,
                    29.9
                ],
                [
                    32.8,
                    22.2
                ],
                [
                    17.5,
                    22.2
                ],
                [
                    12.5,
                    29.9
                ],
                [
                    0.6,
                    29.9
                ]
            ],
            closed: true
        });
        let segment_2 = new this.paper.ps.Path({
            fillColor: [255, 255, 255],
            strokeWidth: 1,
            strokeColor: '#000',
            insert: false,
            segments: [
                [
                    6.9,
                    29.8
                ],
                [
                    6.9,
                    36
                ],
                [
                    43.2,
                    36
                ],
                [
                    43.2,
                    29.8
                ]
            ],
            closed: true
        });
        let segment_3 = new this.paper.ps.Path({
            fillColor: [255, 255, 255],
            strokeWidth: 1,
            strokeColor: '#000',
            insert: false,
            segments: [
                [
                    0.6,
                    14.4
                ],
                [
                    49.5,
                    14.4
                ],
                [
                    49.5,
                    29.9
                ],
                [
                    0.6,
                    29.9
                ]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [segment_3, segment_2, segment_1]
        });
    };
}

module.exports = Fireplace;
