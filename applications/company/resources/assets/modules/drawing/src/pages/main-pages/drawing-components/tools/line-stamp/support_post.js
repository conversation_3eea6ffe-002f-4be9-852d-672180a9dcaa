'use strict';

const LineStamp = require('../line_stamp');
const Node = require('../../nodes/base');
const LineStampNode = require('../../nodes/line_stamp');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp
 */
class SupportPost extends LineStamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, SupportPost.Type.SUPPORT_POST);
        Object.assign(this.state, {
            label: 'Support Post',
            icon: 'module--drawing--tools--support-post',
            node_type: Node.Entity.Type.SUPPORT_POST
        });
    };

    /**
     * Get side of line point resides
     *
     * @param {?module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Line} line
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {number}
     */
    getSide(line, point) {
        return LineStampNode.Side.NONE;
    };
}

module.exports = SupportPost;
