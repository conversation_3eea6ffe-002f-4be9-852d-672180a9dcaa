'use strict';

const Entry = require('../entry');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening/Entry
 */
class GarageDoor extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: GarageDoor.Entity.Type.GARAGE_DOOR,
            tool_type: Tool.Type.GARAGE_DOOR,
            door_thickness: Math.ceil(paper.getPixelsFromUnit({inches: 2}))
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({feet: 8}),
            depth: this.paper.getPixelsFromUnit({feet: 7})
        });
        return state;
    };

    /**
     * Calculate node offset based on line and paper.js path info
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {object} path - paper.js path
     * @returns {number}
     */
    calculateNodeOffset(line, path) {
        return Math.floor(path.bounds.height / 2) + line.half_width;
    };

    /**
     * Create paper.js path
     *
     * @returns {*} - paper.js item
     */
    getPath() {
        let door_color = this.line !== null ? this.line.color : '#000000',
            door_size = {
                width: this.state.width,
                depth: this.state.depth,
                thickness: this.properties.door_thickness
            },
            door = new this.paper.ps.Path.Rectangle({
                point: [0, 0],
                size: [door_size.width, door_size.thickness],
                insert: false,
                fillColor: this.selected ? this.properties.selected_color : door_color,
                strokeWidth: 0
            }),
            quarter_door_size = door_size.thickness / 4,
            background = new this.paper.ps.Path.Rectangle({
                point: [0, quarter_door_size],
                size: [door_size.width - quarter_door_size, door_size.depth],
                strokeColor: this.selected ? this.properties.selected_color : '#b4b4b4',
                insert: false,
                dashArray: [8, 2]
            });

        return new this.paper.ps.Group({
            position: [0, 0],
            children: [background, door],
            applyMatrix: false
        });
    };
}

module.exports = GarageDoor;
