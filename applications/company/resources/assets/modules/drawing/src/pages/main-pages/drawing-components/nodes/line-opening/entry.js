/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening/Entry
 */

'use strict';

const LineOpening = require('../line_opening');

const Swings = {
    LEFT_HAND: 1,
    RIGHT_HAND: 2
};

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening
 * @abstract
 */
class Entry extends LineOpening {
    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            swing: Swings.LEFT_HAND
        });
        return state;
    };

    /**
     * Get available swings
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Swings() {
        return Swings;
    };

    /**
     * Get swing
     *
     * @returns {number}
     */
    get swing() {
        return this.state.swing;
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        let data = super.entityData();
        data.swing = this.swing;
        return data;
    };
}

module.exports = Entry;
