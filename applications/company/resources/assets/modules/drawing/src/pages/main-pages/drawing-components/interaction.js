'use strict';

const interact = require('interactjs');

const Event = require('./event');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents
 */
class Interaction {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} panel
     */
    constructor(panel) {
        this.state = {
            panel,
            down_point: null,
            methods: {
                [Event.Type.TAP]: 'onInteractTap',
                [Event.Type.DOWN]: 'onInteractDown',
                [Event.Type.MOVE]: 'onInteractMove',
                [Event.Type.UP]: 'onInteractUp',
                [Event.Type.HOLD]: 'onInteractHold',
                [Event.Type.ZOOM]: 'onInteractZoom',
                [Event.Type.GESTURE_START]: 'onInteractGestureStart',
                [Event.Type.GESTURE_ROTATE]: 'onInteractGestureRotate'
            },
            down_hit_test: true,
            prev_event: null,

            handler_data: {
                down: false,
                down_pointer_id: null,
                down_time: null,
                last_event: null,
                tap: true,
                hold_timer: null,
                moved: false,
                gesture: false,
                gesture_config: {
                    min_angle_change: 5,
                    min_scale_change: 0.015
                },
                gesture_data: {
                    angle: 0,
                    angle_change: 0,
                    scale: 1,
                    scale_change: 0,
                    page: null,
                    event_type: null
                }
            }
        };
    };

    /**
     * Get if pointer is currently recorded as down
     *
     * Can become wrong if mouseup/touchend event happens outside the event tracked area. This flag is used to correct
     * that in the application.
     *
     * @returns {boolean}
     */
    get pointer_down() {
        return this.state.handler_data.down;
    };

    /**
     * Set if generic hit testing should be performed
     *
     * Tools will use this to take over hit testing since each tool can work differently
     *
     * @param {boolean} value
     */
    set down_hit_test(value) {
        this.state.down_hit_test = value;
    };

    /**
     * Get event instance from native event
     *
     * @param {string} type
     * @param {Event} e
     * @param {object} [data={}]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents}
     */
    getEvent(type, e, data = {}) {
        let point = this.state.panel.paper.view.getPointFromEvent(e);
        if (type === Event.Type.DOWN) {
            this.state.down_point = point;
        }
        let event = new Event(type, e, Object.assign({
            point: point,
            down_point: this.state.down_point
        }, data));
        if (type === Event.Type.UP) {
            this.state.down_point = null;
        }
        return event;
    };

    /**
     * Handle event
     *
     * @param {string} type
     * @param {Event} e - native event
     * @param {boolean} [reset=false]
     * @returns {boolean}
     */
    handle(type, e, reset = false) {
        let tool = null;
        if (this.state.panel.current_tool !== null) {
            tool = this.state.panel.current_tool.instance;
        }
        let event = this.getEvent(type, e);
        switch (type) {
            case Event.Type.DOWN:
                if (this.state.down_hit_test) {
                    let results = this.state.paper.project.hitTestAll(event.point, {
                        tolerance: this.state.paper.hit_test_tolerance,
                        fill: true,
                        stroke: true,
                        segments: true
                    });
                    if (results.length > 0) {
                        let nodes = [];
                        for (let result of results) {
                            let node,
                                item = result.item;
                            do {
                                node = item.data.node_instance;
                                item = item.parent;
                                if (item === null) {
                                    break;
                                }
                            } while (node === undefined);
                            if (node === undefined) {
                                continue;
                            }
                            nodes.push({node, result});
                        }
                        let nodes_length = nodes.length;
                        if (nodes_length > 0) {
                            let {node, result} = nodes_length === 1 ? nodes[0] : nodes.sort(({node: node_a}, {node: node_b}) => node_b.hit_test_priority - node_a.hit_test_priority)[0];
                            node = node.handleDownHitTest(result, event);
                            this.state.panel.toggleTool(node.tool_type, true, node);
                            return false;
                        }
                    }
                }
                break;
            case Event.Type.MOVE:
                // require a minimum move distance if the previous event wasn't a move type (meaning this threshold
                // has been past at least once before)
                if (
                    (this.state.prev_event === null || this.state.prev_event.type !== Event.Type.MOVE) &&
                    event.point.subtract(event.down_point).length <= this.state.paper.min_move_distance
                ) {
                    return false;
                }
                break;
        }
        this.state.prev_event = event;
        let method = this.state.methods[type],
            send_to_view = true;
        if (tool !== null) {
            if (reset) {
                tool.interactReset();
            }
            let tool_accept_event = tool.acceptsEvent(type);
            if (tool_accept_event === true) {
                tool[method](event);
            }
            send_to_view = tool_accept_event === false;
        }
        if (send_to_view) {
            if (reset) {
                this.state.paper.view.interactReset();
            }
            if (this.state.paper.view.acceptsEvent(type)) {
                this.state.paper.view[method](event);
            }
        }
        return true;
    };

    /**
     * Handle gesture start
     *
     * Only used with touch devices.
     *
     * @param {object} e - interact.js event
     */
    handleGestureStart(e) {
        e.preventDefault();
        this.state.handler_data.gesture = true;
        this.state.handler_data.gesture_data.angle -= e.angle;
        this.handle(Event.Type.GESTURE_START, e);
    };

    /**
     * Handle gesture move
     *
     * Track scale and angles changes so we can determine if user is zooming or rotating.
     *
     * @param {object} e - interact.js event
     */
    handleGestureMove(e) {
        e.preventDefault();
        let gesture_config = this.state.handler_data.gesture_config,
            gesture_data = this.state.handler_data.gesture_data,
            angle = e.angle + gesture_data.angle,
            scale = e.scale * gesture_data.scale;
        gesture_data.angle_change += e.da;
        gesture_data.scale_change += e.ds;

        // cache page position so it doesn't move as the user moves around
        if (gesture_data.page === null) {
            gesture_data.page = {
                x: e.box.x + (e.box.width / 2),
                y: e.box.y + (e.box.height / 2)
            };
        }

        if (gesture_data.event_type === null) {
            if (Math.abs(gesture_data.angle_change) >= gesture_config.min_angle_change) {
                gesture_data.event_type = Event.Type.GESTURE_ROTATE;
            } else if (Math.abs(gesture_data.scale_change) >= gesture_config.min_scale_change) {
                gesture_data.event_type = Event.Type.ZOOM;
            }
        }
        if (gesture_data.event_type !== null) {
            e.pageX = gesture_data.page.x;
            e.pageY = gesture_data.page.y;
            switch (gesture_data.event_type) {
                case Event.Type.GESTURE_ROTATE:
                    e.fxAngle = angle;
                    break;
                case Event.Type.ZOOM:
                    e.fxScale = scale;
                    e.deltaY = Math.sign(e.ds) * -1;
                    break;
            }
            this.handle(gesture_data.event_type, e);
        }
    };

    /**
     * Handle gesture end
     *
     * Clean up all tracking data.
     *
     * @param {object} e - interact.js event
     */
    handleGestureEnd(e) {
        e.preventDefault();
        Object.assign(this.state.handler_data, {
            gesture: false,
            gesture_data: {
                angle: 0,
                angle_change: 0,
                scale: 1,
                scale_change: 0,
                page: null,
                event_type: null
            }
        });
    };

    /**
     * Handle down
     *
     * @param {object} e - interact.js event
     */
    handleDown(e) {
        let data = this.state.handler_data;
        // if down is still enabled when another down event occurs, this indicates the up event handled outside our
        // event tracking. we need to will call the up handler to things are reset properly
        if (data.down) {
            // if pointer (finger) doesn't match we just ignore it
            if (data.down_pointer_id !== e.pointerId) {
                return;
            }
            // this will only be called in situations where the pointer id doesn't change (like using a mouse)
            this.handleUp(e);
        }
        e.preventDefault();
        data.down_time = performance.now();
        if (this.handle(Event.Type.DOWN, e)) {
            data.down = true;
            data.down_pointer_id = e.pointerId;
            data.hold_timer = setTimeout(() => {
                if (data.moved) {
                    return;
                }
                this.handle(Event.Type.HOLD, e);
            }, 1000);
            data.last_event = e;
        } else {
            // if we are purposefully not handing event (for instance, we ended up selecting a node), we disable
            // tap events temporarily
            data.tap = false;
        }
    };

    /**
     * Handle move
     *
     * @param {object} e - interact.js event
     */
    handleMove(e) {
        e.preventDefault();
        let data = this.state.handler_data;
        if (!data.gesture && data.down && data.down_pointer_id === e.pointerId) {
            if (this.handle(Event.Type.MOVE, e)) {
                if (!data.moved) {
                    data.moved = true;
                    clearTimeout(data.hold_timer);
                }
                data.last_event = e;
            }
        }
    };

    /**
     * Handle up
     *
     * @param {object} e - interact.js event
     */
    handleUp(e) {
        e.preventDefault();
        let data = this.state.handler_data;
        if (data.down) {
            // if pointer (finger) doesn't match the original one, we just ignore it
            if (data.down_pointer_id !== e.pointerId) {
                return;
            }
            clearTimeout(data.hold_timer);
            data.hold_timer = null;
            data.down = false;
            data.down_pointer_id = null;
            this.handle(Event.Type.UP, e);
            if (!data.moved && (performance.now() - data.down_time) < 400 && data.tap) {
                this.handle(Event.Type.TAP, e);
            }
            data.down_time = null;
            data.moved = false;
            data.last_event = null;
        }
        // always reset tap events to enabled
        data.tap = true;
    };

    /**
     * Handle zoom
     *
     * Wheel events do not work well with interact.js so we just use jquery to pass native wheel events to be handled.
     *
     * @param {Event} e - DOM event
     */
    handleZoom(e) {
        this.handle(Event.Type.ZOOM, e);
    };

    /**
     * Fire up event manually using the last recorded down/move event
     */
    fireUpEvent() {
        let data = this.state.handler_data;
        if (data.last_event === null) {
            return;
        }
        this.handleUp(data.last_event);
    };

    /**
     * Boot interaction
     *
     * Configure interact library to handle gestures
     */
    boot() {
        let paper = this.state.paper = this.state.panel.paper;
        interact.addDocument(window.document, {
            events: {passive: false}
        });
        interact(paper.elem.canvas[0])
            .gesturable({
                onstart: this.handleGestureStart.bind(this),
                onmove: this.handleGestureMove.bind(this),
                onend: this.handleGestureEnd.bind(this)
            })
            .on('down', this.handleDown.bind(this))
            .on('move', this.handleMove.bind(this))
            // .on('cancel', (e) => {}) // not used currently
            .on('up', this.handleUp.bind(this));
        paper.elem.canvas[0].addEventListener('wheel', (e) => {
            e.stopPropagation();
            e.preventDefault();
            this.handleZoom(e);
            return false;
        });
        paper.elem.canvas.fxEvent([
            'click', 'dblclick', 'mousedown', 'mouseup', 'touchstart', 'touchmove', 'touchend', 'gesturestart',
            'gestureend', 'gesturechange'
        ], e => e.stopPropagation());
    };
}

module.exports = Interaction;
