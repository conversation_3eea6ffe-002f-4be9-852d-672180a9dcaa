'use strict';

const LineStamp = require('../line_stamp');
const Node = require('../../nodes/line-stamp/wall_crack');
const ConfigPanel = require('../../config-panels/base');
const Angle = require('../../utils/angle');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp
 */
class WallCrack extends LineStamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, WallCrack.Type.WALL_CRACK);
        Object.assign(this.state, {
            label: 'Wall Crack',
            icon: 'module--drawing--tools--wall-crack',
            node_type: Node.Entity.Type.WALL_CRACK,
            config_panel_type: ConfigPanel.Type.WALL_CRACK,
            lengths: {
                min_length: {feet: 1}
            }
        });
    };

    /**
     * Determine if stamp with specified length and point can fit on line and snap position if near edges
     *
     * Kept as separate method for use by config panel to validate length changes.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {number} stamp_length
     * @returns {null|module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    positionOnLine(point, line, stamp_length) {
        let line_length = line.vector.length;
        if (stamp_length > line_length) {
            return null;
        }
        let vector = point.subtract(line.from),
            distance = vector.length,
            half_width = stamp_length / 2,
            from_distance = half_width;
        // if point is to close to from point to fit, we need to move it down the line so it fits
        if (distance < from_distance) {
            point = line.from.add(vector.normalize(from_distance));
        } else {
            // if point is to close to 'to' point to fit, we adjust
            let to_distance = half_width,
                to_threshold = line_length - to_distance;
            if (distance > to_threshold) {
                point = line.from.add(vector.normalize(to_threshold));
            }
        }
        return point;
    };

    /**
     * Fit wall crack on line
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    fitOnLine(line, point) {
        let length = this.node.length;
        if (length === null) {
            return point;
        }
        return this.positionOnLine(point, line, length);
    };

    /**
     * Determine positioning on line using movement delta
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Vector} delta
     * @param {LineAttachmentData} attachment_data
     * @returns {null|module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    getPositionAlongLine(delta, attachment_data) {
        if (this.node.orientation !== Node.Orientation.HORIZONTAL) {
            return super.getPositionAlongLine(delta, attachment_data);
        }
        let angle = Angle.to360(delta.angle),
            position = null;
        for (let direction of ['forward', 'back']) {
            if (!Angle.between(angle, ...attachment_data[direction].range)) {
                continue;
            }
            let vector = attachment_data[direction].vector.normalize(delta.length);
            this.state.line_position = this.state.line_position.add(vector);

            let pos_vector = this.state.line_position.subtract(attachment_data.node.from),
                line_angle = Angle.to360(Math.round(attachment_data.node.vector.angle)),
                line_length = attachment_data.node.vector.length,
                half_length = this.node.length / 2,
                distance = pos_vector.length;
            if (Angle.to360(Math.round(pos_vector.angle)) !== line_angle) {
                distance *= -1;
            }
            if (distance < 0 || distance > line_length) {
                break;
            }

            position = this.state.line_position;

            // if point is to close to from point to fit, we need to move it down the line so it fits
            let adjust_distance = null;
            if (distance < half_length) {
                adjust_distance = half_length;
            } else {
                let to_distance = line_length - half_length;
                if (distance > to_distance) { // if point is to close to 'to' point to fit, we adjust
                    adjust_distance = to_distance;
                }
            }
            if (adjust_distance !== null) {
                position = attachment_data.node.from.add(attachment_data.node.vector.normalize(adjust_distance));
            }
            break;
        }
        return position;
    };
}

module.exports = WallCrack;
