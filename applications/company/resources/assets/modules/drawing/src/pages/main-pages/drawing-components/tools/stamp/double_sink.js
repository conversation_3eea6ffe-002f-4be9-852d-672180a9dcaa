'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class DoubleSink extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, DoubleSink.Type.DOUBLE_SINK);
        Object.assign(this.state, {
            label: 'Double Sink',
            icon: 'module--drawing--tools--double-sink',
            node_type: Node.Entity.Type.DOUBLE_SINK
        });
    };
}

module.exports = DoubleSink;
