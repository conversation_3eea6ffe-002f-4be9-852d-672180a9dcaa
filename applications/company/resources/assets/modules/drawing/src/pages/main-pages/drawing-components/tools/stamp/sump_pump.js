'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class SumpPump extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, SumpPump.Type.SUMP_PUMP);
        Object.assign(this.state, {
            label: 'Sump Pump',
            icon: 'module--drawing--tools--sump-pump',
            node_type: Node.Entity.Type.SUMP_PUMP
        });
    };
}

module.exports = SumpPump;
