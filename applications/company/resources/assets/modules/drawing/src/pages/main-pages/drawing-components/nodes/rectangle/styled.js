/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle/Styled
 */

'use strict';

const Rectangle = require('../rectangle');
const Point = require('../../utils/point');

/**
 * Base class for all rectangles which have style components (color, border width, etc)
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle
 * @abstract
 */
class Styled extends Rectangle {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            show_dimensions: false,
            label: null,
            last_size: null,
            base_font_size: 12,
            base_min_font_size: 8,
            calculated_font_size: null
        });
        this.updateFontSize(paper.pdf_scale);
        this.paperSubscribe('pdf-scale-changed', 'onPdfScaleChange');
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: null,
            border_color: '#000000',
            border_width: 0,
            info_enabled: true
        });
        return state;
    };

    /**
     * Load data into state
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        this.properties.last_size = state.size;
        return state;
    };

    /**
     * Get color
     *
     * @readonly
     *
     * @returns {string}
     */
    get color() {
        return this.state.color;
    };

    /**
     * Get border color
     *
     * @readonly
     *
     * @returns {string}
     */
    get border_color() {
        return this.state.border_color;
    };

    /**
     * Get border width
     *
     * @readonly
     *
     * @returns {number}
     */
    get border_width() {
        return this.state.border_width;
    };

    /**
     * Determines if info should be show for rectangle
     *
     * @returns {boolean}
     */
    get info_enabled() {
        return this.state.info_enabled;
    };

    /**
     * Update font size for text using PDF scale factor
     *
     * @param {number} pdf_scale
     */
    updateFontSize(pdf_scale) {
        this.properties.font_size = Math.max(Math.floor(this.properties.base_font_size * pdf_scale), this.properties.base_font_size);
        this.properties.min_font_size = Math.max(Math.floor(this.properties.base_min_font_size * pdf_scale), this.properties.base_min_font_size);
    };

    /**
     * Handle PDF scaling change
     *
     * @param {object} data
     * @param {number} data.scale
     */
    onPdfScaleChange({scale}) {
        this.updateFontSize(scale);
        this.properties.calculated_font_size = null;
        this.render(true);
    };

    /**
     * Draw rectangle
     *
     * @returns {*} - paper.js item
     */
    drawShape() {
        let group = new this.paper.ps.Group({
            position: this.state.point,
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            visible: this.valid,
            applyMatrix: false
        });
        let config = {
            point: [0, 0],
            size: this.state.size,
            fillColor: this.state.color,
            parent: group
        };
        if (!this.selected && this.state.border_width > 0) {
            config.strokeWidth = this.state.border_width;
            config.strokeColor = this.state.border_color;
        }
        new this.paper.ps.Path.Rectangle(config);

        if (this.properties.show_dimensions && this.info_enabled && this.properties.calculated_font_size !== false) {
            let width = this.paper.getUnitsFromPixels(this.state.size.width),
                height = this.paper.getUnitsFromPixels(this.state.size.height),
                area = this.paper.getAreaUnitsFromPixels(this.state.size),
                text_bounds = this.state.size.multiply(0.8),
                font_size = this.properties.calculated_font_size !== null ? this.properties.calculated_font_size : this.properties.font_size;
            let content = `${width} x ${height}\n${area}`;
            if (this.properties.label !== null) {
                content = `${this.properties.label}\n${content}`;
            }
            let text = new this.paper.ps.PointText({
                position: [this.state.size.width / 2, this.state.size.height / 2],
                content,
                justification: 'center',
                fillColor: this.properties.text_color,
                fontFamily: this.properties.font_family,
                fontSize: font_size,
                parent: group
            });
            // if the calculated font size is not cached, we cycle through smaller font sizes until we find one that
            // fits within the bounds
            if (this.properties.calculated_font_size === null) {
                while (text.bounds.width > text_bounds.width || text.bounds.height > text_bounds.height) {
                    if (font_size < this.properties.min_font_size) {
                        text.remove();
                        font_size = false;
                        break;
                    }
                    font_size--;
                    text.fontSize = font_size;
                }
                this.properties.calculated_font_size = font_size;
            }
        } else if (this.properties.label !== null) {
            let text_bounds = this.state.size.multiply(0.8),
                font_size = this.properties.calculated_font_size !== null ? this.properties.calculated_font_size : this.properties.font_size,
                text = new this.paper.ps.PointText({
                    position: [this.state.size.width / 2, this.state.size.height / 2],
                    content: this.properties.label,
                    justification: 'center',
                    fillColor: this.properties.text_color,
                    fontFamily: this.properties.font_family,
                    fontSize: font_size,
                    parent: group
                });
            while (text.bounds.width > text_bounds.width || text.bounds.height > text_bounds.height) {
                if (font_size < this.properties.min_font_size) {
                    text.remove();
                    font_size = false;
                    break;
                }
                font_size--;
                text.fontSize = font_size;
            }
            this.properties.calculated_font_size = font_size;
        }

        group.pivot = Point.of([0, 0]);
        group.rotation = this.state.rotation;

        return group;
    };

    /**
     * Handle properties after update
     *
     * Determine if size was changed and clear out calculated font size if so.
     */
    postUpdate(data) {
        super.postUpdate(data);
        if (!this.state.size.equals(this.properties.last_size)) {
            this.properties.calculated_font_size = null;
            this.properties.last_size = this.state.size;
        }
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        let data = super.entityData();
        return Object.assign(data, {
            color: this.color,
            border_color: this.border_color,
            border_width: this.border_width,
            info_enabled: this.info_enabled
        });
    };
}

module.exports = Styled;
