'use strict';

const FileSaver = require('file-saver');

const DrawingRepo = require('./repositories/drawing');
const ImageEntity = require('./entities/drawing/image');

/**
 * @memberof module:Drawing
 */
class Exporter {
    /**
     * Get Blob of JSON payload
     *
     * @param {object} payload
     * @returns {Blob}
     */
    getBlob(payload) {
        return new Blob([JSON.stringify(payload)], {type: 'application/json;charset=utf-8'});
    };

    /**
     * Get payload of drawing and save as file for user
     *
     * @param {string} drawing_id
     * @param {boolean} [duplicate=false]
     */
    payload(drawing_id, duplicate = false) {
        DrawingRepo.findById(drawing_id, ['nodes']).then((drawing) => {
            if (duplicate) {
                drawing = drawing.clone(true);
            }
            let payload = drawing.getPayload(['nodes']);
            FileSaver.saveAs(this.getBlob(payload), `drawing_local_${drawing.id}.json`);
        }).catch(e => console.error('Unable to find drawing', e));
    };

    /**
     * Get sync payload (sent to server) and save as file for user
     *
     * @param {string} drawing_id
     * @param {boolean} [duplicate=false]
     */
    syncPayload(drawing_id, duplicate = false) {
        DrawingRepo.findById(drawing_id, ['nodes']).then((drawing) => {
            if (duplicate) {
                drawing = drawing.clone(true);
            }
            let payload = drawing.getSyncPayload(['nodes']);
            FileSaver.saveAs(this.getBlob(payload), `drawing_server_${drawing.id}.json`);
        }).catch(e => console.error('Unable to find drawing', e));
    };

    /**
     * Get SVG image of drawing and save as file
     *
     * @param {string} drawing_id
     */
    image(drawing_id) {
        DrawingRepo.findById(drawing_id, ['nodes']).then(async (drawing) => {
            const SvgExport = require('./pages/main-pages/drawing-components/svg_export');
            let image_data = await SvgExport.run(drawing),
                image_entity = new ImageEntity({id: drawing_id, data: image_data, content_type: 'image/svg+xml'});
            FileSaver.saveAs(image_entity.getData(true), `drawing_image_${drawing_id}.svg`);
        }).catch(e => console.error('Unable to find drawing', e));
    };
}

module.exports = Exporter;
