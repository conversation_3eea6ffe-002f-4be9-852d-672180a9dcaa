/**
 * @module Drawing/Entities/Drawing
 */

'use strict';

const moment = require('moment-timezone');
const includes = require('lodash/includes');

const getIso8601DateTime = require('@cac-js/utils/iso8601_datetime');

const Entity = require('./syncable');
const Node = require('./drawing/node');
const User = require('./user');
const ImageRepo = require('../repositories/drawing/image');
const NodeRepo = require('../repositories/drawing/node');
const UserRepo = require('../repositories/user');

const VERSION = 2;

const relation_config = {
    created_by_user: {
        key: 'created_by_user_id',
        foreign_key: 'id',
        load: (id) => {
            return UserRepo.findById(id);
        },
        bulk_load: (ids) => {
            return UserRepo.findByIds(ids);
        }
    },
    image: {
        key: 'id',
        load: (id) => {
            return ImageRepo.findById(id);
        },
        bulk_load: (ids) => {
            return ImageRepo.findByIds(ids);
        }
    },
    nodes: {
        key: 'id',
        foreign_key: 'drawing_id',
        many: true,
        load: (id) => {
            return NodeRepo.findByDrawingId(id);
        },
        bulk_load: (ids) => {
            return NodeRepo.findByDrawingIds(ids);
        }
    }
};

/**
 * @memberof module:Drawing/Entities
 */
class Drawing extends Entity {
    /**
     * Get current version
     *
     * @readonly
     *
     * @returns {number}
     */
    static get VERSION() {
        return VERSION;
    };

    /**
     * Get statuses
     *
     * @readonly
     *
     * @returns {{IN_PROGRESS: number, FINALIZED: number}}
     */
    static get Status() {
        return {
            IN_PROGRESS: 1,
            FINALIZED: 2
        };
    };

    /**
     * Get units
     *
     * @readonly
     *
     * @returns {{METER: number, FEET: number}}
     */
    static get Unit() {
        return {
            FEET: 1,
            METER: 2
        };
    };

    /**
     * Set initial state of entity from input data
     *
     * @param {object} data
     * @param {boolean} existing
     */
    setInitialState(data, existing) {
        super.setInitialState(data, existing);
        Object.assign(this.state, {
            version: 1,
            status: Drawing.Status.IN_PROGRESS,
            name: null,
            project_id: null,
            started_at: null,
            last_modified_at: null,
            created_by_user_id: null,
            unit: Drawing.Unit.FEET,
            pixels_per_unit: 24,
            background_size: [100, 100],
            image: null,
            nodes: null,
            // relations
            project: null,
            created_by_user: null
        });
        // map subversion to version until API is updated
        if (typeof data.subversion === 'number') {
            data.version = data.subversion;
        }
        this.fill([
            'name', 'version', 'status', 'project_id', 'project', 'started_at', 'last_modified_at', 'created_by_user_id',
            ['config.unit', 'unit'], ['config.pixels_per_unit', 'pixels_per_unit'], ['config.background_size', 'background_size']
        ], data);
        if (data.nodes !== undefined) {
            let nodes = [];
            for (let node of data.nodes) {
                nodes.push(new Node(node, true));
            }
            this.nodes = nodes;
        }
        if (data.created_by_user !== undefined) {
            this.created_by_user = new User(data.created_by_user, true);
        }
    };

    /**
     * Clone entity state
     *
     * @param {object} state
     * @param {boolean} duplicate
     * @returns {object}
     */
    cloneState(state, duplicate) {
        state = super.cloneState(state, duplicate);
        if (duplicate) {
            state.status = Drawing.Status.IN_PROGRESS;
            state.started_at = null;
            state.last_modified_at = null;
        }
        if (state.image !== null) {
            state.image = state.image.clone(duplicate);
            if (duplicate) {
                state.image.setState({id: state.id}, false);
            }
        }
        if (state.nodes !== null) {
            let nodes = state.nodes.slice(0),
                node_id_map = {},
                prev_id;
            for (let idx in nodes) {
                if (duplicate) {
                    prev_id = nodes[idx].id;
                }
                nodes[idx] = nodes[idx].clone(duplicate);
                if (duplicate) {
                    node_id_map[prev_id] = nodes[idx].id;
                    nodes[idx].drawing_id = state.id;
                }
            }
            if (duplicate) {
                // if duplicating, we have to map old ids to the new ones in the attachments
                for (let node of nodes) {
                    let attachments = node.attachments;
                    if (attachments === null || attachments.nodes === undefined) {
                        continue;
                    }
                    for (let attachment_node of attachments.nodes) {
                        attachment_node[0] = node_id_map[attachment_node[0]];
                    }
                    node.attachments = attachments;
                }
            }
            state.nodes = nodes;
        }
        return state;
    };

    /**
     * Get version
     *
     * @readonly
     *
     * @returns {number}
     */
    get version() {
        return this.state.version;
    };

    /**
     * Set version
     *
     * @param {number} version
     */
    set version(version) {
        this.setState({version});
    };

    /**
     * Get status
     *
     * @readonly
     *
     * @returns {number}
     */
    get status() {
        return this.state.status;
    };

    /**
     * Set status
     *
     * @param {number} status
     */
    set status(status) {
        this.setState({status});
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Set name
     *
     * @param {string} name
     */
    set name(name) {
        if (typeof name !== 'string') {
            throw new Error('Name must be a string');
        }
        if (name.length < 1 || name.length > 100) {
            throw new Error('Name must be between 1 and 100 characters');
        }
        this.setState({name});
    };

    /**
     * Get project id
     *
     * @readonly
     *
     * @returns {?number}
     */
    get project_id() {
        return this.state.project_id;
    };

    /**
     * Set project id
     *
     * @param {?number} project_id
     */
    set project_id(project_id) {
        if (project_id !== null) {
            if (typeof project_id !== 'number') {
                throw new Error('Project id must be a number');
            }
        }
        this.setState({project_id});
    };

    /**
     * Get project data
     *
     * @returns {{customer_name: string, property_address: string, description: string}}
     */
    get project() {
        return this.state.project;
    };

    /**
     * Set project data
     *
     * @param {{customer_name: string, property_address: string, description: string}} project
     */
    set project(project) {
        this.state.project = project;
    };

    /**
     * Get started at date
     *
     * If not set, current timestamp is saved
     *
     * @param {boolean} [as_date=true] - determines if moment instance is returned
     * @returns {(string|moment)}
     */
    getStartedAt(as_date = true) {
        if (this.state.started_at === null) {
            this.setState({
                started_at: getIso8601DateTime(new Date)
            });
        }
        return as_date ? moment.utc(this.state.started_at) : this.state.started_at;
    };

    /**
     * Get started at datetime
     *
     * @returns {string}
     */
    get started_at() {
        return this.getStartedAt(false);
    };

    /**
     * Set started at datetime
     *
     * @param {(Date|string)} started_at - must be ISO8601 formatted UTC datetime string
     */
    set started_at(started_at) {
        if (started_at instanceof Date) {
            started_at = getIso8601DateTime(started_at);
        }
        this.setState({started_at});
    };

    /**
     * Get last modified at datetime
     *
     * @param {boolean} [as_date=true] - determines if moment instance is returned
     * @returns {(null|moment|string)}
     */
    getLastModifiedAt(as_date = true) {
        if (this.state.last_modified_at === null) {
            return null;
        }
        return as_date ? moment(this.state.last_modified_at) : this.state.last_modified_at;
    };

    /**
     * Get last modified at datetime
     *
     * @returns {(null|string)}
     */
    get last_modified_at() {
        return this.getLastModifiedAt(false);
    };

    /**
     * Set last modified at datetime
     *
     * @param {(null|Date|string)} last_modified_at - must be ISO8601 formatted UTC datetime string
     */
    set last_modified_at(last_modified_at) {
        if (last_modified_at instanceof Date) {
            last_modified_at = getIso8601DateTime(last_modified_at);
        }
        this.setState({last_modified_at});
    };

    /**
     * Get created by user id
     *
     * @returns {number}
     */
    get created_by_user_id() {
        return this.state.created_by_user_id;
    };

    /**
     * Set created by user id
     *
     * @param {number} created_by_user_id
     */
    set created_by_user_id(created_by_user_id) {
        this.setState({created_by_user_id});
    };

    /**
     * Get created by user
     *
     * @returns {(null|module:Drawing/Entities.User)}
     */
    get created_by_user() {
        return this.state.created_by_user;
    };

    /**
     * Set created by user
     *
     * @param {(null|module:Drawing/Entities.User)} user
     */
    set created_by_user(user) {
        this.state.created_by_user = user;
    };

    /**
     * Get unit
     *
     * @returns {number}
     */
    get unit() {
        return this.state.unit;
    };

    /**
     * Set unit
     *
     * @param {number} unit
     */
    set unit(unit) {
        if (!includes(Drawing.Unit, unit)) {
            throw new Error('Unit is not valid');
        }
        this.setState({unit});
    };

    /**
     * Get pixels per unit
     *
     * @returns {number}
     */
    get pixels_per_unit() {
        return this.state.pixels_per_unit;
    };

    /**
     * Set background size in units
     *
     * @param {[number, number]} background_size - array of width and height in units
     */
    set background_size(background_size) {
        this.setState({background_size});
    };

    /**
     * Get background size
     *
     * @returns {[number, number]}
     */
    get background_size() {
        return this.state.background_size;
    };

    /**
     * Set pixels per unit
     *
     * @param {number} pixels_per_unit
     */
    set pixels_per_unit(pixels_per_unit) {
        this.setState({pixels_per_unit});
    };

    /**
     * Get image
     *
     * @returns {(null|module:Drawing/Entities/Drawing.Image)}
     */
    get image() {
        return this.state.image;
    };

    /**
     * Set image
     *
     * @param {(null|module:Drawing/Entities/Drawing.Image)} data
     */
    set image(data) {
        this.state.image = data;
    };

    /**
     * Get nodes
     *
     * @returns {module:Drawing/Entities/Drawing.Node[]}
     */
    get nodes() {
        return this.state.nodes;
    };

    /**
     * Set nodes
     *
     * @param {(null|module:Drawing/Entities/Drawing.Node[])} nodes
     */
    set nodes(nodes) {
        this.setState({nodes});
    };

    /**
     * Load relations onto entity
     *
     * @param {string[]} relations - list of relationships
     * @param {boolean} [force=false]
     * @returns {Promise<module:Drawing/Entities.Drawing>}
     */
    async load(relations, force = false) {
        return await super.load(relation_config, relations, force);
    };

    /**
     * Load relations onto collection of raw drawing objects
     *
     * @param {object[]} data - collection of raw drawing objects
     * @param {string[]} relations - list of relationships
     * @returns {Promise<module:Drawing/Entities.Drawing[]>}
     */
    static async bulkLoad(data, relations) {
        return await super.bulkLoad(relation_config, data, relations);
    };

    /**
     * Get payload for storage
     *
     * @param {string[]} [relations=[]] - list of relationships to package up
     * @returns {object}
     */
    getPayload(relations = []) {
        let payload = super.getPayload();
        let data = {
            version: this.version,
            status: this.status,
            name: this.name,
            project_id: this.project_id,
            project: this.project,
            started_at: this.started_at,
            last_modified_at: this.last_modified_at,
            created_by_user_id: this.created_by_user_id,
            config: {
                unit: this.unit,
                pixels_per_unit: this.pixels_per_unit,
                background_size: this.background_size
            }
        };
        if (relations.indexOf('nodes') !== -1) {
            data.nodes = this.nodes.map(node => node.getPayload());
        }
        return Object.assign(payload, data);
    };

    /**
     * Get server ready payload
     *
     * @param {string[]} [relations=[]] - list of relationships to package up
     * @returns {object}
     */
    getSyncPayload(relations = []) {
        let payload = super.getSyncPayload();
        let data = {
            subversion: this.version,
            status: this.status,
            name: this.name,
            project_id: this.project_id,
            started_at: this.started_at,
            last_modified_at: this.last_modified_at,
            config: {
                unit: this.unit,
                pixels_per_unit: this.pixels_per_unit,
                background_size: this.background_size
            }
        };
        if (relations.indexOf('nodes') !== -1) {
            data.nodes = this.nodes.map(node => node.getSyncPayload());
        }
        return Object.assign(payload, data);
    };

    /**
     * Get JSON representation of entity
     *
     * @returns {object}
     */
    toJSON() {
        return this.getPayload(['nodes']);
    };
}

module.exports = Drawing;
