export async function get(url, data = {}, fetch_params = {}) {
    if (Object.keys(data).length > 0) {
        let query = new URLSearchParams();
        for (let [key, value] of Object.entries(data)) {
            query.append(key, value);
        }
        url += '?' + query.toString();
    }
    let response = await fetch(url, Object.assign({
        headers: {
            Accept: 'application/json'
        }
    }, fetch_params));
    if (!response.ok) {
        throw response;
    }
    return {data: await response.json(), response};
}
export function abortableGet(url, data = {}, fetch_params = {}) {
    let controller = new AbortController();
    return {
        abort: () => controller.abort(),
        request: get(url, data, Object.assign(fetch_params, {signal: controller.signal}))
    };
}
export async function post(url, data = {}, fetch_params = {}) {
    let headers = {
        Accept: 'application/json'
    };
    if (!(data instanceof FormData)) {
        headers['Content-Type'] = 'application/json';
        data = JSON.stringify(data);
    }
    let response = await fetch(url, Object.assign({
        method: 'POST',
        cache: 'no-cache',
        headers,
        body: data
    }, fetch_params));
    if (!response.ok) {
        throw response;
    }
    let response_data = null;
    if (response.status !== 204) {
        response_data = await response.json();
    }
    return {data: response_data, response};
}
export function abortablePost(url, data = {}, fetch_params = {}) {
    let controller = new AbortController();
    return {
        abort: () => controller.abort(),
        request: post(url, data, Object.assign(fetch_params, {signal: controller.signal}))
    };
}
