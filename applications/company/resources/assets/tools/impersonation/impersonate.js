import {toggleClass, addClass, removeClass, escapeHTML, append, empty, watch, data, text} from './dom.js';
import {abortableGet, get, post} from './ajax.js';

const States = {
    IDLE: 1,
    SEARCHING: 2,
    NO_RESULTS: 3,
    RESULTS: 4,
    IMPERSONATING: 5
};

const CompanyStatusMap = new Map([
    [1, 'Active'],
    [2, 'Suspended'],
    [3, 'Dormant'],
    [4, 'Setup'],
    [5, 'Trial']
]);

export class Impersonate {
    constructor() {
        this.elem = {};
        this.state = {
            visible: false,
            state: States.IDLE,
            current: null,
            search: null,
            term: null
        };
    };

    async getCurrent() {
        let {data} = await get('/api/admin/impersonation/current');
        if (data.user === null) {
            removeClass(this.elem.current, 't-show');
            this.state.current = null;
            return;
        }
        text(this.elem.current_name, `Impersonating: ${data.user.first_name} ${data.user.last_name}`);
        addClass(this.elem.current, 't-show');
        this.state.current = data.user;
    };

    toggle() {
        let visible = !this.state.visible;
        toggleClass(this.elem.root, 't-show', visible);
        this.state.visible = visible;
        if (visible) {
            this.getCurrent().catch(e => console.log(e));
        }
    };

    setState(state) {
        let prev_state = this.state.state;
        if (prev_state === state) {
            if (prev_state === States.SEARCHING && this.state.search !== null) {
                this.state.search.abort();
                this.state.search = null;
            }
            return;
        }
        switch (prev_state) {
            case States.SEARCHING:
            case States.NO_RESULTS:
            case States.IMPERSONATING:
                removeClass(this.elem.message, 't-show');
                text(this.elem.message, '');
                break;
            case States.RESULTS:
                removeClass(this.elem.results, 't-show');
                empty(this.elem.results);
                break;
        }
        switch (state) {
            case States.SEARCHING:
            case States.NO_RESULTS:
            case States.IMPERSONATING:
                let StateMessageMap = {
                    [States.SEARCHING]: 'Searching...',
                    [States.NO_RESULTS]: 'No results found',
                    [States.IMPERSONATING]: 'Impersonating...'
                }
                text(this.elem.message, StateMessageMap[state]);
                addClass(this.elem.message, 't-show');
                break;
            case States.RESULTS:
                addClass(this.elem.results, 't-show');
                break;
        }
        this.state.state = state;
    };

    getResultHtml(data) {
        let primary = data.primary ? '<span class="c-iirru-primary">Primary</span>' : '';
        return `
<a class="c-iir-result" data-id="${escapeHTML(data.id)}">
    <div class="c-iirr-user">${escapeHTML(`${data.first_name} ${data.last_name}`)}${primary}</div>
    <div class="c-iirr-company">${escapeHTML(data.company_name)}<span class="c-iirrc-status">(${CompanyStatusMap.get(data.company_status)})</span></div>
</a>
`;
    };

    isNumeric(str) {
        return !isNaN(str) && !isNaN(parseFloat(str));
    };

    search(term) {
        term = term.trim();
        if (term.length < 2 && !this.isNumeric(term)) {
            this.setState(States.IDLE);
            return;
        }
        if (term === this.state.term) {
            return;
        }

        this.setState(States.SEARCHING);
        this.state.term = term;

        this.state.search = abortableGet('/api/admin/impersonation/search', {term});
        this.state.search.request.then(({data}) => {
            if (data.results.length === 0) {
                this.setState(States.NO_RESULTS);
                return;
            }
            for (let result of data.results) {
                append(this.elem.results, this.getResultHtml(result));
            }
            this.state.search = null;
            this.setState(States.RESULTS);
        }).catch(response => {
            if (response.status === 403) {
                alert('Permission denied');
            }
            this.state.search = null;
            this.setState(States.IDLE);
        });
    };

    async impersonate(user_id) {
        try {
            this.setState(States.IMPERSONATING);
            await post('/api/admin/impersonation/impersonate', {user_id});
            window.location.reload();
        } catch (e) {
            alert('Unable to impersonate user');
            this.setState(States.IDLE);
            throw e;
        }
    };

    boot(elem) {
        this.elem.root = elem;
        this.elem.inner = elem.querySelector('.c-i-inner');
        this.elem.current = elem.querySelector('.c-ii-current');
        this.elem.current_name = this.elem.current.querySelector('.c-iic-name');
        this.elem.stop = this.elem.current.querySelector('.c-iic-stop');
        this.elem.form = elem.querySelector('.c-ii-input-wrapper');
        this.elem.input = this.elem.form.querySelector('.c-iiiw-input');
        this.elem.message = elem.querySelector('.c-ii-message');
        this.elem.results = elem.querySelector('.c-ii-results');

        elem.addEventListener('click', () => {
            this.toggle();
        });
        this.elem.inner.addEventListener('click', e => {
            e.stopPropagation();
        });

        this.elem.stop.addEventListener('click', e => {
            e.preventDefault();
            this.impersonate(-1).catch(e => console.log(e));
        });

        this.elem.form.addEventListener('submit', e => {
            e.preventDefault();
            this.search(this.elem.input.value);
        });

        watch(this.elem.results, 'click', '.c-iir-result', (e, target) => {
            this.impersonate(parseInt(data(target, 'id'))).catch(e => console.log(e));
        });
    };

    render() {
        return `
<div class="m-impersonation">
    <div class="c-i-inner">
        <div class="c-ii-current"><div class="c-iic-name"></div><a class="c-iic-stop">Stop</a></div>
        <h1 class="c-ii-header">Impersonation</h1>
        <form class="c-ii-input-wrapper">
            <input class="c-iiiw-input" type="text">
            <button class="c-iiiw-search">Search</button>
        </form>
        <div class="c-ii-message"></div>
        <div class="c-ii-results"></div>
    </div>
</div>`;
    };
}
