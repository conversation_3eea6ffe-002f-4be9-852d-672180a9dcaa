.header {
    text-align: center;
}
.line-item-table-wrapper {
    page-break-inside: avoid;
}
.table-header {
    margin: 15px 0 0;
}
.line-item-table {
    margin-top: 5px;
    width: 100%;
    border-collapse: collapse;
    page-break-inside: avoid;
}
.line-item-table tr {
    page-break-inside: avoid;
}
.line-item-table th,
.line-item-table td {
    padding: 5px;
}
.line-item-table th {
    background-color: lightgrey;
}
.line-item-table td {
    border-top: 1px #000 solid;
}
/* section */
.line-item-table.section .line-item-name {
    width: 75%;
    text-align: left;
}
.line-item-table.section .line-item-quantity {
    width: 10%;
    text-align: right;
}
.line-item-table.section .line-item-total {
    width: 15%;
    text-align: right;
}
/* price adjustment */
.line-item-table.price-adjustment .line-item-name {
    width: 60%;
    text-align: left;
}
.line-item-table.price-adjustment .line-item-quantity {
    width: 10%;
    text-align: right;
}
.line-item-table.price-adjustment .line-item-amount,
.line-item-table.price-adjustment .line-item-total {
    width: 15%;
    text-align: right;
}

.line-item-table tfoot .line-item-total-label,
.line-item-table tfoot .line-item-total {
    text-align: right;
    border-top: 2px #000 solid;
}
.line-item-table tfoot .line-item-total-label {
    font-weight: bold;
}
.pricing-disclaimers {
    list-style: none;
    margin: 10px 0 20px;
    padding: 0;
}
.pricing-disclaimers > li {
    position: relative;
    font-size: 9pt;
    font-style: italic;
    margin-bottom: 4px;
    padding: 2px 0 0 10px;
}
.pricing-disclaimers .pricing-disclaimer-id {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 7pt;
    font-style: normal;
}
.pricing-disclaimers > li:last-child {
    margin-bottom: 0;
}
.section-subtotal,
.bid-total {
    margin: 20px 0;
    text-align: right;
    font-size: 16pt;
    padding: 5px;
    border-top: 1px #000 solid;
    border-bottom: 1px #000 solid;
}
.payment-terms-header {
    margin-top: 30px;
}
.payment-terms-table {
    margin-top: 5px;
    width: 100%;
    border-collapse: collapse;
}
.payment-terms-table td {
    padding: 5px;
    border-top: 1px #000 solid;
}
.payment-terms-table .payment-term-name {
    width: 25%;
}
.payment-terms-table .payment-term-due {
    width: 60%;
    color: #919191;
    font-size: 11px;
    font-style: italic;
}
.payment-terms-table .payment-term-amount {
    width: 15%;
    text-align: right;
}

.wt-section {
    margin-top: 2em;
}

.wt-table {
    margin-top: 5px;
    width: 100%;
    border-collapse: collapse;
}

.wt-primary-button {
    background-color: #005AD0;
    color: white;
    width:100%;
    margin: 0.5em;
    padding:1em 0.5em;
}

.wt-preview-mode {
    margin-bottom: 1em;
    display: block;
    text-decoration: underline
}

.link {
    width:25%;
    text-align: right;
}

.link a {
    color: #005AD0;
}

.wt-flex-wrapper-callout {
    margin: 1em 0;
    padding: 1em;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: space-between;

    border: 1px solid #006EFF1A;
    background-color: #E8F2FF;
    color: #005AD0;
}

.wt-flex-wrapper-callout {
    margin: 1em 0;
    padding: 1em;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: space-between;

    border: 1px solid #006EFF1A;
    background-color: #E8F2FF;
    color: #005AD0;
}

.wt-footnote {
    font-size: 0.8em;
}
