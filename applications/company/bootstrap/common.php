<?php

declare(strict_types=1);

use Core\Classes\{Kernel, Path};
use Dotenv\Dotenv;
// include Composer autoloader
include __DIR__ . '/../vendor/autoload.php';

$env_path = dirname(__DIR__);
$env_file = '.env';
if (file_exists($env_path . DIRECTORY_SEPARATOR . $env_file)) {
    Dotenv::createImmutable($env_path, $env_file)->load();
}

// include legacy project constants
include __DIR__ . '/../app-legacy/includes/settings.php';

define('PATH_ROOT', dirname(__DIR__) . DIRECTORY_SEPARATOR);

// define application paths
$path = new Path();
$path->register('root', PATH_ROOT);
$path->register('config', PATH_ROOT . 'config');
$path->register('public', PATH_ROOT . 'public');
$path->register('storage', PATH_ROOT . 'storage');
$path->register('frameworkCache', PATH_ROOT . 'storage' . DIRECTORY_SEPARATOR . 'framework');
$path->register('app', PATH_ROOT . 'app');
$path->register('appLegacy', PATH_ROOT . 'app-legacy');
$path->register('resource', PATH_ROOT . 'resources');
$path->register('route', PATH_ROOT . 'routes');

// get kernel instance
$kernel = new Kernel($path);

$kernel->setAsGlobal();