<?php

use App\Classes\FX\API\ViewDrawing;
use Core\StaticAccessors\Path;

header('Content-Type: image/jpeg');

	if (isset($_GET["token"])) {
		$token = filter_input(INPUT_GET, 'token', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_GET["drawingID"])) {
		$drawingID = filter_input(INPUT_GET, 'drawingID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	function showFile($filePath){
		// open the file in a binary mode
		if (file_exists($filePath)) {
			$file = fopen($filePath, 'rb');

			// send the right headers
			header("Content-Type: " . mime_content_type($filePath));
			header("Content-Length: " . filesize($filePath)); 

			// dump the picture and stop the script
			fpassthru($file); 
			exit;
		}
		else{
			// echo 'Cannot open file ' . $filePath;
		}	
	}

		$object = new ViewDrawing();
		$object->setToken($token);
		$object->setDrawingID($drawingID);
		$object->setIDs();
		$object->setDrawingName();
		$companyID = $object->getCompanyID();
		$projectID = $object->getProjectID();
		$evaluationID = $object->getEvaluationID();
		$drawingName = $object->getDrawingName();

		$path = Path::evaluationAppDrawing($drawingName . '.jpeg', $companyID, $projectID, $evaluationID, $drawingID);
		showFile($path);
?>