<?php

use App\Classes\Acl;
use App\NotificationJobs\User\BidAcceptedNotificationJob;
use App\Resources\Bid\ItemResource;
use Carbon\Carbon;
use Common\Models\BidItem;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\StaticAccessors\App;
use Ramsey\Uuid\Uuid;

class Bid {
		
		private $db;
		private $evaluationID;
		private $customEvaluation;
		private $bidAcceptedName;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setBid($evaluationID, $customEvaluation, $bidAcceptedName) {
			
			$this->evaluationID = $evaluationID;
			$this->customEvaluation = $customEvaluation;
			$this->bidAcceptedName = $bidAcceptedName;
		}

        /**
         * @throws \Core\Exceptions\AppException
         * @throws Exception
         */
        public function sendBid() {
			if (empty($this->evaluationID)) {
                return;
            }

            /** @var PDOStatement $query */
            $query = $this->db->prepare('SELECT 
                IF(e.bidItemID IS NOT NULL, HEX(e.bidItemID), NULL) as bidItemID, bi.type AS bidType, 
                e.evaluationFinalizedByID, p.projectSalesperson

                FROM evaluation AS e 
                JOIN project AS p ON p.projectID = e.projectID
                LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID 

                WHERE e.evaluationID = :evaluationID');
            $query->execute([':evaluationID' => $this->evaluationID]);
            $result = $query->fetch(PDO::FETCH_ASSOC);
            if ($result === false) {
                throw new Exception('Unable to find bid item ID for evaluation');
            }

            if ($result['bidItemID'] !== null) {
                $acl = Auth::acl();
                if ($acl === null) {
                    $acl = Acl::make();
                }
                $update_entity = Entity::make([
                    'id' => $result['bidItemID'],
                    'status' => ItemResource::STATUS_ACCEPTED,
                    'accepted_signature' => $this->bidAcceptedName
                ]);
                if ((int) $result['bidType'] === BidItem::TYPE_LEGACY) {
                    $update_entity->set('is_file_valid', false);
                }
                try {

                    $update_request = ItemResource::make($acl)->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)
                        ->partialUpdate($update_entity)
                        ->force(true)
                        ->findConfig([
                            'check_mutability' => false
                        ]);



                    // inject ip address info since resource system doesn't have access to that information and
                    // it doesn't need to be provided via a field since users shouldn't be responsible for it
                    /** @var RequestInterface $request */
                    $request = App::get(RequestInterface::class);
                    $ip = $request->ip(true);
                    if ($ip !== false) {
                        $ip_type_map = [
                            RequestInterface::IP_TYPE_V4 => BidItem::IP_ADDRESS_TYPE_V4,
                            RequestInterface::IP_TYPE_V6 => BidItem::IP_ADDRESS_TYPE_V6
                        ];
                        $update_request->store('request_ip', [
                            'type' => $ip_type_map[$ip[0]],
                            'ip' => $ip[1]
                        ]);
                        $update_request->run();
                    }
                } catch (Throwable $e) {
                    throw new Exception('Unable to update bid item status: ' . $e->getMessage());
                }
            }

            $accept_time = Carbon::now('UTC')->format('Y-m-d H:i:s');

            $table = (int) $result['bidType'] === BidItem::TYPE_LEGACY ? 'evaluationBid' : 'customBid';
            $st = $this->db->prepare("
                UPDATE {$table} SET	
                bidAccepted = :acceptTime, 
                bidAcceptedName = :bidAcceptedName
                WHERE evaluationID = :evaluationID"
            );

            $st->bindValue(':acceptTime', $accept_time);
            $st->bindParam(':evaluationID', $this->evaluationID);
            $st->bindParam(':bidAcceptedName', $this->bidAcceptedName);

            $st->execute();

            $this->results = [
                'bidAccepted' => $accept_time
            ];

            BidAcceptedNotificationJob::enqueue((int) $this->evaluationID);
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}

?>