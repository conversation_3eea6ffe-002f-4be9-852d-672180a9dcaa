<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateSignupModuleMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('companies')
            ->columns(
                function (Blueprint $table) {
                    $table->renameColumn('setupStatus', 'signupStatus');
                }
            )
            ->useHistory(true)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('companies')
            ->columns(
                function (Blueprint $table) {
                    $table->renameColumn('signupStatus', 'setupStatus');
                }
            )
            ->useHistory(true)
            ->alter();
    }
}
