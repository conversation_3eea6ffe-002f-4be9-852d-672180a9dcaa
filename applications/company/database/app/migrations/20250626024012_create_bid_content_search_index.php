<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class CreateBidContentSearchIndex extends Migration
{
    public function up()
    {
        $this->updateTable('bidContent')
            ->index('search', Table::INDEX_TYPE_FULLTEXT, ['name'])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidContent')
            ->indexes(function (Blueprint $table) {
                $table->dropIndex('search');
            })
            ->alter();
    }
}
