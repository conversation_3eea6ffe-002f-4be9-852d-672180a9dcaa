<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class RemoveFeaturesFromCompanyTableMigration extends Migration
{
    public function up()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['featureCrewManagement', 'featureDocumentLibrary', 'featureQuickbooks']);
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('featureCrewManagement')->default(1);
                $table->unsignedTinyInteger('featureDocumentLibrary')->default(0);
                $table->unsignedTinyInteger('featureQuickbooks')->default(1);
            })
            ->alter();
    }
}
