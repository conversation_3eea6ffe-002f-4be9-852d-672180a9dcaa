<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class EmailAutoResponseRecipientsMigration extends Migration
{
    public function up()
    {
        $this->newTable('emailAutoResponseRecipients')
            ->primaryKey('emailAutoResponseRecipientID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // notification inbox
                $table->string('email', 100);
                $table->unsignedSmallInteger('hits');
                $table->dateTime('lastHitAt')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index(['type', 'email'], 'type_email_index');
            })
            ->useHistory(false)
            ->timestamps(true, false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['emailAutoResponseRecipients']);
    }
}
