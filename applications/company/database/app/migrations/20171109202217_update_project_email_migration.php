<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateProjectEmailMigration extends Migration
{
    public function up()
    {
        $this->schema->table('projectEmail', function (Blueprint $table) {
            $table->string('phoneNumber', 15)->after('name')->nullable();
            $table->string('email', 100)->nullable()->change();
        });
    }

    public function down()
    {
        $this->schema->table('projectEmail', function (Blueprint $table) {
            $table->dropColumn('phoneNumber');
            $table->string('email', 100)->nullable(false)->change();
        });
    }
}