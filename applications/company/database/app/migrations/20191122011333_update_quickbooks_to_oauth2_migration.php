<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class UpdateQuickbooksToOauth2Migration extends Migration
{
    public function up()
    {
        $this->newTable('quickbooksAuthRequests')
            ->primaryKey('quickbooksAuthRequestID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('status'); // pending, completed, access denied, failure
                $table->dateTime('completedAt')->nullable();
                $table->unsignedInteger('quickbooksOAuthID')->nullable();
                $table->dateTime('accessDeniedAt')->nullable();
                $table->dateTime('failedAt')->nullable();
            })
            ->column('quickbooksAuthRequestUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'quickbooksAuthRequestID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID', 'company_id_index');
            })
            ->useHistory(false)
            ->create();

        $this->newTable('quickbooksOAuth')
            ->primaryKey('quickbooksOAuthID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('status'); // connected, disconnected
                $table->string('realmID', 32);
                $table->text('accessToken')->nullable();
                $table->dateTime('accessTokenExpiresAt')->nullable();
                $table->text('refreshToken')->nullable();
                $table->dateTime('refreshTokenExpiresAt')->nullable();
                $table->unsignedTinyInteger('isCurrent');
                $table->dateTime('disconnectedAt')->nullable();
                $table->unsignedInteger('disconnectedByUserID')->nullable();
                $table->unsignedTinyInteger('isExternalDisconnect')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID', 'company_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['quickbooksStatus', 'quickbooksRealm']);
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('quickbooksStatus')->default(0)->after('longitude');
                $table->string('quickbooksRealm', 255)->nullable()->after('quickbooksDefaultService');
            })
            ->alter();

        $this->dropTables(['quickbooksAuthRequests', 'quickbooksOAuth']);
    }
}
