<?php

use App\Classes\DB\Migration;

use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class UserPasswordReset extends Migration
{
    public function up()
    {
        $this->schema->create('userPasswordResets', function (Blueprint $table) {
            $table->unsignedInteger('userID');
            $table->unsignedInteger('emailID');
            $table->dateTime('completedAt')->nullable();
            Schema::timestamps($table, true);
        });
        Schema::binaryColumn('userPasswordResets', 'userPasswordResetID', 16, [
            'first' => true,
            'primary' => true
        ]);
    }

    public function down()
    {
        $this->schema->drop('userPasswordResets');
    }
}
