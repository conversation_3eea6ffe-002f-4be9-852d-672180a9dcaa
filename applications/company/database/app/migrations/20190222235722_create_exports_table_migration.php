<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateExportsTableMigration extends Migration
{
    public function up()
    {
        $this->newTable('exports')
            ->primaryKey('exportID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // customer
                $table->text('scope');
                $table->unsignedBigInteger('totalRows');
                $table->unsignedBigInteger('time');
                $table->dateTime('createdAt');
                $table->unsignedInteger('createdByUserID');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();
    }

    public function down()
    {
        $this->dropTables(['exports']);
    }
}
