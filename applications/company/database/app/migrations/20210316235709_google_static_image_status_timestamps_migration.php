<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class GoogleStaticImageStatusTimestampsMigration extends Migration
{
    public function up()
    {
        $this->updateTable('googleStaticImages')
            ->columns(function (Blueprint $table) {
                $table->dateTime('lookupFailedAt')->nullable()->after('expiresAt');
                $table->dateTime('notFoundAt')->nullable()->after('lookupFailedAt');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('googleStaticImages')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['lookupFailedAt', 'notFoundAt']);
            })
            ->alter();
    }
}
