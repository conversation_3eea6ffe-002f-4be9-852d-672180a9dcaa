<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddSubmittedColumnsToBidMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->dateTime('submittedAt')->nullable()->after('lockedByUserApiTokenID');
                $table->unsignedInteger('submittedByUserID')->nullable()->after('submittedAt');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['submittedAt', 'submittedByUserID']);
            })
            ->alter();
    }
}
