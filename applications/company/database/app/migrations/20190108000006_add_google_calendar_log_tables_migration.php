<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class AddGoogleCalendarLogTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('googleCalendarPushNotificationLog')
            ->primaryKey('googleCalendarPushNotificationLogID')
            ->columns(function (Blueprint $table) {
                $table->string('channelID', 64)->nullable();
                $table->text('headers');
                $table->string('message', 500)->nullable();
            })
            ->column('createdAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'message'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('channelID', 'channel_id_index');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();
    }

    public function down()
    {
        $this->dropTables(['googleCalendarPushNotificationLog']);
    }
}
