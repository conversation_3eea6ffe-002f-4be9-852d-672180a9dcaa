<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class AddUuidToEmailAndProjectScheduleMigration extends Migration
{
    public function up()
    {
        $this->updateTable('emails')
            ->column('emailUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'emailID'
            ])
            ->useHistory(false)
            ->alter();

        $this->updateTable('projectSchedule')
            ->column('projectScheduleUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'projectScheduleID'
            ])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('emails')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('emailUUID');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('projectSchedule')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('projectScheduleUUID');
            })
            ->alter();
    }
}
