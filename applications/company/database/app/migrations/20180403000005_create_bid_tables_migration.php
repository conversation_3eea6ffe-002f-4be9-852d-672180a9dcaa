<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class CreateBidTablesMigration extends Migration
{
    public function up()
    {
        // bid items table
        $this->newTable('bidItems')
            ->primaryKey('bidItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('projectID');
                $table->unsignedTinyInteger('type');
                $table->unsignedTinyInteger('status');
                $table->decimal('total', 12, 4)->nullable();
                $table->unsignedTinyInteger('isFileValid');
                $table->unsignedTinyInteger('isScopeOfWorkFileValid');
                $table->unsignedTinyInteger('isLocked');
                $table->dateTime('lockedAt')->nullable();
                $table->dateTime('finalizedAt')->nullable();
                $table->unsignedInteger('finalizedByUserID')->nullable();
                $table->dateTime('acceptedAt')->nullable();
                $table->unsignedInteger('acceptedByUserID')->nullable();
                $table->dateTime('cancelledAt')->nullable();
                $table->unsignedInteger('cancelledByUserID')->nullable();
            })
            ->column('coverContentTemplateID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'status',
                'nullable' => true
            ])
            ->column('introContentTemplateID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'coverContentTemplateID',
                'nullable' => true
            ])
            ->column('sectionsContentTemplateID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'introContentTemplateID',
                'nullable' => true
            ])
            ->column('lineItemsContentTemplateID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'sectionsContentTemplateID',
                'nullable' => true
            ])
            ->column('imagesContentTemplateID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'lineItemsContentTemplateID',
                'nullable' => true
            ])
            ->column('fileID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'total',
                'nullable' => true
            ])
            ->column('scopeOfWorkFileID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'isFileValid',
                'nullable' => true
            ])
            ->column('lockedByUserApiTokenID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'lockedAt',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('projectID');
            })
            ->create();

        // form bid items table
        $this->newTable('formBidItems')
            ->primaryKey('formBidItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isBidDefault');
                $table->unsignedTinyInteger('isSectionDefault');
                $table->unsignedTinyInteger('isHiddenFromList');
                $table->unsignedTinyInteger('isHiddenFromBid');
                $table->unsignedTinyInteger('isHiddenFromScopeOfWork');
                $table->unsignedMediumInteger('defaultOrder');
            })
            ->create();

        // bid item sections table
        $this->newTable('bidItemSections')
            ->primaryKey('bidItemSectionID')
            ->columns(function (Blueprint $table) {
                $table->string('name', 100)->nullable();
                $table->unsignedMediumInteger('order');
            })
            ->column('bidItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemSectionID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemID');
            })
            ->create();

        // bid item section forms table
        $this->newTable('bidItemSectionForms')
            ->primaryKey('bidItemSectionFormID')
            ->columns(function (Blueprint $table) {
                $table->unsignedMediumInteger('order');
            })
            ->column('bidItemSectionID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemSectionFormID'
            ])
            ->column('formItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemSectionID'
            ])
            ->column('formItemEntryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemSectionID');
                $table->index('formItemID');
            })
            ->create();

        // bid item line items
        $this->newTable('bidItemLineItems')
            ->primaryKey('bidItemLineItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // general, product, fee, discount
                $table->unsignedTinyInteger('source'); // bid sidebar pricing, form event
                $table->string('name', 200);
                $table->decimal('quantity', 12, 4);
                $table->decimal('amount', 12, 4);
                $table->decimal('subtotal', 12, 4);
                $table->decimal('total', 12, 4);
                $table->unsignedSmallInteger('order');
                $table->unsignedTinyInteger('isLocked');
            })
            ->column('bidItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemLineItemID'
            ])
            ->column('itemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'type',
                'nullable' => true
            ])
            ->column('bidItemSectionID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'total',
                'nullable' => true
            ])
            ->column('formItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemSectionID',
                'nullable' => true
            ])
            ->column('formItemGroupRuleEventID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemID');
                $table->index(['type', 'itemID']);
            })
            ->create();

        // bid item product line items
        $this->newTable('bidItemProductLineItems')
            ->primaryKey('bidItemProductLineItemID')
            ->columns(function (Blueprint $table) {
                $table->string('name', 100);
                $table->unsignedTinyInteger('isIntangible');
                $table->string('unitName', 100);
                $table->string('unitAbbreviation', 100);
            })
            ->column('productItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemProductLineItemID'
            ])
            ->column('productItemPriceID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'productItemID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('productItemID');
                $table->index('productItemPriceID');
            })
            ->create();

        // bid item price adjustment line items
        $this->newTable('bidItemPriceAdjustmentLineItems')
            ->primaryKey('bidItemPriceAdjustmentLineItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('amountType'); // total, percentage
                $table->unsignedDecimal('amount', 12, 4);
            })
            ->create();

        // bid item drawings
        $this->newTable('bidItemDrawings')
            ->primaryKey('bidItemDrawingID')
            ->column('bidItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemDrawingID'
            ])
            ->column('drawingID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'bidItems', 'formBidItems', 'bidItemSections', 'bidItemSectionForms',
            'bidItemLineItems', 'bidItemProductLineItems', 'bidItemPriceAdjustmentLineItems',
            'bidItemDrawings'
        ], true);
    }
}
