<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class AddCacheStatusToGlobalSearchMigration extends Migration
{
    public function up()
    {
        $this->updateTable('searches')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('cacheStatus')->nullable()->after('resultCount');
            })
            ->useHistory(false)
            ->alter();
    }

    public function down()
    {
        $this->updateTable('searches')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('cacheStatus');
            })
            ->useHistory(false)
            ->alter();
    }
}
