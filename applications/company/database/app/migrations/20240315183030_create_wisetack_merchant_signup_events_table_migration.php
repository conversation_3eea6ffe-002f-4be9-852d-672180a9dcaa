<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateWisetackMerchantSignupEventsTableMigration extends Migration
{
    public function up()
    {
        if (!$this->schema->hasTable('wisetackMerchantSignupEvents')) {
            $this->newTable('wisetackMerchantSignupEvents')
                ->columns(function (Blueprint $table) {
                    $table->uuid('id')->primary();
                    $table->dateTime('eventTimestamp');
                    $table->char('merchantId', 36);
                    $table->string('event');
                    $table->string('externalId');
                    $table->json('payload');
                })
                ->timestamps(false, false)
                ->useHistory(false)
                ->create();
        }
    }

    public function down()
    {
        $this->schema->dropIfExists('wisetackMerchantSignupEvents');
    }
}