<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class AddTermsConditionsColumnsToBidMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('version')->after('bidItemID');
                $table->string('acceptedSignature', 100)->nullable()->after('acceptedAt');
                $table->enum('acceptedIpAddressType', ['IPv4', 'IPv6'])->nullable()->after('acceptedSignature');
            })
            ->column('termsConditionsContentTemplateID', Table::COLUMN_TYPE_UUID, [
                'nullable' => true,
                'after' => 'lineItemsContentTemplateID'
            ])
            ->column('acceptedIpAddress', Table::COLUMN_TYPE_BINARY, [
                'length' => 16,
                'nullable' => true,
                'after' => 'acceptedIpAddressType'
            ])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn([
                    'version', 'termsConditionsContentTemplateID', 'acceptedSignature', 'acceptedIpAddressType',
                    'acceptedIpAddress'
                ]);
            })
            ->alter();
    }
}
