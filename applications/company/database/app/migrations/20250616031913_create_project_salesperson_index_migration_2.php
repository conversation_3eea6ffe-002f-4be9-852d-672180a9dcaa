<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateProjectSalespersonIndexMigration2 extends Migration
{
    public function up(): void
    {
        $this->updateTable('project')
            ->columns(function (Blueprint $table) {
                $table->dropIndex('projectSalesperson');
            })
            ->alter();

        $this->updateTable('project')
            ->indexes(function (Blueprint $table) {
                $table->index('projectSalesperson', 'project_salesperson_id_index');
            })
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('project')
            ->columns(function (Blueprint $table) {
                $table->dropIndex('project_salesperson_id_index');
            })
            ->alter();

        $this->updateTable('project')
            ->indexes(function (Blueprint $table) {
                $table->index('projectSalesperson', 'projectSalesperson');
            })
            ->alter();
    }
}
