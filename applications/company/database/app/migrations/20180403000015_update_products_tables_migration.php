<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateProductsTablesMigration extends Migration
{
    public function up()
    {
        // units
        $this->newTable('units')
            ->primaryKey('unitID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('ownerType');
                $table->unsignedInteger('ownerID');
                $table->string('alias', 100)->nullable();
                $table->string('name', 100);
                $table->string('abbreviation', 100);
                $table->unsignedTinyInteger('status');
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->index('search', Schema\Table::INDEX_TYPE_FULLTEXT, ['name', 'abbreviation'])
            ->indexes(function (Blueprint $table) {
                $table->index(['ownerType', 'ownerID']);
            })
            ->create();

        // product categories
        $this->schema->rename('productCategories', 'productCategoriesOld');
        $this->newTable('productCategories')
            ->primaryKey('productCategoryID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('ownerType');
                $table->unsignedInteger('ownerID');
                $table->string('alias', 100)->nullable();
                $table->string('name', 200);
                $table->unsignedTinyInteger('status');
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->column('parentProductCategoryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'ownerID',
                'nullable' => true
            ])
            ->index('search', Schema\Table::INDEX_TYPE_FULLTEXT, ['name'])
            ->indexes(function (Blueprint $table) {
                $table->index(['ownerType', 'ownerID']);
                $table->index('parentProductCategoryID');
            })
            ->create();

        // product items
        $this->schema->rename('products', 'productsOld');
        $this->newTable('productItems')
            ->primaryKey('productItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('ownerType');
                $table->unsignedInteger('ownerID');
                $table->string('alias', 100)->nullable();
                $table->string('name', 100);
                $table->string('description', 1000)->nullable();
                $table->unsignedTinyInteger('isIntangible');
                $table->unsignedTinyInteger('status');
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->column('unitID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'description'
            ])
            ->index('search', Schema\Table::INDEX_TYPE_FULLTEXT, ['name', 'description'])
            ->indexes(function (Blueprint $table) {
                $table->index(['ownerType', 'ownerID']);
            })
            ->create();

        // product categories items
        $this->newTable('productCategoriesItems')
            ->primaryKey('productCategoryItemID')
            ->column('productCategoryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'productCategoryItemID'
            ])
            ->column('productItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'productCategoryID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('productCategoryID');
                $table->index('productItemID');
            })
            ->create();

        // product item prices
        $this->newTable('productItemPrices')
            ->primaryKey('productItemPriceID')
            ->columns(function (Blueprint $table) {
                $table->unsignedDecimal('minCount', 12, 4);
                $table->unsignedDecimal('maxCount', 12, 4)->nullable();
                $table->unsignedDecimal('price', 12, 4);
            })
            ->column('productItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'productItemPriceID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('productItemID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'units', 'productCategories', 'productItems', 'productItemPrices', 'productCategoriesItems'
        ], true);

        $this->schema->rename('productCategoriesOld', 'productCategories');
        $this->schema->rename('productsOld', 'products');
    }
}
