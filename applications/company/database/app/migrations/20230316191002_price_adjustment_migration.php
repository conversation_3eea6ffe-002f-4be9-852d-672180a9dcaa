<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class PriceAdjustmentMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('bidItemProductLineItems')
            ->columns(
                function (Blueprint $table) {
                    $table->unsignedTinyInteger('adjustmentMode')->nullable()->after('unitAbbreviation'); // plus, minus
                }
            )
            ->useHistory(true)
            ->alter();
    }

    public function down(): void
    {

        $this->updateTable('bidItemProductLineItems')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['adjustmentMode']);
                }
            )
            ->useHistory(true)
            ->alter();
    }
}
