<?php

use App\Classes\DB\Migration;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

class UpdateProjectNoteTableMigration extends Migration
{
    public function up()
    {
        // update projectNote table
        $this->schema->table('projectNote', function (Blueprint $table) {
            $table->text('note')->nullable(false)->change();
            $table->renameColumn('noteEdited', 'updatedAt');
            $table->renameColumn('noteDeleted', 'deletedAt');
            $table->index('projectID');
            $table->index('tiedID');
        });

        // fix columns which schema builder can't handle
        DB::statement('ALTER TABLE `projectNote` MODIFY COLUMN `noteID` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT');
        DB::statement('ALTER TABLE `projectNote` MODIFY COLUMN `projectID` INT(10) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `projectNote` MODIFY COLUMN `tiedID` INT(10) UNSIGNED DEFAULT NULL');
        DB::statement('ALTER TABLE `projectNote` MODIFY COLUMN `isPinned` TINYINT(3) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `projectNote` CHANGE COLUMN `noteAdded` `createdAt` DATETIME NOT NULL');
        DB::statement('ALTER TABLE `projectNote` CHANGE COLUMN `noteAddedByID` `createdByUserID` INT(10) UNSIGNED DEFAULT NULL');
        DB::statement('ALTER TABLE `projectNote` CHANGE COLUMN `noteEditedByID` `updatedByUserID` INT(10) UNSIGNED DEFAULT NULL');
        DB::statement('ALTER TABLE `projectNote` CHANGE COLUMN `noteDeletedByID` `deletedByUserID` INT(10) UNSIGNED DEFAULT NULL');

        // add index
        DB::statement('CREATE INDEX `created_by_user_id_index` ON `projectNote` (`createdByUserID`)');
        DB::statement('CREATE INDEX `updated_by_user_id_index` ON `projectNote` (`updatedByUserID`)');

        // create history table for projectNote
        $this->newTable('projectNote')
            ->primaryKey('noteID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('projectID');
                $table->unsignedInteger('tiedID')->nullable();
                $table->text('note');
                $table->unsignedTinyInteger('isPinned');
                $table->string('noteTag', 5)->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('projectID');
                $table->index('tiedID');
            })
            ->historyOnly()
            ->create();
    }

    public function down()
    {
        $this->dropTables(['projectNoteHistory']);
    }
}
