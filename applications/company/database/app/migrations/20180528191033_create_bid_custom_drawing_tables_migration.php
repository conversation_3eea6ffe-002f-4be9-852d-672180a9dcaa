<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CreateBidCustomDrawingTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('bidItemCustomDrawings')
            ->primaryKey('bidItemCustomDrawingID')
            ->columns(function (Blueprint $table) {
                $table->unsignedMediumInteger('order');
            })
            ->column('bidItemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemCustomDrawingID'
            ])
            ->column('fileID', Table::COLUMN_TYPE_UUID, [
                'after' => 'bidItemID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['bidItemCustomDrawings'], true);
    }
}
