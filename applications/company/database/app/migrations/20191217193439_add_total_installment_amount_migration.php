<?php

use App\Classes\DB\Migration;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

class AddTotalInstallmentAmountMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItemInstallmentPaymentTermInstallments')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('amountType')->after('name'); // percentage or total
                $table->renameColumn('percentage', 'amount');
            })
            ->alter();

        // update decimal column manually since change isn't working properly in eloquent schema migration
        DB::statement('ALTER TABLE `bidItemInstallmentPaymentTermInstallments` MODIFY COLUMN `amount` DECIMAL(12, 4) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `bidItemInstallmentPaymentTermInstallmentsHistory` MODIFY COLUMN `amount` DECIMAL(12, 4) UNSIGNED NOT NULL');
    }

    public function down()
    {
        $this->updateTable('bidItemInstallmentPaymentTermInstallments')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('amountType');
                $table->renameColumn('amount', 'percentage');
            })
            ->alter();

        // update decimal column manually since change isn't working properly in eloquent schema migration
        DB::statement('ALTER TABLE `bidItemInstallmentPaymentTermInstallments` MODIFY COLUMN `percentage` DECIMAL(5, 4) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `bidItemInstallmentPaymentTermInstallmentsHistory` MODIFY COLUMN `percentage` DECIMAL(5, 4) UNSIGNED NOT NULL');
    }
}
