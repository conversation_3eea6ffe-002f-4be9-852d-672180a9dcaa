<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddDrawingNodeTypeTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('drawingNodeTypes')
            ->primaryKey('drawingNodeTypeID', false)
            ->columns(function (Blueprint $table) {
                $table->string('name', 50);
                $table->unsignedTinyInteger('showInLegend');
                $table->unsignedTinyInteger('version')->comment('Version of drawing system which node was made available');
                $table->unsignedTinyInteger('isInUse')->comment('Shows if node is currently used');
            })
            ->useHistory(false)
            ->timestamps(false, false)
            ->create();

        $this->newTable('companyDrawingNodeTypes')
            ->primaryKey('companyDrawingNodeTypeID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedInteger('drawingNodeTypeID');
                $table->string('name', 50);
                $table->unsignedTinyInteger('showInLegend');
            })
            ->indexes(function (Blueprint $table) {
                $table->unique(['companyID', 'drawingNodeTypeID'], 'company_id_drawing_node_type_index');
            })
            ->useHistory(false)
            ->timestamps(false, false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['drawingNodeTypes', 'companyDrawingNodeTypes']);
    }
}
