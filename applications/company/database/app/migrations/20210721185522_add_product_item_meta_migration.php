<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class AddProductItemMetaMigration extends Migration
{
    public function up()
    {
        $this->newTable('productItemMeta')
            ->primaryKey('productItemMetaID')
            ->columns(function (Blueprint $table) {
                $table->string('name', 100);
                $table->unsignedTinyInteger('valueType');
                $table->longText('value')->nullable();
            })
            ->column('productItemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'productItemMetaID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('productItemID', 'product_item_id_index');
            })
            ->timestamps(true, true)
            ->useHistory()
            ->create();
    }

    public function down()
    {
        $this->dropTables(['productItemMeta'], true);
    }
}
