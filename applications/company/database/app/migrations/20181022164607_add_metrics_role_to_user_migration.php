<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddMetricsRoleToUserMigration extends Migration
{
    public function up()
    {
        $this->schema->table('user', function (Blueprint $table) {
            $table->unsignedTinyInteger('metrics')->after('timecardApprover');
        });
    }

    public function down()
    {
        $this->schema->table('user', function (Blueprint $table) {
            $table->dropColumn('metrics');
        });
    }
}
