<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateFormFieldTooltipMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('formItemGroupFields')
            ->columns(function (Blueprint $table) {
                $table->string('tooltip', 1000)->nullable()->after('isRequired');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('companyFormItemFields')
            ->columns(function (Blueprint $table) {
                $table->string('tooltip', 1000)->nullable()->after('isRequired');
            })
            ->useHistory(false)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('formItemGroupFields')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('tooltip');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('companyFormItemFields')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('tooltip');
            })
            ->useHistory(false)
            ->alter();
    }
}
