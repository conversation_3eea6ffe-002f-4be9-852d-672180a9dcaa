<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

final class CreateSetupWizardMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('companies')
            ->columns(
                function (Blueprint $table) {
                    $table->unsignedTinyInteger('isSetupWizard')->nullable()->after('trialExpiresAt');
                    $table->unsignedSmallInteger('setupWizardStep')->nullable()->after('isSetupWizard');
                    $table->datetime('setupWizardCompletedAt')->nullable()->after('setupWizardStep');
                }
            )
            ->useHistory(true)
            ->alter();

        $this->newTable('companySetup')
            ->primaryKey('companySetupID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('companyID');
                $table->unsignedTinyInteger('instruction')->nullable();
                $table->dateTime('instructionCompletedAt')->nullable();
                $table->unsignedTinyInteger('general')->nullable();
                $table->dateTime('generalCompletedAt')->nullable();
                $table->unsignedTinyInteger('users')->nullable();
                $table->dateTime('usersCompletedAt')->nullable();
                $table->unsignedTinyInteger('bidCustomization')->nullable();
                $table->dateTime('bidCustomizationCompletedAt')->nullable();
                $table->unsignedTinyInteger('emails')->nullable();
                $table->dateTime('emailsCompletedAt')->nullable();
                $table->unsignedTinyInteger('termsConditions')->nullable();
                $table->dateTime('termsConditionsCompletedAt')->nullable();
                $table->unsignedTinyInteger('products')->nullable();
                $table->dateTime('productsCompletedAt')->nullable();
                $table->unsignedTinyInteger('media')->nullable();
                $table->dateTime('mediaCompletedAt')->nullable();
                $table->unsignedTinyInteger('warrantyPacket')->nullable();
                $table->dateTime('warrantyPacketCompletedAt')->nullable();
                $table->unsignedTinyInteger('quickbooksOnline')->nullable();
                $table->dateTime('quickbooksOnlineCompletedAt')->nullable();
                $table->unsignedTinyInteger('googleCalendar')->nullable();
                $table->dateTime('googleCalendarCompletedAt')->nullable();
                $table->unsignedTinyInteger('additionalServices')->nullable();
                $table->unsignedTinyInteger('isProductsServices')->nullable();
                $table->unsignedTinyInteger('isTeamTraining')->nullable();
                $table->unsignedTinyInteger('isAdditionalSupport')->nullable();
                $table->unsignedTinyInteger('isPreviousCustomerData')->nullable();
                $table->dateTime('additionalServicesCompletedAt')->nullable();
                $table->unsignedTinyInteger('setupComplete')->nullable();
                $table->dateTime('setupCompleteCompletedAt')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID', 'company_id_index');
            })
            ->useHistory(false)
            ->create();

        $this->newTable('companySetupProductItems')
            ->primaryKey('companySetupProductItemID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companySetupID');
            })
            ->column('intakeIndustryProductItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'companySetupID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('companySetupID', 'company_id_index');
            })
            ->useHistory(false)
            ->create();

        $this->newTable('intakeIndustryProductItems')
            ->primaryKey('intakeIndustryProductItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('intakeIndustryID');
                $table->string('name', 100);
                $table->string('description', 1000)->nullable();
                $table->unsignedDecimal('price', 12, 4);
                $table->string('unit', 100);
                $table->string('productCategoryName', 200);
                $table->string('formName', 100);
                $table->string('formContent', 1000)->nullable();

            })
            ->column('systemFormItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'productCategoryName',
                'nullable' => false
            ])
            ->indexes(function (Blueprint $table) {
                $table->index(['intakeIndustryID']);
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->updateTable('user')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isUserInvited')->default(0)->after('isPasswordValid');
            })
            ->useHistory(false)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('companies')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['isSetupWizard', 'setupWizardStep', 'setupWizardCompletedAt']);
                }
            )
            ->useHistory(true)
            ->alter();

        $this->dropTables(['companySetup', 'companySetupProductItems', 'intakeIndustryProductItems']);

        $this->updateTable('user')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('isUserInvited');
            })
            ->useHistory(false)
            ->alter();
    }
}
