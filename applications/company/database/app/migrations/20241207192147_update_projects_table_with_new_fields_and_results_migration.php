<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class UpdateProjectsTableWithNewFieldsAndResultsMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->updateTable('project')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('priority')->after('status')->nullable();
                $table->text('summary')->after('projectDescription')->nullable();
                $table->unsignedInteger('secondaryMarketingTypeID')->after('referralMarketingTypeID')->nullable();
            })
            ->column('resultTypeID', Table::COLUMN_TYPE_UUID, [
                'after' => 'summary',
                'nullable' => true
            ])
            ->column('type', Table::COLUMN_TYPE_UUID, [
                'after' => 'priority',
                'nullable' => true
            ])
            ->alter();

        $this->updateTable('leads')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('leadTypeID', 'projectTypeID');
            })
            ->alter();

        $this->newTable('resultTypes')
            ->primaryKey('resultTypeID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // 1 => projects, 2 => appointment, 3 => jobs
                $table->string('name', 100);
                $table->unsignedTinyInteger('status'); // active, inactive, archived
                $table->dateTime('inactiveAt')->nullable();
                $table->unsignedInteger('inactiveByUserID')->nullable();
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('status');
            })
            ->create();

        $this->moveTable('projectTypes', 'projectTypesLegacy');

        $this->moveTable('leadTypes', 'projectTypes', true);
        $this->updateTable('projectTypes')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('leadTypeID', 'projectTypeID');
            })
            ->alter();

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $this->updateTable('project')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('priority');
                $table->dropColumn('summary');
                $table->dropColumn('secondaryMarketingTypeID');
                $table->dropColumn('resultTypeID');
                $table->dropColumn('type');
            })
            ->alter();

        $this->updateTable('leads')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('projectTypeID', 'leadTypeID');
            })
            ->alter();

        $this->schema->dropIfExists('resultTypes');
        $this->schema->dropIfExists('resultTypesHistory');

        $this->moveTable('projectTypes', 'leadTypes',true);
        $this->updateTable('leadTypes')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('projectTypeID', 'leadTypeID');
            })
            ->alter();

        $this->moveTable('projectTypesLegacy', 'projectTypes');
    }
}