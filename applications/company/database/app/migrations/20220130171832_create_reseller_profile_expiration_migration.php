<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateResellerProfileExpirationMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('resellerRegistrationProfiles')
            ->columns(function (Blueprint $table) {
                $table->dateTime('expiresAt')->after('isActive')->nullable();
            })
            ->useHistory(false)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('resellerRegistrationProfiles')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['expiresAt']);
            })
            ->useHistory(false)
            ->alter();
    }
}
