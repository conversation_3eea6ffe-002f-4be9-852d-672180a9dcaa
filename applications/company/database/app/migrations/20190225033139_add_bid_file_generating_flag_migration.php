<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddBidFileGeneratingFlagMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isFileGenerating')->after('isFileValid');
                $table->unsignedTinyInteger('isScopeOfWorkFileGenerating')->after('isScopeOfWorkFileValid');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['isFileGenerating', 'isScopeOfWorkFileGenerating']);
            })
            ->alter();
    }
}
