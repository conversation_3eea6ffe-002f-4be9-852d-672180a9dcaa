<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CompanyFormMigration extends Migration
{
    public function up()
    {
        $this->moveTable('formBidItems', 'companyFormBidItems', true);
        $this->updateTable('companyFormBidItems')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('formBidItemID', 'companyFormBidItemID');
            })
            ->alter();

        $this->moveTable('formCategories', 'companyFormCategories', true);
        $this->updateTable('companyFormCategories')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('formCategoryID', 'companyFormCategoryID');
                $table->renameColumn('parentFormCategoryID', 'parentCompanyFormCategoryID');
            })
            ->alter();

        $this->moveTable('formCategoriesItems', 'companyFormCategoriesItems', true);
        $this->updateTable('companyFormCategoriesItems')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('formCategoryItemID', 'companyFormCategoryItemID');
                $table->renameColumn('formCategoryID', 'companyFormCategoryID');
                $table->renameColumn('formItemID', 'companyFormItemID');
            })
            ->alter();

        $this->updateTable('formItems')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('ownerType')->after('formItemID');
                $table->renameColumn('companyID', 'ownerID');
                $table->dropIndex(['companyID']);
            })
            ->indexes(function (Blueprint $table) {
                $table->index(['ownerType', 'ownerID'], 'owner_type_owner_id_index');
            })
            ->alter();

        // fix columns which schema builder can't handle
        DB::statement('ALTER TABLE `formItems` MODIFY COLUMN `ownerID` INT(10) UNSIGNED DEFAULT NULL');
        DB::statement('ALTER TABLE `formItemsHistory` MODIFY COLUMN `ownerID` INT(10) UNSIGNED DEFAULT NULL');

        // form items table
        $this->newTable('companyFormItems')
            ->primaryKey('companyFormItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // bid
                $table->unsignedTinyInteger('status');
                $table->string('name', 100);
                $table->unsignedMediumInteger('order');
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->column('formItemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'companyID'
            ])
            ->column('itemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'type',
                'nullable' => true
            ])
            ->column('replacedByCompanyFormItemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'archivedByUserID',
                'nullable' => true
            ])
            ->index('search', Table::INDEX_TYPE_FULLTEXT, ['name'])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index(['type', 'itemID']);
                $table->index('replacedByCompanyFormItemID');
            })
            ->create();

        $this->updateTable('bidItemSectionForms')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('formItemID', 'companyFormItemID');
            })
            ->alter();
    }

    public function down()
    {
        $this->moveTable('companyFormBidItems', 'formBidItems', true);
        $this->updateTable('formBidItems')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('companyFormBidItemID', 'formBidItemID');
            })
            ->alter();

        $this->moveTable('companyFormCategories', 'formCategories', true);
        $this->updateTable('formCategories')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('companyFormCategoryID', 'formCategoryID');
                $table->renameColumn('parentCompanyFormCategoryID', 'parentFormCategoryID');
            })
            ->alter();

        $this->moveTable('companyFormCategoriesItems', 'formCategoriesItems', true);
        $this->updateTable('formCategoriesItems')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('companyFormCategoryItemID', 'formCategoryItemID');
                $table->renameColumn('companyFormCategoryID', 'formCategoryID');
                $table->renameColumn('companyFormItemID', 'formItemID');
            })
            ->alter();

        $this->updateTable('formItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('ownerType');
                $table->renameColumn('ownerID', 'companyID');
                $table->dropIndex('owner_type_owner_id_index');
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
            })
            ->alter();

        DB::statement('ALTER TABLE `formItems` MODIFY COLUMN `companyID` INT(10) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `formItemsHistory` MODIFY COLUMN `companyID` INT(10) UNSIGNED NOT NULL');

        $this->dropTables(['companyFormItems'], true);

        $this->updateTable('bidItemSectionForms')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('companyFormItemID', 'formItemID');
            })
            ->alter();
    }
}
