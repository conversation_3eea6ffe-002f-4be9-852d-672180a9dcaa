const jsdoc = require("eslint-plugin-jsdoc");
const sortClassMembers = require("eslint-plugin-sort-class-members");
const prettier = require("eslint-plugin-prettier");

module.exports = [
    {
        files: ["**/*.js"],
        languageOptions: {
            ecmaVersion: 2021,
            sourceType: "script",
        },
        plugins: {
            jsdoc,
            "sort-class-members": sortClassMembers,
            prettier,
        },
        settings: {
            // 👇 Configurações do Prettier diretamente aqui
            prettier: {
                tabWidth: 4,
                useTabs: false,
                semi: false,
                singleQuote: true,
                trailingComma: "none",
                printWidth: 100,
            },
        },
        rules: {
            // 🔽 Desabilita uso de ponto e vírgula
            semi: "off",
            "no-unexpected-multiline": "error",

            // ✅ Estilo
            curly: ["error", "all"],
            "padding-line-between-statements": [
                "error",
                { blankLine: "always", prev: "function", next: "function" },
            ],
            "no-multiple-empty-lines": [
                "error",
                { max: 1, maxEOF: 1, maxBOF: 0 },
            ],

            // ✅ JSDoc
            "jsdoc/require-jsdoc": [
                "warn",
                {
                    require: {
                        FunctionDeclaration: true,
                        MethodDefinition: true,
                    },
                },
            ],
            "jsdoc/check-tag-names": "warn",

            // ✅ Ordem de métodos na classe
            "sort-class-members/sort-class-members": [
                "error",
                {
                    order: [
                        "[constructor]",
                        "[methods]",
                        "load",
                        "unload",
                        "boot",
                        "render",
                    ],
                    groups: {
                        load: [{ name: "load", type: "method" }],
                        unload: [{ name: "unload", type: "method" }],
                        boot: [{ name: "boot", type: "method" }],
                        render: [{ name: "render", type: "method" }],
                    },
                    accessorPairPositioning: "getThenSet",
                },
            ],

            // ✅ Aplicar Prettier como regra de lint
            "prettier/prettier": [
                "error",
                {
                    tabWidth: 4,
                    useTabs: false,
                },
            ],
        },
    },
];
